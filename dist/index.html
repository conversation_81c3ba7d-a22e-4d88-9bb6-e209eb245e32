<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/images/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数智渝泰</title>
    <script>
      (function(w, d, s, q) {
          w[q] =w[q] || [];
          var f=d.getElementsByTagName(s)[0],j=d.createElement(s);
          j.async=true;
          j.id='beacon-aplus';
          j.src='https://d.alicdn.com/alilog/mlog/aplus/205412007.js';
          f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'aplus_queue');
      let id='20000116'
      if(window.location.host=='www.cqytxny.com'){
          id='20000115'
      }
      console.log('-------------'+id)
      //集成应用的appKey
      aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['appKey', id]  })
      
      //如果是私有云部署还需要在上面那段JS后面紧接着添加日志域名埋点
      //通常私有云日志服务端域名类似于：quickaplus-web-api.xxx.com.cn, 具体域名要找交付同学要
      aplus_queue.push({ action: 'aplus.setMetaInfo', arguments: ['aplus-rhost-v', 'aserver-emas.spic.com.cn:32583']  });
      aplus_queue.push({
          action: 'aplus.setMetaInfo',
          arguments: ['DEBUG', true]
      });
      aplus_queue.push({
          action: 'aplus.setMetaInfo',
          arguments: ['aplus-disable-apv', true]
      });
      // aplus.getMetaInfo('aplus-rhost-v');
      aplus_queue.push({
          action: 'aplus.getMetaInfo',
          arguments: ['aplus-rhost-v']
      });
    </script>
    <script type="module" crossorigin src="/assets/index-db94d997.js"></script>
    <link rel="stylesheet" href="/assets/style-7416b012.css">
  </head>
  <body>
    <div id="app"></div>
    
  </body>
</html>
