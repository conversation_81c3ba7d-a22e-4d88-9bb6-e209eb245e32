import{_ as d}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as q,r as n,bL as z,K as B,o as R,a as y,v as S,f as m,e,u as i,b as E,h as f,y as M,i as u,g as x,bO as b,bg as V,bn as P,_ as T}from"./index-db94d997.js";import{b as W,c as j}from"./index-d81e3920.js";import{_ as F}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const G={class:"receivableDetail"},H={class:"text-hide"},K={class:"text-hide"},L=q({__name:"Detail",setup(O){var h;const v=n([{key:"产权公司：",dataIndex:"projectName"},{key:"装机容量(kW)：",dataIndex:"capins",formatMoney:!0},{key:"电站数量：",dataIndex:"stationCount"}]);z();const g=n([]),c=n([]);B({projectId:""});const p=n((h=history.state)==null?void 0:h.pdata),D=n([{title:"电站编号",dataIndex:"stationCode",width:120,search:!1,resizable:!0},{title:"业主名称",dataIndex:"stationName",resizable:!0,search:!1,width:120},{title:"产权公司",dataIndex:"companyName",resizable:!0,search:!1,width:120},{title:"账单周期",dataIndex:"billMonth",search:!1,width:120,resizable:!0},{title:"实收金额(元)",dataIndex:"fee",width:120,search:!1,resizable:!0,formatMoney:!0}]),C=n(),I=async()=>{var o;const a={stationCode:(o=p.value)==null?void 0:o.stationCode};j({...a}).then(_=>{console.log("导出成功")})},N=()=>{g.value=v.value.map(a=>({...a,value:p.value[a.dataIndex]}))},w=(a,o)=>new Promise(_=>{var l;let s={stationCode:(l=p.value)==null?void 0:l.stationCode};s={...s,...a},_(s)}),$=a=>{console.log(a,"mmmm---"),c.value=a||[]};return R(()=>{N()}),(a,o)=>{const _=V,s=F,l=P,k=d;return y(),S("div",G,[m(k,{columns:i(D),ref_key:"actionRef",ref:C,request:i(W),"label-col":{style:{width:"125px"}},"wrapper-col":{span:16},scroll:{x:500},search:!1,allDataSource:!0,pagination:!1,onGetDataSource:$,"before-query-params":w},{tableHeader:e(()=>[m(s,null,{default:e(()=>[i(c).length>0?(y(),E(_,{key:0,type:"primary",onClick:I},{default:e(()=>[f("导出")]),_:1})):M("",!0)]),_:1})]),cityTreeRender:e(({record:t})=>[m(l,null,{title:e(()=>[f(u(`${(t==null?void 0:t.prvName)||""}${(t==null?void 0:t.cityname)||""}${(t==null?void 0:t.distName)||""}${(t==null?void 0:t.townname)||""}${(t==null?void 0:t.vilname)||""}`),1)]),default:e(()=>[x("span",H,u(`${(t==null?void 0:t.prvName)||""}${(t==null?void 0:t.cityName)||""}${(t==null?void 0:t.distName)||""}${(t==null?void 0:t.townname)||""}${(t==null?void 0:t.vilname)||""}`),1)]),_:2},1024)]),rateRender:e(({record:t})=>[m(l,null,{title:e(()=>[f(u(t.rate?i(b)(t.rate,100)+"%":""),1)]),default:e(()=>[x("span",K,u(t.rate?i(b)(t.rate,100)+"%":""),1)]),_:2},1024)]),_:1},8,["columns","request"])])}}});const at=T(L,[["__scopeId","data-v-1a1a376c"]]);export{at as default};
