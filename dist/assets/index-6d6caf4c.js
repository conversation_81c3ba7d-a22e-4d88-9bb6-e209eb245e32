import{d as ge,I as _e,r as C,b2 as fe,w as ae,o as ve,a as o,v as n,b as ye,e as p,a1 as se,y,f as k,g as r,ag as le,n as w,u as _,i as h,F as b,x,ai as A,h as L,S as oe,b6 as ne,H as ke,q as $,bU as Ce,bn as be,et as me,bg as we,az as Ae,p as Se,j as xe,_ as Le}from"./index-db94d997.js";import{C as Pe}from"./CForm-ffa1b2bc.js";import{_ as Be}from"./index-39334618.js";const Ie="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADxSURBVHgBrZOxDcIwEEX/mYAsKjYgZVoaWkaBTABMACOwQWATOhokKCnDBumIUJLDF0gkhHAsyJdi3Sm+8/O3DfwpksE/RQNdqDmYfacq5mvqYROPwkRJ3s+wIOYZCiaXjxRNtamRWk+GgiAr7y/jMHQBCI7RFkRDiZVtomwtOES+bY5n+6lzbNAlNuFXMiuBiz4IgpNBzmklMREmYNnzNpI87fBSnLcSaCCpHa/0ynWKQSPB+bnCrKQRt0FsO532PXhTB+sUnPzc4DIKY7gQKEZsbJpWbjdJTocL3tUNeuZh3DM2b6S80i4NdjdTgzb0AH49ZhbwQM9+AAAAAElFTkSuQmCC",F=R=>(Se("data-v-f7a75c83"),R=R(),xe(),R),De={class:"table"},Ne={class:"table_header"},Te={class:"table_content"},Fe={class:"card"},Re=["src"],ze={class:"text"},Qe=["onClick"],Ue={class:"title"},Ve={class:"left"},We={key:0,class:"title-name"},Ee={key:0,class:"right-box",style:{width:"20px",height:"20px"}},Oe=["onClick"],je=F(()=>r("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.39816 3.2119C4.51532 3.09474 4.70528 3.09474 4.82243 3.21191L10.0006 8.39055L15.1788 3.21327C15.2959 3.09613 15.4859 3.09614 15.603 3.21329L16.7878 4.39813C16.905 4.5153 16.905 4.70526 16.7878 4.82241L11.6097 9.99965L16.7185 15.1085C16.8356 15.2256 16.8356 15.4156 16.7185 15.5327L15.5336 16.7176C15.4165 16.8347 15.2265 16.8347 15.1094 16.7176L10.0006 11.6088L4.82123 16.788C4.70408 16.9052 4.51413 16.9052 4.39697 16.788L3.21213 15.6032C3.09498 15.486 3.09498 15.2961 3.21213 15.1789L8.39145 9.99965L3.21331 4.82099C3.09616 4.70383 3.09617 4.51389 3.21332 4.39674L4.39816 3.2119Z",fill:"black","fill-opacity":"0.65"},null,-1)),$e=[je],Me={class:"content"},Ke={key:0,class:"item"},Ye={class:"item_left"},Ze=["onClick"],qe=F(()=>r("span",null,null,-1)),He=["onClick"],Xe=["onClick"],Je=F(()=>r("span",null,null,-1)),Ge=["onClick"],et={key:0,class:"table_footer"},tt={class:"table_content"},at=["onClick"],st={class:"title"},lt={class:"left"},ot={key:0,class:"title-name"},nt={key:1,xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",class:"left_img",style:{cursor:"pointer"}},it=F(()=>r("path",{d:"M8 1C4.14062 1 1 4.14062 1 8C1 11.8594 4.14062 15 8 15C11.8594 15 15 11.8594 15 8C15 4.14062 11.8594 1 8 1ZM8.0375 11.1562L7.42812 10.5016L10.1187 8H4V7H10.1203L7.42812 4.49844L8.0375 3.84375L11.9703 7.5L8.0375 11.1562Z",fill:"#29CCA0"},null,-1)),rt=[it],dt={key:0,class:"right-box"},ct=["onClick"],ut=F(()=>r("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.39816 3.2119C4.51532 3.09474 4.70528 3.09474 4.82243 3.21191L10.0006 8.39055L15.1788 3.21327C15.2959 3.09613 15.4859 3.09614 15.603 3.21329L16.7878 4.39813C16.905 4.5153 16.905 4.70526 16.7878 4.82241L11.6097 9.99965L16.7185 15.1085C16.8356 15.2256 16.8356 15.4156 16.7185 15.5327L15.5336 16.7176C15.4165 16.8347 15.2265 16.8347 15.1094 16.7176L10.0006 11.6088L4.82123 16.788C4.70408 16.9052 4.51413 16.9052 4.39697 16.788L3.21213 15.6032C3.09498 15.486 3.09498 15.2961 3.21213 15.1789L8.39145 9.99965L3.21331 4.82099C3.09616 4.70383 3.09617 4.51389 3.21332 4.39674L4.39816 3.2119Z",fill:"black","fill-opacity":"0.65"},null,-1)),ht=[ut],pt={key:1,class:"right-box"},gt={class:"content"},_t={key:0,class:"item"},ft={class:"item_left"},vt=["onClick"],yt=F(()=>r("span",null,null,-1)),kt=["onClick"],Ct={key:0,class:"footer"},bt=ge({__name:"index",props:{columns:{},dataSource:{},request:{},beforeQueryParams:{},pagination:{},size:{},scroll:{},labelCol:{},wrapperCol:{},search:{},rowSelection:{},defaultQuery:{type:Boolean,default:!0},allDataSource:{type:Boolean,default:!1},paginationAllData:{type:Boolean,default:!1},getDataSource:{},bordered:{type:Boolean},showReset:{type:Boolean,default:!0},showSubmit:{type:Boolean,default:!0},submitText:{default:"查询"},isEdit:{type:Boolean,default:!0},isDelete:{type:Boolean,default:!0},isAdd:{type:Boolean,default:!0},isSee:{type:Boolean,default:!0},batchVisible:{type:Boolean,default:!1},activeKey:{default:1},addText:{}},emits:["getDataSource","add","see","delete","cancel","confirm"],setup(R,{expose:ie,emit:I}){var ee;const i=R,M=_e(),Q=C(),re=(ee=i.columns)==null?void 0:ee.filter(e=>!e.hideInTable).map(e=>({...e,align:(e==null?void 0:e.align)||"left",ellipsis:{showTitle:!1},minWidth:(e==null?void 0:e.minWidth)||(e==null?void 0:e.width),maxWidth:(e==null?void 0:e.maxWidth)||600,rightWidth:(e==null?void 0:e.rightWidth)||90})),K=C(re),f=C([]),z=C(!1),U=C(0),W=C(1),P=C(),Y=C({}),de=C(["11","15","23","51"]);let D={};fe().tableHeader;const B={pageNum:1,pageSize:11},ce=()=>{I("add")},Z=e=>{I("delete",e)},q=e=>{I("see",e)},H=()=>{v.value=[];for(let e of f.value)e.selected=!1;I("cancel")},ue=()=>{I("confirm")},v=C([]),X=(e,l)=>{if(e.target.checked)v.value.push(ke(l));else for(let s in v.value)v.value[s].id==l.id&&v.value.splice(s,1)},he=()=>{let e=!0;for(let l of f.value)l.selected||(e=!1);if(e)for(let l of f.value){l.selected=!1;for(let s in v.value)l.id==v.value[s].id&&v.value.splice(s,1)}else{for(let l of f.value)l.selected=!0,v.value.push(l);v.value=$.uniqBy(v.value,"id")}};ae(()=>i.activeKey,e=>{for(let l of f.value)l.selected=!1;for(let l of v.value)for(let s of f.value)l.id==s.id&&(s.selected=!0)});const N=(e,l)=>{let s=[];for(let d of e[l.checkFieldList]){const c={label:d[l.fieldLabel],value:d[l.fieldValue]};s.push(c)}return s},V=(e,l,s)=>{const d=s[l.checkFieldList].filter(c=>c.id==e.value)[0];d[l.fieldValue]=d[l.fieldValueReplace],M.push({path:l.goUrl,state:{pdata:$.cloneDeep(d)}})},J=(e,l)=>{const d={[l.fieldValue]:e[l.fieldValueReplace]};M.push({path:l.goUrl,state:{pdata:$.cloneDeep(d)}})};ie({getInitialFormState:()=>{var e;return(e=P.value)==null?void 0:e.getInitialFormState()},getInitialFormStateNew:()=>Y.value,setLoading:e=>{z.value=e},reload:()=>{S(D)},reloadBeforeQueryParamsAndFormParams:()=>{var e;(e=P.value)==null||e.getFormParams().then(l=>{i!=null&&i.beforeQueryParams?i==null||i.beforeQueryParams().then(s=>{let d={...l,...s};S(d)}):S(l)})},getDataSource:()=>f,setDataSource:e=>{f.value=e},getFormParams:async()=>{var e;return await((e=P.value)==null?void 0:e.getFormParams())},validateFormParams:async()=>{var e;return await((e=P.value)==null?void 0:e.validateFormParams())},setFormParams:(e,l)=>new Promise(async s=>{var d,c;if(await((d=P.value)==null?void 0:d.setFormParams(e)),l){const u=await((c=P.value)==null?void 0:c.getFormParams());S({...D,...u}).then(T=>{s(T)})}}),selectAll:he,selectArr:v,cancel:H});const{request:G}=i,S=e=>new Promise(l=>{if(G){z.value=!0;const s={...B,...e};if(!(e!=null&&e.noJoin))for(const c in s){const u=s[c];Array.isArray(u)&&(s[c]=u.join(","))}delete s.current,W.value=s.pageNum,B.pageNum=s.pageNum;const d={};Object.keys(s).forEach(c=>{s[c]!==null&&s[c]!==""&&(d[c]=s[c])}),D=d,G(d).then(c=>{let{records:u,total:T}=c;i.allDataSource&&(u=c),u=Array.isArray(u)?u:[];for(let E of v.value)for(let a of u)E.id==a.id&&(a.selected=!0);f.value=u,U.value=T,z.value=!1,I("getDataSource",u,T),l(u),Y.value=D}).catch(()=>{z.value=!1})}else if(i.paginationAllData){const s={...B,...e};for(const c in s){const u=s[c];Array.isArray(u)&&(s[c]=u.join(","))}delete s.current,W.value=s.pageNum;const d={};Object.keys(s).forEach(c=>{s[c]!==null&&s[c]!==""&&(d[c]=s[c])}),D=d,f.value=i.dataSource,U.value=i.dataSource.length}});ae(()=>i.dataSource,()=>{i.paginationAllData&&(f.value=i.dataSource,U.value=i.dataSource.length)});const pe=(e,l)=>{W.value=e;const s={...D,pageNum:e,pageSize:l};S(s)};return ve(()=>{i.defaultQuery&&(i!=null&&i.beforeQueryParams?i==null||i.beforeQueryParams().then(e=>{S(e)}):S())}),(e,l)=>{const s=Ce,d=be,c=me,u=we,T=Be,E=Ae;return o(),n("div",{class:"cardListBox",ref_key:"cardListBox",ref:Q},[i.search||i.search===void 0?(o(),ye(Pe,{key:0,ref_key:"cFormRef",ref:P,"data-source":e.columns,"get-page-data":S,"label-col":i.labelCol,"wrapper-col":i.wrapperCol,"before-query-params":i.beforeQueryParams,showReset:i.showReset,showSubmit:i.showSubmit,submitText:i.submitText},{formRender:p(()=>[se(e.$slots,"formRender",{},void 0,!0)]),_:3},8,["data-source","label-col","wrapper-col","before-query-params","showReset","showSubmit","submitText"])):y("",!0),k(E,{spinning:_(z)},{default:p(()=>[r("div",De,[r("div",Ne,[se(e.$slots,"tableHeader",{},void 0,!0)]),le(r("div",null,[r("div",Te,[r("div",Fe,[r("div",{class:"background_image",style:w({height:_(f).length>0?"100%":"200px"})},[r("div",{class:"content",onClick:ce},[r("img",{src:_(Ie),style:{width:"16px",height:"16px"}},null,8,Re),r("span",ze,h(e.addText),1)])],4)]),(o(!0),n(b,null,x(_(f),(a,te)=>(o(),n("div",{class:"card",onClick:t=>q(a)},[r("div",{class:"card_content",style:w({borderTopColor:a.status===0?"#29cca0":a.status===1?"red":""})},[r("div",Ue,[r("div",Ve,[e.columns&&e.columns.length>0?(o(),n("span",We,h(a[e.columns[0].dataIndex]),1)):y("",!0)]),e.batchVisible?y("",!0):(o(),n("div",Ee,[e.isDelete?(o(),n("svg",{key:0,xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",class:"right",onClick:A(t=>Z(a),["stop"]),style:{cursor:"pointer"}},$e,8,Oe)):y("",!0)])),e.batchVisible?(o(),n("div",{key:1,class:"right-box",onClick:l[0]||(l[0]=A(()=>{},["stop"]))},[k(s,{checked:a.selected,"onUpdate:checked":t=>a.selected=t,onChange:t=>X(t,a)},null,8,["checked","onUpdate:checked","onChange"])])):y("",!0)]),r("div",Me,[(o(!0),n(b,null,x(_(K),(t,O)=>(o(),n(b,null,[O!==0?(o(),n("div",Ke,[r("span",Ye,h(t.title)+" ：",1),(t==null?void 0:t.valueType)==="select"&&!t.render&&t.checkInfo?(o(),n("span",{key:0,class:"item_right",style:w({width:t.rightWidth+"px"})},[k(d,{class:"tooltip-box",getPopupContainer:g=>_(Q)},{title:p(()=>[(o(!0),n(b,null,x(N(a,t.checkInfo),(g,m)=>(o(),n("span",{class:"light-name",key:m,onClick:A(j=>V(g,t.checkInfo,a),["stop"])},h(g.label),9,Ze))),128))]),default:p(()=>[qe,(o(!0),n(b,null,x(N(a,t.checkInfo),(g,m)=>(o(),n("span",{class:"light-name",key:m,onClick:A(j=>V(g,t.checkInfo,a),["stop"])},h(g.label)+h(N(a,t.checkInfo).length-1==m?"":"，"),9,He))),128))]),_:2},1032,["getPopupContainer"])],4)):(t==null?void 0:t.valueType)==="select"&&!t.render&&t.valueEnum?(o(),n("span",{key:1,class:"item_right",style:w({width:t.rightWidth+"px"})},[k(d,{class:"tooltip-box"},{title:p(()=>[L(h(_(oe)(a[t.dataIndex],t==null?void 0:t.valueEnum)),1)]),default:p(()=>[L(" "+h(_(oe)(a[t.dataIndex],t==null?void 0:t.valueEnum)),1)]),_:2},1024)],4)):t.goPageUrlType1?(o(),n("span",{key:2,class:"item_right",style:w({width:t.rightWidth+"px"})},[k(d,{class:"tooltip-box",getPopupContainer:g=>_(Q)},{title:p(()=>[r("span",{class:"light-name",onClick:A(g=>J(a,t.goPageUrlType1),["stop"])},h(a[t.dataIndex]),9,Xe)]),default:p(()=>[Je,r("span",{class:"light-name",onClick:A(g=>J(a,t.goPageUrlType1),["stop"])},h(a[t.dataIndex]),9,Ge)]),_:2},1032,["getPopupContainer"])],4)):(o(),n("span",{key:3,class:"item_right",style:w({width:t.rightWidth+"px"})},[k(d,{class:"tooltip-box"},{title:p(()=>[L(h(a[t.dataIndex]),1)]),default:p(()=>[L(" "+h(a[t.dataIndex]),1)]),_:2},1024)],4))])):y("",!0)],64))),256))])],4)],8,Qe))),256))]),_(f)&&_(f).length>0?(o(),n("div",et,[k(c,{total:_(U),showTotal:(a,te)=>`共 ${a} 条`,"show-size-changer":"","show-quick-jumper":"",pageSizeOptions:_(de),current:B.pageNum,"onUpdate:current":l[1]||(l[1]=a=>B.pageNum=a),"page-size":B.pageSize,"onUpdate:pageSize":l[2]||(l[2]=a=>B.pageSize=a),onChange:pe},null,8,["total","showTotal","pageSizeOptions","current","page-size"])])):y("",!0)],512),[[ne,e.activeKey==1]]),le(r("div",null,[r("div",tt,[(o(!0),n(b,null,x(_(v),(a,te)=>(o(),n("div",{class:"card",onClick:t=>q(a)},[r("div",{class:"card_content",style:w({borderTopColor:a.status===0?"#29cca0":a.status===1?"red":""})},[r("div",st,[r("div",lt,[e.columns&&e.columns.length>0?(o(),n("span",ot,h(a[e.columns[0].dataIndex]),1)):y("",!0),e.isSee?(o(),n("svg",nt,rt)):y("",!0)]),e.batchVisible?y("",!0):(o(),n("div",dt,[e.isDelete?(o(),n("svg",{key:0,xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",class:"right",onClick:A(t=>Z(a),["stop"]),style:{cursor:"pointer"}},ht,8,ct)):y("",!0)])),e.batchVisible?(o(),n("div",pt,[k(s,{checked:a.selected,"onUpdate:checked":t=>a.selected=t,onChange:t=>X(t,a)},null,8,["checked","onUpdate:checked","onChange"])])):y("",!0)]),r("div",gt,[(o(!0),n(b,null,x(_(K),(t,O)=>(o(),n(b,null,[O!==0?(o(),n("div",_t,[r("span",ft,h(t.title)+" ：",1),(t==null?void 0:t.valueType)==="select"&&!t.render?(o(),n("span",{key:0,class:"item_right",style:w({width:t.rightWidth+"px"})},[k(d,{class:"tooltip-box",getPopupContainer:g=>_(Q)},{title:p(()=>[(o(!0),n(b,null,x(N(a,t.checkInfo),(g,m)=>(o(),n("span",{class:"light-name",key:m,onClick:A(j=>V(g,t.checkInfo,a),["stop"])},h(g.label),9,vt))),128))]),default:p(()=>[yt,(o(!0),n(b,null,x(N(a,t.checkInfo),(g,m)=>(o(),n("span",{class:"light-name",key:m,onClick:A(j=>V(g,t.checkInfo,a),["stop"])},h(g.label)+h(N(a,t.checkInfo).length-1==m?"":"，"),9,kt))),128))]),_:2},1032,["getPopupContainer"])],4)):(o(),n("span",{key:1,class:"item_right",style:w({width:t.rightWidth+"px"})},[k(d,{class:"tooltip-box"},{title:p(()=>[L(h(a[t.dataIndex]),1)]),default:p(()=>[L(" "+h(a[t.dataIndex]),1)]),_:2},1024)],4))])):y("",!0)],64))),256))])],4)],8,at))),256))])],512),[[ne,e.activeKey==2]])]),e.batchVisible?(o(),n("div",Ct,[k(T,null,{default:p(()=>[k(u,{onClick:H},{default:p(()=>[L("取消")]),_:1}),k(u,{type:"primary",onClick:ue},{default:p(()=>[L("确认分配")]),_:1})]),_:1})])):y("",!0)]),_:3},8,["spinning"])],512)}}});const St=Le(bt,[["__scopeId","data-v-f7a75c83"]]);export{St as _};
