import{a2 as r,bP as t}from"./index-db94d997.js";function a(e){return r({url:"/web/receivableEqFeeForecast/v1/forecast",method:"POST",isTable:!0,data:e})}function s(e){return t({url:"/web/receivableEqFeeForecast/v1/exportForecast",method:"POST",data:e})}function i(e){return r({url:"/web/powerReceivables/v1/pages",method:"POST",isTable:!0,data:e})}function l(e){return t({url:"/web/powerReceivables/v1/listExport",method:"POST",data:e})}function c(e){return r({url:"/web/powerReceivables/v1/planEqConfirm",method:"GET",data:e})}function n(e){return r({url:"/web/powerReceivables/v1/detailPages",method:"POST",isTable:!0,data:e})}function u(e){return t({url:"/web/powerReceivables/v1/detailExport",method:"POST",data:e})}export{i as a,l as b,c,n as d,s as e,u as f,a as g};
