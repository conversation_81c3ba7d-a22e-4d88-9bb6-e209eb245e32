import{cH as M,d as p,bu as b,c as r,bo as a,bs as d,bz as S,f as s}from"./index-db94d997.js";var P=function(){return{prefixCls:String,type:{type:String,default:"horizontal"},dashed:{type:Boolean,default:!1},orientation:{type:String,default:"center"},plain:{type:Boolean,default:!1},orientationMargin:[String,Number]}},j=p({compatConfig:{MODE:3},name:"ADivider",props:P(),setup:function(n,g){var l=g.slots,u=b("divider",n),o=u.prefixCls,h=u.direction,c=r(function(){return n.orientation==="left"&&n.orientationMargin!=null}),f=r(function(){return n.orientation==="right"&&n.orientationMargin!=null}),m=r(function(){var t,i=n.type,x=n.dashed,_=n.plain,e=o.value;return t={},a(t,e,!0),a(t,"".concat(e,"-").concat(i),!0),a(t,"".concat(e,"-dashed"),!!x),a(t,"".concat(e,"-plain"),!!_),a(t,"".concat(e,"-rtl"),h.value==="rtl"),a(t,"".concat(e,"-no-default-orientation-margin-left"),c.value),a(t,"".concat(e,"-no-default-orientation-margin-right"),f.value),t}),y=r(function(){var t=typeof n.orientationMargin=="number"?"".concat(n.orientationMargin,"px"):n.orientationMargin;return d(d({},c.value&&{marginLeft:t}),f.value&&{marginRight:t})}),C=r(function(){return n.orientation.length>0?"-"+n.orientation:n.orientation});return function(){var t,i=S((t=l.default)===null||t===void 0?void 0:t.call(l));return s("div",{class:[m.value,i.length?"".concat(o.value,"-with-text ").concat(o.value,"-with-text").concat(C.value):""],role:"separator"},[i.length?s("span",{class:"".concat(o.value,"-inner-text"),style:y.value},[i]):null])}}});const D=M(j);export{D as _};
