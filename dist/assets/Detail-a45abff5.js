import{_ as z}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as T,r as s,o as S,D as N,au as k,a as b,v as M,f as x,e as p,u as v,b as K,h as F,y as P,bg as E,_ as q}from"./index-db94d997.js";import{a as B,b as L}from"./index-ca482e6a.js";import{_ as V}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const A={class:"areaPrice"},R=T({__name:"Detail",setup(j){var m;const y=s(((m=history.state)==null?void 0:m.pdata)||{}),i=s();s([]);const d=s([]);s([{label:"是",value:1},{label:"否",value:0}]);let c="",u="";const w=[{title:"电站编码",dataIndex:"stationCode",resizable:!0,fixed:"left",width:120},{title:"业主名称",dataIndex:"stationName",resizable:!0,fixed:"left",width:120},{title:"产权公司",dataIndex:"companyName",width:150,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:d,valueTreeLoad:({id:t})=>f(t),onSelect:(t,e,a)=>{var n,l;const r=((l=(n=a==null?void 0:a.triggerNode)==null?void 0:n.props)==null?void 0:l.level)||1;c=["provinceCode","cityCode","areaCode","town","vil"][r-1],u=t},render:!0,width:120,resizable:!0,hideInTable:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:100},{title:"月份",dataIndex:"monthKey",resizable:!0,search:!1,width:120},{title:"组件数量",dataIndex:"componentQuantity",resizable:!0,search:!1,width:120},{title:"装机容量",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0,search:!1},{title:"分享价格",dataIndex:"sharePrice",resizable:!0,search:!1,width:120},{title:"农户收益",dataIndex:"farmerEarnings",formatMoney:!0,resizable:!0,search:!1,width:120},{title:"季度支付调整",dataIndex:"seasonPayAdjust",formatMoney:!0,resizable:!0,search:!1,width:120},{title:"特殊支付调整",dataIndex:"specialPayAdjust",formatMoney:!0,resizable:!0,search:!1,width:120},{title:"本月应付预测",dataIndex:"monthPayForecast",formatMoney:!0,resizable:!0,search:!1,width:120}];S(()=>{f()}),N(()=>{i.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&D()});const f=t=>new Promise((e,a)=>{k({pid:t||"0"}).then(r=>{console.log("行政区res=",r);let o=h(r);d.value=o,console.log(d.value),e(!0)}).catch(()=>{a()})}),h=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,h(e.subDistrict))}),t),I=()=>{var e;let t=(e=i.value)==null?void 0:e.getInitialFormStateNew();L(t)},_=s([]),g=t=>{_.value=t||[]},D=()=>{var t;(t=i.value)==null||t.reload()},C=(t,e)=>{let a={};return new Promise(r=>{var n,l;e&&e.hasOwnProperty("cityTree")&&!(e!=null&&e.cityTree)&&(c="",u=""),c&&(a={[c]:u});const o={...a,...t,noJoin:!0,delStatus:0,companyCode:(n=y.value)==null?void 0:n.companyCode,monthKey:(l=y.value)==null?void 0:l.monthKey};r(o)})};return(t,e)=>{const a=E,r=V,o=z;return b(),M("div",A,[x(o,{columns:w,ref_key:"actionRef",ref:i,request:v(B),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:g,"before-query-params":C},{tableHeader:p(()=>[x(r,null,{default:p(()=>[v(_).length>0?(b(),K(a,{key:0,type:"primary",onClick:I},{default:p(()=>[F("导出")]),_:1})):P("",!0)]),_:1})]),_:1},8,["request"])])}}});const te=q(R,[["__scopeId","data-v-eb117760"]]);export{te as default};
