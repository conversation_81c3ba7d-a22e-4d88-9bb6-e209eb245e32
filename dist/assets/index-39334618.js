import{cH as V,d as k,bu as H,cI as J,c as o,r as F,w as L,cJ as M,bD as R,bo as y,bs as p,bF as W,f,F as q,bt as $,bE as B}from"./index-db94d997.js";var K={small:8,middle:16,large:24},Q=function(){return{prefixCls:String,size:{type:[String,Number,Array]},direction:$.oneOf(B("horizontal","vertical")).def("horizontal"),align:$.oneOf(B("start","end","center","baseline")),wrap:{type:<PERSON>olean,default:void 0}}};function U(n){return typeof n=="string"?K[n]:n||0}var X=k({compatConfig:{MODE:3},name:"ASpace",props:Q(),slots:["split"],setup:function(t,G){var c=G.slots,d=H("space",t),l=d.prefixCls,O=d.space,x=d.direction,z=J(),i=o(function(){var a,e,r;return(a=(e=t.size)!==null&&e!==void 0?e:(r=O.value)===null||r===void 0?void 0:r.size)!==null&&a!==void 0?a:"small"}),m=F(),s=F();L(i,function(){var a=(Array.isArray(i.value)?i.value:[i.value,i.value]).map(function(r){return U(r)}),e=M(a,2);m.value=e[0],s.value=e[1]},{immediate:!0});var S=o(function(){return t.align===void 0&&t.direction==="horizontal"?"center":t.align}),P=o(function(){var a;return R(l.value,"".concat(l.value,"-").concat(t.direction),(a={},y(a,"".concat(l.value,"-rtl"),x.value==="rtl"),y(a,"".concat(l.value,"-align-").concat(S.value),S.value),a))}),j=o(function(){return x.value==="rtl"?"marginLeft":"marginRight"}),D=o(function(){var a={};return z.value&&(a.columnGap="".concat(m.value,"px"),a.rowGap="".concat(s.value,"px")),p(p({},a),t.wrap&&{flexWrap:"wrap",marginBottom:"".concat(-s.value,"px")})});return function(){var a,e,r=t.wrap,b=t.direction,E=b===void 0?"horizontal":b,h=(a=c.default)===null||a===void 0?void 0:a.call(c),w=W(h),C=w.length;if(C===0)return null;var u=(e=c.split)===null||e===void 0?void 0:e.call(c),A="".concat(l.value,"-item"),I=m.value,g=C-1;return f("div",{class:P.value,style:D.value},[w.map(function(N,_){var T=h.indexOf(N),v={};return z.value||(E==="vertical"?_<g&&(v={marginBottom:"".concat(I/(u?2:1),"px")}):v=p(p({},_<g&&y({},j.value,"".concat(I/(u?2:1),"px"))),r&&{paddingBottom:"".concat(s.value,"px")})),f(q,{key:T},[f("div",{class:A,style:v},[N]),_<g&&u&&f("span",{class:"".concat(A,"-split"),style:v},[u])])})])}}});const Z=V(X);export{Z as _};
