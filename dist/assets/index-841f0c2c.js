import{_ as Z}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as K,bP as F,d as ee,I as te,r as f,o as ae,D as se,at as ne,a as o,v as r,g,f as d,e as s,h as c,b as S,u as _,i as u,y as w,bi as le,bc as N,bg as oe,av as ie,bn as de,p as re,j as ce,_ as ue}from"./index-db94d997.js";import{p as m,c as pe}from"./dictLocal-9822709a.js";import{E as _e}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as ye}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";function fe(i){return K({url:"/web/specialPay/v1/page",method:"POST",isTable:!0,data:i})}function me(i){return F({url:"/web/specialPay/v1/exprot",method:"POST",data:i})}function M(i){return K({url:"/web/specialPay/v1/update",method:"POST",data:i})}const R=i=>(re("data-v-835d7ed9"),i=i(),ce(),i),he={class:"areaPrice"},xe={class:"areaPrice_table"},be={key:1,class:"status_tag"},ge={key:0,class:"tag_three"},ke={key:1,class:"tag_four"},ve={key:2,class:"tag_one"},Ie={key:3,class:"tag_two"},Se={class:"text-hide"},we={key:0,class:""},Ce=R(()=>g("span",{class:""},"已确认",-1)),Te=[Ce],Pe={key:1,class:"g"},qe=R(()=>g("span",{class:""},"待确认",-1)),ze=[qe],De={class:"text-hide"},Ee=ee({__name:"index",setup(i){const L=te(),k=f();f([]);const P=f([]),v=f(!1),I=f(!1),B=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:1,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:P,render:!0,resizable:!0,order:4,fixed:"left"},{title:"支付金额",dataIndex:"payMoney",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"支付方式",dataIndex:"payWay",valueType:"select",valueEnum:pe,resizable:!0,order:3,width:120},{title:"收益归属季度",key:"quarterKey",dataIndex:"quarterKey",valueType:"quarterDate",resizable:!0,render:!0,width:120},{title:"确认状态",key:"confirmStatus",dataIndex:"confirmStatus",resizable:!0,render:!0,search:!1,width:120},{title:"支付状态",key:"value",dataIndex:"payStatus",valueType:"select",valueEnum:m,resizable:!0,render:!0,width:120},{title:"确认时间",dataIndex:"confirmDate",resizable:!0,search:!1,width:150},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:150}];ae(()=>{O()}),se(()=>{k.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&C()});const O=()=>{ne({}).then(e=>{let l=(e||[]).map(p=>({label:p.companyName,value:p.companyCode}));P.value=l})},U=async(e,n)=>{le.confirm({title:"确认提示",icon:d(_e),content:"点击确认后将不可修改，请慎重",okText:"确定",cancelText:"取消",centered:!0,onCancel:$,onOk:()=>V(e)})},V=e=>{if(v.value)return;const n={id:e.id,confirmStatus:1};q(n,"2")},$=()=>{console.log("confirm-取消")},W=e=>{e.isEdit=!0,e.payStatusTem=e.payStatus},j=e=>{e.isEdit=!1,e.payStatus=e.payStatusTem},A=e=>{const n={id:e.id,payStatus:e.payStatus};q(n,"1")},q=(e,n)=>{n=="1"?(I.value=!0,M(e).then(l=>{I.value=!1,N.info("保存成功"),C(!0)}).catch(l=>{I.value=!1})):(console.log(e,"确认--"),v.value=!0,M(e).then(l=>{v.value=!1,N.info("确认成功"),C()}).catch(l=>{v.value=!1}))},G=()=>{L.push({path:"/financeManage/farmerIncomeManage/specialPaymentsDocument/dataImport",query:{templateType:12,fileType:".csv,.xls,.xlsx",fileSize:30}})},H=()=>{var n;let e=(n=k.value)==null?void 0:n.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),me(e)},J=f([]),Q=e=>{J.value=e||[]},C=e=>{var n;(n=k.value)==null||n.reload(e)},Y=(e,n)=>{let l={};return new Promise(p=>{if(e!=null&&e.quarterKey){const h=new Date(e==null?void 0:e.quarterKey),T=h.getMonth(),a=h.getFullYear(),t=Math.floor(T/3)+1;l={yearKey:a,quarter:t}}const y={...l,...e,noJoin:!0,delStatus:0};y==null||delete y.quarterKey,p(y)})};return(e,n)=>{const l=oe,p=ye,y=ie,h=de,T=Z;return o(),r("div",he,[g("div",xe,[d(T,{columns:B,ref_key:"actionRef",ref:k,request:_(fe),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:Q,"before-query-params":Y},{tableHeader:s(()=>[d(p,null,{default:s(()=>[d(l,{onClick:G},{default:s(()=>[c("导入支付单")]),_:1}),d(l,{type:"primary",onClick:H},{default:s(()=>[c("导出")]),_:1})]),_:1})]),valueRender:s(({column:a,record:t,index:x})=>{var b,z,D,E;return[t.isEdit?(o(),S(y,{key:0,value:t[a.dataIndex],"onUpdate:value":X=>t[a.dataIndex]=X,options:_(m),placeholder:"请选择"},null,8,["value","onUpdate:value","options"])):(o(),r("span",be,[t[a.dataIndex]=="1"?(o(),r("span",ge,u(t[a.dataIndex]&&((b=_(m)[t[a.dataIndex]-1])==null?void 0:b.label)),1)):t[a.dataIndex]=="2"?(o(),r("span",ke,u(t[a.dataIndex]&&((z=_(m)[t[a.dataIndex]-1])==null?void 0:z.label)),1)):t[a.dataIndex]=="3"?(o(),r("span",ve,u(t[a.dataIndex]&&((D=_(m)[t[a.dataIndex]-1])==null?void 0:D.label)),1)):t[a.dataIndex]=="4"?(o(),r("span",Ie,u(t[a.dataIndex]&&((E=_(m)[t[a.dataIndex]-1])==null?void 0:E.label)),1)):w("",!0)]))]}),companyCodeListRender:s(({column:a,record:t,index:x})=>[d(h,null,{title:s(()=>[c(u(t.companyName),1)]),default:s(()=>[g("span",Se,u(t.companyName),1)]),_:2},1024)]),confirmStatusRender:s(({column:a,record:t,index:x})=>[t[a.dataIndex]==1?(o(),r("span",we,Te)):(o(),r("span",Pe,ze))]),quarterKeyRender:s(({column:a,record:t,index:x})=>[d(h,null,{title:s(()=>[c(u(t.yearKey+"-"+t.quarter+"季度"),1)]),default:s(()=>[g("span",De,u(t.yearKey+"-"+t.quarter+"季度"),1)]),_:2},1024)]),actionRender:s(({column:a,record:t,index:x})=>[d(p,null,{default:s(()=>[t.isEdit?w("",!0):(o(),S(l,{key:0,type:"link",size:"small",onClick:b=>W(t),disabled:t.confirmStatus=="1"},{default:s(()=>[c("编辑")]),_:2},1032,["onClick","disabled"])),t.isEdit?(o(),S(l,{key:1,type:"link",size:"small",onClick:b=>j(t),disabled:!1},{default:s(()=>[c("取消")]),_:2},1032,["onClick"])):w("",!0),t.isEdit?(o(),S(l,{key:2,type:"link",size:"small",onClick:b=>A(t),loading:_(I)},{default:s(()=>[c("保存")]),_:2},1032,["onClick","loading"])):w("",!0),d(l,{type:"link",size:"small",onClick:()=>U(t,x),disabled:t.confirmStatus=="1"},{default:s(()=>[c("确认")]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1},8,["request"])])])}}});const We=ue(Ee,[["__scopeId","data-v-835d7ed9"]]);export{We as default};
