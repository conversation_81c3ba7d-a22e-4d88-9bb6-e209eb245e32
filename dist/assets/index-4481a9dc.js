import{d as Z,af as q,a as E,b as O,e as X,a1 as ce,aI as Se,cy as Te,u as Be,a_ as Ae,aj as ee,aq as ye,cz as oe,a7 as Ie,aC as Q,aZ as de,K as Le,aT as _,a5 as Ne,cj as Oe,ab as $e,aa as z,bm as ue,ao as Y,r as D,cA as te,cB as Pe,E as Me,aP as He,cC as Fe,bb as Ce,w,V as fe,cD as ze,aA as L,ag as se,b6 as ae,v as H,g as re,aB as qe,Z as x,ai as A,y as U,f as ne,n as je,F as ke,x as ve,cw as pe,o as Re,cE as Ue,cx as We,aS as I,aK as Ye,c as Ze,cF as Ge,i as Je}from"./index-db94d997.js";import{s as Qe}from"./index-7c60ebfa.js";import{i as Ve}from"./icon-831229e8.js";const Xe=Z({name:"ElCollapseTransition"}),_e=Z({...Xe,setup(t){const e=q("collapse-transition"),o=n=>{n.style.maxHeight="",n.style.overflow=n.dataset.oldOverflow,n.style.paddingTop=n.dataset.oldPaddingTop,n.style.paddingBottom=n.dataset.oldPaddingBottom},d={beforeEnter(n){n.dataset||(n.dataset={}),n.dataset.oldPaddingTop=n.style.paddingTop,n.dataset.oldPaddingBottom=n.style.paddingBottom,n.style.height&&(n.dataset.elExistsHeight=n.style.height),n.style.maxHeight=0,n.style.paddingTop=0,n.style.paddingBottom=0},enter(n){requestAnimationFrame(()=>{n.dataset.oldOverflow=n.style.overflow,n.dataset.elExistsHeight?n.style.maxHeight=n.dataset.elExistsHeight:n.scrollHeight!==0?n.style.maxHeight=`${n.scrollHeight}px`:n.style.maxHeight=0,n.style.paddingTop=n.dataset.oldPaddingTop,n.style.paddingBottom=n.dataset.oldPaddingBottom,n.style.overflow="hidden"})},afterEnter(n){n.style.maxHeight="",n.style.overflow=n.dataset.oldOverflow},enterCancelled(n){o(n)},beforeLeave(n){n.dataset||(n.dataset={}),n.dataset.oldPaddingTop=n.style.paddingTop,n.dataset.oldPaddingBottom=n.style.paddingBottom,n.dataset.oldOverflow=n.style.overflow,n.style.maxHeight=`${n.scrollHeight}px`,n.style.overflow="hidden"},leave(n){n.scrollHeight!==0&&(n.style.maxHeight=0,n.style.paddingTop=0,n.style.paddingBottom=0)},afterLeave(n){o(n)},leaveCancelled(n){o(n)}};return(n,s)=>(E(),O(Ae,Se({name:Be(e).b()},Te(d)),{default:X(()=>[ce(n.$slots,"default")]),_:3},16,["name"]))}});var et=ee(_e,[["__file","collapse-transition.vue"]]);const tt=ye(et),F="$treeNodeId",ge=function(t,e){!e||e[F]||Object.defineProperty(e,F,{value:t.id,enumerable:!1,configurable:!1,writable:!1})},he=function(t,e){return t?e[t]:e[F]},ie=(t,e,o)=>{const d=t.value.currentNode;o();const n=t.value.currentNode;d!==n&&e("current-change",n?n.data:null,n)},le=t=>{let e=!0,o=!0,d=!0;for(let n=0,s=t.length;n<s;n++){const a=t[n];(a.checked!==!0||a.indeterminate)&&(e=!1,a.disabled||(d=!1)),(a.checked!==!1||a.indeterminate)&&(o=!1)}return{all:e,none:o,allWithoutDisable:d,half:!e&&!o}},W=function(t){if(t.childNodes.length===0||t.loading)return;const{all:e,none:o,half:d}=le(t.childNodes);e?(t.checked=!0,t.indeterminate=!1):d?(t.checked=!1,t.indeterminate=!0):o&&(t.checked=!1,t.indeterminate=!1);const n=t.parent;!n||n.level===0||t.store.checkStrictly||W(n)},V=function(t,e){const o=t.store.props,d=t.data||{},n=o[e];if(_(n))return n(d,t);if(Ne(n))return d[n];if(de(n)){const s=d[e];return s===void 0?"":s}};let nt=0;class ${constructor(e){this.id=nt++,this.text=null,this.checked=!1,this.indeterminate=!1,this.data=null,this.expanded=!1,this.parent=null,this.visible=!0,this.isCurrent=!1,this.canFocus=!1;for(const o in e)oe(e,o)&&(this[o]=e[o]);this.level=0,this.loaded=!1,this.childNodes=[],this.loading=!1,this.parent&&(this.level=this.parent.level+1)}initialize(){const e=this.store;if(!e)throw new Error("[Node]store is required!");e.registerNode(this);const o=e.props;if(o&&typeof o.isLeaf<"u"){const s=V(this,"isLeaf");Ie(s)&&(this.isLeafByUser=s)}if(e.lazy!==!0&&this.data?(this.setData(this.data),e.defaultExpandAll&&(this.expanded=!0,this.canFocus=!0)):this.level>0&&e.lazy&&e.defaultExpandAll&&!this.isLeafByUser&&this.expand(),Q(this.data)||ge(this,this.data),!this.data)return;const d=e.defaultExpandedKeys,n=e.key;n&&d&&d.includes(this.key)&&this.expand(null,e.autoExpandParent),n&&e.currentNodeKey!==void 0&&this.key===e.currentNodeKey&&(e.currentNode=this,e.currentNode.isCurrent=!0),e.lazy&&e._initDefaultCheckedNode(this),this.updateLeafState(),this.parent&&(this.level===1||this.parent.expanded===!0)&&(this.canFocus=!0)}setData(e){Q(e)||ge(this,e),this.data=e,this.childNodes=[];let o;this.level===0&&Q(this.data)?o=this.data:o=V(this,"children")||[];for(let d=0,n=o.length;d<n;d++)this.insertChild({data:o[d]})}get label(){return V(this,"label")}get key(){const e=this.store.key;return this.data?this.data[e]:null}get disabled(){return V(this,"disabled")}get nextSibling(){const e=this.parent;if(e){const o=e.childNodes.indexOf(this);if(o>-1)return e.childNodes[o+1]}return null}get previousSibling(){const e=this.parent;if(e){const o=e.childNodes.indexOf(this);if(o>-1)return o>0?e.childNodes[o-1]:null}return null}contains(e,o=!0){return(this.childNodes||[]).some(d=>d===e||o&&d.contains(e))}remove(){const e=this.parent;e&&e.removeChild(this)}insertChild(e,o,d){if(!e)throw new Error("InsertChild error: child is required.");if(!(e instanceof $)){if(!d){const n=this.getChildren(!0);n.includes(e.data)||(de(o)||o<0?n.push(e.data):n.splice(o,0,e.data))}Object.assign(e,{parent:this,store:this.store}),e=Le(new $(e)),e instanceof $&&e.initialize()}e.level=this.level+1,de(o)||o<0?this.childNodes.push(e):this.childNodes.splice(o,0,e),this.updateLeafState()}insertBefore(e,o){let d;o&&(d=this.childNodes.indexOf(o)),this.insertChild(e,d)}insertAfter(e,o){let d;o&&(d=this.childNodes.indexOf(o),d!==-1&&(d+=1)),this.insertChild(e,d)}removeChild(e){const o=this.getChildren()||[],d=o.indexOf(e.data);d>-1&&o.splice(d,1);const n=this.childNodes.indexOf(e);n>-1&&(this.store&&this.store.deregisterNode(e),e.parent=null,this.childNodes.splice(n,1)),this.updateLeafState()}removeChildByData(e){let o=null;for(let d=0;d<this.childNodes.length;d++)if(this.childNodes[d].data===e){o=this.childNodes[d];break}o&&this.removeChild(o)}expand(e,o){const d=()=>{if(o){let n=this.parent;for(;n.level>0;)n.expanded=!0,n=n.parent}this.expanded=!0,e&&e(),this.childNodes.forEach(n=>{n.canFocus=!0})};this.shouldLoadData()?this.loadData(n=>{Q(n)&&(this.checked?this.setChecked(!0,!0):this.store.checkStrictly||W(this),d())}):d()}doCreateChildren(e,o={}){e.forEach(d=>{this.insertChild(Object.assign({data:d},o),void 0,!0)})}collapse(){this.expanded=!1,this.childNodes.forEach(e=>{e.canFocus=!1})}shouldLoadData(){return this.store.lazy===!0&&this.store.load&&!this.loaded}updateLeafState(){if(this.store.lazy===!0&&this.loaded!==!0&&typeof this.isLeafByUser<"u"){this.isLeaf=this.isLeafByUser;return}const e=this.childNodes;if(!this.store.lazy||this.store.lazy===!0&&this.loaded===!0){this.isLeaf=!e||e.length===0;return}this.isLeaf=!1}setChecked(e,o,d,n){if(this.indeterminate=e==="half",this.checked=e===!0,this.store.checkStrictly)return;if(!(this.shouldLoadData()&&!this.store.checkDescendants)){const{all:a,allWithoutDisable:i}=le(this.childNodes);!this.isLeaf&&!a&&i&&(this.checked=!1,e=!1);const f=()=>{if(o){const y=this.childNodes;for(let h=0,p=y.length;h<p;h++){const N=y[h];n=n||e!==!1;const k=N.disabled?N.checked:n;N.setChecked(k,o,!0,n)}const{half:l,all:c}=le(y);c||(this.checked=c,this.indeterminate=l)}};if(this.shouldLoadData()){this.loadData(()=>{f(),W(this)},{checked:e!==!1});return}else f()}const s=this.parent;!s||s.level===0||d||W(s)}getChildren(e=!1){if(this.level===0)return this.data;const o=this.data;if(!o)return null;const d=this.store.props;let n="children";return d&&(n=d.children||"children"),o[n]===void 0&&(o[n]=null),e&&!o[n]&&(o[n]=[]),o[n]}updateChildren(){const e=this.getChildren()||[],o=this.childNodes.map(s=>s.data),d={},n=[];e.forEach((s,a)=>{const i=s[F];!!i&&o.findIndex(y=>y[F]===i)>=0?d[i]={index:a,data:s}:n.push({index:a,data:s})}),this.store.lazy||o.forEach(s=>{d[s[F]]||this.removeChildByData(s)}),n.forEach(({index:s,data:a})=>{this.insertChild({data:a},s)}),this.updateLeafState()}loadData(e,o={}){if(this.store.lazy===!0&&this.store.load&&!this.loaded&&(!this.loading||Object.keys(o).length)){this.loading=!0;const d=s=>{this.childNodes=[],this.doCreateChildren(s,o),this.loaded=!0,this.loading=!1,this.updateLeafState(),e&&e.call(this,s)},n=()=>{this.loading=!1};this.store.load(this,d,n)}else e&&e.call(this)}eachNode(e){const o=[this];for(;o.length;){const d=o.shift();o.unshift(...d.childNodes),e(d)}}reInitChecked(){this.store.checkStrictly||W(this)}}class ot{constructor(e){this.currentNode=null,this.currentNodeKey=null;for(const o in e)oe(e,o)&&(this[o]=e[o]);this.nodesMap={}}initialize(){if(this.root=new $({data:this.data,store:this}),this.root.initialize(),this.lazy&&this.load){const e=this.load;e(this.root,o=>{this.root.doCreateChildren(o),this._initDefaultCheckedNodes()})}else this._initDefaultCheckedNodes()}filter(e){const o=this.filterNodeMethod,d=this.lazy,n=function(s){const a=s.root?s.root.childNodes:s.childNodes;if(a.forEach(i=>{i.visible=o.call(i,e,i.data,i),n(i)}),!s.visible&&a.length){let i=!0;i=!a.some(f=>f.visible),s.root?s.root.visible=i===!1:s.visible=i===!1}e&&s.visible&&!s.isLeaf&&(!d||s.loaded)&&s.expand()};n(this)}setData(e){e!==this.root.data?(this.nodesMap={},this.root.setData(e),this._initDefaultCheckedNodes(),this.setCurrentNodeKey(this.currentNodeKey)):this.root.updateChildren()}getNode(e){if(e instanceof $)return e;const o=Oe(e)?he(this.key,e):e;return this.nodesMap[o]||null}insertBefore(e,o){const d=this.getNode(o);d.parent.insertBefore({data:e},d)}insertAfter(e,o){const d=this.getNode(o);d.parent.insertAfter({data:e},d)}remove(e){const o=this.getNode(e);o&&o.parent&&(o===this.currentNode&&(this.currentNode=null),o.parent.removeChild(o))}append(e,o){const d=$e(o)?this.root:this.getNode(o);d&&d.insertChild({data:e})}_initDefaultCheckedNodes(){const e=this.defaultCheckedKeys||[],o=this.nodesMap;e.forEach(d=>{const n=o[d];n&&n.setChecked(!0,!this.checkStrictly)})}_initDefaultCheckedNode(e){(this.defaultCheckedKeys||[]).includes(e.key)&&e.setChecked(!0,!this.checkStrictly)}setDefaultCheckedKey(e){e!==this.defaultCheckedKeys&&(this.defaultCheckedKeys=e,this._initDefaultCheckedNodes())}registerNode(e){const o=this.key;!e||!e.data||(o?e.key!==void 0&&(this.nodesMap[e.key]=e):this.nodesMap[e.id]=e)}deregisterNode(e){!this.key||!e||!e.data||(e.childNodes.forEach(d=>{this.deregisterNode(d)}),delete this.nodesMap[e.key])}getCheckedNodes(e=!1,o=!1){const d=[],n=function(s){(s.root?s.root.childNodes:s.childNodes).forEach(i=>{(i.checked||o&&i.indeterminate)&&(!e||e&&i.isLeaf)&&d.push(i.data),n(i)})};return n(this),d}getCheckedKeys(e=!1){return this.getCheckedNodes(e).map(o=>(o||{})[this.key])}getHalfCheckedNodes(){const e=[],o=function(d){(d.root?d.root.childNodes:d.childNodes).forEach(s=>{s.indeterminate&&e.push(s.data),o(s)})};return o(this),e}getHalfCheckedKeys(){return this.getHalfCheckedNodes().map(e=>(e||{})[this.key])}_getAllNodes(){const e=[],o=this.nodesMap;for(const d in o)oe(o,d)&&e.push(o[d]);return e}updateChildren(e,o){const d=this.nodesMap[e];if(!d)return;const n=d.childNodes;for(let s=n.length-1;s>=0;s--){const a=n[s];this.remove(a.data)}for(let s=0,a=o.length;s<a;s++){const i=o[s];this.append(i,d.data)}}_setCheckedKeys(e,o=!1,d){const n=this._getAllNodes().sort((f,y)=>f.level-y.level),s=Object.create(null),a=Object.keys(d);n.forEach(f=>f.setChecked(!1,!1));const i=f=>{f.childNodes.forEach(y=>{var l;s[y.data[e]]=!0,(l=y.childNodes)!=null&&l.length&&i(y)})};for(let f=0,y=n.length;f<y;f++){const l=n[f],c=l.data[e].toString();if(!a.includes(c)){l.checked&&!s[c]&&l.setChecked(!1,!1);continue}if(l.childNodes.length&&i(l),l.isLeaf||this.checkStrictly){l.setChecked(!0,!1);continue}if(l.setChecked(!0,!0),o){l.setChecked(!1,!1);const p=function(N){N.childNodes.forEach(v=>{v.isLeaf||v.setChecked(!1,!1),p(v)})};p(l)}}}setCheckedNodes(e,o=!1){const d=this.key,n={};e.forEach(s=>{n[(s||{})[d]]=!0}),this._setCheckedKeys(d,o,n)}setCheckedKeys(e,o=!1){this.defaultCheckedKeys=e;const d=this.key,n={};e.forEach(s=>{n[s]=!0}),this._setCheckedKeys(d,o,n)}setDefaultExpandedKeys(e){e=e||[],this.defaultExpandedKeys=e,e.forEach(o=>{const d=this.getNode(o);d&&d.expand(null,this.autoExpandParent)})}setChecked(e,o,d){const n=this.getNode(e);n&&n.setChecked(!!o,d)}getCurrentNode(){return this.currentNode}setCurrentNode(e){const o=this.currentNode;o&&(o.isCurrent=!1),this.currentNode=e,this.currentNode.isCurrent=!0}setUserCurrentNode(e,o=!0){const d=e[this.key],n=this.nodesMap[d];this.setCurrentNode(n),o&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0)}setCurrentNodeKey(e,o=!0){if(this.currentNodeKey=e,e==null){this.currentNode&&(this.currentNode.isCurrent=!1),this.currentNode=null;return}const d=this.getNode(e);d&&(this.setCurrentNode(d),o&&this.currentNode.level>1&&this.currentNode.parent.expand(null,!0))}}const dt=Z({name:"ElTreeNodeContent",props:{node:{type:Object,required:!0},renderContent:Function},setup(t){const e=q("tree"),o=z("NodeInstance"),d=z("RootTree");return()=>{const n=t.node,{data:s,store:a}=n;return t.renderContent?t.renderContent(ue,{_self:o,node:n,data:s,store:a}):ce(d.ctx.slots,"default",{node:n,data:s},()=>[ue("span",{class:e.be("node","label")},[n.label])])}}});var st=ee(dt,[["__file","tree-node-content.vue"]]);function me(t){const e=z("TreeNodeMap",null),o={treeNodeExpand:d=>{t.node!==d&&t.node.collapse()},children:[]};return e&&e.children.push(o),Y("TreeNodeMap",o),{broadcastExpanded:d=>{if(t.accordion)for(const n of o.children)n.treeNodeExpand(d)}}}const be=Symbol("dragEvents");function at({props:t,ctx:e,el$:o,dropIndicator$:d,store:n}){const s=q("tree"),a=D({showDropIndicator:!1,draggingNode:null,dropNode:null,allowDrop:!0,dropType:null});return Y(be,{treeNodeDragStart:({event:l,treeNode:c})=>{if(_(t.allowDrag)&&!t.allowDrag(c.node))return l.preventDefault(),!1;l.dataTransfer.effectAllowed="move";try{l.dataTransfer.setData("text/plain","")}catch{}a.value.draggingNode=c,e.emit("node-drag-start",c.node,l)},treeNodeDragOver:({event:l,treeNode:c})=>{const h=c,p=a.value.dropNode;p&&p.node.id!==h.node.id&&te(p.$el,s.is("drop-inner"));const N=a.value.draggingNode;if(!N||!h)return;let k=!0,v=!0,K=!0,S=!0;_(t.allowDrop)&&(k=t.allowDrop(N.node,h.node,"prev"),S=v=t.allowDrop(N.node,h.node,"inner"),K=t.allowDrop(N.node,h.node,"next")),l.dataTransfer.dropEffect=v||k||K?"move":"none",(k||v||K)&&(p==null?void 0:p.node.id)!==h.node.id&&(p&&e.emit("node-drag-leave",N.node,p.node,l),e.emit("node-drag-enter",N.node,h.node,l)),k||v||K?a.value.dropNode=h:a.value.dropNode=null,h.node.nextSibling===N.node&&(K=!1),h.node.previousSibling===N.node&&(k=!1),h.node.contains(N.node,!1)&&(v=!1),(N.node===h.node||N.node.contains(h.node))&&(k=!1,v=!1,K=!1);const T=h.$el.querySelector(`.${s.be("node","content")}`).getBoundingClientRect(),P=o.value.getBoundingClientRect();let b;const G=k?v?.25:K?.45:1:-1,J=K?v?.75:k?.55:0:1;let M=-9999;const u=l.clientY-T.top;u<T.height*G?b="before":u>T.height*J?b="after":v?b="inner":b="none";const C=h.$el.querySelector(`.${s.be("node","expand-icon")}`).getBoundingClientRect(),m=d.value;b==="before"?M=C.top-P.top:b==="after"&&(M=C.bottom-P.top),m.style.top=`${M}px`,m.style.left=`${C.right-P.left}px`,b==="inner"?Pe(h.$el,s.is("drop-inner")):te(h.$el,s.is("drop-inner")),a.value.showDropIndicator=b==="before"||b==="after",a.value.allowDrop=a.value.showDropIndicator||S,a.value.dropType=b,e.emit("node-drag-over",N.node,h.node,l)},treeNodeDragEnd:l=>{const{draggingNode:c,dropType:h,dropNode:p}=a.value;if(l.preventDefault(),l.dataTransfer&&(l.dataTransfer.dropEffect="move"),c&&p){const N={data:c.node.data};h!=="none"&&c.node.remove(),h==="before"?p.node.parent.insertBefore(N,p.node):h==="after"?p.node.parent.insertAfter(N,p.node):h==="inner"&&p.node.insertChild(N),h!=="none"&&(n.value.registerNode(N),n.value.key&&c.node.eachNode(k=>{var v;(v=n.value.nodesMap[k.data[n.value.key]])==null||v.setChecked(k.checked,!n.value.checkStrictly)})),te(p.$el,s.is("drop-inner")),e.emit("node-drag-end",c.node,p.node,h,l),h!=="none"&&e.emit("node-drop",c.node,p.node,h,l)}c&&!p&&e.emit("node-drag-end",c.node,null,h,l),a.value.showDropIndicator=!1,a.value.draggingNode=null,a.value.dropNode=null,a.value.allowDrop=!0}}),{dragState:a}}const rt=Z({name:"ElTreeNode",components:{ElCollapseTransition:tt,ElCheckbox:Me,NodeContent:st,ElIcon:He,Loading:Fe},props:{node:{type:$,default:()=>({})},props:{type:Object,default:()=>({})},accordion:Boolean,renderContent:Function,renderAfterExpand:Boolean,showCheckbox:{type:Boolean,default:!1}},emits:["node-expand"],setup(t,e){const o=q("tree"),{broadcastExpanded:d}=me(t),n=z("RootTree"),s=D(!1),a=D(!1),i=D(null),f=D(null),y=D(null),l=z(be),c=Ce();Y("NodeInstance",c),t.node.expanded&&(s.value=!0,a.value=!0);const h=n.props.props.children||"children";w(()=>{const u=t.node.data[h];return u&&[...u]},()=>{t.node.updateChildren()}),w(()=>t.node.indeterminate,u=>{k(t.node.checked,u)}),w(()=>t.node.checked,u=>{k(u,t.node.indeterminate)}),w(()=>t.node.childNodes.length,()=>t.node.reInitChecked()),w(()=>t.node.expanded,u=>{fe(()=>s.value=u),u&&(a.value=!0)});const p=u=>he(n.props.nodeKey,u.data),N=u=>{const C=t.props.class;if(!C)return{};let m;if(_(C)){const{data:j}=u;m=C(j,u)}else m=C;return Ne(m)?{[m]:!0}:m},k=(u,C)=>{(i.value!==u||f.value!==C)&&n.ctx.emit("check-change",t.node.data,u,C),i.value=u,f.value=C},v=u=>{ie(n.store,n.ctx.emit,()=>{var C;if((C=n==null?void 0:n.props)==null?void 0:C.nodeKey){const j=p(t.node);n.store.value.setCurrentNodeKey(j)}else n.store.value.setCurrentNode(t.node)}),n.currentNode.value=t.node,n.props.expandOnClickNode&&S(),n.props.checkOnClickNode&&!t.node.disabled&&T(null,{target:{checked:!t.node.checked}}),n.ctx.emit("node-click",t.node.data,t.node,c,u)},K=u=>{n.instance.vnode.props.onNodeContextmenu&&(u.stopPropagation(),u.preventDefault()),n.ctx.emit("node-contextmenu",u,t.node.data,t.node,c)},S=()=>{t.node.isLeaf||(s.value?(n.ctx.emit("node-collapse",t.node.data,t.node,c),t.node.collapse()):t.node.expand(()=>{e.emit("node-expand",t.node.data,t.node,c)}))},T=(u,C)=>{t.node.setChecked(C.target.checked,!n.props.checkStrictly),fe(()=>{const m=n.store.value;n.ctx.emit("check",t.node.data,{checkedNodes:m.getCheckedNodes(),checkedKeys:m.getCheckedKeys(),halfCheckedNodes:m.getHalfCheckedNodes(),halfCheckedKeys:m.getHalfCheckedKeys()})})};return{ns:o,node$:y,tree:n,expanded:s,childNodeRendered:a,oldChecked:i,oldIndeterminate:f,getNodeKey:p,getNodeClass:N,handleSelectChange:k,handleClick:v,handleContextMenu:K,handleExpandIconClick:S,handleCheckChange:T,handleChildNodeExpand:(u,C,m)=>{d(C),n.ctx.emit("node-expand",u,C,m)},handleDragStart:u=>{n.props.draggable&&l.treeNodeDragStart({event:u,treeNode:t})},handleDragOver:u=>{u.preventDefault(),n.props.draggable&&l.treeNodeDragOver({event:u,treeNode:{$el:y.value,node:t.node}})},handleDrop:u=>{u.preventDefault()},handleDragEnd:u=>{n.props.draggable&&l.treeNodeDragEnd(u)},CaretRight:ze}}});function it(t,e,o,d,n,s){const a=L("el-icon"),i=L("el-checkbox"),f=L("loading"),y=L("node-content"),l=L("el-tree-node"),c=L("el-collapse-transition");return se((E(),H("div",{ref:"node$",class:x([t.ns.b("node"),t.ns.is("expanded",t.expanded),t.ns.is("current",t.node.isCurrent),t.ns.is("hidden",!t.node.visible),t.ns.is("focusable",!t.node.disabled),t.ns.is("checked",!t.node.disabled&&t.node.checked),t.getNodeClass(t.node)]),role:"treeitem",tabindex:"-1","aria-expanded":t.expanded,"aria-disabled":t.node.disabled,"aria-checked":t.node.checked,draggable:t.tree.props.draggable,"data-key":t.getNodeKey(t.node),onClick:A(t.handleClick,["stop"]),onContextmenu:t.handleContextMenu,onDragstart:A(t.handleDragStart,["stop"]),onDragover:A(t.handleDragOver,["stop"]),onDragend:A(t.handleDragEnd,["stop"]),onDrop:A(t.handleDrop,["stop"])},[re("div",{class:x(t.ns.be("node","content")),style:je({paddingLeft:(t.node.level-1)*t.tree.props.indent+"px"})},[t.tree.props.icon||t.CaretRight?(E(),O(a,{key:0,class:x([t.ns.be("node","expand-icon"),t.ns.is("leaf",t.node.isLeaf),{expanded:!t.node.isLeaf&&t.expanded}]),onClick:A(t.handleExpandIconClick,["stop"])},{default:X(()=>[(E(),O(qe(t.tree.props.icon||t.CaretRight)))]),_:1},8,["class","onClick"])):U("v-if",!0),t.showCheckbox?(E(),O(i,{key:1,"model-value":t.node.checked,indeterminate:t.node.indeterminate,disabled:!!t.node.disabled,onClick:A(()=>{},["stop"]),onChange:t.handleCheckChange},null,8,["model-value","indeterminate","disabled","onClick","onChange"])):U("v-if",!0),t.node.loading?(E(),O(a,{key:2,class:x([t.ns.be("node","loading-icon"),t.ns.is("loading")])},{default:X(()=>[ne(f)]),_:1},8,["class"])):U("v-if",!0),ne(y,{node:t.node,"render-content":t.renderContent},null,8,["node","render-content"])],6),ne(c,null,{default:X(()=>[!t.renderAfterExpand||t.childNodeRendered?se((E(),H("div",{key:0,class:x(t.ns.be("node","children")),role:"group","aria-expanded":t.expanded},[(E(!0),H(ke,null,ve(t.node.childNodes,h=>(E(),O(l,{key:t.getNodeKey(h),"render-content":t.renderContent,"render-after-expand":t.renderAfterExpand,"show-checkbox":t.showCheckbox,node:h,accordion:t.accordion,props:t.props,onNodeExpand:t.handleChildNodeExpand},null,8,["render-content","render-after-expand","show-checkbox","node","accordion","props","onNodeExpand"]))),128))],10,["aria-expanded"])),[[ae,t.expanded]]):U("v-if",!0)]),_:1})],42,["aria-expanded","aria-disabled","aria-checked","draggable","data-key","onClick","onContextmenu","onDragstart","onDragover","onDragend","onDrop"])),[[ae,t.node.visible]])}var lt=ee(rt,[["render",it],["__file","tree-node.vue"]]);function ct({el$:t},e){const o=q("tree"),d=pe([]),n=pe([]);Re(()=>{a()}),Ue(()=>{d.value=Array.from(t.value.querySelectorAll("[role=treeitem]")),n.value=Array.from(t.value.querySelectorAll("input[type=checkbox]"))}),w(n,i=>{i.forEach(f=>{f.setAttribute("tabindex","-1")})}),We(t,"keydown",i=>{const f=i.target;if(!f.className.includes(o.b("node")))return;const y=i.code;d.value=Array.from(t.value.querySelectorAll(`.${o.is("focusable")}[role=treeitem]`));const l=d.value.indexOf(f);let c;if([I.up,I.down].includes(y)){if(i.preventDefault(),y===I.up){c=l===-1?0:l!==0?l-1:d.value.length-1;const p=c;for(;!e.value.getNode(d.value[c].dataset.key).canFocus;){if(c--,c===p){c=-1;break}c<0&&(c=d.value.length-1)}}else{c=l===-1?0:l<d.value.length-1?l+1:0;const p=c;for(;!e.value.getNode(d.value[c].dataset.key).canFocus;){if(c++,c===p){c=-1;break}c>=d.value.length&&(c=0)}}c!==-1&&d.value[c].focus()}[I.left,I.right].includes(y)&&(i.preventDefault(),f.click());const h=f.querySelector('[type="checkbox"]');[I.enter,I.space].includes(y)&&h&&(i.preventDefault(),h.click())});const a=()=>{var i;d.value=Array.from(t.value.querySelectorAll(`.${o.is("focusable")}[role=treeitem]`)),n.value=Array.from(t.value.querySelectorAll("input[type=checkbox]"));const f=t.value.querySelectorAll(`.${o.is("checked")}[role=treeitem]`);if(f.length){f[0].setAttribute("tabindex","0");return}(i=d.value[0])==null||i.setAttribute("tabindex","0")}}const ht=Z({name:"ElTree",components:{ElTreeNode:lt},props:{data:{type:Array,default:()=>[]},emptyText:{type:String},renderAfterExpand:{type:Boolean,default:!0},nodeKey:String,checkStrictly:Boolean,defaultExpandAll:Boolean,expandOnClickNode:{type:Boolean,default:!0},checkOnClickNode:Boolean,checkDescendants:{type:Boolean,default:!1},autoExpandParent:{type:Boolean,default:!0},defaultCheckedKeys:Array,defaultExpandedKeys:Array,currentNodeKey:[String,Number],renderContent:Function,showCheckbox:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},allowDrag:Function,allowDrop:Function,props:{type:Object,default:()=>({children:"children",label:"label",disabled:"disabled"})},lazy:{type:Boolean,default:!1},highlightCurrent:Boolean,load:Function,filterNodeMethod:Function,accordion:Boolean,indent:{type:Number,default:18},icon:{type:Ve}},emits:["check-change","current-change","node-click","node-contextmenu","node-collapse","node-expand","check","node-drag-start","node-drag-end","node-drop","node-drag-leave","node-drag-enter","node-drag-over"],setup(t,e){const{t:o}=Ye(),d=q("tree"),n=z(Qe,null),s=D(new ot({key:t.nodeKey,data:t.data,lazy:t.lazy,props:t.props,load:t.load,currentNodeKey:t.currentNodeKey,checkStrictly:t.checkStrictly,checkDescendants:t.checkDescendants,defaultCheckedKeys:t.defaultCheckedKeys,defaultExpandedKeys:t.defaultExpandedKeys,autoExpandParent:t.autoExpandParent,defaultExpandAll:t.defaultExpandAll,filterNodeMethod:t.filterNodeMethod}));s.value.initialize();const a=D(s.value.root),i=D(null),f=D(null),y=D(null),{broadcastExpanded:l}=me(t),{dragState:c}=at({props:t,ctx:e,el$:f,dropIndicator$:y,store:s});ct({el$:f},s);const h=Ze(()=>{const{childNodes:r}=a.value,g=n?n.hasFilteredOptions!==0:!1;return(!r||r.length===0||r.every(({visible:B})=>!B))&&!g});w(()=>t.currentNodeKey,r=>{s.value.setCurrentNodeKey(r)}),w(()=>t.defaultCheckedKeys,r=>{s.value.setDefaultCheckedKey(r)}),w(()=>t.defaultExpandedKeys,r=>{s.value.setDefaultExpandedKeys(r)}),w(()=>t.data,r=>{s.value.setData(r)},{deep:!0}),w(()=>t.checkStrictly,r=>{s.value.checkStrictly=r});const p=r=>{if(!t.filterNodeMethod)throw new Error("[Tree] filterNodeMethod is required when filter");s.value.filter(r)},N=r=>he(t.nodeKey,r.data),k=r=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in getNodePath");const g=s.value.getNode(r);if(!g)return[];const B=[g.data];let R=g.parent;for(;R&&R!==a.value;)B.push(R.data),R=R.parent;return B.reverse()},v=(r,g)=>s.value.getCheckedNodes(r,g),K=r=>s.value.getCheckedKeys(r),S=()=>{const r=s.value.getCurrentNode();return r?r.data:null},T=()=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in getCurrentKey");const r=S();return r?r[t.nodeKey]:null},P=(r,g)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedNodes");s.value.setCheckedNodes(r,g)},b=(r,g)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCheckedKeys");s.value.setCheckedKeys(r,g)},G=(r,g,B)=>{s.value.setChecked(r,g,B)},J=()=>s.value.getHalfCheckedNodes(),M=()=>s.value.getHalfCheckedKeys(),u=(r,g=!0)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentNode");ie(s,e.emit,()=>{l(r),s.value.setUserCurrentNode(r,g)})},C=(r,g=!0)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in setCurrentKey");ie(s,e.emit,()=>{l(),s.value.setCurrentNodeKey(r,g)})},m=r=>s.value.getNode(r),j=r=>{s.value.remove(r)},Ee=(r,g)=>{s.value.append(r,g)},Ke=(r,g)=>{s.value.insertBefore(r,g)},De=(r,g)=>{s.value.insertAfter(r,g)},we=(r,g,B)=>{l(g),e.emit("node-expand",r,g,B)},xe=(r,g)=>{if(!t.nodeKey)throw new Error("[Tree] nodeKey is required in updateKeyChild");s.value.updateChildren(r,g)};return Y("RootTree",{ctx:e,props:t,store:s,root:a,currentNode:i,instance:Ce()}),Y(Ge,void 0),{ns:d,store:s,root:a,currentNode:i,dragState:c,el$:f,dropIndicator$:y,isEmpty:h,filter:p,getNodeKey:N,getNodePath:k,getCheckedNodes:v,getCheckedKeys:K,getCurrentNode:S,getCurrentKey:T,setCheckedNodes:P,setCheckedKeys:b,setChecked:G,getHalfCheckedNodes:J,getHalfCheckedKeys:M,setCurrentNode:u,setCurrentKey:C,t:o,getNode:m,remove:j,append:Ee,insertBefore:Ke,insertAfter:De,handleNodeExpand:we,updateKeyChildren:xe}}});function ut(t,e,o,d,n,s){const a=L("el-tree-node");return E(),H("div",{ref:"el$",class:x([t.ns.b(),t.ns.is("dragging",!!t.dragState.draggingNode),t.ns.is("drop-not-allow",!t.dragState.allowDrop),t.ns.is("drop-inner",t.dragState.dropType==="inner"),{[t.ns.m("highlight-current")]:t.highlightCurrent}]),role:"tree"},[(E(!0),H(ke,null,ve(t.root.childNodes,i=>(E(),O(a,{key:t.getNodeKey(i),node:i,props:t.props,accordion:t.accordion,"render-after-expand":t.renderAfterExpand,"show-checkbox":t.showCheckbox,"render-content":t.renderContent,onNodeExpand:t.handleNodeExpand},null,8,["node","props","accordion","render-after-expand","show-checkbox","render-content","onNodeExpand"]))),128)),t.isEmpty?(E(),H("div",{key:0,class:x(t.ns.e("empty-block"))},[ce(t.$slots,"empty",{},()=>{var i;return[re("span",{class:x(t.ns.e("empty-text"))},Je((i=t.emptyText)!=null?i:t.t("el.tree.emptyText")),3)]})],2)):U("v-if",!0),se(re("div",{ref:"dropIndicator$",class:x(t.ns.e("drop-indicator"))},null,2),[[ae,t.dragState.showDropIndicator]])],2)}var ft=ee(ht,[["render",ut],["__file","tree.vue"]]);const Nt=ye(ft);export{Nt as E};
