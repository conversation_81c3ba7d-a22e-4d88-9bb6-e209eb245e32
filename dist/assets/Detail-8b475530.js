import{_ as G}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as H,I as J,r as f,o as O,D as Q,at as W,a as o,v as p,g as C,f as r,e as s,h as u,b as v,u as c,i as d,y as b,bc as X,bg as Y,av as Z,bn as F,_ as ee}from"./index-db94d997.js";import{b as ae,c as te,d as se}from"./index-7c2ccd19.js";import{p as _,b as ne}from"./dictLocal-9822709a.js";import{_ as le}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const oe={class:"areaPrice"},ie={class:"areaPrice_table"},de={key:1,class:"status_tag"},re={key:0,class:"tag_three"},ue={key:1,class:"tag_four"},ce={key:2,class:"tag_one"},pe={key:3,class:"tag_two"},_e={class:"text-hide"},ye={class:"text-hide"},fe=H({__name:"Detail",setup(he){var P;const q=J(),h=f(),m=f((P=history.state)==null?void 0:P.pdata),S=f([]),x=f(!1),E=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:1,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:S,render:!0,resizable:!0,search:!1,order:4,fixed:"left"},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:120},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:120},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:120},{title:"季度",key:"quarterKey",dataIndex:"quarterKey",width:120,resizable:!0,render:!0,search:!1},{title:"分享金额-正泰",dataIndex:"shareMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"抵扣金额-正泰",dataIndex:"guarantyMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"应付确认金额-正泰",dataIndex:"oughtPayConfirmMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"调整金额-正泰",dataIndex:"adjustMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"实付确认金额-正泰",dataIndex:"practicalPayConfirmMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付建议",key:"paySuggest",dataIndex:"paySuggest",valueType:"select",valueEnum:ne,resizable:!0,order:6,width:120},{title:"支付状态",key:"value",dataIndex:"cashPayStatus",valueType:"select",valueEnum:_,resizable:!0,render:!0,order:5,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:100}];O(()=>{console.log(m.value),T()}),Q(()=>{h.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&z()});const T=()=>{W({}).then(e=>{let n=(e||[]).map(l=>({label:l.companyName,value:l.companyCode}));S.value=n})},L=e=>{e.isEdit=!0,e.cashPayStatusTem=e.cashPayStatus},R=e=>{e.isEdit=!1,e.cashPayStatus=e.cashPayStatusTem},B=e=>{console.log("编辑");let i={id:e.id,cashPayStatus:e.cashPayStatus};x.value=!0,te(i).then(n=>{x.value=!1,X.info("保存成功"),z(!0)}).catch(n=>{x.value=!1})},K=()=>{q.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/cashPaymentsDocument/dataImport",query:{templateType:10,fileType:".csv,.xls,.xlsx",fileSize:30}})},U=()=>{var n,l;let e=(n=h.value)==null?void 0:n.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus;const i={initFormState:e,id:((l=m.value)==null?void 0:l.id)||""};console.log(i,"导出---"),se(e)},V=f([]),$=e=>{V.value=e||[]},z=e=>{var i;(i=h.value)==null||i.reload(e)},j=(e,i)=>{let n={};return new Promise(l=>{if(m.value){let{yearKey:g,quarter:k,companyCode:t}=m.value;n={yearKey:g,quarter:k,companyCode:t,delStatus:0,noJoin:!0}}const I={...n,...e,delStatus:0};l(I)})};return(e,i)=>{const n=Y,l=le,I=Z,g=F,k=G;return o(),p("div",oe,[C("div",ie,[r(k,{columns:E,ref_key:"actionRef",ref:h,request:c(ae),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:$,"before-query-params":j},{tableHeader:s(()=>[r(l,null,{default:s(()=>[r(n,{onClick:K},{default:s(()=>[u("导入支付状态")]),_:1}),r(n,{type:"primary",onClick:U},{default:s(()=>[u("导出")]),_:1})]),_:1})]),valueRender:s(({column:t,record:a,index:w})=>{var y,D,M,N;return[a.isEdit?(o(),v(I,{key:0,value:a[t.dataIndex],"onUpdate:value":A=>a[t.dataIndex]=A,options:c(_),style:{width:"100%"},placeholder:"请选择"},null,8,["value","onUpdate:value","options"])):(o(),p("span",de,[a[t.dataIndex]=="1"?(o(),p("span",re,d(a[t.dataIndex]&&((y=c(_)[a[t.dataIndex]-1])==null?void 0:y.label)),1)):a[t.dataIndex]=="2"?(o(),p("span",ue,d(a[t.dataIndex]&&((D=c(_)[a[t.dataIndex]-1])==null?void 0:D.label)),1)):a[t.dataIndex]=="3"?(o(),p("span",ce,d(a[t.dataIndex]&&((M=c(_)[a[t.dataIndex]-1])==null?void 0:M.label)),1)):a[t.dataIndex]=="4"?(o(),p("span",pe,d(a[t.dataIndex]&&((N=c(_)[a[t.dataIndex]-1])==null?void 0:N.label)),1)):b("",!0)]))]}),companyCodeListRender:s(({column:t,record:a,index:w})=>[r(g,null,{title:s(()=>[u(d(a.companyName),1)]),default:s(()=>[C("span",_e,d(a.companyName),1)]),_:2},1024)]),quarterKeyRender:s(({column:t,record:a,index:w})=>[r(g,null,{title:s(()=>[u(d(a.yearKey+"-"+a.quarter+"季度"),1)]),default:s(()=>[C("span",ye,d(a.yearKey+"-"+a.quarter+"季度"),1)]),_:2},1024)]),actionRender:s(({column:t,record:a,index:w})=>[r(l,null,{default:s(()=>[a.isEdit?b("",!0):(o(),v(n,{key:0,type:"link",size:"small",onClick:y=>L(a),disabled:a.paySuggest=="1"},{default:s(()=>[u("编辑")]),_:2},1032,["onClick","disabled"])),a.isEdit?(o(),v(n,{key:1,type:"link",size:"small",onClick:y=>R(a),disabled:!1},{default:s(()=>[u("取消")]),_:2},1032,["onClick"])):b("",!0),a.isEdit?(o(),v(n,{key:2,type:"link",size:"small",onClick:y=>B(a),loading:c(x)},{default:s(()=>[u("保存")]),_:2},1032,["onClick","loading"])):b("",!0)]),_:2},1024)]),_:1},8,["request"])])])}}});const ze=ee(fe,[["__scopeId","data-v-5a14a897"]]);export{ze as default};
