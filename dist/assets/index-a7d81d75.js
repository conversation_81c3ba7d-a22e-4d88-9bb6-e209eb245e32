import{_ as he}from"./index-914bbc8b.js";import{_ as ye}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ke,I as xe,r,K as Ie,bL as Ce,o as we,D as Se,at as De,a as c,v as p,f as i,e as l,u as o,b as z,h as g,y as N,i as _,g as m,S as W,s as G,F as Re,bi as L,bc as H,bg as Fe,bn as ze,av as Ne,bf as Le,aw as Te,ax as Ee,bh as Pe,bW as qe}from"./index-db94d997.js";import{b as Be,c as Me}from"./index-5fcafee1.js";import{m as Oe}from"./dictLocal-9822709a.js";import{g as J,e as $e,r as Ue}from"./index-d38c0948.js";import{E as Q}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as Ke}from"./index-39334618.js";import"./index-326d414f.js";import"./icon-831229e8.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Ve={class:"text-hide"},Ae={key:0,class:"status_tag"},We={class:"tag_one"},Ge={key:1,class:"status_tag"},He={class:"tag_two"},Je={key:0,class:"status_tag"},Qe={key:1,class:"status_tag"},je={key:0,class:"status_tag"},Xe={class:"tag_two"},Ye={key:1,class:"status_tag"},Ze={class:"tag_one"},ea={key:2,class:"status_tag"},aa={class:"tag_three"},ta={key:3,class:"status_tag"},sa=m("span",{class:"tag_two"},"待提交",-1),na=[sa],ka=ke({__name:"index",setup(la){const T=xe(),y=r(),E=r([]),f=r(!1),k=r(!1),v=Ie({}),x=r(),P=r([]),w=r([]),S=r({}),D=r(!1),j=r([]),R=r([{label:"审批中",value:1},{label:"审批通过",value:2},{label:"审批驳回",value:3}]),X=r([{label:"是",value:1},{label:"否",value:0}]),q=r([{label:"已确认",value:1},{label:"未确认",value:0}]),Y=r([{label:"待审批",value:0},{label:"审批中",value:1},{label:"审核通过",value:2},{label:"审核驳回",value:3}]),Z=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:150,valueEnum:E,fixed:"left",render:!0},{title:"电站编号",dataIndex:"stationCode",resizable:!0,width:100},{title:"电站名称",dataIndex:"stationName",resizable:!0,search:!1,width:120},{title:"错误转账金额",dataIndex:"errorFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"错误转账发生时间",dataIndex:"errorPaymentTime",resizable:!0,search:!1,width:120},{title:"金额归集到项目公司期间",dataIndex:"moneyCollectionTime",resizable:!0,search:!1,width:150},{title:"错误转账事由",dataIndex:"reason",resizable:!0,search:!1,width:120},{title:"是否退回农户",key:"rollbackFlag",dataIndex:"rollbackFlag",valueType:"select",valueEnum:X,width:100,resizable:!0,search:!1,render:!0},{title:"审批状态",key:"approveStatus",dataIndex:"approveStatus",valueType:"select",valueEnum:Y,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",resizable:!1,width:160}],ee=Ce();we(()=>{ae(),se()}),Se(()=>{y.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&F()});const ae=()=>{De({}).then(e=>{console.log("产权公司res=",e);let s=(e||[]).map(d=>({label:d.companyName,value:d.companyCode}));E.value=s})},te=()=>{var a;let e=(a=y.value)==null?void 0:a.getInitialFormStateNew();$e(e)},se=()=>{Be().then(e=>{P.value=e==null?void 0:e.map(a=>({label:a.roleName,value:a.id}))})},ne=e=>{Me({roleId:e}).then(a=>{w.value=a==null?void 0:a.map(s=>({label:s.userName,value:s.userId}))})},le=e=>{console.log(`selected ${e}`),ne(e)},B=e=>{var a;f.value=!0,S.value=e,w.value=[],(a=x.value)==null||a.resetFields()},oe=()=>{var e;f.value=!1,(e=x.value)==null||e.resetFields()},ie=e=>{L.confirm({title:"确认提示",icon:i(Q),content:"该电站下有未完成的误转账记录,是否继续提交？",onOk:()=>B(e),onCancel:()=>M()})},de=e=>{let a={stationCode:S.value.stationCode,rollbackFlag:0,pageNum:1,pageSize:10};e.loading=!0,J(a).then(s=>{var d;e.loading=!1,s&&((d=s==null?void 0:s.records)==null?void 0:d.length)>1?ie(e):(console.log("该电站存在没有未完成的误转账记录"),B(e))}).catch(s=>{e.loading=!1})},re=()=>{x.value.validateFields().then(e=>{let a=S.value.id;const s="businessKeyErrorPayment",d=Oe[0].value;let u={assigneeId:e.assigneeId,relationId:a,businessKey:s,processKey:d};k.value=!0,qe(u).then(b=>{f.value=!1,k.value=!1,H.info("保存成功"),F()}).catch(b=>{k.value=!1})})},ce=()=>{re()},M=()=>{console.log("onCancel")},ue=e=>{L.confirm({title:"确认提示",icon:i(Q),content:"确认后不可修改，是否确认？",onOk(){return new Promise((a,s)=>{let d={id:e==null?void 0:e.id};Ue(d).then(u=>{H.info("确认成功"),a(u),F()}).catch(u=>{console.log("err=",u),s()})})},onCancel:()=>M()})},pe=()=>{T.push({path:"/financeManage/everydayManage/mistakenTransfer/detail",query:{type:0}})},_e=e=>{T.push({path:"/financeManage/everydayManage/mistakenTransfer/detail",query:{type:2,id:e==null?void 0:e.id,showEdit:e.approveStatus===1||e.approveStatus===2?0:1}})},O=r([]),me=e=>{O.value=e||[]},F=()=>{var e;(e=y.value)==null||e.reload()},fe=(e,a)=>{var u;let s={};const d=(u=ee.query)==null?void 0:u.relationId;return d&&(s.id=d),new Promise(b=>{const I={...s,...e,noJoin:!0,delStatus:0};b(I)})};return(e,a)=>{const s=Fe,d=Ke,u=ze,b=ye,I=Ne,$=Le,U=Te,K=Ee,ge=Pe,ve=L,be=he;return c(),p(Re,null,[i(b,{columns:Z,ref_key:"actionRef",ref:y,request:o(J),onGetDataSource:me,"before-query-params":fe},{tableHeader:l(()=>[i(d,null,{default:l(()=>[o(O).length>0?(c(),z(s,{key:0,onClick:te},{default:l(()=>[g("导出")]),_:1})):N("",!0),i(s,{type:"primary",onClick:pe},{default:l(()=>[g("新增")]),_:1})]),_:1})]),companyCodeListRender:l(({column:n,record:t,index:C})=>[i(u,null,{title:l(()=>[g(_(t.companyName),1)]),default:l(()=>[m("span",Ve,_(t.companyName),1)]),_:2},1024)]),confirmFlagRender:l(({column:n,record:t,index:C})=>[t[n.dataIndex]===1?(c(),p("span",Ae,[m("span",We,_(o(W)(t[n.dataIndex],o(q))),1)])):N("",!0),t[n.dataIndex]===0?(c(),p("span",Ge,[m("span",He,_(o(W)(t[n.dataIndex],o(q))),1)])):N("",!0)]),rollbackFlagRender:l(({column:n,record:t,index:C})=>[t[n.dataIndex]==1?(c(),p("span",Je,"是")):(c(),p("span",Qe,"否"))]),approveStatusRender:l(({column:n,record:t,index:C})=>{var h,V,A;return[t[n.dataIndex]==1?(c(),p("span",je,[m("span",Xe,_((h=o(R)[t[n.dataIndex]-1])==null?void 0:h.label),1)])):t[n.dataIndex]==2?(c(),p("span",Ye,[m("span",Ze,_((V=o(R)[t[n.dataIndex]-1])==null?void 0:V.label),1)])):t[n.dataIndex]==3?(c(),p("span",ea,[m("span",aa,_((A=o(R)[t[n.dataIndex]-1])==null?void 0:A.label),1)])):(c(),p("span",ta,na))]}),actionRender:l(({column:n,record:t,index:C})=>[i(d,null,{default:l(()=>[t.approveStatus===2?(c(),z(s,{key:0,type:"link",size:"small",onClick:h=>ue(t),disabled:t.rollbackFlag===1},{default:l(()=>[g("已退回农户 ")]),_:2},1032,["onClick","disabled"])):(c(),z(s,{key:1,type:"link",size:"small",onClick:h=>de(t),disabled:t.approveStatus===1||t.approveStatus===2,loading:t.loading},{default:l(()=>[g("提交审批 ")]),_:2},1032,["onClick","disabled","loading"])),i(s,{type:"link",size:"small",onClick:h=>_e(t)},{default:l(()=>[g("查看详情")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"]),i(ve,{visible:o(f),"onUpdate:visible":a[2]||(a[2]=n=>G(f)?f.value=n:null),title:"提交审批","confirm-loading":o(k),okText:"提交审批",onOk:ce,onCancel:oe},{default:l(()=>[i(ge,{model:o(v),ref_key:"formRef2",ref:x,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:l(()=>[i(K,{span:24},{default:l(()=>[i(U,{span:24},{default:l(()=>[i($,{label:"审批角色",name:"roleId",required:""},{default:l(()=>[i(I,{value:o(v).roleId,"onUpdate:value":a[0]||(a[0]=n=>o(v).roleId=n),options:o(P),placeholder:"请选择",style:{width:"100%"},onChange:le},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),i(K,{span:24},{default:l(()=>[i(U,{span:24},{default:l(()=>[i($,{label:"审批人员",name:"assigneeId",required:""},{default:l(()=>[i(I,{value:o(v).assigneeId,"onUpdate:value":a[1]||(a[1]=n=>o(v).assigneeId=n),options:o(w),placeholder:"请选择"},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"]),i(be,{visible:o(D),"onUpdate:visible":a[3]||(a[3]=n=>G(D)?D.value=n:null),rows:o(j),title:"电站收入确认审批"},null,8,["visible","rows"])],64)}}});export{ka as default};
