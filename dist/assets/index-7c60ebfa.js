import{cg as _l,ch as xl,ci as et,aa as ye,c as o,cj as q,bb as Te,H as lt,ck as Q,w as G,aj as Se,d as ae,af as te,al as ol,u as Ve,K as ne,t as $e,l as tt,V as F,ag as le,b6 as ke,a as m,v as V,a1 as k,g as $,i as N,Z as d,ai as B,r as I,o as Me,cl as U,y as T,n as he,aK as nt,am as at,an as st,aC as P,ac as ot,aZ as me,cm as xe,aL as ge,ap as it,bl as ut,a6 as rt,bZ as dt,aT as ee,cn as Ie,aU as ct,aS as pt,a4 as j,a8 as il,a5 as ft,a3 as vt,a9 as mt,aD as be,co as bt,aG as ht,aY as gt,aH as yt,ak as St,aW as Ct,Y as Ot,aP as wt,b5 as Vt,ao as ul,aA as A,cp as It,f as H,e as D,F as el,x as ll,h as tl,b as W,b0 as x,cq as Et,aB as Ee,cr as Tt,cs as $t,aq as kt,ar as rl}from"./index-db94d997.js";import{t as nl,E as Mt}from"./index-326d414f.js";import{c as z,d as Rt,a as Dt,b as Lt,s as Bt,u as Nt}from"./index-ec316fb4.js";import{V as Wt,i as al}from"./icon-831229e8.js";var Ft=Math.max,zt=Math.min;function Pt(e,i,a){var b=e==null?0:e.length;if(!b)return-1;var s=b-1;return a!==void 0&&(s=_l(a),s=a<0?Ft(b+s,0):zt(s,b-1)),xl(e,et(i),s,!0)}const Kt=(e="")=>e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d"),dl=Symbol("ElSelectGroup"),Ce=Symbol("ElSelect");function At(e,i){const a=ye(Ce),b=ye(dl,{disabled:!1}),s=o(()=>h(z(a.props.modelValue),e.value)),O=o(()=>{var p;if(a.props.multiple){const v=z((p=a.props.modelValue)!=null?p:[]);return!s.value&&v.length>=a.props.multipleLimit&&a.props.multipleLimit>0}else return!1}),t=o(()=>e.label||(q(e.value)?"":e.value)),f=o(()=>e.value||e.label||""),c=o(()=>e.disabled||i.groupDisabled||O.value),g=Te(),h=(p=[],v)=>{if(q(e.value)){const r=a.props.valueKey;return p&&p.some(E=>lt(Q(E,r))===Q(v,r))}else return p&&p.includes(v)},y=()=>{!e.disabled&&!b.disabled&&(a.states.hoveringIndex=a.optionsArray.indexOf(g.proxy))},C=p=>{const v=new RegExp(Kt(p),"i");i.visible=v.test(t.value)||e.created};return G(()=>t.value,()=>{!e.created&&!a.props.remote&&a.setSelected()}),G(()=>e.value,(p,v)=>{const{remote:r,valueKey:E}=a.props;if(p!==v&&(a.onOptionDestroy(v,g.proxy),a.onOptionCreate(g.proxy)),!e.created&&!r){if(E&&q(p)&&q(v)&&p[E]===v[E])return;a.setSelected()}}),G(()=>b.disabled,()=>{i.groupDisabled=b.disabled},{immediate:!0}),{select:a,currentLabel:t,currentValue:f,itemSelected:s,isDisabled:c,hoverItem:y,updateOption:C}}const Ht=ae({name:"ElOption",componentName:"ElOption",props:{value:{required:!0,type:[String,Number,Boolean,Object]},label:[String,Number],created:Boolean,disabled:Boolean},setup(e){const i=te("select"),a=ol(),b=o(()=>[i.be("dropdown","item"),i.is("disabled",Ve(f)),i.is("selected",Ve(t)),i.is("hovering",Ve(C))]),s=ne({index:-1,groupDisabled:!1,visible:!0,hover:!1}),{currentLabel:O,itemSelected:t,isDisabled:f,select:c,hoverItem:g,updateOption:h}=At(e,s),{visible:y,hover:C}=$e(s),p=Te().proxy;c.onOptionCreate(p),tt(()=>{const r=p.value,{selected:E}=c.states,se=(c.props.multiple?E:[E]).some(oe=>oe.value===p.value);F(()=>{c.states.cachedOptions.get(r)===p&&!se&&c.states.cachedOptions.delete(r)}),c.onOptionDestroy(r,p)});function v(){f.value||c.handleOptionSelect(p)}return{ns:i,id:a,containerKls:b,currentLabel:O,itemSelected:t,isDisabled:f,select:c,hoverItem:g,updateOption:h,visible:y,hover:C,selectOptionClick:v,states:s}}});function Ut(e,i,a,b,s,O){return le((m(),V("li",{id:e.id,class:d(e.containerKls),role:"option","aria-disabled":e.isDisabled||void 0,"aria-selected":e.itemSelected,onMouseenter:e.hoverItem,onClick:B(e.selectOptionClick,["stop"])},[k(e.$slots,"default",{},()=>[$("span",null,N(e.currentLabel),1)])],42,["id","aria-disabled","aria-selected","onMouseenter","onClick"])),[[ke,e.visible]])}var Re=Se(Ht,[["render",Ut],["__file","option.vue"]]);const Gt=ae({name:"ElSelectDropdown",componentName:"ElSelectDropdown",setup(){const e=ye(Ce),i=te("select"),a=o(()=>e.props.popperClass),b=o(()=>e.props.multiple),s=o(()=>e.props.fitInputWidth),O=I("");function t(){var f;O.value=`${(f=e.selectRef)==null?void 0:f.offsetWidth}px`}return Me(()=>{t(),U(e.selectRef,t)}),{ns:i,minWidth:O,popperClass:a,isMultiple:b,isFitInputWidth:s}}});function jt(e,i,a,b,s,O){return m(),V("div",{class:d([e.ns.b("dropdown"),e.ns.is("multiple",e.isMultiple),e.popperClass]),style:he({[e.isFitInputWidth?"width":"minWidth"]:e.minWidth})},[e.$slots.header?(m(),V("div",{key:0,class:d(e.ns.be("dropdown","header"))},[k(e.$slots,"header")],2)):T("v-if",!0),k(e.$slots,"default"),e.$slots.footer?(m(),V("div",{key:1,class:d(e.ns.be("dropdown","footer"))},[k(e.$slots,"footer")],2)):T("v-if",!0)],6)}var qt=Se(Gt,[["render",jt],["__file","select-dropdown.vue"]]);const Qt=11,Zt=(e,i)=>{const{t:a}=nt(),b=ol(),s=te("select"),O=te("input"),t=ne({inputValue:"",options:new Map,cachedOptions:new Map,disabledOptions:new Map,optionValues:[],selected:[],selectionWidth:0,calculatorWidth:0,collapseItemWidth:0,selectedLabel:"",hoveringIndex:-1,previousQuery:null,inputHovering:!1,menuVisibleOnFocus:!1,isBeforeHide:!1}),f=I(null),c=I(null),g=I(null),h=I(null),y=I(null),C=I(null),p=I(null),v=I(null),r=I(null),E=I(null),Z=I(null),se=I(null),{isComposing:oe,handleCompositionStart:pl,handleCompositionUpdate:fl,handleCompositionEnd:vl}=Rt({afterComposition:l=>je(l)}),{wrapperRef:De,isFocused:Le,handleBlur:ml}=Dt(y,{beforeFocus(){return X.value},afterFocus(){e.automaticDropdown&&!S.value&&(S.value=!0,t.menuVisibleOnFocus=!0)},beforeBlur(l){var n,u;return((n=g.value)==null?void 0:n.isFocusInsideContent(l))||((u=h.value)==null?void 0:u.isFocusInsideContent(l))},afterBlur(){S.value=!1,t.menuVisibleOnFocus=!1}}),S=I(!1),Y=I(),{form:Be,formItem:J}=at(),{inputId:bl}=st(e,{formItemContext:J}),{valueOnClear:hl,isEmptyValue:gl}=Lt(e),X=o(()=>e.disabled||(Be==null?void 0:Be.disabled)),Oe=o(()=>P(e.modelValue)?e.modelValue.length>0:!gl(e.modelValue)),yl=o(()=>e.clearable&&!X.value&&t.inputHovering&&Oe.value),Ne=o(()=>e.remote&&e.filterable&&!e.remoteShowSuffix?"":e.suffixIcon),Sl=o(()=>s.is("reverse",Ne.value&&S.value)),We=o(()=>(J==null?void 0:J.validateState)||""),Cl=o(()=>Wt[We.value]),Ol=o(()=>e.remote?300:0),Fe=o(()=>e.loading?e.loadingText||a("el.select.loading"):e.remote&&!t.inputValue&&t.options.size===0?!1:e.filterable&&t.inputValue&&t.options.size>0&&ie.value===0?e.noMatchText||a("el.select.noMatch"):t.options.size===0?e.noDataText||a("el.select.noData"):null),ie=o(()=>M.value.filter(l=>l.visible).length),M=o(()=>{const l=Array.from(t.options.values()),n=[];return t.optionValues.forEach(u=>{const w=l.findIndex(R=>R.value===u);w>-1&&n.push(l[w])}),n.length>=l.length?n:l}),wl=o(()=>Array.from(t.cachedOptions.values())),Vl=o(()=>{const l=M.value.filter(n=>!n.created).some(n=>n.currentLabel===t.inputValue);return e.filterable&&e.allowCreate&&t.inputValue!==""&&!l}),ze=()=>{e.filterable&&ee(e.filterMethod)||e.filterable&&e.remote&&ee(e.remoteMethod)||M.value.forEach(l=>{var n;(n=l.updateOption)==null||n.call(l,t.inputValue)})},Pe=ot(),Il=o(()=>["small"].includes(Pe.value)?"small":"default"),El=o({get(){return S.value&&Fe.value!==!1},set(l){S.value=l}}),Tl=o(()=>{if(e.multiple&&!me(e.modelValue))return z(e.modelValue).length===0&&!t.inputValue;const l=P(e.modelValue)?e.modelValue[0]:e.modelValue;return e.filterable||me(l)?!t.inputValue:!0}),$l=o(()=>{var l;const n=(l=e.placeholder)!=null?l:a("el.select.placeholder");return e.multiple||!Oe.value?n:t.selectedLabel}),kl=o(()=>xe?null:"mouseenter");G(()=>e.modelValue,(l,n)=>{e.multiple&&e.filterable&&!e.reserveKeyword&&(t.inputValue="",ue("")),re(),!ge(l,n)&&e.validateEvent&&(J==null||J.validate("change").catch(u=>it()))},{flush:"post",deep:!0}),G(()=>S.value,l=>{l?ue(t.inputValue):(t.inputValue="",t.previousQuery=null,t.isBeforeHide=!0),i("visible-change",l)}),G(()=>t.options.entries(),()=>{var l;if(!ut)return;const n=((l=f.value)==null?void 0:l.querySelectorAll("input"))||[];(!e.filterable&&!e.defaultFirstOption&&!me(e.modelValue)||!Array.from(n).includes(document.activeElement))&&re(),e.defaultFirstOption&&(e.filterable||e.remote)&&ie.value&&Ke()},{flush:"post"}),G(()=>t.hoveringIndex,l=>{rt(l)&&l>-1?Y.value=M.value[l]||{}:Y.value={},M.value.forEach(n=>{n.hover=Y.value===n})}),dt(()=>{t.isBeforeHide||ze()});const ue=l=>{t.previousQuery===l||oe.value||(t.previousQuery=l,e.filterable&&ee(e.filterMethod)?e.filterMethod(l):e.filterable&&e.remote&&ee(e.remoteMethod)&&e.remoteMethod(l),e.defaultFirstOption&&(e.filterable||e.remote)&&ie.value?F(Ke):F(Ml))},Ke=()=>{const l=M.value.filter(R=>R.visible&&!R.disabled&&!R.states.groupDisabled),n=l.find(R=>R.created),u=l[0],w=M.value.map(R=>R.value);t.hoveringIndex=Ye(w,n||u)},re=()=>{if(e.multiple)t.selectedLabel="";else{const n=P(e.modelValue)?e.modelValue[0]:e.modelValue,u=Ae(n);t.selectedLabel=u.currentLabel,t.selected=[u];return}const l=[];me(e.modelValue)||z(e.modelValue).forEach(n=>{l.push(Ae(n))}),t.selected=l},Ae=l=>{let n;const u=Ie(l).toLowerCase()==="object",w=Ie(l).toLowerCase()==="null",R=Ie(l).toLowerCase()==="undefined";for(let K=t.cachedOptions.size-1;K>=0;K--){const L=wl.value[K];if(u?Q(L.value,e.valueKey)===Q(l,e.valueKey):L.value===l){n={value:l,currentLabel:L.currentLabel,get isDisabled(){return L.isDisabled}};break}}if(n)return n;const fe=u?l.label:!w&&!R?l:"";return{value:l,currentLabel:fe}},Ml=()=>{t.hoveringIndex=M.value.findIndex(l=>t.selected.some(n=>pe(n)===pe(l)))},Rl=()=>{t.selectionWidth=c.value.getBoundingClientRect().width},He=()=>{t.calculatorWidth=C.value.getBoundingClientRect().width},Dl=()=>{t.collapseItemWidth=Z.value.getBoundingClientRect().width},we=()=>{var l,n;(n=(l=g.value)==null?void 0:l.updatePopper)==null||n.call(l)},Ue=()=>{var l,n;(n=(l=h.value)==null?void 0:l.updatePopper)==null||n.call(l)},Ge=()=>{t.inputValue.length>0&&!S.value&&(S.value=!0),ue(t.inputValue)},je=l=>{if(t.inputValue=l.target.value,e.remote)qe();else return Ge()},qe=ct(()=>{Ge()},Ol.value),_=l=>{ge(e.modelValue,l)||i(il,l)},Ll=l=>Pt(l,n=>!t.disabledOptions.has(n)),Bl=l=>{if(e.multiple&&l.code!==pt.delete&&l.target.value.length<=0){const n=z(e.modelValue).slice(),u=Ll(n);if(u<0)return;const w=n[u];n.splice(u,1),i(j,n),_(n),i("remove-tag",w)}},Nl=(l,n)=>{const u=t.selected.indexOf(n);if(u>-1&&!X.value){const w=z(e.modelValue).slice();w.splice(u,1),i(j,w),_(w),i("remove-tag",n.value)}l.stopPropagation(),ce()},Qe=l=>{l.stopPropagation();const n=e.multiple?[]:hl.value;if(e.multiple)for(const u of t.selected)u.isDisabled&&n.push(u.value);i(j,n),_(n),t.hoveringIndex=-1,S.value=!1,i("clear"),ce()},Ze=l=>{var n;if(e.multiple){const u=z((n=e.modelValue)!=null?n:[]).slice(),w=Ye(u,l);w>-1?u.splice(w,1):(e.multipleLimit<=0||u.length<e.multipleLimit)&&u.push(l.value),i(j,u),_(u),l.created&&ue(""),e.filterable&&!e.reserveKeyword&&(t.inputValue="")}else i(j,l.value),_(l.value),S.value=!1;ce(),!S.value&&F(()=>{de(l)})},Ye=(l=[],n)=>q(n==null?void 0:n.value)?l.findIndex(u=>ge(Q(u,e.valueKey),pe(n))):l.indexOf(n.value),de=l=>{var n,u,w,R,fe;const ve=P(l)?l[0]:l;let K=null;if(ve!=null&&ve.value){const L=M.value.filter(_e=>_e.value===ve.value);L.length>0&&(K=L[0].$el)}if(g.value&&K){const L=(R=(w=(u=(n=g.value)==null?void 0:n.popperRef)==null?void 0:u.contentRef)==null?void 0:w.querySelector)==null?void 0:R.call(w,`.${s.be("dropdown","wrap")}`);L&&Bt(L,K)}(fe=se.value)==null||fe.handleScroll()},Wl=l=>{t.options.set(l.value,l),t.cachedOptions.set(l.value,l),l.disabled&&t.disabledOptions.set(l.value,l)},Fl=(l,n)=>{t.options.get(l)===n&&t.options.delete(l)},zl=o(()=>{var l,n;return(n=(l=g.value)==null?void 0:l.popperRef)==null?void 0:n.contentRef}),Pl=()=>{t.isBeforeHide=!1,F(()=>de(t.selected))},ce=()=>{var l;(l=y.value)==null||l.focus()},Kl=()=>{var l;if(S.value){S.value=!1,F(()=>{var n;return(n=y.value)==null?void 0:n.blur()});return}(l=y.value)==null||l.blur()},Al=l=>{Qe(l)},Hl=l=>{if(S.value=!1,Le.value){const n=new FocusEvent("focus",l);F(()=>ml(n))}},Ul=()=>{t.inputValue.length>0?t.inputValue="":S.value=!1},Je=()=>{X.value||(xe&&(t.inputHovering=!0),t.menuVisibleOnFocus?t.menuVisibleOnFocus=!1:S.value=!S.value)},Gl=()=>{S.value?M.value[t.hoveringIndex]&&Ze(M.value[t.hoveringIndex]):Je()},pe=l=>q(l.value)?Q(l.value,e.valueKey):l.value,jl=o(()=>M.value.filter(l=>l.visible).every(l=>l.disabled)),ql=o(()=>e.multiple?e.collapseTags?t.selected.slice(0,e.maxCollapseTags):t.selected:[]),Ql=o(()=>e.multiple?e.collapseTags?t.selected.slice(e.maxCollapseTags):[]:[]),Xe=l=>{if(!S.value){S.value=!0;return}if(!(t.options.size===0||t.filteredOptionsCount===0||oe.value)&&!jl.value){l==="next"?(t.hoveringIndex++,t.hoveringIndex===t.options.size&&(t.hoveringIndex=0)):l==="prev"&&(t.hoveringIndex--,t.hoveringIndex<0&&(t.hoveringIndex=t.options.size-1));const n=M.value[t.hoveringIndex];(n.disabled===!0||n.states.groupDisabled===!0||!n.visible)&&Xe(l),F(()=>de(Y.value))}},Zl=()=>{if(!c.value)return 0;const l=window.getComputedStyle(c.value);return Number.parseFloat(l.gap||"6px")},Yl=o(()=>{const l=Zl();return{maxWidth:`${Z.value&&e.maxCollapseTags===1?t.selectionWidth-t.collapseItemWidth-l:t.selectionWidth}px`}}),Jl=o(()=>({maxWidth:`${t.selectionWidth}px`})),Xl=o(()=>({width:`${Math.max(t.calculatorWidth,Qt)}px`}));return U(c,Rl),U(C,He),U(r,we),U(De,we),U(E,Ue),U(Z,Dl),Me(()=>{re()}),{inputId:bl,contentId:b,nsSelect:s,nsInput:O,states:t,isFocused:Le,expanded:S,optionsArray:M,hoverOption:Y,selectSize:Pe,filteredOptionsCount:ie,resetCalculatorWidth:He,updateTooltip:we,updateTagTooltip:Ue,debouncedOnInputChange:qe,onInput:je,deletePrevTag:Bl,deleteTag:Nl,deleteSelected:Qe,handleOptionSelect:Ze,scrollToOption:de,hasModelValue:Oe,shouldShowPlaceholder:Tl,currentPlaceholder:$l,mouseEnterEventName:kl,showClose:yl,iconComponent:Ne,iconReverse:Sl,validateState:We,validateIcon:Cl,showNewOption:Vl,updateOptions:ze,collapseTagSize:Il,setSelected:re,selectDisabled:X,emptyText:Fe,handleCompositionStart:pl,handleCompositionUpdate:fl,handleCompositionEnd:vl,onOptionCreate:Wl,onOptionDestroy:Fl,handleMenuEnter:Pl,focus:ce,blur:Kl,handleClearClick:Al,handleClickOutside:Hl,handleEsc:Ul,toggleMenu:Je,selectOption:Gl,getValueKey:pe,navigateOptions:Xe,dropdownMenuVisible:El,showTagList:ql,collapseTagList:Ql,tagStyle:Yl,collapseTagStyle:Jl,inputStyle:Xl,popperRef:zl,inputRef:y,tooltipRef:g,tagTooltipRef:h,calculatorRef:C,prefixRef:p,suffixRef:v,selectRef:f,wrapperRef:De,selectionRef:c,scrollbarRef:se,menuRef:r,tagMenuRef:E,collapseItemRef:Z}};var Yt=ae({name:"ElOptions",setup(e,{slots:i}){const a=ye(Ce);let b=[];return()=>{var s,O;const t=(s=i.default)==null?void 0:s.call(i),f=[];function c(g){P(g)&&g.forEach(h=>{var y,C,p,v;const r=(y=(h==null?void 0:h.type)||{})==null?void 0:y.name;r==="ElOptionGroup"?c(!ft(h.children)&&!P(h.children)&&ee((C=h.children)==null?void 0:C.default)?(p=h.children)==null?void 0:p.default():h.children):r==="ElOption"?f.push((v=h.props)==null?void 0:v.value):P(h.children)&&c(h.children)})}return t.length&&c((O=t[0])==null?void 0:O.children),ge(f,b)||(b=f,a&&(a.states.optionValues=f)),t}}});const Jt=vt({name:String,id:String,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:mt,effect:{type:be(String),default:"light"},disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:{type:String,default:""},popperOptions:{type:be(Object),default:()=>({})},remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},collapseTags:Boolean,collapseTagsTooltip:Boolean,maxCollapseTags:{type:Number,default:1},teleported:bt.teleported,persistent:{type:Boolean,default:!0},clearIcon:{type:al,default:ht},fitInputWidth:Boolean,suffixIcon:{type:al,default:gt},tagType:{...nl.type,default:"info"},tagEffect:{...nl.effect,default:"light"},validateEvent:{type:Boolean,default:!0},remoteShowSuffix:Boolean,placement:{type:be(String),values:yt,default:"bottom-start"},fallbackPlacements:{type:be(Array),default:["bottom-start","top-start","right","left"]},appendTo:String,...Nt,...St(["ariaLabel"])}),sl="ElSelect",Xt=ae({name:sl,componentName:sl,components:{ElSelectMenu:qt,ElOption:Re,ElOptions:Yt,ElTag:Mt,ElScrollbar:Ct,ElTooltip:Ot,ElIcon:wt},directives:{ClickOutside:Vt},props:Jt,emits:[j,il,"remove-tag","clear","visible-change","focus","blur"],setup(e,{emit:i}){const a=o(()=>{const{modelValue:t,multiple:f}=e,c=f?[]:void 0;return P(t)?f?t:c:f?c:t}),b=ne({...$e(e),modelValue:a}),s=Zt(b,i);ul(Ce,ne({props:b,states:s.states,optionsArray:s.optionsArray,handleOptionSelect:s.handleOptionSelect,onOptionCreate:s.onOptionCreate,onOptionDestroy:s.onOptionDestroy,selectRef:s.selectRef,setSelected:s.setSelected}));const O=o(()=>e.multiple?s.states.selected.map(t=>t.currentLabel):s.states.selectedLabel);return{...s,modelValue:a,selectedLabel:O}}});function _t(e,i,a,b,s,O){const t=A("el-tag"),f=A("el-tooltip"),c=A("el-icon"),g=A("el-option"),h=A("el-options"),y=A("el-scrollbar"),C=A("el-select-menu"),p=It("click-outside");return le((m(),V("div",{ref:"selectRef",class:d([e.nsSelect.b(),e.nsSelect.m(e.selectSize)]),[Tt(e.mouseEnterEventName)]:v=>e.states.inputHovering=!0,onMouseleave:v=>e.states.inputHovering=!1},[H(f,{ref:"tooltipRef",visible:e.dropdownMenuVisible,placement:e.placement,teleported:e.teleported,"popper-class":[e.nsSelect.e("popper"),e.popperClass],"popper-options":e.popperOptions,"fallback-placements":e.fallbackPlacements,effect:e.effect,pure:"",trigger:"click",transition:`${e.nsSelect.namespace.value}-zoom-in-top`,"stop-popper-mouse-event":!1,"gpu-acceleration":!1,persistent:e.persistent,"append-to":e.appendTo,onBeforeShow:e.handleMenuEnter,onHide:v=>e.states.isBeforeHide=!1},{default:D(()=>{var v;return[$("div",{ref:"wrapperRef",class:d([e.nsSelect.e("wrapper"),e.nsSelect.is("focused",e.isFocused),e.nsSelect.is("hovering",e.states.inputHovering),e.nsSelect.is("filterable",e.filterable),e.nsSelect.is("disabled",e.selectDisabled)]),onClick:B(e.toggleMenu,["prevent"])},[e.$slots.prefix?(m(),V("div",{key:0,ref:"prefixRef",class:d(e.nsSelect.e("prefix"))},[k(e.$slots,"prefix")],2)):T("v-if",!0),$("div",{ref:"selectionRef",class:d([e.nsSelect.e("selection"),e.nsSelect.is("near",e.multiple&&!e.$slots.prefix&&!!e.states.selected.length)])},[e.multiple?k(e.$slots,"tag",{key:0},()=>[(m(!0),V(el,null,ll(e.showTagList,r=>(m(),V("div",{key:e.getValueKey(r),class:d(e.nsSelect.e("selected-item"))},[H(t,{closable:!e.selectDisabled&&!r.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:he(e.tagStyle),onClose:E=>e.deleteTag(E,r)},{default:D(()=>[$("span",{class:d(e.nsSelect.e("tags-text"))},[k(e.$slots,"label",{label:r.currentLabel,value:r.value},()=>[tl(N(r.currentLabel),1)])],2)]),_:2},1032,["closable","size","type","effect","style","onClose"])],2))),128)),e.collapseTags&&e.states.selected.length>e.maxCollapseTags?(m(),W(f,{key:0,ref:"tagTooltipRef",disabled:e.dropdownMenuVisible||!e.collapseTagsTooltip,"fallback-placements":["bottom","top","right","left"],effect:e.effect,placement:"bottom",teleported:e.teleported},{default:D(()=>[$("div",{ref:"collapseItemRef",class:d(e.nsSelect.e("selected-item"))},[H(t,{closable:!1,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",style:he(e.collapseTagStyle)},{default:D(()=>[$("span",{class:d(e.nsSelect.e("tags-text"))}," + "+N(e.states.selected.length-e.maxCollapseTags),3)]),_:1},8,["size","type","effect","style"])],2)]),content:D(()=>[$("div",{ref:"tagMenuRef",class:d(e.nsSelect.e("selection"))},[(m(!0),V(el,null,ll(e.collapseTagList,r=>(m(),V("div",{key:e.getValueKey(r),class:d(e.nsSelect.e("selected-item"))},[H(t,{class:"in-tooltip",closable:!e.selectDisabled&&!r.isDisabled,size:e.collapseTagSize,type:e.tagType,effect:e.tagEffect,"disable-transitions":"",onClose:E=>e.deleteTag(E,r)},{default:D(()=>[$("span",{class:d(e.nsSelect.e("tags-text"))},[k(e.$slots,"label",{label:r.currentLabel,value:r.value},()=>[tl(N(r.currentLabel),1)])],2)]),_:2},1032,["closable","size","type","effect","onClose"])],2))),128))],2)]),_:3},8,["disabled","effect","teleported"])):T("v-if",!0)]):T("v-if",!0),e.selectDisabled?T("v-if",!0):(m(),V("div",{key:1,class:d([e.nsSelect.e("selected-item"),e.nsSelect.e("input-wrapper"),e.nsSelect.is("hidden",!e.filterable)])},[le($("input",{id:e.inputId,ref:"inputRef","onUpdate:modelValue":r=>e.states.inputValue=r,type:"text",name:e.name,class:d([e.nsSelect.e("input"),e.nsSelect.is(e.selectSize)]),disabled:e.selectDisabled,autocomplete:e.autocomplete,style:he(e.inputStyle),role:"combobox",readonly:!e.filterable,spellcheck:"false","aria-activedescendant":((v=e.hoverOption)==null?void 0:v.id)||"","aria-controls":e.contentId,"aria-expanded":e.dropdownMenuVisible,"aria-label":e.ariaLabel,"aria-autocomplete":"none","aria-haspopup":"listbox",onKeydown:[x(B(r=>e.navigateOptions("next"),["stop","prevent"]),["down"]),x(B(r=>e.navigateOptions("prev"),["stop","prevent"]),["up"]),x(B(e.handleEsc,["stop","prevent"]),["esc"]),x(B(e.selectOption,["stop","prevent"]),["enter"]),x(B(e.deletePrevTag,["stop"]),["delete"])],onCompositionstart:e.handleCompositionStart,onCompositionupdate:e.handleCompositionUpdate,onCompositionend:e.handleCompositionEnd,onInput:e.onInput,onClick:B(e.toggleMenu,["stop"])},null,46,["id","onUpdate:modelValue","name","disabled","autocomplete","readonly","aria-activedescendant","aria-controls","aria-expanded","aria-label","onKeydown","onCompositionstart","onCompositionupdate","onCompositionend","onInput","onClick"]),[[Et,e.states.inputValue]]),e.filterable?(m(),V("span",{key:0,ref:"calculatorRef","aria-hidden":"true",class:d(e.nsSelect.e("input-calculator")),textContent:N(e.states.inputValue)},null,10,["textContent"])):T("v-if",!0)],2)),e.shouldShowPlaceholder?(m(),V("div",{key:2,class:d([e.nsSelect.e("selected-item"),e.nsSelect.e("placeholder"),e.nsSelect.is("transparent",!e.hasModelValue||e.expanded&&!e.states.inputValue)])},[e.hasModelValue?k(e.$slots,"label",{key:0,label:e.currentPlaceholder,value:e.modelValue},()=>[$("span",null,N(e.currentPlaceholder),1)]):(m(),V("span",{key:1},N(e.currentPlaceholder),1))],2)):T("v-if",!0)],2),$("div",{ref:"suffixRef",class:d(e.nsSelect.e("suffix"))},[e.iconComponent&&!e.showClose?(m(),W(c,{key:0,class:d([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.iconReverse])},{default:D(()=>[(m(),W(Ee(e.iconComponent)))]),_:1},8,["class"])):T("v-if",!0),e.showClose&&e.clearIcon?(m(),W(c,{key:1,class:d([e.nsSelect.e("caret"),e.nsSelect.e("icon"),e.nsSelect.e("clear")]),onClick:e.handleClearClick},{default:D(()=>[(m(),W(Ee(e.clearIcon)))]),_:1},8,["class","onClick"])):T("v-if",!0),e.validateState&&e.validateIcon?(m(),W(c,{key:2,class:d([e.nsInput.e("icon"),e.nsInput.e("validateIcon")])},{default:D(()=>[(m(),W(Ee(e.validateIcon)))]),_:1},8,["class"])):T("v-if",!0)],2)],10,["onClick"])]}),content:D(()=>[H(C,{ref:"menuRef"},{default:D(()=>[e.$slots.header?(m(),V("div",{key:0,class:d(e.nsSelect.be("dropdown","header")),onClick:B(()=>{},["stop"])},[k(e.$slots,"header")],10,["onClick"])):T("v-if",!0),le(H(y,{id:e.contentId,ref:"scrollbarRef",tag:"ul","wrap-class":e.nsSelect.be("dropdown","wrap"),"view-class":e.nsSelect.be("dropdown","list"),class:d([e.nsSelect.is("empty",e.filteredOptionsCount===0)]),role:"listbox","aria-label":e.ariaLabel,"aria-orientation":"vertical"},{default:D(()=>[e.showNewOption?(m(),W(g,{key:0,value:e.states.inputValue,created:!0},null,8,["value"])):T("v-if",!0),H(h,null,{default:D(()=>[k(e.$slots,"default")]),_:3})]),_:3},8,["id","wrap-class","view-class","class","aria-label"]),[[ke,e.states.options.size>0&&!e.loading]]),e.$slots.loading&&e.loading?(m(),V("div",{key:1,class:d(e.nsSelect.be("dropdown","loading"))},[k(e.$slots,"loading")],2)):e.loading||e.filteredOptionsCount===0?(m(),V("div",{key:2,class:d(e.nsSelect.be("dropdown","empty"))},[k(e.$slots,"empty",{},()=>[$("span",null,N(e.emptyText),1)])],2)):T("v-if",!0),e.$slots.footer?(m(),V("div",{key:3,class:d(e.nsSelect.be("dropdown","footer")),onClick:B(()=>{},["stop"])},[k(e.$slots,"footer")],10,["onClick"])):T("v-if",!0)]),_:3},512)]),_:3},8,["visible","placement","teleported","popper-class","popper-options","fallback-placements","effect","transition","persistent","append-to","onBeforeShow","onHide"])],16,["onMouseleave"])),[[p,e.handleClickOutside,e.popperRef]])}var xt=Se(Xt,[["render",_t],["__file","select.vue"]]);const en=ae({name:"ElOptionGroup",componentName:"ElOptionGroup",props:{label:String,disabled:Boolean},setup(e){const i=te("select"),a=I(null),b=Te(),s=I([]);ul(dl,ne({...$e(e)}));const O=o(()=>s.value.some(g=>g.visible===!0)),t=g=>{var h,y;return((h=g.type)==null?void 0:h.name)==="ElOption"&&!!((y=g.component)!=null&&y.proxy)},f=g=>{const h=z(g),y=[];return h.forEach(C=>{var p,v;t(C)?y.push(C.component.proxy):(p=C.children)!=null&&p.length?y.push(...f(C.children)):(v=C.component)!=null&&v.subTree&&y.push(...f(C.component.subTree))}),y},c=()=>{s.value=f(b.subTree)};return Me(()=>{c()}),$t(a,c,{attributes:!0,subtree:!0,childList:!0}),{groupRef:a,visible:O,ns:i}}});function ln(e,i,a,b,s,O){return le((m(),V("ul",{ref:"groupRef",class:d(e.ns.be("group","wrap"))},[$("li",{class:d(e.ns.be("group","title"))},N(e.label),3),$("li",null,[$("ul",{class:d(e.ns.b("group"))},[k(e.$slots,"default")],2)])],2)),[[ke,e.visible]])}var cl=Se(en,[["render",ln],["__file","option-group.vue"]]);const on=kt(xt,{Option:Re,OptionGroup:cl}),un=rl(Re);rl(cl);export{un as E,on as a,Kt as e,Ce as s};
