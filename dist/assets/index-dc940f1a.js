import{a2 as ce,bP as pe,d as re,r as c,K as _e,o as ue,D as me,at as ye,a as g,v as A,g as d,f as p,e as i,u as o,y as L,h,b as S,i as v,z as E,e9 as R,F as fe,q as W,k as ge,bc as he,bU as ve,av as xe,bf as be,bV as ke,bg as we,bh as Ce,bn as Ie,ay as Te,p as De,j as ze,_ as Fe}from"./index-db94d997.js";import{m as $}from"./dealTable-c35ad2da.js";import{D as Ne}from"./dayjs-a8e42122.js";import{_ as <PERSON>}from"./index-42d7fb9b.js";import{_ as Se}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";function Ke(u){return ce({url:"/web/companyEq/v1/page",method:"POST",isTable:!0,data:u})}function Ye(u){return pe({url:"/web/companyEq/v1/export",method:"POST",data:u})}const V=u=>(De("data-v-cddf9f89"),u=u(),ze(),u),Pe={class:"statements_container"},Be={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ve={style:{flex:"1","padding-right":"5%"}},je=V(()=>d("span",{style:{"margin-left":"5px"}},"全选",-1)),qe={style:{flex:"1","padding-right":"5%"}},Me=V(()=>d("div",{style:{flex:"1","padding-right":"5%"}},null,-1)),Oe=V(()=>d("div",{class:"item_title"},"指标维度",-1)),Ue={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ae={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},Ee={style:{display:"flex","justify-content":"flex-end","flex-direction":"row"}},Re={style:{display:"flex","flex-direction":"row","align-items":"center"}},We={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},$e={class:"statements_container",style:{"margin-top":"24px","margin-bottom":"24px","max-height":"740px",position:"relative"}},Je={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},Qe={class:"text-hide"},Ge={class:"text-hide"},He={class:"text-hide"},Xe=re({__name:"index",setup(u){const j=c(),C=c(!1);c([]);const I=c({}),T=c([]),q=c([]),x=c(0),K=c(1),D=c(12),y=c([]),a=_e({companyCodeList:"",targetTypes:["0"],companyId:[],companyId_checked:!1,companyId_indeterminate:!1}),M=[{label:"逆变器电量(万kWh)",value:"0"},{label:"人工调整发电量(万kWh)",value:"1"},{label:"调整后发电量(万kWh)",value:"2"}],J=[{label:"逆变器电量",value:"0"},{label:"人工调整发电量",value:"1"},{label:"调整后发电量",value:"2"}],Y=c(1),O=c([{title:"产权公司",dataIndex:"companyName",resizable:!0,width:120,fixed:"left",customCell:(e,t,l)=>$(Y.value,e,t)},{title:"省份",dataIndex:"provinceName",resizable:!0,width:120,fixed:"left",customCell:(e,t,l)=>$(Y.value,e,t)},{title:"指标",key:"targetType",dataIndex:"targetType",resizable:!0,width:200,fixed:"left"}]),z=c([]),P=c([]),Q=(e,{attrs:t})=>t.vnodes,G=(e,t)=>{a[e]=t.map(l=>l.value)},H=(e,t,l)=>{var _;((_=e==null?void 0:e.target)==null?void 0:_.checked)?G(t,l):a[t]=[],a[t+"_indeterminate"]=!1},X=(e,t,l,s)=>{t.length===0?(a[s+"_indeterminate"]=!1,a[s+"_checked"]=!1):t.length===l.length?(a[s+"_indeterminate"]=!1,a[s+"_checked"]=!0):(a[s+"_indeterminate"]=!0,a[s+"_checked"]=!1)},Z=c({yearKey:[{required:!0}]}),ee=e=>{U(e)},te=e=>{console.log(e)},ae=()=>{j.value.resetFields(),I.value={},T.value=[],q.value=[],z.value=[],P.value=[...O.value,...z.value],x.value=0},ne=(e,t,l)=>{K.value=e.current,D.value=e.pageSize,x.value=e.total;const s={...a,pageNum:e.current,pageSize:D.value};U(s)},U=e=>{var s;a.yearKey;const l={companyCodeList:W.cloneDeep(e.companyId)||[],pageNum:K.value,pageSize:D.value/((s=a.targetTypes)==null?void 0:s.length),targetTypes:e.targetTypes,yearKey:e!=null&&e.yearKey?ge(e.yearKey).format("YYYY"):null};C.value=!0,Ke(l).then(_=>{C.value=!1,I.value=l,console.log("公司维度res=",_),oe(_,1)}).catch(_=>{C.value=!1})},oe=(e,t)=>{var l,s,_,f,F;if(t===1){Y.value=(l=a.targetTypes)==null?void 0:l.length,z.value=(s=e==null?void 0:e.result)==null?void 0:s.timeList.map((k,N)=>({title:N+1+"月",dataIndex:k,formatMoney:!0,resizable:!0,width:120})),P.value=[...O.value,...z.value],T.value=((_=e==null?void 0:e.result)==null?void 0:_.dataList)||[];let b=((f=a.targetTypes)==null?void 0:f.length)||1;x.value=e!=null&&e.total?(e==null?void 0:e.total)*b:0}else if(t===2){q.value=(e==null?void 0:e.result)||[];let b=((F=a.targetTypes)==null?void 0:F.length)||1;x.value=e!=null&&e.total?(e==null?void 0:e.total)*b:0}};ue(()=>{le()}),me(()=>{console.log("onActivated")});const le=()=>{ye({}).then(e=>{let l=(e||[]).map(s=>({label:s.companyName,value:s.companyCode}));y.value=l})},se=()=>{console.log("pageParams=",I.value);const e=a!=null&&a.companyId?W.cloneDeep(a==null?void 0:a.companyId):[],t={...I.value,companyCodeList:e};t.yearKey?Ye(t).then(l=>{console.log("公司维度导出res:",l)}):he.warning("请选择时间周期")};return(e,t)=>{const l=ve,s=Le,_=xe,f=be,F=Ne,b=ke,k=we,N=Se,ie=Ce,B=Ie,de=Te;return g(),A(fe,null,[d("div",Pe,[p(ie,{ref_key:"formRef",ref:j,name:"formRef",model:o(a),rules:o(Z),onFinish:ee,onFinishFailed:te},{default:i(()=>[d("div",Be,[d("div",Ve,[p(f,{label:"产权公司",name:"companyId"},{default:i(()=>[p(_,{value:o(a).companyId,"onUpdate:value":t[3]||(t[3]=n=>o(a).companyId=n),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:o(y),mode:"multiple","filter-option":(n,r)=>((r==null?void 0:r.label)??"").toLowerCase().includes(n.toLowerCase()),onChange:t[4]||(t[4]=(n,r)=>X(n,r,o(y),"companyId"))},{dropdownRender:i(({menuNode:n})=>[o(y)&&o(y).length>0?(g(),A("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[2]||(t[2]=r=>r.preventDefault())},[p(l,{checked:o(a).companyId_checked,"onUpdate:checked":t[0]||(t[0]=r=>o(a).companyId_checked=r),indeterminate:o(a).companyId_indeterminate,onChange:t[1]||(t[1]=r=>H(r,"companyId",o(y)))},null,8,["checked","indeterminate"]),je],32)):L("",!0),p(s,{style:{margin:"4px 0"}}),p(Q,{vnodes:n},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1})]),d("div",qe,[p(f,{label:"时间周期",name:"yearKey"},{default:i(()=>[p(F,{value:o(a).yearKey,"onUpdate:value":t[5]||(t[5]=n=>o(a).yearKey=n),style:{width:"100%"},placeholder:"请选择",format:"YYYY",picker:"year"},null,8,["value"])]),_:1})]),Me]),Oe,d("div",Ue,[d("div",Ae,[d("div",Ee,[p(f,{name:"targetTypes"},{default:i(()=>[p(b,{value:o(a).targetTypes,"onUpdate:value":t[6]||(t[6]=n=>o(a).targetTypes=n),name:"checkboxgroup",options:J},null,8,["value"])]),_:1})])])]),d("div",Re,[d("div",We,[p(N,null,{default:i(()=>[p(k,{onClick:ae},{default:i(()=>[h("重置")]),_:1}),p(k,{type:"primary","html-type":"submit"},{default:i(()=>[h("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),d("div",$e,[d("div",Je,[p(N,null,{default:i(()=>[o(T).length>0?(g(),S(k,{key:0,type:"primary",onClick:se},{default:i(()=>[h("导出")]),_:1})):L("",!0)]),_:1})]),p(de,{style:{"margin-bottom":"24px"},columns:o(P),"data-source":o(T),pagination:{current:o(K),total:o(x),showTotal:n=>`共 ${n} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0,pageSizeOptions:["6","12","24","60","120"],pageSize:o(D)},scroll:{y:500},loading:o(C),onChange:ne},{bodyCell:i(({column:n,text:r,record:m,index:Ze})=>[n!=null&&n.formatMoney&&!n.render?(g(),S(B,{key:0},{title:i(()=>[h(v(o(E)(m[n.dataIndex])),1)]),default:i(()=>[d("span",Qe,v(o(E)(m[n.dataIndex])),1)]),_:2},1024)):n!=null&&n.formatFixed&&!n.render?(g(),S(B,{key:1},{title:i(()=>[h(v(m[n.dataIndex]&&o(R)(m[n.dataIndex])),1)]),default:i(()=>[d("span",Ge,v(m[n.dataIndex]&&o(R)(m[n.dataIndex])),1)]),_:2},1024)):L("",!0),n.key=="targetType"?(g(),S(B,{key:2},{title:i(()=>{var w;return[h(v((w=M[m[n.dataIndex]])==null?void 0:w.label),1)]}),default:i(()=>{var w;return[d("span",He,v((w=M[m[n.dataIndex]])==null?void 0:w.label),1)]}),_:2},1024)):L("",!0)]),_:1},8,["columns","data-source","pagination","loading"])])],64)}}});const st=Fe(Xe,[["__scopeId","data-v-cddf9f89"]]);export{st as default};
