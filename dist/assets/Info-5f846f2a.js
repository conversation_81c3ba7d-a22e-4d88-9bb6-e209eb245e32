import{_ as g}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as D,r as o,o as F,D as S,au as T,a as _,v as N,f as x,e as f,u as I,b as k,h as V,y as B,bg as K,_ as L}from"./index-db94d997.js";import{a as E,b as q}from"./index-895935b7.js";import{_ as R}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const A={class:"areaPrice"},O=D({__name:"Info",setup(H){var b;const h=o(((b=history.state)==null?void 0:b.pdata)||{}),n=o();o([]);const c=o([]);o([{label:"已确认",value:1},{label:"待确认",value:0}]);let d="",u="";const w=[{title:"电站编号",dataIndex:"stationCode",resizable:!0,fixed:"left",width:120},{title:"业主名称",dataIndex:"stationName",resizable:!0,fixed:"left",width:120},{title:"产权公司",dataIndex:"companyName",width:150,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:c,valueTreeLoad:({id:t})=>m(t),onSelect:(t,e,a)=>{var i,l;const r=((l=(i=a==null?void 0:a.triggerNode)==null?void 0:i.props)==null?void 0:l.level)||1;d=["prvCode","cityCode","distCode","town","vil"][r-1],u=t},render:!0,width:150,resizable:!0,hideInTable:!0},{title:"省",dataIndex:"prvName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"distName",search:!1,resizable:!0,width:100},{title:"材料费",dataIndex:"materialFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"外购电力费",dataIndex:"buyElectricityFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"过网费",dataIndex:"passNetFee",resizable:!0,search:!1,formatMoney:!0,width:100},{title:"职工薪酬",dataIndex:"empFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"折旧费",dataIndex:"deprecitionFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"修理费",dataIndex:"fixFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"委托运行费",dataIndex:"entrustRunFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他费用",dataIndex:"otherFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"主营业务成本",dataIndex:"mainBusinessCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他业务成本",dataIndex:"otherBusinessCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"税金及附加",dataIndex:"taxAndAdd",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"销售费用",dataIndex:"salesFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"管理费用",dataIndex:"managementFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"研发费用",dataIndex:"developFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"财务费用",dataIndex:"financeFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他收益",dataIndex:"otherIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"投资收益",dataIndex:"investIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:'净敞口套期收益(损失以"-"号填列)',dataIndex:"netOpenHedgingIncome",resizable:!0,search:!1,formatMoney:!0,width:230},{title:'公允价值变动收益(损失以"-"号填列)',dataIndex:"fairValueChangeIncome",resizable:!0,search:!1,formatMoney:!0,width:240},{title:'信用减值损失(损失以"-"号填列)',dataIndex:"creditSubValueLoss",resizable:!0,search:!1,formatMoney:!0,width:230},{title:'资产减值损失(损失以"-"号填列)',dataIndex:"assetSubValueLoss",resizable:!0,search:!1,formatMoney:!0,width:230},{title:'资产处理收益(损失以"-"号填列)',dataIndex:"assetDisposeIncome",resizable:!0,search:!1,formatMoney:!0,width:230}];F(()=>{m()}),S(()=>{n.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&M()});const m=t=>new Promise((e,a)=>{T({pid:t||"0"}).then(r=>{console.log("行政区res=",r);let s=y(r);c.value=s,console.log(c.value),e(!0)}).catch(()=>{a()})}),y=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,y(e.subDistrict))}),t),v=()=>{var e;let t=(e=n.value)==null?void 0:e.getInitialFormStateNew();q(t)},p=o([]),z=t=>{p.value=t||[]},M=()=>{var t;(t=n.value)==null||t.reload()},C=(t,e)=>{let a={};return new Promise(r=>{var i,l;e&&e.hasOwnProperty("cityTree")&&!(e!=null&&e.cityTree)&&(d="",u=""),d&&(a={[d]:u});const s={...a,...t,delStatus:0,companyCode:(i=h.value)==null?void 0:i.companyCode,monthKey:(l=h.value)==null?void 0:l.monthKey};r(s)})};return(t,e)=>{const a=K,r=R,s=g;return _(),N("div",A,[x(s,{columns:w,ref_key:"actionRef",ref:n,request:I(E),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:z,"before-query-params":C},{tableHeader:f(()=>[x(r,null,{default:f(()=>[I(p).length>0?(_(),k(a,{key:0,type:"primary",onClick:v},{default:f(()=>[V("导出")]),_:1})):B("",!0)]),_:1})]),_:1},8,["request"])])}}});const te=L(O,[["__scopeId","data-v-5d05c2c9"]]);export{te as default};
