import{a3 as M,ct as N,d as v,ac as V,af as w,c as P,a as t,v as $,g as l,a1 as f,Z as o,u as s,b as c,e as i,f as g,cu as k,ai as y,aP as b,y as C,n as h,a_ as I,aj as j,aq as q}from"./index-db94d997.js";const F=M({type:{type:String,values:["primary","success","info","warning","danger"],default:"primary"},closable:Boolean,disableTransitions:<PERSON><PERSON><PERSON>,hit:Boolean,color:String,size:{type:String,values:N},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),K={close:n=>n instanceof MouseEvent,click:n=>n instanceof MouseEvent},Z=v({name:"ElTag"}),A=v({...Z,props:F,emits:K,setup(n,{emit:r}){const _=n,S=V(),a=w("tag"),u=P(()=>{const{type:e,hit:m,effect:E,closable:z,round:T}=_;return[a.b(),a.is("closable",z),a.m(e||"primary"),a.m(S.value),a.m(E),a.is("hit",m),a.is("round",T)]}),p=e=>{r("close",e)},d=e=>{r("click",e)},B=e=>{e.component.subTree.component.bum=null};return(e,m)=>e.disableTransitions?(t(),$("span",{key:0,class:o(s(u)),style:h({backgroundColor:e.color}),onClick:d},[l("span",{class:o(s(a).e("content"))},[f(e.$slots,"default")],2),e.closable?(t(),c(s(b),{key:0,class:o(s(a).e("close")),onClick:y(p,["stop"])},{default:i(()=>[g(s(k))]),_:1},8,["class","onClick"])):C("v-if",!0)],6)):(t(),c(I,{key:1,name:`${s(a).namespace.value}-zoom-in-center`,appear:"",onVnodeMounted:B},{default:i(()=>[l("span",{class:o(s(u)),style:h({backgroundColor:e.color}),onClick:d},[l("span",{class:o(s(a).e("content"))},[f(e.$slots,"default")],2),e.closable?(t(),c(s(b),{key:0,class:o(s(a).e("close")),onClick:y(p,["stop"])},{default:i(()=>[g(s(k))]),_:1},8,["class","onClick"])):C("v-if",!0)],6)]),_:3},8,["name"]))}});var D=j(A,[["__file","tag.vue"]]);const H=q(D);export{H as E,F as t};
