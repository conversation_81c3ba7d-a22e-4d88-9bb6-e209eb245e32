import{bl as Ie,c as p,bb as je,cR as Ke,a6 as he,a3 as We,a9 as Ue,aD as Z,ak as Ye,a4 as ee,a5 as q,d as Ee,aJ as Xe,b2 as Ze,am as qe,an as Ge,ac as Je,ad as Qe,af as ye,cw as G,r as J,cS as _e,cT as et,bk as tt,cl as at,w as Q,V as F,ap as ge,o as ot,b4 as nt,a as d,v as b,y as f,F as _,Z as m,u as t,a1 as D,g as V,b as S,e as R,aB as j,aP as B,aI as be,f as st,aG as lt,ai as it,aQ as rt,i as K,n as xe,aj as ut,cj as we,aq as ct}from"./index-db94d997.js";import{i as Se,V as dt}from"./icon-831229e8.js";import{a as pt,d as ft}from"./index-ec316fb4.js";const vt=()=>Ie&&/firefox/i.test(window.navigator.userAgent),mt=o=>o,ht=["class","style"],yt=/^on[A-Z]/,gt=(o={})=>{const{excludeListeners:v=!1,excludeKeys:r}=o,a=p(()=>((r==null?void 0:r.value)||[]).concat(ht)),i=je();return i?p(()=>{var u;return Ke(Object.entries((u=i.proxy)==null?void 0:u.$attrs).filter(([s])=>!a.value.includes(s)&&!(v&&yt.test(s))))}):p(()=>({}))};function bt(o){let v;function r(){if(o.value==null)return;const{selectionStart:i,selectionEnd:u,value:s}=o.value;if(i==null||u==null)return;const x=s.slice(0,Math.max(0,i)),c=s.slice(Math.max(0,u));v={selectionStart:i,selectionEnd:u,value:s,beforeTxt:x,afterTxt:c}}function a(){if(o.value==null||v==null)return;const{value:i}=o.value,{beforeTxt:u,afterTxt:s,selectionStart:x}=v;if(u==null||s==null||x==null)return;let c=i.length;if(i.endsWith(s))c=i.length-s.length;else if(i.startsWith(u))c=u.length;else{const C=u[x-1],h=i.indexOf(C,x-1);h!==-1&&(c=h+1)}o.value.setSelectionRange(c,c)}return[r,a]}let g;const xt=`
  height:0 !important;
  visibility:hidden !important;
  ${vt()?"":"overflow:hidden !important;"}
  position:absolute !important;
  z-index:-1000 !important;
  top:0 !important;
  right:0 !important;
`,wt=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function St(o){const v=window.getComputedStyle(o),r=v.getPropertyValue("box-sizing"),a=Number.parseFloat(v.getPropertyValue("padding-bottom"))+Number.parseFloat(v.getPropertyValue("padding-top")),i=Number.parseFloat(v.getPropertyValue("border-bottom-width"))+Number.parseFloat(v.getPropertyValue("border-top-width"));return{contextStyle:wt.map(s=>`${s}:${v.getPropertyValue(s)}`).join(";"),paddingSize:a,borderSize:i,boxSizing:r}}function Ce(o,v=1,r){var a;g||(g=document.createElement("textarea"),document.body.appendChild(g));const{paddingSize:i,borderSize:u,boxSizing:s,contextStyle:x}=St(o);g.setAttribute("style",`${x};${xt}`),g.value=o.value||o.placeholder||"";let c=g.scrollHeight;const C={};s==="border-box"?c=c+u:s==="content-box"&&(c=c-i),g.value="";const h=g.scrollHeight-i;if(he(v)){let y=h*v;s==="border-box"&&(y=y+i+u),c=Math.max(y,c),C.minHeight=`${y}px`}if(he(r)){let y=h*r;s==="border-box"&&(y=y+i+u),c=Math.min(y,c)}return C.height=`${c}px`,(a=g.parentNode)==null||a.removeChild(g),g=void 0,C}const Ct=We({id:{type:String,default:void 0},size:Ue,disabled:Boolean,modelValue:{type:Z([String,Number,Object]),default:""},maxlength:{type:[String,Number]},minlength:{type:[String,Number]},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:Z([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String},readonly:Boolean,clearable:Boolean,showPassword:Boolean,showWordLimit:Boolean,suffixIcon:{type:Se},prefixIcon:{type:Se},containerRole:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:Z([Object,Array,String]),default:()=>mt({})},autofocus:Boolean,rows:{type:Number,default:2},...Ye(["ariaLabel"])}),It={[ee]:o=>q(o),input:o=>q(o),change:o=>q(o),focus:o=>o instanceof FocusEvent,blur:o=>o instanceof FocusEvent,clear:()=>!0,mouseleave:o=>o instanceof MouseEvent,mouseenter:o=>o instanceof MouseEvent,keydown:o=>o instanceof Event,compositionstart:o=>o instanceof CompositionEvent,compositionupdate:o=>o instanceof CompositionEvent,compositionend:o=>o instanceof CompositionEvent},Et=Ee({name:"ElInput",inheritAttrs:!1}),zt=Ee({...Et,props:Ct,emits:It,setup(o,{expose:v,emit:r}){const a=o,i=Xe(),u=gt(),s=Ze(),x=p(()=>[a.type==="textarea"?te.b():n.b(),n.m(ze.value),n.is("disabled",E.value),n.is("exceed",Te.value),{[n.b("group")]:s.prepend||s.append,[n.m("prefix")]:s.prefix||a.prefixIcon,[n.m("suffix")]:s.suffix||a.suffixIcon||a.clearable||a.showPassword,[n.bm("suffix","password-clear")]:A.value&&U.value,[n.b("hidden")]:a.type==="hidden"},i.class]),c=p(()=>[n.e("wrapper"),n.is("focus",O.value)]),{form:C,formItem:h}=qe(),{inputId:y}=Ge(a,{formItemContext:h}),ze=Je(),E=Qe(),n=ye("input"),te=ye("textarea"),L=G(),w=G(),W=J(!1),M=J(!1),ae=J(),H=G(a.inputStyle),z=p(()=>L.value||w.value),{wrapperRef:Pe,isFocused:O,handleFocus:ke,handleBlur:Fe}=pt(z,{beforeFocus(){return E.value},afterBlur(){var e;a.validateEvent&&((e=h==null?void 0:h.validate)==null||e.call(h,"blur").catch(l=>ge()))}}),oe=p(()=>{var e;return(e=C==null?void 0:C.statusIcon)!=null?e:!1}),N=p(()=>(h==null?void 0:h.validateState)||""),ne=p(()=>N.value&&dt[N.value]),Ve=p(()=>M.value?_e:et),Ne=p(()=>[i.style]),se=p(()=>[a.inputStyle,H.value,{resize:a.resize}]),I=p(()=>tt(a.modelValue)?"":String(a.modelValue)),A=p(()=>a.clearable&&!E.value&&!a.readonly&&!!I.value&&(O.value||W.value)),U=p(()=>a.showPassword&&!E.value&&!!I.value&&(!!I.value||O.value)),P=p(()=>a.showWordLimit&&!!a.maxlength&&(a.type==="text"||a.type==="textarea")&&!E.value&&!a.readonly&&!a.showPassword),Y=p(()=>I.value.length),Te=p(()=>!!P.value&&Y.value>Number(a.maxlength)),$e=p(()=>!!s.suffix||!!a.suffixIcon||A.value||a.showPassword||P.value||!!N.value&&oe.value),[Re,Be]=bt(L);at(w,e=>{if(Le(),!P.value||a.resize!=="both")return;const l=e[0],{width:k}=l.contentRect;ae.value={right:`calc(100% - ${k+15+6}px)`}});const T=()=>{const{type:e,autosize:l}=a;if(!(!Ie||e!=="textarea"||!w.value))if(l){const k=we(l)?l.minRows:void 0,ve=we(l)?l.maxRows:void 0,me=Ce(w.value,k,ve);H.value={overflowY:"hidden",...me},F(()=>{w.value.offsetHeight,H.value=me})}else H.value={minHeight:Ce(w.value).minHeight}},Le=(e=>{let l=!1;return()=>{var k;if(l||!a.autosize)return;((k=w.value)==null?void 0:k.offsetParent)===null||(e(),l=!0)}})(T),$=()=>{const e=z.value,l=a.formatter?a.formatter(I.value):I.value;!e||e.value===l||(e.value=l)},X=async e=>{Re();let{value:l}=e.target;if(a.formatter&&(l=a.parser?a.parser(l):l),!ie.value){if(l===I.value){$();return}r(ee,l),r("input",l),await F(),$(),Be()}},le=e=>{r("change",e.target.value)},{isComposing:ie,handleCompositionStart:re,handleCompositionUpdate:ue,handleCompositionEnd:ce}=ft({emit:r,afterComposition:X}),Me=()=>{M.value=!M.value,de()},de=async()=>{var e;await F(),(e=z.value)==null||e.focus()},He=()=>{var e;return(e=z.value)==null?void 0:e.blur()},Oe=e=>{W.value=!1,r("mouseleave",e)},Ae=e=>{W.value=!0,r("mouseenter",e)},pe=e=>{r("keydown",e)},De=()=>{var e;(e=z.value)==null||e.select()},fe=()=>{r(ee,""),r("change",""),r("clear"),r("input","")};return Q(()=>a.modelValue,()=>{var e;F(()=>T()),a.validateEvent&&((e=h==null?void 0:h.validate)==null||e.call(h,"change").catch(l=>ge()))}),Q(I,()=>$()),Q(()=>a.type,async()=>{await F(),$(),T()}),ot(()=>{!a.formatter&&a.parser,$(),F(T)}),v({input:L,textarea:w,ref:z,textareaStyle:se,autosize:nt(a,"autosize"),isComposing:ie,focus:de,blur:He,select:De,clear:fe,resizeTextarea:T}),(e,l)=>(d(),b("div",{class:m([t(x),{[t(n).bm("group","append")]:e.$slots.append,[t(n).bm("group","prepend")]:e.$slots.prepend}]),style:xe(t(Ne)),onMouseenter:Ae,onMouseleave:Oe},[f(" input "),e.type!=="textarea"?(d(),b(_,{key:0},[f(" prepend slot "),e.$slots.prepend?(d(),b("div",{key:0,class:m(t(n).be("group","prepend"))},[D(e.$slots,"prepend")],2)):f("v-if",!0),V("div",{ref_key:"wrapperRef",ref:Pe,class:m(t(c))},[f(" prefix slot "),e.$slots.prefix||e.prefixIcon?(d(),b("span",{key:0,class:m(t(n).e("prefix"))},[V("span",{class:m(t(n).e("prefix-inner"))},[D(e.$slots,"prefix"),e.prefixIcon?(d(),S(t(B),{key:0,class:m(t(n).e("icon"))},{default:R(()=>[(d(),S(j(e.prefixIcon)))]),_:1},8,["class"])):f("v-if",!0)],2)],2)):f("v-if",!0),V("input",be({id:t(y),ref_key:"input",ref:L,class:t(n).e("inner")},t(u),{minlength:e.minlength,maxlength:e.maxlength,type:e.showPassword?M.value?"text":"password":e.type,disabled:t(E),readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.ariaLabel,placeholder:e.placeholder,style:e.inputStyle,form:e.form,autofocus:e.autofocus,role:e.containerRole,onCompositionstart:t(re),onCompositionupdate:t(ue),onCompositionend:t(ce),onInput:X,onChange:le,onKeydown:pe}),null,16,["id","minlength","maxlength","type","disabled","readonly","autocomplete","tabindex","aria-label","placeholder","form","autofocus","role","onCompositionstart","onCompositionupdate","onCompositionend"]),f(" suffix slot "),t($e)?(d(),b("span",{key:1,class:m(t(n).e("suffix"))},[V("span",{class:m(t(n).e("suffix-inner"))},[!t(A)||!t(U)||!t(P)?(d(),b(_,{key:0},[D(e.$slots,"suffix"),e.suffixIcon?(d(),S(t(B),{key:0,class:m(t(n).e("icon"))},{default:R(()=>[(d(),S(j(e.suffixIcon)))]),_:1},8,["class"])):f("v-if",!0)],64)):f("v-if",!0),t(A)?(d(),S(t(B),{key:1,class:m([t(n).e("icon"),t(n).e("clear")]),onMousedown:it(t(rt),["prevent"]),onClick:fe},{default:R(()=>[st(t(lt))]),_:1},8,["class","onMousedown"])):f("v-if",!0),t(U)?(d(),S(t(B),{key:2,class:m([t(n).e("icon"),t(n).e("password")]),onClick:Me},{default:R(()=>[(d(),S(j(t(Ve))))]),_:1},8,["class"])):f("v-if",!0),t(P)?(d(),b("span",{key:3,class:m(t(n).e("count"))},[V("span",{class:m(t(n).e("count-inner"))},K(t(Y))+" / "+K(e.maxlength),3)],2)):f("v-if",!0),t(N)&&t(ne)&&t(oe)?(d(),S(t(B),{key:4,class:m([t(n).e("icon"),t(n).e("validateIcon"),t(n).is("loading",t(N)==="validating")])},{default:R(()=>[(d(),S(j(t(ne))))]),_:1},8,["class"])):f("v-if",!0)],2)],2)):f("v-if",!0)],2),f(" append slot "),e.$slots.append?(d(),b("div",{key:1,class:m(t(n).be("group","append"))},[D(e.$slots,"append")],2)):f("v-if",!0)],64)):(d(),b(_,{key:1},[f(" textarea "),V("textarea",be({id:t(y),ref_key:"textarea",ref:w,class:[t(te).e("inner"),t(n).is("focus",t(O))]},t(u),{minlength:e.minlength,maxlength:e.maxlength,tabindex:e.tabindex,disabled:t(E),readonly:e.readonly,autocomplete:e.autocomplete,style:t(se),"aria-label":e.ariaLabel,placeholder:e.placeholder,form:e.form,autofocus:e.autofocus,rows:e.rows,role:e.containerRole,onCompositionstart:t(re),onCompositionupdate:t(ue),onCompositionend:t(ce),onInput:X,onFocus:t(ke),onBlur:t(Fe),onChange:le,onKeydown:pe}),null,16,["id","minlength","maxlength","tabindex","disabled","readonly","autocomplete","aria-label","placeholder","form","autofocus","rows","role","onCompositionstart","onCompositionupdate","onCompositionend","onFocus","onBlur"]),t(P)?(d(),b("span",{key:0,style:xe(ae.value),class:m(t(n).e("count"))},K(t(Y))+" / "+K(e.maxlength),7)):f("v-if",!0)],64))],38))}});var Pt=ut(zt,[["__file","input.vue"]]);const Tt=ct(Pt);export{Tt as E,gt as u};
