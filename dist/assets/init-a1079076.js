import{$ as Y,d as A,I as N,r as B,k as E,c as w,o as $,l as Q,a as i,v as c,g as s,y as X,f as q,u as _,s as F,i as l,a0 as j,p as D,j as M,_ as b,Z as S,a1 as P,n as m,F as f,x as k,W as z,z as V,a2 as r}from"./index-db94d997.js";import{g as G}from"./index-96df45ba.js";import{E as K}from"./index-834c2e10.js";const Z="/assets/goBack-1bf09cb2.svg",tt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGeSURBVHgB7ZhLTsMwEIZ/p90WrpANVJyC3oTepOUkwE3KKSzKhhsgyIJVO3jEQ8HxOE7sxFLJJ2UTu+PPcf0YAxMnjrJf6HdaF4QSARwVXq7O1L2vTmy8uR1spnDX1HYz+/oNJMkU8Yp6hdCe2kH7lEnYDnNP3R0d8egphzJDcukZYi7bmy9CUscVbkyMEh5EQZZbnqstIpE68FTRxox8iRYKZOBbbvv7woyEVHd0QVuOgFs64EGqP6qgS2658P+NRhPsI8eMIthXjhlcMEaOGVQwVo75I6hq0115pn4I+pVWoXKd2jUr/5ofRKIrWu0rIn7Ml9y21U/Vbif0B5XPRhQTE/8VXkrMU2JM+JiuA6Y7L8I/y4hOMFOldt05Cfy5hr1D+I7loXJSu2JOIuUnru3rYqF2iMDXbqe9OMXe2pVgwRxyTJBgLjmmVTCnHOMVzC3HyCsEJ9W1vDWHHCN+wXrGn0uOaV9j+YR7MMP91jx0prrdUgWu0VvQBDcBNq6i1LdbLsScJJTUt1u2Q6NvnBdQ4DVc2+3WEPEmTo5PxpENMQiIozQAAAAASUVORK5CYII=",et=Y("companyCode",{state:()=>({companyCode:["67dbc931-294f-4bc4-aea4-148699e97615"]}),actions:{setCompany(t){this.companyCode=t}}}),C=t=>(D("data-v-be34c3f8"),t=t(),M(),t),st={class:"header"},at={style:{position:"absolute",top:"25px",left:"40px",display:"flex","align-items":"center"}},nt=C(()=>s("img",{src:Z,alt:""},null,-1)),ot=C(()=>s("div",null,"返回上一级",-1)),it=[nt,ot],ct=C(()=>s("div",{class:"leftArrow"},null,-1)),rt=C(()=>s("div",{class:"rightArrow"},null,-1)),lt={class:"rightContent"},dt={class:"left"},pt=C(()=>s("div",{class:"slice"},null,-1)),ut={class:"right"},_t={style:{display:"inline-block"}},gt={style:{display:"inline-block"}},yt={style:{"margin-left":"28px",cursor:"pointer"}},ht=["src"],vt=A({__name:"index",props:{title:{},isGoBack:{type:Boolean},goBack:{}},setup(t){const a=t,d=et(),y=N(),o=B(E()),n=B(""),e=B([]),h=w(()=>a.title??""),v=["星期一","星期二","星期三","星期四","星期五","星期六","星期日"],I=w(()=>v[o.value.day()-1]),H=w(()=>o.value.format("YYYY/MM/DD")),L=w(()=>o.value.format("HH:mm:ss"));let T=null;$(()=>{U(),T=setInterval(()=>{o.value=E()},1e3)}),Q(()=>{T&&clearInterval(T)});const R=(p,g)=>{console.log("data=",p),console.log("node=",g);let u=p==null?void 0:p.code;if(u&&u!=="000000"){let O=new Array(u);d.setCompany(O)}},x=(p,g)=>{p.forEach(u=>{u.code===g?e.value=new Array(u):u.children&&u.children.length>0&&x(u.children,g)})},U=async()=>{let g=await G({ownOrOperation:1})||[];x(g,"2df47652-7d34-42aa-80df-76391a55ec6b"),n.value="67dbc931-294f-4bc4-aea4-148699e97615"},J=()=>{a.goBack?a.goBack():y.go(-1)},W=()=>{let p=document.querySelector(".ant-layout-content");j(p)};return(p,g)=>{const u=K;return i(),c("div",st,[s("div",at,[p.isGoBack?(i(),c("div",{key:0,class:"goBack",onClick:J,style:{"margin-right":"12px"}},it)):X("",!0),q(u,{modelValue:_(n),"onUpdate:modelValue":g[0]||(g[0]=O=>F(n)?n.value=O:null),data:_(e),"check-strictly":"","render-after-expand":!1,class:"tree_select_class","popper-class":"custom_popper",size:"default","fit-input-width":!0,placeholder:"请选择",props:{label:"name",value:"code"},onNodeClick:R},null,8,["modelValue","data"])]),ct,s("p",null,l(_(h)),1),rt,s("div",lt,[s("div",dt,l(_(L)),1),pt,s("div",ut,[s("span",_t,l(_(H)),1),s("span",gt,l(_(I)),1)]),s("div",yt,[s("img",{style:{width:"20px",height:"20px"},src:_(tt),onClick:W},null,8,ht)])])])}}});const Jt=b(vt,[["__scopeId","data-v-be34c3f8"]]),mt=t=>(D("data-v-f38065d9"),t=t(),M(),t),Ct={class:"Card_wrap"},wt={class:"name"},ft=mt(()=>s("div",{class:"angle angle-bottom"},null,-1)),kt=A({__name:"index",props:{title:{type:String,default:""},activeBg:{type:String},isHidePadding:{type:Boolean,default:!1}},setup(t){return(a,d)=>(i(),c("div",Ct,[s("div",{class:S(`Card_title ${t.activeBg}`)},[s("p",wt,l(t.title),1)],2),s("div",{class:"Card_content",style:m({padding:t.isHidePadding?"0":"16px"})},[P(a.$slots,"content",{},void 0,!0),ft],4)]))}});const Wt=b(kt,[["__scopeId","data-v-f38065d9"]]),St={class:"BTable_wrap"},At={class:"b_header"},bt={class:"b_columns"},It={key:1},Tt={class:"b_content"},Ot={key:0},Bt={key:0,class:"serial"},Pt={key:1,class:"item"},xt={key:2,class:"item"},Et={key:3,class:"item"},Vt={key:1,class:"item"},$t=A({__name:"index",props:{columns:{},dataList:{},isHideRowSty:{type:Boolean},minHeight:{}},setup(t){const a=t;return(d,y)=>(i(),c("div",St,[s("div",At,[s("div",bt,[(i(!0),c(f,null,k(a.columns,(o,n)=>(i(),c("div",{style:m({width:o.width||"20%"}),key:n},[o.isCustom===!0?P(d.$slots,`${o.key}Title`,{key:0,record:o,index:n,column:o,style:m({width:o.width||"20%"})},void 0,!0):(i(),c("div",It,l(o.name),1))],4))),128))])]),s("div",Tt,[(i(!0),c(f,null,k(a.dataList,(o,n)=>(i(),c("div",{class:S(d.isHideRowSty?"b_item active":"b_item"),style:m({"min-height":a.minHeight?a.minHeight:"32px"}),key:n},[(i(!0),c(f,null,k(a.columns,(e,h)=>(i(),c("div",{key:h,style:m({width:e.width||"20%"})},[e.render?(i(),c("div",Vt,[P(d.$slots,`${e.key}Render`,{record:o,index:n,column:e},void 0,!0)])):(i(),c("div",Ot,[e.isSort?(i(),c("div",Bt,[s("div",{class:S(`active${n}`)},[s("span",null,l(n<9?"0"+(n+1):n+1),1)],2)])):e.formatPercentage?(i(),c("div",Pt,l(_(z)(o[e.key],e.formatPercentage))+l(e.unit?e.unit:""),1)):e.formatMoney?(i(),c("div",xt,l(typeof e.formatMoney=="number"?_(V)(o[e.key],e.formatMoney):_(V)(o[e.key]))+l(e.unit?e.unit:""),1)):(i(),c("div",Et,l(o[e.key])+l(e.unit?e.unit:""),1))]))],4))),128))],6))),128))])]))}});const Yt=b($t,[["__scopeId","data-v-1b7769ef"]]),Nt=t=>r({url:"/web/cockpitInstallCapacity/v1/capacityCompare",method:"POST",data:t}),Qt=t=>r({url:"/web/cockpitInstallCapacity/v1/capacityCompareCompany",method:"POST",data:t}),Xt=t=>r({url:"/web/cockpitInstallCapacity/v1/powerPlanCompletedList",method:"POST",data:t}),qt=t=>r({url:"/web/cockpitInstallCapacity/v1/powerPlanCompletedCompanyList",method:"POST",data:t}),Ft=t=>r({url:"/web/cockpitInstallCapacity/v1/energySavingAnalysisList",method:"POST",data:t}),jt=t=>r({url:"/web/cockpitInstallCapacity/v1/cockpitCompareAnalysisList",method:"POST",data:t}),zt=t=>r({url:"/web/cockpitInstallCapacity/v1/cockpitOtherList",method:"POST",data:t}),Gt=t=>r({url:"/web/cockpitInstallCapacity/v1/workTicket",method:"POST",data:t}),Kt=t=>r({url:"/web/cockpitInstallCapacity/v1/cockpitUnderLevelOtherList",method:"POST",data:t}),Zt=t=>r({url:"/web/cockpitEconomicTargetDataConfig/v1/list",method:"POST",data:t}),te=t=>r({url:"/web/cockpitInstallCapacity/v1/listCityStationDetail",method:"POST",data:t}),ee=t=>r({url:"/web/cockpitInstallCapacity/v1/getStationOneDayEnergytData",method:"POST",data:t}),se=t=>r({url:"/web/cockpitInstallCapacity/v1/getStationDateScopeEnergy",method:"POST",data:t}),ae=t=>r({url:"/web/cockpitInstallCapacity/v1/getStationHealth",data:t}),ne=t=>r({url:"/web/cockpitInstallCapacity/v1/stationGeneralSituation",data:t}),oe=t=>r({url:"/web/cockpitInstallCapacity/v1/getSocialContribution",data:t}),ie=t=>r({url:"/web/cockpitInstallCapacity/v1/getStationEnergyStreamData",data:t}),ce=t=>r({url:"/web/cockpitInstallCapacity/v1/getStationDailyRealTimePower",data:t});function re(t){return r({url:"/web/workorder/v1/pages",method:"POST",data:t})}function le(t){return r({url:"/web/station/v1/pages",method:"POST",data:t})}const Dt={class:"tabWrap"},Mt=["onClick"],Ht=A({__name:"index",props:{tabList:{type:Array,default:()=>[]},tabValue:{type:Number}},emits:["tabChange"],setup(t,{emit:a}){const d=y=>{a("tabChange",y.value)};return $(()=>{}),(y,o)=>(i(),c("div",Dt,[(i(!0),c(f,null,k(t.tabList,(n,e)=>(i(),c("div",{key:e,class:S(t.tabValue===n.value?"serial active":"serial"),onClick:()=>d(n)},[s("div",null,l(n==null?void 0:n.label),1)],10,Mt))),128))]))}});const de=b(Ht,[["__scopeId","data-v-a5706a76"]]),pe="/assets/contentArrowR-6ed8aa17.svg",ue="/assets/cardTitleIcon-06c04167.svg",_e=(t,a=document.querySelector("#bigscreen-page-container"),d=1920,y=0)=>{const o=window.screen.width,n=window.screen.height;if(console.log(a==null?void 0:a.clientWidth),o/n>100){const e="width: 100%;height: 100%;overflow: auto";return t==null||t.setAttribute("style",e),t==null||t.setAttribute("hairtail-screen",!0),()=>{}}else{const e="height: 1080pxwidth: "+d+"px;margin: 0;overflow: hidden;transform-origin: 0 0;transition: transform 0.3s;";t==null||t.setAttribute("style",e),t==null||t.removeAttribute("hairtail-screen");let h;return()=>{h&&clearTimeout(h),h=setTimeout(()=>{console.log(a==null?void 0:a.clientWidth);let v;v=a&&a.clientWidth/d;const I="scale("+v+")";t.style.transform=I,a.style.height=t.offsetHeight*v+"px"},y)}}};export{Yt as B,Jt as H,ue as _,Qt as a,Wt as b,Xt as c,qt as d,de as e,Gt as f,Nt as g,jt as h,Ft as i,Zt as j,pe as k,Kt as l,te as m,zt as n,ne as o,le as p,oe as q,_e as r,ae as s,ce as t,et as u,se as v,ee as w,ie as x,re as y};
