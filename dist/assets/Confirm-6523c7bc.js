import{_ as F}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as j,r as m,I as O,o as V,D as Y,at as A,a as h,v as u,f as r,e as n,b as G,h as y,y as H,i as w,g,u as J,q as Q,bi as W,bc as $,bg as U,bn as X,p as Z,j as v,_ as ee}from"./index-db94d997.js";import{a as te,c as oe,b as ae}from"./index-e683f274.js";import{E as ne}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as ce}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const S=p=>(Z("data-v-70d5ecfe"),p=p(),v(),p),se={class:"areaPrice"},re={class:"text-hide"},ie={key:0,class:"status_tag"},de=S(()=>g("span",{class:"tag_one"},"已确认",-1)),le=[de],me={key:1,class:"status_tag"},pe=S(()=>g("span",{class:"tag_two"},"待确认",-1)),_e=[pe],fe=j({__name:"Confirm",setup(p){const _=m(),I=["provinceCodeList","cityCodeList","areaCodeList"],D=m([]),x=m([]),f=m(!1),T=O(),K=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:120,valueEnum:x,order:1,render:!0,fixed:"left"},{title:"账单周期",dataIndex:"monthKey",valueType:"dateRange",width:120,dateFormat:"YYYY-MM",order:2},{title:"实际发电量(kWh)",dataIndex:"eq",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"应收电费(元)",dataIndex:"fee",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"状态",key:"confirmStatus",dataIndex:"confirmStatus",search:!1,resizable:!0,width:100,render:!0,align:"center",fixed:"right"},{title:"操作",key:"action",dataIndex:"action",search:!1,width:100,render:!0,align:"center",fixed:"right"}];V(()=>{E()}),Y(()=>{_.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&b()});const E=()=>{A({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(a=>({label:a.companyName,value:a.companyCode}));x.value=o})},L=(e,t)=>{T.push({path:"/financeManage/feeManage/receivableFee/confirm/detail",state:{pdata:Q.cloneDeep(e)}})},M=async(e,t)=>{W.confirm({title:"确认提示",icon:r(ne),content:"点击确认后将不可修改，请慎重",okText:"确定",cancelText:"取消",centered:!0,onCancel:R,onOk:()=>N(e)})},N=e=>{if(f.value)return;console.log("确认",e);let t={companyCode:e.companyCode,monthKey:e.monthKey};f.value=!0,oe(t).then(o=>{f.value=!1,$.info("确认成功"),b()}).catch(o=>{f.value=!1})},R=()=>{console.log("取消")},P=()=>{var t;let e=(t=_.value)==null?void 0:t.getInitialFormStateNew();ae(e)},C=m([]),q=e=>{C.value=e||[],console.log("dataSource=",e)},b=()=>{var e;(e=_.value)==null||e.reload()},z=e=>{const t=new Map;return e.forEach(o=>{const a=I[o.level-1];if(t.has(a))t.get(a).push(o.value);else{let s=[];s.push(o.value),t.set(a,s)}}),Object.fromEntries(t)},k=(e,t,o)=>!e||!t?[]:(e.forEach(a=>{t.find(d=>a.value===d)&&o.push(a),a.children&&a.children.length>0&&k(a.children,t,o)}),o),B=(e,t)=>new Promise(o=>{const a=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[0]:"",s=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[1]:"";let d={delStatus:0,noJoin:!0,startTime:a,endTime:s};const l=k(D.value,e==null?void 0:e.cityTree,[]);let c=z(l);e==null||delete e.monthKey;const i={...d,...e,...c};o(i)});return(e,t)=>{const o=U,a=ce,s=X,d=F;return h(),u("div",se,[r(d,{columns:K,ref_key:"actionRef",ref:_,request:J(te),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:q,"before-query-params":B},{tableHeader:n(()=>[r(a,null,{default:n(()=>[C.value.length>0?(h(),G(o,{key:0,type:"primary",onClick:P},{default:n(()=>[y("导出")]),_:1})):H("",!0)]),_:1})]),companyCodeListRender:n(({column:l,record:c,index:i})=>[r(s,null,{title:n(()=>[y(w(c.companyName),1)]),default:n(()=>[g("span",re,w(c.companyName),1)]),_:2},1024)]),confirmStatusRender:n(({column:l,record:c,index:i})=>[c[l.dataIndex]===1?(h(),u("span",ie,le)):(h(),u("span",me,_e))]),actionRender:n(({column:l,record:c,index:i})=>[r(a,null,{default:n(()=>[r(o,{size:"small",type:"link",onClick:()=>M(c,i),disabled:c.confirmStatus===1},{default:n(()=>[y(" 确认 ")]),_:2},1032,["onClick","disabled"]),r(o,{size:"small",type:"link",onClick:()=>L(c,i)},{default:n(()=>[y(" 查看明细 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const Ie=ee(fe,[["__scopeId","data-v-70d5ecfe"]]);export{Ie as default};
