import{_ as N}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as R,k as F,r as h,o as P,D as B,at as O,a as p,v as x,f as c,e as r,b as Q,h as m,y as V,i as _,g as f,u as A,bi as G,bc as H,bg as $,bn as U,p as X,j as Z,_ as v}from"./index-db94d997.js";import{i as ee,j as te,k as ae}from"./index-2196a4be.js";import{E as ne}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as re}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const J=W=>(X("data-v-e2c031a8"),W=W(),Z(),W),ie={class:"areaPrice"},oe={class:"text-hide"},de={class:"text-hide"},le={key:0,class:"status_tag"},ce=J(()=>f("span",{class:"tag_one"},"已确认",-1)),se=[ce],he={key:1,class:"status_tag"},ue=J(()=>f("span",{class:"tag_two"},"待确认",-1)),We=[ue],ye=R({__name:"MonthElectQuery",setup(W){const s=F().format("YYYY");h(!1);const l=h(),I=["provinceCodeList","cityCodeList","areaCodeList"],z=h([]),b=h([]),y=h(!1),j=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:3,width:120,valueEnum:b,order:1,render:!0,fixed:"left"},{title:"时间周期",key:"yKey",dataIndex:"yKey",valueType:"dateRange",width:120,dateFormat:"YYYY",search:!0,allowClear:!1,order:2,fixed:"left"},{title:"年度基准电量(万kWh)",dataIndex:"yearEq",search:!1,resizable:!0,kWtoWanKW:!0,width:180},{title:"年度JYKJ电量(万kWh)",dataIndex:"yearJykj",search:!1,resizable:!0,kWtoWanKW:!0,width:180},{title:"1月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"januaryEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"januaryJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"2月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"februaryEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"februaryJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"3月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"marchEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"marchJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"4月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"aprilEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"aprilJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"5月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"mayEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"mayJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"6月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"juneEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"juneJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"7月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"julyEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"julyJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"8月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"augustEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"auguJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"9月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"septemberEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"septemberJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"10月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"octoberEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"octoberJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"11月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"novemberEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"novemberJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"12月",align:"center",width:120,children:[{title:"月度基准电量(万kWh)",dataIndex:"decemberEq",resizable:!0,kWtoWanKW:!0,width:120},{title:"月度JYKJ电量(万kWh)",dataIndex:"decemberJykj",resizable:!0,kWtoWanKW:!0,width:120}]},{title:"状态",key:"confirmStatus",dataIndex:"confirmStatus",search:!1,resizable:!0,width:100,render:!0,align:"center",fixed:"right"},{title:"操作",key:"action",dataIndex:"action",search:!1,width:100,render:!0,align:"center",fixed:"right"}];P(()=>{C(),l.value.setFormParams({yKey:[s,s]},!1)}),B(()=>{let e=l.value.getInitialFormStateNew();Object.prototype.hasOwnProperty.call(e,"delStatus")&&K()});const C=()=>{O({}).then(e=>{console.log("产权公司res=",e);let n=(e||[]).map(a=>({label:a.companyName,value:a.companyCode}));b.value=n})},E=async(e,t)=>{G.confirm({title:"确认提示",icon:c(ne),content:"点击确认后将不可修改，请慎重",okText:"确定",cancelText:"取消",centered:!0,onCancel:S,onOk:()=>Y(e)})},Y=e=>{if(y.value)return;console.log("确认",e);let t={...e};y.value=!0,te(t).then(n=>{y.value=!1,H.info("确认成功"),K()}).catch(n=>{y.value=!1})},S=()=>{console.log("取消")},q=()=>{var t;let e=(t=l.value)==null?void 0:t.getInitialFormStateNew();ae(e)},w=h([]),L=e=>{w.value=e||[],console.log("dataSource=",e)},K=()=>{var e;(e=l.value)==null||e.reload()},T=e=>{const t=new Map;return e.forEach(n=>{const a=I[n.level-1];if(t.has(a))t.get(a).push(n.value);else{let o=[];o.push(n.value),t.set(a,o)}}),Object.fromEntries(t)},g=(e,t,n)=>!e||!t?[]:(e.forEach(a=>{t.find(u=>a.value===u)&&n.push(a),a.children&&a.children.length>0&&g(a.children,t,n)}),n),D=()=>{var e,t;(e=l.value)==null||e.setFormParams({yKey:[s,s],companyCodeList:[],companyCodeList_indeterminate:!1,companyCodeList_checked:!1}),(t=l.value)==null||t.reload()},M=e=>new Promise(t=>{const n=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[0]:s,a=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[1]:s;let o={delStatus:0,noJoin:!0,startTime:n,endTime:a};const u=g(z.value,e==null?void 0:e.cityTree,[]);let d=T(u);const i={...o,...e,...d};t(i)});return(e,t)=>{const n=$,a=re,o=U,u=N;return p(),x("div",ie,[c(u,{columns:j,ref_key:"actionRef",ref:l,request:A(ee),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:L,"on-cancel":D,"before-query-params":M},{tableHeader:r(()=>[c(a,null,{default:r(()=>[w.value.length>0?(p(),Q(n,{key:0,type:"primary",onClick:q},{default:r(()=>[m("导出")]),_:1})):V("",!0)]),_:1})]),companyCodeListRender:r(({column:d,record:i,index:k})=>[c(o,null,{title:r(()=>[m(_(i.companyName),1)]),default:r(()=>[f("span",oe,_(i.companyName),1)]),_:2},1024)]),yKeyRender:r(({column:d,record:i,index:k})=>[c(o,null,{title:r(()=>[m(_(i[d.dataIndex]),1)]),default:r(()=>[f("span",de,_(i[d.dataIndex]),1)]),_:2},1024)]),confirmStatusRender:r(({column:d,record:i,index:k})=>[i[d.dataIndex]===1?(p(),x("span",le,se)):(p(),x("span",he,We))]),actionRender:r(({column:d,record:i,index:k})=>[c(a,null,{default:r(()=>[c(n,{size:"small",type:"link",onClick:()=>E(i,k),disabled:i.confirmStatus===1},{default:r(()=>[m(" 确认 ")]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1},8,["request"])])}}});const Je=v(ye,[["__scopeId","data-v-e2c031a8"]]);export{Je as default};
