import{_ as se}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as S,d as ie,r as d,o as de,w as M,a as c,v as C,g as R,f as r,e as o,u as l,b as _,h as y,y as q,s as pe,F as ue,au as ce,k as L,bc as x,bd as me,be as fe,bf as _e,aw as ye,av as ve,bg as he,ax as be,bh as Te,bi as ge,p as ke,j as we,_ as Ce}from"./index-db94d997.js";import{g as xe}from"./index-96df45ba.js";import{D as Re}from"./dayjs-a8e42122.js";import{_ as Se}from"./index-83ca18bc.js";import{E as Ee}from"./index-834c2e10.js";import{_ as Ye}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";import"./index-7c60ebfa.js";import"./index-326d414f.js";import"./index-ec316fb4.js";import"./icon-831229e8.js";import"./index-4481a9dc.js";function Ie(p){return S({url:"/web/companyElectricityReport/v1/page",method:"POST",isTable:!0,data:p})}function Ne(p){return S({url:"/web/companyElectricityReport/v1/save",method:"POST",data:p})}function De(p){return S({url:"/web/companyElectricityReport/v1/delete",method:"GET",data:p})}function qe(p){return S({url:"/web/companyElectricityReport/v1/update",method:"GET",data:p})}const Le=p=>(ke("data-v-987bbf45"),p=p(),we(),p),Ue={class:"card"},Fe={style:{display:"flex","flex-direction":"row",border:"solid 1px #d9d9d9","border-radius":"4px"}},Ve=Le(()=>R("div",{class:"pre_title"},"电量报表",-1)),Be={key:0},Oe={key:1,style:{color:"#29cca0"}},Pe={key:2,style:{color:"#f44"}},Me=ie({__name:"index",setup(p){const E=d(),h=d(!1),i=d({reportName:"",reportTimeType:1,year:"",month:""}),T=d(!1),u=d({companyCode:[],provinceCode:[],reportTimeType:1,reportTime:""}),U=d([]),Y=d([{label:"月度",value:1},{label:"年度",value:2}]),$=d([]),z=()=>{ce({pid:0}).then(t=>{if(t&&t.length>0){let e=t.map(n=>({label:n.name,value:n.code}));$.value=e}})},F=t=>{const e=t.year(),n=t.month(),s=L().year(),b=L().month();let m=!1;return e>s||e===s&&n>b?m=!0:m=!1,m},V=t=>{const e=t.year(),n=L().year();let s=!1;return e>n?s=!0:s=!1,s},j=d(""),A=()=>{i.value={reportName:"",reportTimeType:1,reportTime:""},g.value.getTableList()},G=()=>{g.value.getTableList()},B=()=>{h.value=!1,E.value.resetFields()},H=()=>{E.value.validate().then(t=>{var n;((n=t==null?void 0:t.companyCode)==null?void 0:n.length)>0&&(t.companyCode=t.companyCode.join(","));let e={...t};T.value=!0,Ne(e).then(s=>{x.info("保存成功"),T.value=!1,B(),I()}).catch(s=>{T.value=!1})})},Q=()=>{h.value=!0},J=(t,e)=>new Promise(n=>{let s={reportName:i.value.reportName,reportTime:i.value.reportTime};s={...s,...t,delStatus:0},n(s)}),K=d([{title:"报表名称",dataIndex:"reportName",width:140},{title:"时间维度",dataIndex:"reportTimeType",valueType:"select",valueEnum:Y,hideInTable:!0,width:140},{title:"选择时间",dataIndex:"reportTime",valueType:"date",hideInTable:!0,width:140},{title:"创建人",dataIndex:"createBy",search:!1,width:100},{title:"创建时间",dataIndex:"createTime",search:!1,width:100},{title:"报表状态",dataIndex:"reportStatus",key:"reportStatus",search:!1,render:!0,width:100},{title:"操作",key:"action",width:100,fixed:"right",resizable:!1,render:!0}]),g=d(),W=()=>{I()},I=()=>{var t;(t=g.value)==null||t.reload()},k=d(!1),X=t=>{const e={id:t.id};k.value=!0,qe(e).then(n=>{console.log("res=",n),k.value=!1,x.info("")}).catch(n=>{k.value=!1})},Z=t=>{if(t.reportStatus===0||t.reportStatus===1||t.reportStatus===3){x.warning("文件未生成");return}const e={fileId:t.reportFileId};me(e)},N=d(!1),ee=t=>{const e={id:t.id};N.value=!0,De(e).then(n=>{N.value=!1,x.success("删除成功"),I()}).catch(n=>{N.value=!1})};de(()=>{te(),z()}),M(()=>i.value.reportTimeType,t=>{i.value.reportTime=""}),M(()=>u.value.reportTimeType,t=>{u.value.reportTime=""});const te=async()=>{let e=await xe({ownOrOperation:1})||[];O(e,"2df47652-7d34-42aa-80df-76391a55ec6b")},O=(t,e)=>{t.forEach(n=>{n.code===e?U.value=new Array(n):n.children&&n.children.length>0&&O(n.children,e)})};return(t,e)=>{const n=fe,s=_e,b=ye,m=ve,w=Re,f=he,D=Ye,ae=be,P=Te,oe=Se,le=se,re=Ee,ne=ge;return c(),C(ue,null,[R("div",Ue,[r(P,{ref_key:"fRef",ref:j,model:l(i),"label-col":{style:{width:"80px",marginRight:"5px"}},autocomplete:"off"},{default:o(()=>[r(ae,{span:24},{default:o(()=>[r(b,{span:8},{default:o(()=>[r(s,{name:"reportName",label:"报表名称"},{default:o(()=>[r(n,{value:l(i).reportName,"onUpdate:value":e[0]||(e[0]=a=>l(i).reportName=a),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),r(b,{span:8},{default:o(()=>[r(s,{label:"时间",name:"reportTime",style:{width:"100%"}},{default:o(()=>[R("div",Fe,[r(m,{options:l(Y),value:l(i).reportTimeType,"onUpdate:value":e[1]||(e[1]=a=>l(i).reportTimeType=a),placeholder:"请选择",showArrow:"",bordered:!1,style:{width:"20%","border-right":"solid 1px #d9d9d9"}},null,8,["options","value"]),l(i).reportTimeType===1?(c(),_(w,{key:0,value:l(i).reportTime,"onUpdate:value":e[2]||(e[2]=a=>l(i).reportTime=a),picker:"month","value-format":"YYYY-MM",placeholder:"请选择",bordered:!1,style:{width:"80%"},"disabled-date":F},null,8,["value"])):(c(),_(w,{key:1,value:l(i).reportTime,"onUpdate:value":e[3]||(e[3]=a=>l(i).reportTime=a),picker:"year","value-format":"YYYY",placeholder:"请选择",bordered:!1,style:{width:"80%"},"disabled-date":V},null,8,["value"]))])]),_:1})]),_:1}),r(b,{span:8,style:{display:"flex","justify-content":"flex-end"}},{default:o(()=>[r(s,null,{default:o(()=>[r(D,null,{default:o(()=>[r(f,{onClick:A},{default:o(()=>[y("重置")]),_:1}),r(f,{type:"primary",onClick:G},{default:o(()=>[y("查询")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),r(le,{columns:l(K),ref_key:"actionRef",ref:g,"wrapper-col":{span:18},"before-query-params":J,request:l(Ie),search:!1},{tableHeaderLeft:o(()=>[Ve]),tableHeader:o(()=>[R("div",null,[r(D,null,{default:o(()=>[r(f,{onClick:W},{default:o(()=>[y("刷新列表")]),_:1}),r(f,{type:"primary",onClick:Q},{default:o(()=>[y("生成报表")]),_:1})]),_:1})])]),reportStatusRender:o(({record:a})=>[a.reportStatus==0||a.reportStatus==1?(c(),C("div",Be,"生成中")):q("",!0),a.reportStatus==2?(c(),C("div",Oe,"生成成功")):q("",!0),a.reportStatus==3?(c(),C("div",Pe,"生成失败")):q("",!0)]),actionRender:o(({record:a})=>[r(D,null,{default:o(()=>[a.reportStatus==3?(c(),_(f,{key:0,size:"small",type:"link",onClick:v=>X(a),loading:l(k)},{default:o(()=>[y(" 重新生成 ")]),_:2},1032,["onClick","loading"])):(c(),_(f,{key:1,size:"small",type:"link",disabled:a.reportStatus!=2,onClick:v=>Z(a)},{default:o(()=>[y(" 下载 ")]),_:2},1032,["disabled","onClick"])),a.reportStatus===2||a.reportStatus===3?(c(),_(oe,{key:2,title:"确认删除?",onConfirm:v=>ee(a)},{default:o(()=>[r(f,{size:"small",type:"link"},{default:o(()=>[y(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])):(c(),_(f,{key:3,size:"small",type:"link",disabled:""},{default:o(()=>[y(" 删除 ")]),_:1}))]),_:2},1024)]),_:1},8,["columns","request"]),r(ne,{visible:l(h),"onUpdate:visible":e[8]||(e[8]=a=>pe(h)?h.value=a:null),title:"生成报表","confirm-loading":l(T),onOk:H,onCancel:B},{default:o(()=>[r(P,{ref_key:"formRef",ref:E,model:l(u),"label-col":{style:{width:"80px",marginRight:"5px"}},autocomplete:"off"},{default:o(()=>[r(s,{label:"项目公司",name:"companyCode",rules:[{required:!0,message:"请选择"}]},{default:o(()=>[r(re,{modelValue:l(u).companyCode,"onUpdate:modelValue":e[4]||(e[4]=a=>l(u).companyCode=a),data:l(U),"check-strictly":"","render-after-expand":!1,multiple:"","default-expand-all":"","collapse-tags":"","fit-input-width":!0,placeholder:"请选择",props:{label:"name",value:"code"}},null,8,["modelValue","data"])]),_:1}),r(s,{label:"时间维度",name:"reportTimeType",rules:[{required:!0,message:"请选择"}]},{default:o(()=>[r(m,{options:l(Y),value:l(u).reportTimeType,"onUpdate:value":e[5]||(e[5]=a=>l(u).reportTimeType=a),placeholder:"请选择",showArrow:"","show-search":"","filter-option":(a,v)=>((v==null?void 0:v.label)??"").toLowerCase().includes(a.toLowerCase()),style:{width:"100%"}},null,8,["options","value","filter-option"])]),_:1}),l(u).reportTimeType===1?(c(),_(s,{key:0,label:"时间",name:"reportTime",rules:[{required:!0,message:"请选择"}]},{default:o(()=>[r(w,{value:l(u).reportTime,"onUpdate:value":e[6]||(e[6]=a=>l(u).reportTime=a),picker:"month","value-format":"YYYY-MM",placeholder:"请选择",style:{width:"100%"},"disabled-date":F},null,8,["value"])]),_:1})):(c(),_(s,{key:1,label:"时间",name:"reportTime",rules:[{required:!0,message:"请选择"}]},{default:o(()=>[r(w,{value:l(u).reportTime,"onUpdate:value":e[7]||(e[7]=a=>l(u).reportTime=a),picker:"year","value-format":"YYYY",placeholder:"请选择",style:{width:"100%"},"disabled-date":V},null,8,["value"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const lt=Ce(Me,[["__scopeId","data-v-987bbf45"]]);export{lt as default};
