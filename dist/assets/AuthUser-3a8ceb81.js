import{d as ce,r as f,K as ie,I as pe,c as _e,o as de,a as i,v as k,g as v,f as l,e,u as s,h as I,b,F as S,x as B,i as d,s as me,bi as re,H as fe,q as W,ey as ve,be as ye,bf as ge,aw as he,ax as ke,bg as be,bh as Ce,bn as xe,ay as Ne,bM as Ie,bN as Ue,_ as we}from"./index-db94d997.js";import{e as Se}from"./index-4185a861.js";import{E as Pe}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as $e}from"./index-39334618.js";const Re={class:"card top"},ze={style:{width:"100%",display:"flex","flex-direction":"row","align-items":"center","justify-content":"flex-end"}},De={class:"table_container"},Le=["onClick"],Be=["onClick"],Ae=["onClick"],Ke=["onClick"],Fe={class:"text-hide"},Te={class:"table_container"},Ee=["onClick"],Me=["onClick"],Ve=["onClick"],je=["onClick"],Oe={class:"text-hide"},qe={class:"card footer"},He=ce({__name:"AuthUser",setup(Je){const A=f(1),E=f(),U=f(),M=f([{title:"用户编码",dataIndex:"id",width:120},{title:"用户姓名",dataIndex:"userName"},{title:"手机号",dataIndex:"mobile"},{title:"归属公司",dataIndex:"companyName",ellipsis:!0,width:200},{title:"创建人",dataIndex:"createUser"},{title:"最近修改时间",dataIndex:"updateTime"}]),m=ie({userName:"",mobile:"",companyName:"",createUser:"",roleName:""}),V=f([]),C=f(1),w=f(10),K=f(0),P=f(!1),$=f(!1),j=f(history.state.pdata||{}),F=pe(),y=f([]),g=f([]),X=(t,n,c,p)=>{n?(g.value.push(t.id),y.value.push(t)):(g.value=g.value.filter(o=>o!==t.id),y.value=y.value.filter(o=>o.id!==t.id))},Y=(t,n,c)=>{const p=c.map(o=>o.id);t?(g.value=g.value.concat(p),y.value=y.value.concat(c)):(g.value=g.value.filter(o=>!p.includes(o)),y.value=y.value.filter(o=>!p.includes(o.id)))},O=_e(()=>({selectedRowKeys:g,onSelect:X,onSelectAll:Y})),q=()=>{F.go(-1)},Z=()=>{re.confirm({title:"确认提示",icon:l(Pe),content:"确认要授权吗？",onOk:ee,onCancel(){console.log("Cancel")}})},ee=()=>{const t=g.value;let n={companyInfoId:j.value.id,userIds:fe(t)};$.value=!0,Se(n).then(c=>{console.log("授权用户res=",c),$.value=!1,q()}).catch(c=>{$.value=!1})},R=t=>{F.push({path:"/system/user/detail",state:{pdata:W.cloneDeep(t)}})},te=(t,n,c,{currentDataSource:p})=>{console.log("tableChange"),C.value=t==null?void 0:t.current,w.value=t==null?void 0:t.pageSize,K.value=t==null?void 0:t.size;let o={pageNum:C.value,pageSize:w.value,...m};z(o)},ae=()=>{E.value.resetFields();let t={pageNum:C.value,pageSize:w.value,...m};z(t)},ne=t=>{console.log("values=",t);let n={pageNum:C.value,pageSize:w.value,...t};z(n)},z=t=>{P.value=!0,t.filterCompanyId=j.value.id,ve(t).then(n=>{if(console.log("用户列表res=",n),P.value=!1,n){const{total:c,records:p}=n;K.value=c||0,V.value=p||[]}}).catch(n=>{P.value=!1})},le=(t={})=>{let n={pageNum:C.value,pageSize:w.value,...t};z(n)},x=t=>{const n=t||[];let c=[];for(let p of n){const o={companyName:p.companyName,id:p.companyInfoId};c.push(o)}return c},D=t=>{F.push({path:"/system/company/detail/list",state:{pdata:W.cloneDeep(t)}})};return de(()=>{le()}),(t,n)=>{const c=ye,p=ge,o=he,H=ke,L=be,J=$e,se=Ce,N=xe,Q=Ne,G=Ie,oe=Ue;return i(),k(S,null,[v("div",Re,[l(se,{model:s(m),ref_key:"formRef",ref:E,name:"basic","label-col":{style:{width:"100px"}},autocomplete:"off",onFinish:ne},{default:e(()=>[l(H,{span:24},{default:e(()=>[l(o,{span:8},{default:e(()=>[l(p,{label:"用户姓名",name:"userName"},{default:e(()=>[l(c,{value:s(m).userName,"onUpdate:value":n[0]||(n[0]=_=>s(m).userName=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[l(p,{label:"手机号",name:"mobile"},{default:e(()=>[l(c,{value:s(m).mobile,"onUpdate:value":n[1]||(n[1]=_=>s(m).mobile=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[l(p,{label:"归属公司",name:"companyName"},{default:e(()=>[l(c,{value:s(m).companyName,"onUpdate:value":n[2]||(n[2]=_=>s(m).companyName=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1})]),_:1}),l(H,{span:24},{default:e(()=>[l(o,{span:8},{default:e(()=>[l(p,{label:"创建人",name:"createUser"},{default:e(()=>[l(c,{value:s(m).createUser,"onUpdate:value":n[3]||(n[3]=_=>s(m).createUser=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[l(p,{label:"角色名称",name:"roleName"},{default:e(()=>[l(c,{value:s(m).roleName,"onUpdate:value":n[4]||(n[4]=_=>s(m).roleName=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[v("div",ze,[l(J,null,{default:e(()=>[l(L,{onClick:ae},{default:e(()=>[I("重置")]),_:1}),l(L,{type:"primary","html-type":"submit"},{default:e(()=>[I("查询")]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1},8,["model"])]),v("div",{class:"card content",ref_key:"companyDetailRef",ref:U},[l(oe,{activeKey:s(A),"onUpdate:activeKey":n[5]||(n[5]=_=>me(A)?A.value=_:null),tabBarStyle:{height:"60px",paddingLeft:"24px"}},{default:e(()=>[(i(),b(G,{key:1,tab:"用户列表"},{default:e(()=>[v("div",De,[l(Q,{columns:s(M),"data-source":s(V),loading:s(P),"row-key":"id",onChange:te,rowSelection:s(O),pagination:{current:s(C),total:s(K),showTotal:_=>`共 ${_} 条`,size:"small",showQuickJumper:!0}},{bodyCell:e(({text:_,record:a,index:ue,column:r})=>[r.dataIndex==="companyName"?(i(),b(N,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:u=>s(U)},{title:e(()=>[(i(!0),k(S,null,B(x(a==null?void 0:a.companyInfoUser),(u,h)=>(i(),k("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName),9,Le))),128))]),default:e(()=>[(i(!0),k(S,null,B(x(a==null?void 0:a.companyInfoUser),(u,h)=>(i(),k("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName)+" "+d(x(a==null?void 0:a.companyInfoUser).length-1==h?"":"，"),9,Be))),128))]),_:2},1032,["getPopupContainer"])):r.dataIndex==="userName"?(i(),b(N,{key:1,placement:"topLeft",getPopupContainer:u=>s(U)},{title:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},d(a[r.dataIndex]),9,Ae)]),default:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},d(a[r.dataIndex]),9,Ke)]),_:2},1032,["getPopupContainer"])):(i(),b(N,{key:2},{title:e(()=>[I(d(a[r.dataIndex]),1)]),default:e(()=>[v("span",Fe,d(a[r.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","loading","rowSelection","pagination"])])]),_:1})),(i(),b(G,{key:2,tab:`已选列表(${s(y).length})`},{default:e(()=>[v("div",Te,[l(Q,{"row-selection":s(O),columns:s(M),"row-key":"id","data-source":s(y)},{bodyCell:e(({text:_,record:a,index:ue,column:r})=>[r.dataIndex==="companyName"?(i(),b(N,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:u=>s(U)},{title:e(()=>[(i(!0),k(S,null,B(x(a==null?void 0:a.companyInfoUser),(u,h)=>(i(),k("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName),9,Ee))),128))]),default:e(()=>[(i(!0),k(S,null,B(x(a==null?void 0:a.companyInfoUser),(u,h)=>(i(),k("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName)+" "+d(x(a==null?void 0:a.companyInfoUser).length-1==h?"":"，"),9,Me))),128))]),_:2},1032,["getPopupContainer"])):r.dataIndex==="userName"?(i(),b(N,{key:1,placement:"topLeft",getPopupContainer:u=>s(U)},{title:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},d(a[r.dataIndex]),9,Ve)]),default:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},d(a[r.dataIndex]),9,je)]),_:2},1032,["getPopupContainer"])):(i(),b(N,{key:2},{title:e(()=>[I(d(a[r.dataIndex]),1)]),default:e(()=>[v("span",Oe,d(a[r.dataIndex]),1)]),_:2},1024))]),_:1},8,["row-selection","columns","data-source"])])]),_:1},8,["tab"]))]),_:1},8,["activeKey"])],512),v("div",qe,[l(J,null,{default:e(()=>[l(L,{onClick:q},{default:e(()=>[I("取消")]),_:1}),l(L,{type:"primary",onClick:Z,loading:s($)},{default:e(()=>[I("确认关联")]),_:1},8,["loading"])]),_:1})])],64)}}});const Ye=we(He,[["__scopeId","data-v-5d033112"]]);export{Ye as default};
