import{aa as gt,ao as mt,d as Ue,dE as Ct,dF as bt,r as Ne,dd as St,c as E,w as Ve,V as It,cw as J,H as Z,d7 as se,f as q,dG as xt,bs as s,bF as wt,cJ as Se,dH as kt,di as Tt,bZ as Le,dI as Et,dJ as Be,d6 as ne,bo as ve,bC as at,dK as Nt,b4 as ae,db as Je,dL as Vt,t as Lt,dM as Dt,dN as qe,du as De,dO as Kt,bt as fe,dP as Pt,bx as Ft,dv as Ht,dQ as At,c2 as Qe,dq as Mt,bu as _t,bD as Ye,dR as Ot,bz as Rt,dS as $t}from"./index-db94d997.js";function Bt(a){return Array.isArray(a)?a:a!==void 0?[a]:[]}function jt(a){var e=a||{},u=e.label,c=e.value,i=e.children,r=c||"value";return{_title:u?[u]:["title","label"],value:r,key:r,children:i||"children"}}function je(a){return a.disabled||a.disableCheckbox||a.checkable===!1}function Wt(a,e){var u=[];function c(i){i.forEach(function(r){u.push(r[e.value]);var t=r[e.children];t&&c(t)})}return c(a),u}function Ze(a){return a==null}var nt=Symbol("TreeSelectContextPropsKey");function Ut(a){return mt(nt,a)}function zt(){return gt(nt,{})}var Gt={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0};const Jt=Ue({compatConfig:{MODE:3},name:"OptionList",inheritAttrs:!1,slots:["notFoundContent","menuItemSelectedIcon"],setup:function(e,u){var c=u.slots,i=u.expose,r=Ct(),t=bt(),n=zt(),o=Ne(),l=St(function(){return n.treeData},[function(){return r.open},function(){return n.treeData}],function(y){return y[0]}),v=E(function(){var y=t.checkable,p=t.halfCheckedKeys,g=t.checkedKeys;return y?{checked:g,halfChecked:p}:null});Ve(function(){return r.open},function(){It(function(){if(r.open&&!r.multiple&&t.checkedKeys.length){var y;(y=o.value)===null||y===void 0||y.scrollTo({key:t.checkedKeys[0]})}})},{immediate:!0,flush:"post"});var h=E(function(){return String(r.searchValue).toLowerCase()}),k=function(p){return h.value?String(p[t.treeNodeFilterProp]).toLowerCase().includes(h.value):!1},b=J(t.treeDefaultExpandedKeys),N=J(null);Ve(function(){return r.searchValue},function(){r.searchValue&&(N.value=Wt(Z(n.treeData),Z(n.fieldNames)))},{immediate:!0});var T=E(function(){return t.treeExpandedKeys?t.treeExpandedKeys.slice():r.searchValue?N.value:b.value}),O=function(p){var g;b.value=p,N.value=p,(g=t.onTreeExpand)===null||g===void 0||g.call(t,p)},V=function(p){p.preventDefault()},_=function(p,g){var F,M=g.node,H=t.checkable,W=t.checkedKeys;if(!(H&&je(M))&&((F=n.onSelect)===null||F===void 0||F.call(n,M.key,{selected:!W.includes(M.key)}),!r.multiple)){var D;(D=r.toggleOpen)===null||D===void 0||D.call(r,!1)}},I=Ne(null),L=E(function(){return t.keyEntities[I.value]}),z=function(p){I.value=p};return i({scrollTo:function(){for(var p,g,F=arguments.length,M=new Array(F),H=0;H<F;H++)M[H]=arguments[H];return(p=o.value)===null||p===void 0||(g=p.scrollTo)===null||g===void 0?void 0:g.call.apply(g,[p].concat(M))},onKeydown:function(p){var g,F=p.which;switch(F){case se.UP:case se.DOWN:case se.LEFT:case se.RIGHT:(g=o.value)===null||g===void 0||g.onKeydown(p);break;case se.ENTER:{if(L.value){var M=L.value.node||{},H=M.selectable,W=M.value;H!==!1&&_(null,{node:{key:I.value},selected:!t.checkedKeys.includes(W)})}break}case se.ESC:r.toggleOpen(!1)}},onKeyup:function(){}}),function(){var y,p=r.prefixCls,g=r.multiple,F=r.searchValue,M=r.open,H=r.notFoundContent,W=H===void 0?(y=c.notFoundContent)===null||y===void 0?void 0:y.call(c):H,D=n.listHeight,m=n.listItemHeight,S=n.virtual,R=t.checkable,re=t.treeDefaultExpandAll,le=t.treeIcon,X=t.showTreeIcon,ee=t.switcherIcon,he=t.treeLine,ue=t.loadData,Q=t.treeLoadedKeys,te=t.treeMotion,ie=t.onTreeLoad,pe=t.checkedKeys;if(l.value.length===0)return q("div",{role:"listbox",class:"".concat(p,"-empty"),onMousedown:V},[W]);var Y={fieldNames:n.fieldNames};return Q&&(Y.loadedKeys=Q),T.value&&(Y.expandedKeys=T.value),q("div",{onMousedown:V},[L.value&&M&&q("span",{style:Gt,"aria-live":"assertive"},[L.value.node.value]),q(xt,s(s({ref:o,focusable:!1,prefixCls:"".concat(p,"-tree"),treeData:l.value,height:D,itemHeight:m,virtual:S,multiple:g,icon:le,showIcon:X,switcherIcon:ee,showLine:he,loadData:F?null:ue,motion:te,activeKey:I.value,checkable:R,checkStrictly:!0,checkedKeys:v.value,selectedKeys:R?[]:pe,defaultExpandAll:re},Y),{},{onActiveChange:z,onSelect:_,onCheck:_,onExpand:O,onLoad:ie,filterTreeNode:k}),s(s({},c),{},{checkable:t.customSlots.treeCheckable}))])}}});var qt="SHOW_ALL",rt="SHOW_PARENT",ze="SHOW_CHILD";function Xe(a,e,u,c){var i=new Set(a);return e===ze?a.filter(function(r){var t=u[r];return!(t&&t.children&&t.children.some(function(n){var o=n.node;return i.has(o[c.value])})&&t.children.every(function(n){var o=n.node;return je(o)||i.has(o[c.value])}))}):e===rt?a.filter(function(r){var t=u[r],n=t?t.parent:null;return!(n&&!je(n.node)&&i.has(n.key))}):a}var Ke=function(){return null};Ke.inheritAttrs=!1;Ke.displayName="ATreeSelectNode";Ke.isTreeSelectNode=!0;const Ge=Ke;var Qt=["title","switcherIcon"];function Yt(a){return a&&a.type&&a.type.isTreeSelectNode}function Zt(a){function e(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return wt(u).map(function(c){var i,r,t;if(!Yt(c))return null;for(var n=c.children||{},o=c.key,l={},v=0,h=Object.entries(c.props);v<h.length;v++){var k=Se(h[v],2),b=k[0],N=k[1];l[kt(b)]=N}var T=l.isLeaf,O=l.checkable,V=l.selectable,_=l.disabled,I=l.disableCheckbox,L={isLeaf:T||T===""||void 0,checkable:O||O===""||void 0,selectable:V||V===""||void 0,disabled:_||_===""||void 0,disableCheckbox:I||I===""||void 0},z=s(s({},l),L),y=l.title,p=y===void 0?(i=n.title)===null||i===void 0?void 0:i.call(n,z):y,g=l.switcherIcon,F=g===void 0?(r=n.switcherIcon)===null||r===void 0?void 0:r.call(n,z):g,M=Tt(l,Qt),H=(t=n.default)===null||t===void 0?void 0:t.call(n),W=s(s({},M),{},{title:p,switcherIcon:F,key:o,isLeaf:T},L),D=e(H);return D.length&&(W.children=D),W})}return e(a)}function We(a){if(!a)return a;var e=s({},a);return"props"in e||Object.defineProperty(e,"props",{get:function(){return e}}),e}function Xt(a,e,u,c,i,r){var t=null,n=null;function o(){function l(v){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"0",k=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;return v.map(function(b,N){var T="".concat(h,"-").concat(N),O=b[r.value],V=u.includes(O),_=l(b[r.children]||[],T,V),I=q(Ge,b,{default:function(){return[_.map(function(y){return y.node})]}});if(e===O&&(t=I),V){var L={pos:T,node:I,children:_};return k||n.push(L),L}return null}).filter(function(b){return b})}n||(n=[],l(c),n.sort(function(v,h){var k=v.node.props.value,b=h.node.props.value,N=u.indexOf(k),T=u.indexOf(b);return N-T}))}Object.defineProperty(a,"triggerNode",{get:function(){return o(),t}}),Object.defineProperty(a,"allCheckedNodes",{get:function(){return o(),i?n:n.map(function(v){var h=v.node;return h})}})}function ea(a,e){var u=e.id,c=e.pId,i=e.rootPId,r={},t=[],n=a.map(function(o){var l=s({},o),v=l[u];return r[v]=l,l.key=l.key||v,l});return n.forEach(function(o){var l=o[c],v=r[l];v&&(v.children=v.children||[],v.children.push(o)),(l===i||!v&&i===null)&&t.push(o)}),t}function ta(a,e,u){var c=J();return Ve([u,a,e],function(){var i=u.value;a.value?c.value=u.value?ea(Z(a.value),s({id:"id",pId:"pId",rootPId:null},i!==!0?i:{})):Z(a.value).slice():c.value=Zt(Z(e.value))},{immediate:!0,deep:!0}),c}const aa=function(a){var e=J({valueLabels:new Map}),u=J();Ve(a,function(){u.value=Z(a.value)},{immediate:!0});var c=E(function(){var i=e.value.valueLabels,r=new Map,t=u.value.map(function(n){var o,l=n.value,v=(o=n.label)!==null&&o!==void 0?o:i.get(l);return r.set(l,v),s(s({},n),{},{label:v})});return e.value.valueLabels=r,t});return[c]},na=function(a,e){var u=J(new Map),c=J({});return Le(function(){var i=e.value,r=Et(a.value,{fieldNames:i,initWrapper:function(n){return s(s({},n),{},{valueEntities:new Map})},processEntity:function(n,o){var l=n.node[i.value];o.valueEntities.set(l,n)}});u.value=r.valueEntities,c.value=r.keyEntities}),{valueEntities:u,keyEntities:c}},ra=function(a,e,u,c,i,r){var t=J([]),n=J([]);return Le(function(){var o=a.value.map(function(k){var b=k.value;return b}),l=e.value.map(function(k){var b=k.value;return b}),v=o.filter(function(k){return!c.value[k]});if(u.value){var h=Be(o,!0,c.value,i.value,r.value);o=h.checkedKeys,l=h.halfCheckedKeys}t.value=Array.from(new Set([].concat(ne(v),ne(o)))),n.value=l}),[t,n]},la=function(a,e,u){var c=u.treeNodeFilterProp,i=u.filterTreeNode,r=u.fieldNames;return E(function(){var t=r.value.children,n=e.value,o=c==null?void 0:c.value;if(!n||i.value===!1)return a.value;var l;if(typeof i.value=="function")l=i.value;else{var v=n.toUpperCase();l=function(b,N){var T=N[o];return String(T).toUpperCase().includes(v)}}function h(k){for(var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,N=[],T=0,O=k.length;T<O;T++){var V=k[T],_=V[t],I=b||l(n,We(V)),L=h(_||[],I);(I||L.length)&&N.push(s(s({},V),{},ve({},t,L)))}return N}return h(a.value)})};function lt(){return s(s({},De(Pt(),["mode"])),{},{prefixCls:String,id:String,value:{type:[String,Number,Object,Array]},defaultValue:{type:[String,Number,Object,Array]},onChange:{type:Function},searchValue:String,inputValue:String,onSearch:{type:Function},autoClearSearchValue:{type:Boolean,default:void 0},filterTreeNode:{type:[Boolean,Function],default:void 0},treeNodeFilterProp:String,onSelect:Function,onDeselect:Function,showCheckedStrategy:{type:String},treeNodeLabelProp:String,fieldNames:{type:Object},multiple:{type:Boolean,default:void 0},treeCheckable:{type:Boolean,default:void 0},treeCheckStrictly:{type:Boolean,default:void 0},labelInValue:{type:Boolean,default:void 0},treeData:{type:Array},treeDataSimpleMode:{type:[Boolean,Object],default:void 0},loadData:{type:Function},treeLoadedKeys:{type:Array},onTreeLoad:{type:Function},treeDefaultExpandAll:{type:Boolean,default:void 0},treeExpandedKeys:{type:Array},treeDefaultExpandedKeys:{type:Array},onTreeExpand:{type:Function},virtual:{type:Boolean,default:void 0},listHeight:Number,listItemHeight:Number,onDropdownVisibleChange:{type:Function},treeLine:{type:[Boolean,Object],default:void 0},treeIcon:fe.any,showTreeIcon:{type:Boolean,default:void 0},switcherIcon:fe.any,treeMotion:fe.any,children:Array,showArrow:{type:Boolean,default:void 0},showSearch:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},placeholder:fe.any,maxTagPlaceholder:{type:Function},dropdownPopupAlign:fe.any,customSlots:Object})}function ua(a){return!a||Ft(a)!=="object"}const ia=Ue({compatConfig:{MODE:3},name:"TreeSelect",inheritAttrs:!1,props:at(lt(),{treeNodeFilterProp:"value",autoClearSearchValue:!0,showCheckedStrategy:ze,listHeight:200,listItemHeight:20,prefixCls:"vc-tree-select"}),setup:function(e,u){var c=u.attrs,i=u.expose,r=u.slots,t=Nt(ae(e,"id")),n=E(function(){return e.treeCheckable&&!e.treeCheckStrictly}),o=E(function(){return e.treeCheckable||e.treeCheckStrictly}),l=E(function(){return e.treeCheckStrictly||e.labelInValue}),v=E(function(){return o.value||e.multiple}),h=E(function(){return jt(e.fieldNames)}),k=Je("",{value:E(function(){return e.searchValue!==void 0?e.searchValue:e.inputValue}),postState:function(d){return d||""}}),b=Se(k,2),N=b[0],T=b[1],O=function(d){var f;T(d),(f=e.onSearch)===null||f===void 0||f.call(e,d)},V=ta(ae(e,"treeData"),ae(e,"children"),ae(e,"treeDataSimpleMode")),_=na(V,h),I=_.keyEntities,L=_.valueEntities,z=function(d){var f=[],C=[];return d.forEach(function(w){L.value.has(w)?C.push(w):f.push(w)}),{missingRawValues:f,existRawValues:C}},y=la(V,N,{fieldNames:h,treeNodeFilterProp:ae(e,"treeNodeFilterProp"),filterTreeNode:ae(e,"filterTreeNode")}),p=function(d){if(d){if(e.treeNodeLabelProp)return d[e.treeNodeLabelProp];for(var f=h.value._title,C=0;C<f.length;C+=1){var w=d[f[C]];if(w!==void 0)return w}}},g=function(d){var f=Bt(d);return f.map(function(C){return ua(C)?{value:C}:C})},F=function(d){var f=g(d);return f.map(function(C){var w=C.label,A=C.value,K=C.halfChecked,B,$=L.value.get(A);if($){var j;w=(j=w)!==null&&j!==void 0?j:p($.node),B=$.node.disabled}return{label:w,value:A,halfChecked:K,disabled:B}})},M=Je(e.defaultValue,{value:ae(e,"value")}),H=Se(M,2),W=H[0],D=H[1],m=E(function(){return g(W.value)}),S=J([]),R=J([]);Le(function(){var x=[],d=[];m.value.forEach(function(f){f.halfChecked?d.push(f):x.push(f)}),S.value=x,R.value=d});var re=E(function(){return S.value.map(function(x){return x.value})}),le=Vt(I),X=le.maxLevel,ee=le.levelEntities,he=ra(S,R,n,I,X,ee),ue=Se(he,2),Q=ue[0],te=ue[1],ie=E(function(){var x=Xe(Q.value,e.showCheckedStrategy,I.value,h.value),d=x.map(function(A){var K,B,$;return(K=(B=I.value[A])===null||B===void 0||($=B.node)===null||$===void 0?void 0:$[h.value.value])!==null&&K!==void 0?K:A}),f=d.map(function(A){var K=S.value.find(function(B){return B.value===A});return{value:A,label:K==null?void 0:K.label}}),C=F(f),w=C[0];return!v.value&&w&&Ze(w.value)&&Ze(w.label)?[]:C.map(function(A){var K;return s(s({},A),{},{label:(K=A.label)!==null&&K!==void 0?K:A.value})})}),pe=aa(ie),Y=Se(pe,1),Pe=Y[0],oe=function(d,f,C){var w=F(d);if(D(w),e.autoClearSearchValue&&T(""),e.onChange){var A=d;if(n.value){var K=Xe(d,e.showCheckedStrategy,I.value,h.value);A=K.map(function(U){var Ee=L.value.get(U);return Ee?Ee.node[h.value.value]:U})}var B=f||{triggerValue:void 0,selected:void 0},$=B.triggerValue,j=B.selected,G=A;if(e.treeCheckStrictly){var me=R.value.filter(function(U){return!A.includes(U.value)});G=[].concat(ne(G),ne(me))}var ce=F(G),de={preValue:S.value,triggerValue:$},Te=!0;(e.treeCheckStrictly||C==="selection"&&!j)&&(Te=!1),Xt(de,$,d,V.value,Te,h.value),o.value?de.checked=j:de.selected=j;var Ce=l.value?ce:ce.map(function(U){return U.value});e.onChange(v.value?Ce:Ce[0],l.value?null:ce.map(function(U){return U.label}),de)}},Ie=function(d,f){var C,w=f.selected,A=f.source,K=Z(I.value),B=Z(L.value),$=K[d],j=$==null?void 0:$.node,G=(C=j==null?void 0:j[h.value.value])!==null&&C!==void 0?C:d;if(!v.value)oe([G],{selected:!0,triggerValue:G},"option");else{var me=w?[].concat(ne(re.value),[G]):Q.value.filter(function(be){return be!==G});if(n.value){var ce=z(me),de=ce.missingRawValues,Te=ce.existRawValues,Ce=Te.map(function(be){return B.get(be).key}),U;if(w){var Ee=Be(Ce,!0,K,X.value,ee.value);U=Ee.checkedKeys}else{var yt=Be(Ce,{checked:!1,halfCheckedKeys:te.value},K,X.value,ee.value);U=yt.checkedKeys}me=[].concat(ne(de),ne(U.map(function(be){return K[be].node[h.value.value]})))}oe(me,{selected:w,triggerValue:G},A||"option")}if(w||!v.value){var Oe;(Oe=e.onSelect)===null||Oe===void 0||Oe.call(e,G,We(j))}else{var Re;(Re=e.onDeselect)===null||Re===void 0||Re.call(e,G,We(j))}},ye=function(d){if(e.onDropdownVisibleChange){var f={};Object.defineProperty(f,"documentClickClose",{get:function(){return!1}}),e.onDropdownVisibleChange(d,f)}},Fe=function(d,f){var C=d.map(function(w){return w.value});if(f.type==="clear"){oe(C,{},"selection");return}f.values.length&&Ie(f.values[0].value,{selected:!1,source:"selection"})},P=Lt(e),He=P.treeNodeFilterProp,ge=P.loadData,Ae=P.treeLoadedKeys,Me=P.onTreeLoad,xe=P.treeDefaultExpandAll,we=P.treeExpandedKeys,_e=P.treeDefaultExpandedKeys,ut=P.onTreeExpand,it=P.virtual,ot=P.listHeight,ct=P.listItemHeight,dt=P.treeLine,st=P.treeIcon,vt=P.showTreeIcon,ft=P.switcherIcon,ht=P.treeMotion,pt=P.customSlots;Dt(qe({checkable:o,loadData:ge,treeLoadedKeys:Ae,onTreeLoad:Me,checkedKeys:Q,halfCheckedKeys:te,treeDefaultExpandAll:xe,treeExpandedKeys:we,treeDefaultExpandedKeys:_e,onTreeExpand:ut,treeIcon:st,treeMotion:ht,showTreeIcon:vt,switcherIcon:ft,treeLine:dt,treeNodeFilterProp:He,keyEntities:I,customSlots:pt})),Ut(qe({virtual:it,listHeight:ot,listItemHeight:ct,treeData:y,fieldNames:h,onSelect:Ie}));var ke=Ne();return i({focus:function(){var d;(d=ke.value)===null||d===void 0||d.focus()},blur:function(){var d;(d=ke.value)===null||d===void 0||d.blur()},scrollTo:function(d){var f;(f=ke.value)===null||f===void 0||f.scrollTo(d)}}),function(){var x,d=De(e,["id","prefixCls","value","defaultValue","onChange","onSelect","onDeselect","searchValue","inputValue","onSearch","autoClearSearchValue","filterTreeNode","treeNodeFilterProp","showCheckedStrategy","treeNodeLabelProp","multiple","treeCheckable","treeCheckStrictly","labelInValue","fieldNames","treeDataSimpleMode","treeData","children","loadData","treeLoadedKeys","onTreeLoad","treeDefaultExpandAll","treeExpandedKeys","treeDefaultExpandedKeys","onTreeExpand","virtual","listHeight","listItemHeight","onDropdownVisibleChange","treeLine","treeIcon","showTreeIcon","switcherIcon","treeMotion"]);return q(Kt,s(s(s({ref:ke},c),d),{},{id:t,prefixCls:e.prefixCls,mode:v.value?"multiple":void 0,displayValues:Pe.value,onDisplayValuesChange:Fe,searchValue:N.value,onSearch:O,OptionList:Jt,emptyOptions:!V.value.length,onDropdownVisibleChange:ye,tagRender:e.tagRender||r.tagRender,dropdownMatchSelectWidth:(x=e.dropdownMatchSelectWidth)!==null&&x!==void 0?x:!0}),r)}}});var et=function(e,u,c){return c!==void 0?c:"".concat(e,"-").concat(u)};function oa(){return s(s({},De(lt(),["showTreeIcon","treeMotion","inputIcon","getInputElement","treeLine","customSlots"])),{},{suffixIcon:fe.any,size:{type:String},bordered:{type:Boolean,default:void 0},treeLine:{type:[Boolean,Object],default:void 0},replaceFields:{type:Object},"onUpdate:value":{type:Function},"onUpdate:treeExpandedKeys":{type:Function},"onUpdate:searchValue":{type:Function}})}var $e=Ue({compatConfig:{MODE:3},name:"ATreeSelect",inheritAttrs:!1,props:at(oa(),{choiceTransitionName:"",listHeight:256,treeIcon:!1,listItemHeight:26,bordered:!0}),slots:["title","titleRender","placeholder","maxTagPlaceholder","treeIcon","switcherIcon","notFoundContent"],setup:function(e,u){var c=u.attrs,i=u.slots,r=u.expose,t=u.emit;At(!(e.treeData===void 0&&i.default)),Le(function(){Qe(e.multiple!==!1||!e.treeCheckable,"TreeSelect","`multiple` will always be `true` when `treeCheckable` is true"),Qe(e.replaceFields===void 0,"TreeSelect","`replaceFields` is deprecated, please use fieldNames instead")});var n=Mt(),o=_t("select",e),l=o.prefixCls,v=o.renderEmpty,h=o.direction,k=o.virtual,b=o.dropdownMatchSelectWidth,N=o.size,T=o.getPopupContainer,O=o.getPrefixCls,V=E(function(){return O()}),_=E(function(){return et(V.value,"slide-up",e.transitionName)}),I=E(function(){return et(V.value,"",e.choiceTransitionName)}),L=E(function(){return O("select-tree",e.prefixCls)}),z=E(function(){return O("tree-select",e.prefixCls)}),y=E(function(){return Ye(e.dropdownClassName,"".concat(z.value,"-dropdown"),ve({},"".concat(z.value,"-dropdown-rtl"),h.value==="rtl"))}),p=E(function(){return!!(e.treeCheckable||e.multiple)}),g=Ne();r({focus:function(){var m,S;(m=(S=g.value).focus)===null||m===void 0||m.call(S)},blur:function(){var m,S;(m=(S=g.value).blur)===null||m===void 0||m.call(S)}});var F=function(){for(var m=arguments.length,S=new Array(m),R=0;R<m;R++)S[R]=arguments[R];t("update:value",S[0]),t.apply(void 0,["change"].concat(S)),n.onFieldChange()},M=function(m){t("update:treeExpandedKeys",m),t("treeExpand",m)},H=function(m){t("update:searchValue",m),t("search",m)},W=function(m){t("blur",m),n.onFieldBlur()};return function(){var D,m,S,R=e.notFoundContent,re=R===void 0?(D=i.notFoundContent)===null||D===void 0?void 0:D.call(i):R,le=e.prefixCls,X=e.bordered,ee=e.listHeight,he=e.listItemHeight,ue=e.multiple,Q=e.treeIcon,te=e.treeLine,ie=e.switcherIcon,pe=ie===void 0?(m=i.switcherIcon)===null||m===void 0?void 0:m.call(i):ie,Y=e.fieldNames,Pe=Y===void 0?e.replaceFields:Y,oe=e.id,Ie=oe===void 0?n.id.value:oe,ye=Ot(s(s({},e),{},{multiple:p.value,prefixCls:l.value}),i),Fe=ye.suffixIcon,P=ye.removeIcon,He=ye.clearIcon,ge;re!==void 0?ge=re:ge=v.value("Select");var Ae=De(e,["suffixIcon","itemIcon","removeIcon","clearIcon","switcherIcon","bordered","onUpdate:value","onUpdate:treeExpandedKeys","onUpdate:searchValue"]),Me=Ye(!le&&z.value,(S={},ve(S,"".concat(l.value,"-lg"),N.value==="large"),ve(S,"".concat(l.value,"-sm"),N.value==="small"),ve(S,"".concat(l.value,"-rtl"),h.value==="rtl"),ve(S,"".concat(l.value,"-borderless"),!X),S),c.class),xe={};return e.treeData===void 0&&i.default&&(xe.children=Rt(i.default())),q(ia,s(s(s(s({},c),Ae),{},{virtual:k.value,dropdownMatchSelectWidth:b.value,id:Ie,fieldNames:Pe,ref:g,prefixCls:l.value,class:Me,listHeight:ee,listItemHeight:he,treeLine:!!te,inputIcon:Fe,multiple:ue,removeIcon:P,clearIcon:He,switcherIcon:function(_e){return $t(L.value,pe,te,_e)},showTreeIcon:Q,notFoundContent:ge,getPopupContainer:T.value,treeMotion:null,dropdownClassName:y.value,choiceTransitionName:I.value,onChange:F,onBlur:W,onSearch:H,onTreeExpand:M},xe),{},{transitionName:_.value,customSlots:s(s({},i),{},{treeCheckable:function(){return q("span",{class:"".concat(l.value,"-tree-checkbox-inner")},null)}}),maxTagPlaceholder:e.maxTagPlaceholder||i.maxTagPlaceholder}),s(s({},i),{},{treeCheckable:function(){return q("span",{class:"".concat(l.value,"-tree-checkbox-inner")},null)}}))}}}),tt=Ge;const da=Ht($e,{TreeNode:Ge,SHOW_ALL:qt,SHOW_PARENT:rt,SHOW_CHILD:ze,install:function(e){return e.component($e.name,$e),e.component(tt.displayName,tt),e}});export{da as _};
