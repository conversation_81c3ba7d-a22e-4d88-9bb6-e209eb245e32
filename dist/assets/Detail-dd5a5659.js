import{_ as C}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as T,r as b,I as q,o as O,D as E,a as x,v as te,f as _,e as o,h as R,i as n,g as e,u as t,bn as B,bg as A,_ as M,a2 as L,b as S,k as Z,bO as ee,S as Y,T as F,z as se,U as oe,y as N,s as ne,F as le,bM as ie,bN as ce,p as de,j as re}from"./index-db94d997.js";import{a as ue}from"./index-78c2ffe1.js";import{_ as $}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const _e={class:"areaPrice"},pe={class:"text-hide"},me=T({__name:"PowerGeneration",props:{pdata:{default:{}}},setup(r){const m=r,c=b();b([]),b([]),q();const l=[{title:"日期",dataIndex:"nowDate",width:120,resizable:!0},{title:"装机容量(kW)",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0},{title:"当日发电(kWh)",dataIndex:"nowDayElec",width:130,formatMoney:!0,resizable:!0},{title:"当月累计发电(kWh)",dataIndex:"nowMonthElec",width:120,formatMoney:!0,resizable:!0},{title:"当年累计发电(kWh)",dataIndex:"nowYearElec",width:120,formatMoney:!0,resizable:!0},{title:"总累计发电(kWh)",dataIndex:"sumElec",width:120,formatMoney:!0,resizable:!0},{title:"日利用小时数",dataIndex:"dayHours",width:100,formatDecimal:2,resizable:!0},{title:"月利用小时数",dataIndex:"monthHours",width:100,formatDecimal:2,resizable:!0},{title:"年利用小时数",dataIndex:"yearHours",resizable:!0,formatDecimal:2,width:100}];O(()=>{}),E(()=>{console.log("onActivated"),c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&k()});const P=(a,v)=>{},D=b([]),I=a=>{D.value=a||[],console.log("dataSource=",a)},k=()=>{var a;(a=c.value)==null||a.reload()},z=(a,v)=>new Promise(i=>{var d;const u={...{stationCode:(d=m.pdata)==null?void 0:d.stationUniqueId},...a};i(u)});return(a,v)=>{const i=B,f=A,u=$,d=C;return x(),te("div",_e,[_(d,{columns:l,ref_key:"actionRef",ref:c,request:t(ue),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:I,"before-query-params":z,search:!1,hidenBoxShadow:!0},{companyCodeListRender:o(({column:y,record:s,index:h})=>[_(i,null,{title:o(()=>[R(n(s.companyName),1)]),default:o(()=>[e("span",pe,n(s.companyName),1)]),_:2},1024)]),actionRender:o(({column:y,record:s,index:h})=>[_(u,null,{default:o(()=>[_(f,{size:"small",type:"link",onClick:()=>P(s,h)},{default:o(()=>[R(" 查看详情 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const he=M(me,[["__scopeId","data-v-1fb21bab"]]);function be(r){return L({url:"/web/stationBusinessData/v1/list",method:"POST",isTable:!0,data:r})}function fe(r){return L({url:"/web/outInsuranceController/v1/list",method:"POST",isTable:!0,data:r})}function ye(r){return L({url:"/web/depreciationReport/v1/list",method:"POST",isTable:!0,data:r})}const ve={class:"text-hide"},we=T({__name:"BusinessData",props:{pdata:{default:{}}},setup(r){const m=r,c=b();q();const l=[{title:"日期",dataIndex:"monthKey",width:120,resizable:!0},{title:"应收电费",dataIndex:"receivableEqFee",width:120,formatMoney:!0,resizable:!0},{title:"电费回款",dataIndex:"eqFeeBack",width:120,formatMoney:!0,resizable:!0},{title:"应付收益",dataIndex:"shouldPayIncome",width:120,formatMoney:!0,resizable:!0},{title:"实付收益",dataIndex:"realityPayIncome",width:120,formatMoney:!0,resizable:!0},{title:"收入",dataIndex:"monthIncome",width:120,formatMoney:!0,resizable:!0},{title:"成本",dataIndex:"monthCost",width:120,formatMoney:!0,resizable:!0},{title:"利润",dataIndex:"monthProfit",width:120,formatMoney:!0,resizable:!0}];O(()=>{}),E(()=>{console.log("onActivated"),c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&k()});const P=(a,v)=>{},D=b([]),I=a=>{D.value=a||[],console.log("dataSource=",a)},k=()=>{var a;(a=c.value)==null||a.reload()},z=(a,v)=>new Promise(i=>{var d;const u={...{stationCode:(d=m.pdata)==null?void 0:d.stationUniqueId},...a};i(u)});return(a,v)=>{const i=B,f=A,u=$,d=C;return x(),S(d,{columns:l,ref_key:"actionRef",ref:c,request:t(be),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:I,"before-query-params":z,search:!1,hidenBoxShadow:!0},{companyCodeListRender:o(({column:y,record:s,index:h})=>[_(i,null,{title:o(()=>[R(n(s.companyName),1)]),default:o(()=>[e("span",ve,n(s.companyName),1)]),_:2},1024)]),actionRender:o(({column:y,record:s,index:h})=>[_(u,null,{default:o(()=>[_(f,{size:"small",type:"link",onClick:()=>P(s,h)},{default:o(()=>[R(" 查看详情 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])}}});const xe=M(we,[["__scopeId","data-v-6aceaee2"]]),Ie={class:"text-hide"},ge=T({__name:"OuterInsuranceRecord",props:{pdata:{default:{}}},setup(r){const m=r,c=b();q();const l=b([{label:"是",value:1},{label:"否",value:0}]),P=b([{label:"运维协商中",value:1},{label:"公司确认中",value:2},{label:"已完成",value:3}]),D=[{title:"案件编号",dataIndex:"caseNumber",width:120,resizable:!0},{title:"创建时间",dataIndex:"createTime",width:150,resizable:!0},{title:"保单号",dataIndex:"policyNumber",width:120,resizable:!0},{title:"险种",dataIndex:"insuranceTypeName",width:120,resizable:!0},{title:"摘要",dataIndex:"digest",width:120,resizable:!0},{title:"理赔金额(元)",dataIndex:"claimAmount",width:120,formatMoney:!0,resizable:!0},{title:"理赔进度",dataIndex:"claimProgress",valueType:"select",valueEnum:P,width:120,formatMoney:!0,resizable:!0},{title:"保费是否到账",dataIndex:"premiumStatus",valueType:"select",valueEnum:l,width:120,resizable:!0},{title:"是否与运维结算",dataIndex:"operationsSettleStatus",valueType:"select",valueEnum:l,width:120,resizable:!0},{title:"结案标志",dataIndex:"caseStatus",valueType:"select",valueEnum:l,width:120,resizable:!0}];O(()=>{}),E(()=>{console.log("onActivated"),c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&a()});const I=(i,f)=>{},k=b([]),z=i=>{k.value=i||[],console.log("dataSource=",i)},a=()=>{var i;(i=c.value)==null||i.reload()},v=(i,f)=>new Promise(u=>{var s;const y={...{stationUniqueId:(s=m.pdata)==null?void 0:s.stationUniqueId},...i};u(y)});return(i,f)=>{const u=B,d=A,y=$,s=C;return x(),S(s,{columns:D,ref_key:"actionRef",ref:c,request:t(fe),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:z,"before-query-params":v,search:!1,hidenBoxShadow:!0},{companyCodeListRender:o(({column:h,record:w,index:g})=>[_(u,null,{title:o(()=>[R(n(w.companyName),1)]),default:o(()=>[e("span",Ie,n(w.companyName),1)]),_:2},1024)]),actionRender:o(({column:h,record:w,index:g})=>[_(y,null,{default:o(()=>[_(d,{size:"small",type:"link",onClick:()=>I(w,g)},{default:o(()=>[R(" 查看详情 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])}}});const Se=M(ge,[["__scopeId","data-v-e27a3651"]]),De={class:"text-hide"},ze={class:"text-hide"},Re={class:"text-hide"},ke=T({__name:"DeprecitionRecord",props:{pdata:{default:{}}},setup(r){const m=r,c=b();q();const l=[{title:"固定资产原值(元)",dataIndex:"fixedAssetsValue",width:120,formatMoney:!0,resizable:!0},{title:"计提折旧首月",key:"datongrd",width:120,resizable:!0,render:!0},{title:"折旧月份",dataIndex:"depreciationMonth",width:120,resizable:!0},{title:"已使用月份",dataIndex:"useMonth",width:120,resizable:!0},{title:"残值率(%)",key:"salvageRate",dataIndex:"salvageRate",width:120,resizable:!0,render:!0},{title:"月折旧额(元)",dataIndex:"monthDepreciationAmount",width:120,formatMoney:!0,resizable:!0},{title:"累计折旧(元)",dataIndex:"accDepreciationAmount",width:120,formatMoney:!0,resizable:!0},{title:"资产净值(元)",dataIndex:"netAssetValue",width:120,formatMoney:!0,resizable:!0}];O(()=>{}),E(()=>{console.log("onActivated"),c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&k()});const P=(a,v)=>{},D=b([]),I=a=>{D.value=a||[],console.log("dataSource=",a)},k=()=>{var a;(a=c.value)==null||a.reload()},z=(a,v)=>new Promise(i=>{var d;const u={...{stationCode:(d=m.pdata)==null?void 0:d.stationUniqueId},...a};i(u)});return(a,v)=>{const i=B,f=A,u=$,d=C;return x(),S(d,{columns:l,ref_key:"actionRef",ref:c,request:t(ye),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:I,"before-query-params":z,search:!1,hidenBoxShadow:!0},{companyCodeListRender:o(({column:y,record:s,index:h})=>[_(i,null,{title:o(()=>[R(n(s.companyName),1)]),default:o(()=>[e("span",De,n(s.companyName),1)]),_:2},1024)]),datongrdRender:o(({column:y,record:s,index:h})=>[_(i,null,{title:o(()=>{var w,g;return[R(n((w=a.pdata)!=null&&w.datongrd?t(Z)((g=a.pdata)==null?void 0:g.datongrd).format("YYYY-MM"):"—"),1)]}),default:o(()=>{var w,g;return[e("span",ze,n((w=a.pdata)!=null&&w.datongrd?t(Z)((g=a.pdata)==null?void 0:g.datongrd).format("YYYY-MM"):"—"),1)]}),_:1})]),salvageRateRender:o(({column:y,record:s,index:h})=>[_(i,null,{title:o(()=>[R(n(s!=null&&s.salvageRate?t(ee)(s==null?void 0:s.salvageRate,100):"—"),1)]),default:o(()=>[e("span",Re,n(s!=null&&s.salvageRate?t(ee)(s==null?void 0:s.salvageRate,100):"—"),1)]),_:2},1024)]),actionRender:o(({column:y,record:s,index:h})=>[_(u,null,{default:o(()=>[_(f,{size:"small",type:"link",onClick:()=>P(s,h)},{default:o(()=>[R(" 查看详情 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])}}});const Pe=M(ke,[["__scopeId","data-v-71afaebe"]]),p=r=>(de("data-v-2dd27cac"),r=r(),re(),r),Te={class:"card top"},Me=p(()=>e("div",{class:"title"},"基本信息",-1)),Ne={class:"content"},Ce={class:"item"},qe=p(()=>e("span",{class:"label"},"电站编号：",-1)),Oe={class:"value"},Ee={class:"item"},Be=p(()=>e("span",{class:"label"},"业主姓名：",-1)),Ae={class:"value"},$e={class:"item"},Ye=p(()=>e("span",{class:"label"},"产权公司：",-1)),Fe={class:"value"},Le={class:"item"},Ue=p(()=>e("span",{class:"label"},"业主类型：",-1)),We={class:"value"},Ge={class:"item"},Ve=p(()=>e("span",{class:"label"},"联系电话：",-1)),He={class:"value"},Ke={class:"item"},Qe=p(()=>e("span",{class:"label"},"身份证号：",-1)),je={class:"value"},Je={class:"item"},Xe=p(()=>e("span",{class:"label"},"业主年龄：",-1)),Ze={class:"value"},et={class:"item"},tt=p(()=>e("span",{class:"label"},"装机容量：",-1)),at={class:"value"},st={class:"item"},ot=p(()=>e("span",{class:"label"},"制单时间：",-1)),nt={class:"value"},lt={class:"item"},it=p(()=>e("span",{class:"label"},"上网类型：",-1)),ct={class:"value"},dt={class:"item"},rt=p(()=>e("span",{class:"label"},"电站类型：",-1)),ut={class:"value"},_t={class:"item"},pt=p(()=>e("span",{class:"label"},"项目公司：",-1)),mt={class:"value"},ht={class:"item"},bt=p(()=>e("span",{class:"label"},"行政区划：",-1)),ft={class:"value"},yt={class:"item"},vt=p(()=>e("span",{class:"label"},"详细地址：",-1)),wt={class:"value"},xt={class:"item"},It=p(()=>e("span",{class:"label"},"组件数量：",-1)),gt={class:"value"},St={class:"card",style:{"margin-bottom":"24px"}},Dt=T({__name:"Detail",setup(r){const m=b(1),c=b(history.state.pdata),l=b(history.state.pdata);return(P,D)=>{var z,a,v,i,f,u,d,y,s,h,w,g,U,W,G,V,H,K,Q,j,J,X;const I=ie,k=ce;return x(),te(le,null,[e("div",Te,[Me,e("div",Ne,[e("div",Ce,[qe,e("span",Oe,n((z=t(l))==null?void 0:z.stationUniqueId),1)]),e("div",Ee,[Be,e("span",Ae,n((v=(a=t(l))==null?void 0:a.householdInfo)==null?void 0:v.nameHoh),1)]),e("div",$e,[Ye,e("span",Fe,n((i=t(l))==null?void 0:i.ownerCompanyName),1)]),e("div",Le,[Ue,e("span",We,n(t(Y)((f=t(l))==null?void 0:f.tyPower,t(F)("PROPRIETOR_TYPE"))),1)]),e("div",Ge,[Ve,e("span",He,n((d=(u=t(l))==null?void 0:u.householdInfo)==null?void 0:d.nimMpHoh),1)]),e("div",Ke,[Qe,e("span",je,n((s=(y=t(l))==null?void 0:y.householdInfo)==null?void 0:s.numIdHoh),1)]),e("div",Je,[Xe,e("span",Ze,n((w=(h=t(l))==null?void 0:h.householdInfo)==null?void 0:w.age),1)]),e("div",et,[tt,e("span",at,n((g=t(l))!=null&&g.capins?t(se)((U=t(l))==null?void 0:U.capins)+" kW":""),1)]),e("div",st,[ot,e("span",nt,n(t(oe)((W=t(l))==null?void 0:W.operateDate)),1)]),e("div",lt,[it,e("span",ct,n(t(Y)((G=t(l))==null?void 0:G.netmode,t(F)("POWERSTA_NETMODE"))),1)]),e("div",dt,[rt,e("span",ut,n(t(Y)((V=t(l))==null?void 0:V.businessStationType,t(F)("EAM_POWER_STATION_TYPE_CONDITION"))),1)]),e("div",_t,[pt,e("span",mt,n((H=t(l))==null?void 0:H.projectCompanyName),1)]),e("div",ht,[bt,e("span",ft,n(((K=t(l))==null?void 0:K.provinceName)||"")+n(((Q=t(l))==null?void 0:Q.cityName)||"")+" "+n(((j=t(l))==null?void 0:j.areaName)||""),1)]),e("div",yt,[vt,e("span",wt,n((J=t(l))==null?void 0:J.projectLocation),1)]),e("div",xt,[It,e("span",gt,n((X=t(l))==null?void 0:X.componentQuantity),1)])])]),e("div",St,[_(k,{activeKey:t(m),"onUpdate:activeKey":D[0]||(D[0]=ae=>ne(m)?m.value=ae:null),tabBarStyle:{height:"60px",paddingLeft:"24px",marginBottom:"0px"}},{default:o(()=>[(x(),S(I,{key:1,tab:"发电情况"},{default:o(()=>[t(m)===1?(x(),S(he,{key:0,pdata:t(c)},null,8,["pdata"])):N("",!0)]),_:1})),(x(),S(I,{key:2,tab:"经营数据"},{default:o(()=>[t(m)===2?(x(),S(xe,{key:0,pdata:t(c)},null,8,["pdata"])):N("",!0)]),_:1})),(x(),S(I,{key:3,tab:"出险记录"},{default:o(()=>[t(m)===3?(x(),S(Se,{key:0,pdata:t(c)},null,8,["pdata"])):N("",!0)]),_:1})),(x(),S(I,{key:4,tab:"折旧报表"},{default:o(()=>[t(m)===4?(x(),S(Pe,{key:0,pdata:t(c)},null,8,["pdata"])):N("",!0)]),_:1}))]),_:1},8,["activeKey"])])],64)}}});const Ot=M(Dt,[["__scopeId","data-v-2dd27cac"]]);export{Ot as default};
