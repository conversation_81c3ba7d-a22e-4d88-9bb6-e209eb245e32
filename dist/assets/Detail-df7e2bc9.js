import{_ as x}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{i as g,j as w}from"./index-0c818432.js";import{d as S,I,bL as k,r as n,o as C,D as N,a as _,v as D,g as m,f as p,e as c,u as d,b as P,h as q,y as B,bg as E,p as R,j as V,_ as F}from"./index-db94d997.js";import{_ as L}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const T=a=>(R("data-v-0fc6d762"),a=a(),V(),a),j={class:"areaPrice"},H={class:"areaPrice_table"},M=T(()=>m("div",null,"更多年份年度基准电量",-1)),Y=S({__name:"Detail",setup(a){I(),k();const o=n();n(!1),n([]);const s=history.state.pdata,u=[{title:(s==null?void 0:s.companyName)||"",align:"center",children:[{title:"年份",dataIndex:"yKey",width:50},{title:"年度基准电量(kWh)",dataIndex:"eq",formatMoney:!0,width:200}]}],f=()=>{var e;(e=o.value)==null||e.reload()},y=(e,r)=>new Promise(i=>{let t={companyCode:s.companyCode};t={...t,...e},i(t)}),h=()=>{var r;let e=(r=o.value)==null?void 0:r.getInitialFormStateNew();w(e)},l=n([]),b=e=>{l.value=e||[]};return C(()=>{}),N(()=>{o.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&f()}),(e,r)=>{const i=E,t=L,v=x;return _(),D("div",j,[m("div",H,[p(v,{columns:u,ref_key:"actionRef",ref:o,request:d(g),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:b,search:!1,pagination:!1,bordered:!0,"before-query-params":y},{tableHeaderLeft:c(()=>[M]),tableHeader:c(()=>[p(t,null,{default:c(()=>[d(l).length>0?(_(),P(i,{key:0,type:"primary",onClick:h},{default:c(()=>[q("导出")]),_:1})):B("",!0)]),_:1})]),_:1},8,["request"])])])}}});const U=F(Y,[["__scopeId","data-v-0fc6d762"]]);export{U as default};
