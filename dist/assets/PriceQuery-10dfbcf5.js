import{_ as j}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as q,r as i,o as B,D as Y,at as F,au as M,a as x,v as Q,g as C,f as m,e as c,u as h,b as V,h as D,y as O,i as w,c7 as R,bc as W,bg as A,bn as J,_ as G}from"./index-db94d997.js";import{d as H}from"./index-4cf21c7d.js";import{_ as K}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const U={class:"areaPrice"},$={class:"areaPrice_table"},X={class:"text-hide"},Z=q({__name:"PriceQuery",setup(v){const n=i(),T=["provinceCodeList","cityCodeList","areaCodeList"],p=i([]),u=i([]),I=[{title:"行政区划",dataIndex:"cityTree",valueType:"multipleTreeSelect",valueEnum:p,maxTagCount:3,hideInTable:!0,order:4},{title:"时间周期",dataIndex:"datongrd",valueType:"dateRange",search:!0,hideInTable:!0,order:4},{title:"电站编号",dataIndex:"stationUniqueId",search:!0,resizable:!0,width:100,order:2},{title:"业主名称",dataIndex:"stationName",search:!0,resizable:!0,width:100,order:3},{title:"产权公司",key:"projectCompanyCodeList",dataIndex:"projectCompanyCodeList",valueType:"multipleSelect",maxTagCount:3,width:120,valueEnum:u,order:1,render:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:100},{title:"并网时间",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",search:!1,resizable:!0,width:100},{title:"合作年限",dataIndex:"cooperationYears",search:!1,resizable:!0,width:100},{title:"标杆电价(元/kWh)",dataIndex:"sightElectrovalences",search:!1,resizable:!0,formatDecimal:4,width:120},{title:"补贴电价(元/kWh)",dataIndex:"electrovalences",search:!1,resizable:!0,formatDecimal:4,width:120},{title:"上网电价(元/kWh)",dataIndex:"feedInTariff",search:!1,resizable:!0,formatDecimal:4,width:120}];B(()=>{P(),N()}),Y(()=>{n.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&z()});const P=()=>{F({}).then(e=>{console.log("产权公司res=",e);let r=(e||[]).map(a=>({label:a.companyName,value:a.companyCode}));u.value=r})},N=e=>new Promise((t,r)=>{M({pid:e||"0"}).then(a=>{let o=_(a);console.log("cityData.value=",o),p.value=o,t(!0)}).catch(()=>{r()})}),_=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,_(t.subDistrict))}),e),d=i(!1),k=()=>{var a;let t={...(a=n==null?void 0:n.value)==null?void 0:a.getInitialFormStateNew(),queryParameterDesc:[]},r={reportType:9,reportParam:JSON.stringify(t)};d.value=!0,R(r).then(o=>{W.info("数据导出中，稍后请去导出中心查看"),d.value=!1}).catch(o=>{d.value=!1})},f=i([]),S=e=>{f.value=e||[]},z=()=>{var e;(e=n.value)==null||e.reload()},L=e=>{const t=new Map;return e.forEach(r=>{const a=T[r.level-1];if(t.has(a))t.get(a).push(r.value);else{let o=[];o.push(r.value),t.set(a,o)}}),Object.fromEntries(t)},y=(e,t,r)=>!e||!t?[]:(e.forEach(a=>{t.find(s=>a.value===s)&&r.push(a),a.children&&a.children.length>0&&y(a.children,t,r)}),r),E=(e,t)=>new Promise(r=>{const a=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[0]:"",o=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[1]:"";let s={delStatus:0,noJoin:!0,startTime:a,endTime:o};const g=y(p.value,e==null?void 0:e.cityTree,[]);let l=L(g);e==null||delete e.datongrd;const b={...s,...e,...l};r(b)});return(e,t)=>{const r=A,a=K,o=J,s=j;return x(),Q("div",U,[C("div",$,[m(s,{columns:I,ref_key:"actionRef",ref:n,request:h(H),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:S,"before-query-params":E},{tableHeader:c(()=>[m(a,null,{default:c(()=>[h(f).length>0?(x(),V(r,{key:0,type:"primary",loading:h(d),onClick:k},{default:c(()=>[D("导出")]),_:1},8,["loading"])):O("",!0)]),_:1})]),projectCompanyCodeListRender:c(({column:g,record:l,index:b})=>[m(o,null,{title:c(()=>[D(w(l.projectCompanyName),1)]),default:c(()=>[C("span",X,w(l.projectCompanyName),1)]),_:2},1024)]),_:1},8,["request"])])])}}});const de=G(Z,[["__scopeId","data-v-61676d91"]]);export{de as default};
