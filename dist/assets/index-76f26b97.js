import{_ as B}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as F,I as O,r as o,bQ as P,T as A,o as V,D as Y,at as j,au as K,a as I,v as Q,g as C,f as p,e as l,u as z,b as U,h as b,y as W,i as N,q as $,bg as G,bn as H,_ as J}from"./index-db94d997.js";import{g as X,e as Z}from"./index-92199609.js";import{_ as ee}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const te={class:"areaPrice"},ae={class:"areaPrice_table"},se={class:"text-hide"},re=F({__name:"index",setup(le){const T=O();o(!1);const n=o(),f=o([]),x=o([]),m=P(),r=o((m==null?void 0:m.stationUniqueId)||""),D=({id:t})=>y(t);let c="",_="";const S=[{title:"行政区划",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:f,valueTreeLoad:D,onSelect:(t,e,a)=>{var d,u;const s=((u=(d=a==null?void 0:a.triggerNode)==null?void 0:d.props)==null?void 0:u.level)||1;c=["provinceCode","cityCode","areaCode","town","vil"][s-1],_=t},width:150,resizable:!0,hideInTable:!0,order:4},{title:"电站编号",dataIndex:"stationUniqueId",search:!0,resizable:!0,width:120,fixed:"left",order:2},{title:"业主名称",dataIndex:"stationName",search:!0,resizable:!0,fixed:"left",width:120,order:3},{title:"产权公司",key:"ownerCompanyCodeList",dataIndex:"ownerCompanyCodeList",valueType:"multipleSelect",maxTagCount:1,width:160,valueEnum:x,fixed:"left",order:1,render:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:100},{title:"详细地址",dataIndex:"projectLocation",search:!1,resizable:!0,width:120},{title:"电站类型",dataIndex:"businessStationType",valueType:"select",valueEnum:A("EAM_POWER_STATION_TYPE_CONDITION"),search:!1,resizable:!0,width:100},{title:"并网时间",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",search:!1,resizable:!0,width:100},{title:"装机容量(kW)",dataIndex:"capins",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"组件型号",dataIndex:"zjModels",resizable:!0,search:!1,width:120},{title:"组件配置数量",dataIndex:"zjConfigureNum",resizable:!0,search:!1,width:120},{title:"组件确认数量",dataIndex:"zjConfirmNum",resizable:!0,search:!1,width:120},{title:"逆变器型号",dataIndex:"nbqModels",resizable:!0,search:!1,width:120},{title:"逆变器配置数量",dataIndex:"nbqConfigureNum",resizable:!0,search:!1,width:120},{title:"逆变器确认数量",dataIndex:"nbqConfirmNum",resizable:!0,search:!1,width:120},{title:"逆变器直流侧容量",dataIndex:"directCapins",resizable:!0,search:!1,width:120},{title:"逆变器交流侧容量",dataIndex:"alternatingCapins",resizable:!0,search:!1,width:120},{title:"逆变器厂家",dataIndex:"manufacturers",resizable:!0,search:!1,width:120},{title:"逆变器SN",dataIndex:"deviceSn",resizable:!0,search:!1,width:120},{title:"逆变器品牌",dataIndex:"deviceBrand",resizable:!0,search:!1,width:120},{title:"电表箱型号",dataIndex:"dbxModels",resizable:!0,search:!1,width:120},{title:"电表箱配置数量",dataIndex:"dbxConfigureNum",resizable:!0,search:!1,width:120},{title:"电表箱确认数量",dataIndex:"dbxConfirmNum",resizable:!0,search:!1,width:120},{title:"设备故障次数",dataIndex:"gzcs",resizable:!0,search:!1,width:120},{title:"设备维护次数",dataIndex:"whcs",resizable:!0,search:!1,width:120},{title:"设备变更次数",dataIndex:"bgcs",resizable:!0,search:!1,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:120}];V(()=>{console.log("设备台账onMounted"),k(),y(),r!=null&&r.value?n.value.setFormParams({stationUniqueId:r.value},!0):v()}),Y(()=>{n.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&(console.log("设备台账onActivated_delStatus"),r!=null&&r.value?n.value.setFormParams({stationUniqueId:r.value},!0):v())});const k=()=>{j({}).then(t=>{console.log("产权公司res=",t);let a=t.map(s=>({label:s.companyName,value:s.companyCode}));x.value=a})},y=t=>new Promise((e,a)=>{K({pid:t||"0"}).then(s=>{console.log("行政区res=",s);let i=w(s);f.value=i,console.log(f.value),e(!0)}).catch(()=>{a()})}),w=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,w(e.subDistrict))}),t),q=()=>{var e;let t=(e=n.value)==null?void 0:e.getInitialFormStateNew();Z(t)},E=t=>{T.push({path:"/pAssetManage/assetManage/device/detail",state:{pdata:$.cloneDeep(t)}})},g=o([]),M=t=>{g.value=t||[]},v=()=>{var t;(t=n.value)==null||t.reload()},L=(t,e)=>{let a={};return new Promise(s=>{e&&e.hasOwnProperty("cityTree")&&!(e!=null&&e.cityTree)&&(c="",_=""),c&&(a={[c]:_});const i={...a,...t,noJoin:!0,delStatus:0};s(i)})};return(t,e)=>{const a=G,s=ee,i=H,d=B;return I(),Q("div",te,[C("div",ae,[p(d,{columns:S,ref_key:"actionRef",ref:n,request:z(X),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:M,"before-query-params":L,"default-query":!1},{tableHeader:l(()=>[p(s,null,{default:l(()=>[z(g).length>0?(I(),U(a,{key:0,type:"primary",onClick:q},{default:l(()=>[b("导出")]),_:1})):W("",!0)]),_:1})]),ownerCompanyCodeListRender:l(({column:u,record:h,index:R})=>[p(i,null,{title:l(()=>[b(N(h.ownerCompanyName),1)]),default:l(()=>[C("span",se,N(h.ownerCompanyName),1)]),_:2},1024)]),actionRender:l(({column:u,record:h,index:R})=>[p(a,{type:"link",size:"small",onClick:ie=>E(h)},{default:l(()=>[b("故障及维护记录")]),_:2},1032,["onClick"])]),_:1},8,["request"])])])}}});const be=J(re,[["__scopeId","data-v-193b8e1b"]]);export{be as default};
