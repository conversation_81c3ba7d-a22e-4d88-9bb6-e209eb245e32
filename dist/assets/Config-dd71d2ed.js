import{_ as qe}from"./index-e7bdfdf4.js";import{_ as Fe}from"./index-4fa991ee.js";import{d as Ke,c9 as Ae,r,q as x,K as X,o as Me,a as p,v as _,f as i,e as l,u as o,h,y as S,g as c,i as w,cc as $,s as Oe,W as re,A as ce,bc as K,cd as J,k as fe,bO as W,ce as Xe,bd as Ye,cf as Ze,bg as Ue,aw as ze,ax as Be,bn as Ve,ay as Ge,p as $e,j as Je,_ as We}from"./index-db94d997.js";import{s as Qe,a as et,b as tt,c as at,d as ot,e as st}from"./index-2196a4be.js";import{P as lt,X as pe,D as _e,w as nt}from"./weiZhi-78534cab.js";import{D as it}from"./dayjs-a8e42122.js";import{_ as dt}from"./index-39334618.js";import{_ as ut}from"./index-83ca18bc.js";import"./CaretUpOutlined-7e71a64b.js";import"./index-4a280682.js";import"./customParseFormat-ed0c33ac.js";const he=A=>($e("data-v-d524e3e7"),A=A(),Je(),A),rt={class:"fixedEdit"},ct=he(()=>c("div",{style:{color:"#000","font-family":"PingFang SC","font-size":"18px","font-style":"normal","font-weight":"500","line-height":"22px","margin-bottom":"16px"}},null,-1)),ft={key:0,style:{display:"flex"}},pt={key:0,style:{display:"flex","justify-content":"flex-end"}},_t={class:"table_wrap"},ht={class:"item_table"},yt={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},mt={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},vt={class:"table_wrap"},gt={class:"item_table"},wt={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},Ct={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},bt={class:"table_wrap"},kt={class:"item_table"},xt={style:{"margin-bottom":"24px"}},St=he(()=>c("div",{style:{"align-self":"center"}},"目标年份：",-1)),Dt={class:"text-hide"},Nt={class:"text-hide"},Et={style:{display:"flex"}},Lt={class:"table_wrap"},Tt={key:0},It={class:"fileTableAction"},Pt=["src"],Ht=["src"],Rt=["src"],jt=["src"],qt=["src"],Ft=["src"],Kt=["src"],At={class:"ellipsis"},Mt={key:1},Ot={key:2},Xt=Ke({__name:"Config",props:{typeTitle:{},reloadTable:{type:Function}},emits:["update:modelValue"],setup(A,{emit:Yt}){const{showModal:ye,setFileState:me}=Ae(),Q=e=>{e===1?v.value.push({}):e===2&&C.value.push({})},Y=(()=>new Array(12).fill(1).map((e,t)=>({mKey:t+1,radiate:"",eqProportion:"",adjust:""})))();console.log("monthData=",Y);const ee=[{year:"去年(m)",history:"",status:""},{year:"前1年(n)",history:"",status:""},{year:"前2年(p)",history:"",status:""}],f=r(),D=r([]),M=r(Y),j=r(ee),v=r([]),C=r(x.cloneDeep(Y)),N=r(x.cloneDeep(ee)),I=r(!1),P=r(!1),E=r(!1),L=r(!1),T=r(!1),b=r(!1),q=r(!1),te=r(!1),ae=r(!1),Z=r(!1),H=r(""),g=X({defaultShow_1:!0,defaultShow_2:!0,defaultShow_3:!0,defaultShow_4:!0,isHideSwitch_1:!1,isHideSwitch_2:!1,isHideSwitch_3:!1,isHideSwitch_4:!1}),ve=X([{title:"辐照月度权重(X)",dataIndex:"weightX",width:140,isEdit:!0,dataType:"number",min:0,max:1,rule:{required:!0,message:"",noAllowZero:!0},formatFixed:!0},{title:"历史电量月度权重(Y)",dataIndex:"weightY",width:140,isEdit:!0,dataType:"number",min:0,max:1,rule:{required:!0,message:"",noAllowZero:!0},formatFixed:!0},{title:"人工调整权重(Z)",dataIndex:"weightZ",width:140,isEdit:!0,dataType:"number",min:0,max:1,rule:{required:!0,message:"",noAllowZero:!0},formatFixed:!0}]),oe=X([{title:"月份",unit:"月",dataIndex:"mKey",width:100,isEdit:!1,rule:{required:!0,message:""},dataType:"number"},{title:"辐照月度占比",dataIndex:"radiate",width:300,isEdit:!1,dataType:"number",unit:"%",formatDecimal:2},{title:"历史电量占比",dataIndex:"eqProportion",width:300,isEdit:!1,dataType:"number",unit:"%",formatDecimal:2},{title:"人工调整占比",dataIndex:"adjust",width:300,isEdit:!0,dataType:"number",unit:"%",min:0,max:100,formatDecimal:2}]),ge=X([{title:"年份",dataIndex:"year",width:140},{title:"历史电量系数",key:"history",dataIndex:"history",width:140,isEdit:!0,min:0,max:1,dataType:"number",render:!0},{title:"月份是否完整",key:"status",dataIndex:"status",width:140,render:!0}]),U=e=>{e===1?(I.value=!0,(!v.value||v.value.length===0)&&v.value.push({})):e===2?P.value=!0:e===3&&(E.value=!0)},z=e=>{e===1?(te.value=!1,I.value=!1,v.value=x.cloneDeep(D.value)):e===2?(ae.value=!1,P.value=!1,C.value=x.cloneDeep(M.value)):e===3&&(Z.value=!1,E.value=!1,N.value=x.cloneDeep(j.value))},B=e=>{if(e===1){const t=xe(D.value,v.value);console.log("删除后的data:",t),Ce(t)}else e===2?(console.log("dataListClone2.value=",C.value),be(C.value)):e===3&&ke();console.log("infoData.value=",f.value)},O=(e,t)=>{console.log("itemKey=",e),console.log("defaultShow=",t),g["defaultShow_"+e]=!t},we=(e,t)=>{let a=!0;return e.forEach(u=>{t.forEach(d=>{var s,y,k;(s=d==null?void 0:d.rule)!=null&&s.required&&(!u[d.dataIndex]||(y=d==null?void 0:d.rule)!=null&&y.noAllowZero&&u[d.dataIndex]==="0"||!u[d.dataIndex]&&!((k=d==null?void 0:d.rule)!=null&&k.noAllowZero)&&u[d.dataIndex]!="0")&&(a=!1)})}),a},Ce=(e=[])=>{let t=e.map(s=>({id:s.id,weightX:s.weightX,weightY:s.weightY,weightZ:s.weightZ,companyCode:f.value.companyCode,companyName:f.value.companyName})),a=t&&t.length>0?t[0]:{};const d=[a.weightX,a.weightY,a.weightZ].reduce((s,y)=>{let k=s&&!isNaN(Number(s))?Number(s):0,F=y&&!isNaN(Number(y))?Number(y):0;return ce(k,F)});if(console.log("sum=",d),d!==1){K.error("权重合计有误，请调整");return}console.log("params=",a),L.value=!0,Qe(a).then(s=>{console.log("res"),K.success({content:"保存成功"}),L.value=!1,I.value=!1,D.value=e,console.log("dataList1.value=",D.value),ne()}).catch(s=>{console.log("err=",s),L.value=!1})},be=e=>{if(!we(e,oe))return;console.log("list=",e);const u=e.map(s=>s.adjust).reduce((s,y)=>{let k=s&&!isNaN(Number(s))?Number(s):0,F=y&&!isNaN(Number(y))?Number(y):0;return ce(k,F)});if(console.log("sum=",u),u!==100){K.error("权重合计有误，请调整");return}const d=e.map(s=>(s.radiate=s.radiate?J(s.radiate,100):s.radiate,s.eqProportion=s.eqProportion?J(s.eqProportion,100):s.eqProportion,s.adjust=s.adjust?J(s.adjust,100):s.adjust,{...s,companyCode:f.value.companyCode,companyName:f.value.companyName}));console.log("newList=",d),T.value=!0,et(d).then(s=>{console.log("res"),T.value=!1,K.success({content:"保存成功"}),P.value=!1,M.value=e,V()}).catch(s=>{console.log("err=",s),T.value=!1})},ke=()=>{Z.value=!0;let t={yKey:H.value?Number(fe(H.value).format("YYYY")):"",yearM:N.value[0].history,yearN:N.value[1].history,yearP:N.value[2].history,companyCode:f.value.companyCode,companyName:f.value.companyName};b.value=!0,tt(t).then(a=>{console.log("res"),K.success({content:"保存成功"}),b.value=!1,E.value=!1,j.value=N.value,console.log("dataList3.value=",j.value),V(),ie()}).catch(a=>{console.log("err=",a),b.value=!1})},se=(e,t,a)=>{if(console.log("index=",a),e===1){let u=v.value.filter((d,s)=>s!=a);v.value=u}else e===2&&(C.value=C.value.filter((u,d)=>d!=a))},xe=(e,t)=>{let a=[];return e.forEach(u=>{let d=0;t.forEach(s=>{u.id===s.id&&(a.push({...s,delStatus:0}),d++)}),d===0&&a.push({...u,delStatus:1})}),t.forEach(u=>{u.id||a.push({...u,delStatus:0})}),a},Se=r([{title:"序号",dataIndex:"id",width:60},{title:"文档名称",dataIndex:"fileName",ellipsis:!0,width:300},{title:"操作",key:"action",width:80}]);let le=r([]);const De=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],R=e=>{if(!e)return"";var t=e.match(/\.([^.]+)$/);return t?t[1].toUpperCase():""},Ne=()=>{ne(),V(),ie(),G()},ne=()=>{L.value=!0,at({companyCode:f.value.companyCode}).then(e=>{console.log("因子权重查询res=",e),L.value=!1;let t=[];e&&t.push(e),D.value=t,v.value=x.cloneDeep(D.value)}).catch(e=>{console.log("因子权重查询err=",e),L.value=!1})},V=()=>{T.value=!0,ot({companyCode:f.value.companyCode}).then(e=>{if(console.log("月度占比：",e),T.value=!1,e&&e.length===12){const t=e.map(a=>(a.radiate=a.radiate?W(a.radiate,100):a.radiate,a.eqProportion=a.eqProportion?W(a.eqProportion,100):a.eqProportion,a.adjust=a.adjust?W(a.adjust,100):a.adjust,a));M.value=t,C.value=x.cloneDeep(t)}}).catch(e=>{console.log("月度占比查询err=",e),T.value=!1})},Ee=e=>{const t=[];return t.push({year:"去年(m)",history:e==null?void 0:e.yearM,status:e==null?void 0:e.mStatus}),t.push({year:"前1年(n)",history:e==null?void 0:e.yearN,status:e==null?void 0:e.nStatus}),t.push({year:"前2年(p)",history:e==null?void 0:e.yearP,status:e==null?void 0:e.pStatus}),t},ie=()=>{b.value=!0,st({companyCode:f.value.companyCode}).then(e=>{if(b.value=!1,console.log("楼层3-历史电量占比：",e),e){const t=Ee(e);H.value=e!=null&&e.yKey?fe((e==null?void 0:e.yKey)+""):"",j.value=t,N.value=x.cloneDeep(t)}}).catch(e=>{b.value=!1})},G=()=>{q.value=!0,Xe({businessType:2,relationCode:f.value.companyCode,delStatus:0,companyCode:f.value.companyCode}).then(e=>{console.log("文件列表：",e),q.value=!1,le.value=e}).catch(e=>{console.log("查询失败err:",e),q.value=!1})};Me(()=>{const e=history.state.pdata;console.log("pdata=",e),f.value=e,Ne()});const Le=()=>{Te()},Te=()=>{me({title:"上传",importUrl:"/web/oss/file/v1/uploadFile",accept:".doc,.docx,.pdf",upLoadSuccess:u=>{console.log("上传成功",u),G()},downloadText:"上传附件",onClickDownload:()=>{console.log("点击下载")},onClickResultOk:u=>{console.log("导入结果点击关闭调用",u)},visible:!0,isTemplate:!1,fileSize:30,data:{businessType:2,relationCode:f.value.companyCode,companyCode:f.value.companyCode}}),ye()},Ie=async e=>{Ye({fileId:e.id,companyCode:f.value.companyCode})},Pe=e=>{console.log(e),q.value=!0,Ze({fileId:e.id,companyCode:f.value.companyCode}).then(t=>{console.log("删除成功res:",t),G()}).catch(t=>{console.log("删除失败err:",t),q.value=!1})};return(e,t)=>{const a=Ue,u=dt,d=Fe,s=qe,y=it,k=ze,F=Be,de=Ve,He=ut,Re=Ge;return p(),_("div",rt,[ct,i(s,{title:"因子权重配置",itemKey:"1",defaultShow:o(g).defaultShow_1,isHideSwitch:o(g).isHideSwitch_1,onChangeKey:O},{btnRender:l(()=>[o(I)?S("",!0):(p(),_("div",ft,[i(a,{type:"primary",onClick:t[0]||(t[0]=()=>U(1))},{default:l(()=>[h("编辑")]),_:1})]))]),footer:l(()=>[o(I)?(p(),_("div",pt,[i(u,null,{default:l(()=>[i(a,{onClick:t[1]||(t[1]=()=>z(1))},{default:l(()=>[h("取消")]),_:1}),i(a,{type:"primary",onClick:t[2]||(t[2]=()=>B(1))},{default:l(()=>[h("保存")]),_:1})]),_:1})])):S("",!0)]),default:l(()=>[c("div",_t,[c("div",ht,[i(d,{bordered:!1,scroll:{y:500},columns:o(ve),dataListClone:o(v),dataList:o(D),isTableEdit:o(I),loading:o(L),startCheck:o(te),onHandleClickAddTable:t[3]||(t[3]=()=>Q(1)),showAdd:!1},{taxRateRender:l(({record:n})=>[c("div",null,w(o($)(n.taxRate/100))+"%",1)]),taxMoneyRender:l(({record:n})=>[c("div",null,w(o($)(n.taxMoney)),1)]),actionRender:l(({record:n,index:m})=>[i(a,{size:"small",type:"link",onClick:()=>se(1,n,m)},{default:l(()=>[h(" 删除 ")]),_:2},1032,["onClick"])]),_:1},8,["columns","dataListClone","dataList","isTableEdit","loading","startCheck"])])])]),_:1},8,["defaultShow","isHideSwitch"]),i(s,{title:"月度占比",itemKey:"2",defaultShow:o(g).defaultShow_2,isHideSwitch:o(g).isHideSwitch_2,onChangeKey:O},{btnRender:l(()=>[o(P)?S("",!0):(p(),_("div",yt,[i(a,{class:"btn add",type:"primary",onClick:t[4]||(t[4]=()=>U(2))},{default:l(()=>[h("编辑")]),_:1})]))]),footer:l(()=>[o(P)?(p(),_("div",mt,[i(u,null,{default:l(()=>[i(a,{onClick:t[5]||(t[5]=()=>z(2))},{default:l(()=>[h("取消")]),_:1}),i(a,{type:"primary",onClick:t[6]||(t[6]=()=>B(2))},{default:l(()=>[h("保存")]),_:1})]),_:1})])):S("",!0)]),default:l(()=>[c("div",vt,[c("div",gt,[i(d,{columns:o(oe),dataListClone:o(C),dataList:o(M),isTableEdit:o(P),loading:o(T),startCheck:o(ae),showAdd:!1},{salvageRateRender:l(({record:n})=>[c("div",null,w(o($)(n.salvageRate/100))+"%",1)]),actionRender:l(({record:n,index:m})=>[i(a,{size:"small",type:"link",onClick:()=>se(2,n,m)},{default:l(()=>[h(" 删除 ")]),_:2},1032,["onClick"])]),_:1},8,["columns","dataListClone","dataList","isTableEdit","loading","startCheck"])])])]),_:1},8,["defaultShow","isHideSwitch"]),i(s,{title:"历史电量占比",itemKey:"3",defaultShow:o(g).defaultShow_3,isHideSwitch:o(g).isHideSwitch_3,onChangeKey:O},{btnRender:l(()=>[o(E)?S("",!0):(p(),_("div",wt,[i(a,{type:"primary",onClick:t[7]||(t[7]=()=>U(3))},{default:l(()=>[h("编辑")]),_:1})]))]),footer:l(()=>[o(E)?(p(),_("div",Ct,[i(u,null,{default:l(()=>[i(a,{onClick:t[8]||(t[8]=()=>z(3))},{default:l(()=>[h("取消")]),_:1}),i(a,{type:"primary",onClick:t[9]||(t[9]=()=>B(3))},{default:l(()=>[h("保存")]),_:1})]),_:1})])):S("",!0)]),default:l(()=>[c("div",bt,[c("div",kt,[i(d,{bordered:!1,scroll:{y:500},columns:o(ge),dataListClone:o(N),dataList:o(j),isTableEdit:o(E),loading:o(b),startCheck:o(Z),onHandleClickAddTable:t[11]||(t[11]=()=>Q(3)),showAdd:!1},{tableHeader:l(()=>[c("div",xt,[i(F,{span:24},{default:l(()=>[St,i(k,{span:4},{default:l(()=>[i(y,{picker:"year",style:{width:"100%"},value:o(H),"onUpdate:value":t[10]||(t[10]=n=>Oe(H)?H.value=n:null),disabled:!o(E)},null,8,["value","disabled"])]),_:1})]),_:1})])]),historyRender:l(({record:n})=>[i(de,null,{title:l(()=>[h(w(n!=null&&n.history?o(re)(n.history):""),1)]),default:l(()=>[c("span",Dt,w(n!=null&&n.history?o(re)(n.history):""),1)]),_:2},1024)]),statusRender:l(({record:n})=>[i(de,null,{title:l(()=>[h(w((n==null?void 0:n.status)==="1"?"YES":n.status==="0"?"NO":""),1)]),default:l(()=>[c("span",Nt,w((n==null?void 0:n.status)==="1"?"YES":n.status==="0"?"NO":""),1)]),_:2},1024)]),_:1},8,["columns","dataListClone","dataList","isTableEdit","loading","startCheck"])])])]),_:1},8,["defaultShow","isHideSwitch"]),i(s,{title:"附件",itemKey:"4",defaultShow:o(g).defaultShow_4,isHideSwitch:o(g).isHideSwitch_4,onChangeKey:O},{btnRender:l(()=>[c("div",Et,[i(a,{type:"primary",onClick:Le},{default:l(()=>[h("上传")]),_:1})])]),default:l(()=>[c("div",Lt,[i(Re,{columns:o(Se),"data-source":o(le),pagination:!1,loading:o(b)},{bodyCell:l(({column:n,text:m,record:ue,index:je})=>[n.dataIndex=="fileName"?(p(),_("div",Tt,[c("div",It,[De.includes(R(m))?(p(),_("img",{key:0,src:m},null,8,Pt)):R(m)=="PDF"?(p(),_("img",{key:1,src:o(lt)},null,8,Ht)):R(m)=="XLS"?(p(),_("img",{key:2,src:o(pe)},null,8,Rt)):R(m)=="XLSX"?(p(),_("img",{key:3,src:o(pe)},null,8,jt)):R(m)=="DOC"?(p(),_("img",{key:4,src:o(_e)},null,8,qt)):R(m)=="DOCX"?(p(),_("img",{key:5,src:o(_e)},null,8,Ft)):(p(),_("img",{key:6,src:o(nt)},null,8,Kt)),c("div",At,w(m),1)])])):n.dataIndex=="id"?(p(),_("div",Mt,w(je+1),1)):S("",!0),n.key=="action"?(p(),_("div",Ot,[i(u,null,{default:l(()=>[i(a,{size:"small",type:"link",onClick:()=>Ie(ue)},{default:l(()=>[h(" 下载 ")]),_:2},1032,["onClick"]),i(He,{title:"确认删除?",onConfirm:Ut=>Pe(ue)},{default:l(()=>[i(a,{size:"small",type:"link"},{default:l(()=>[h(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)])):S("",!0)]),_:1},8,["columns","data-source","loading"])])]),_:1},8,["defaultShow","isHideSwitch"])])}}});const oa=We(Xt,[["__scopeId","data-v-d524e3e7"]]);export{oa as default};
