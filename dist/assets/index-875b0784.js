import{_ as I}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as q,I as L,r as l,o as P,D as z,a as m,v as E,g as y,f as o,e as t,u as f,b as M,h as c,y as R,i as h,q as x,at as T,bg as V,bn as F,_ as Q}from"./index-db94d997.js";import{y as W,e as A}from"./index-0c818432.js";import{_ as G}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const H={class:"areaPrice"},J={class:"areaPrice_table"},O={class:"text-hide"},Y=q({__name:"index",setup($){const u=L(),r=l();l(!1);const d=l([]),b=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:3,valueEnum:d,width:120,resizable:!0,render:!0},{title:"本年基准电量(kWh)",dataIndex:"eq",search:!1,resizable:!0,width:130,formatMoney:!0},{title:"次年基准电量(kWh)",dataIndex:"nextEq",search:!1,formatMoney:!0,resizable:!0,width:130},{title:"操作",key:"action",search:!1,width:50,render:!0,align:"center"}],g=()=>{var e;(e=r.value)==null||e.reload()},k=async e=>{u.push({path:"/financeManage/electricQuantity/yearBase/config",state:{pdata:x.cloneDeep(e)}})},C=async e=>{u.push({path:"/financeManage/electricQuantity/yearBase/more",state:{pdata:x.cloneDeep(e)}})},v=(e,s)=>new Promise(n=>{let a={delStatus:0,noJoin:!0};a={...a,...e},n(a)}),w=()=>{var s;let e=(s=r.value)==null?void 0:s.getInitialFormStateNew();A(e)},_=l([]),S=e=>{_.value=e||[]},B=()=>{T({}).then(e=>{let n=(e||[]).map(a=>({label:a.companyName,value:a.companyCode}));d.value=n})};return P(()=>{B()}),z(()=>{r.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&g()}),(e,s)=>{const n=V,a=G,D=F,N=I;return m(),E("div",H,[y("div",J,[o(N,{columns:b,ref_key:"actionRef",ref:r,request:f(W),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:S,"before-query-params":v},{tableHeader:t(()=>[o(a,null,{default:t(()=>[f(_).length>0?(m(),M(n,{key:0,type:"primary",onClick:w},{default:t(()=>[c("导出")]),_:1})):R("",!0)]),_:1})]),companyCodeListRender:t(({column:i,record:p,index:j})=>[o(D,null,{title:t(()=>[c(h(p.companyName),1)]),default:t(()=>[y("span",O,h(p.companyName),1)]),_:2},1024)]),actionRender:t(({record:i,index:p})=>[o(a,null,{default:t(()=>[o(n,{size:"small",type:"link",onClick:()=>k(i)},{default:t(()=>[c(" 参数配置 ")]),_:2},1032,["onClick"]),o(n,{size:"small",type:"link",onClick:()=>C(i)},{default:t(()=>[c(" 更多年份 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])])}}});const se=Q(Y,[["__scopeId","data-v-0648077b"]]);export{se as default};
