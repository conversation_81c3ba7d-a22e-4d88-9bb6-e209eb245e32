import{s as Le}from"./search-5ccd1e6d.js";import{c as de,b as Ve,d as je,g as ze}from"./index-96df45ba.js";import{f as _,L as Je,eH as Me,d as Ae,r as p,I as Be,K as He,c as Fe,w as Qe,o as qe,a as u,v as h,g as m,e as o,u as a,h as C,i,s as Q,b as P,y as pe,F as L,x as q,bi as re,bc as ae,q as V,be as Ge,bf as We,aw as Xe,bg as Ye,ax as Ze,bh as et,bn as tt,eI as at,ay as nt,eJ as st,eK as lt,p as ot,j as ct,_ as it}from"./index-db94d997.js";import{E as ut}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as dt}from"./index-39334618.js";function _e(v){for(var f=1;f<arguments.length;f++){var g=arguments[f]!=null?Object(arguments[f]):{},I=Object.keys(g);typeof Object.getOwnPropertySymbols=="function"&&(I=I.concat(Object.getOwnPropertySymbols(g).filter(function(k){return Object.getOwnPropertyDescriptor(g,k).enumerable}))),I.forEach(function(k){pt(v,k,g[k])})}return v}function pt(v,f,g){return f in v?Object.defineProperty(v,f,{value:g,enumerable:!0,configurable:!0,writable:!0}):v[f]=g,v}var ne=function(f,g){var I=_e({},f,g.attrs);return _(Je,_e({},I,{icon:Me}),null)};ne.displayName="RightOutlined";ne.inheritAttrs=!1;const rt=ne,me=v=>(ot("data-v-5ea4985c"),v=v(),ct(),v),_t={class:"card-cu bg-white"},mt={class:"company-tree-box bg-white"},ft={class:"title-box"},ht=me(()=>m("div",{class:"title"},"全部公司",-1)),vt={class:"company-tree"},gt={key:0,class:"custom-tree-title"},yt={style:{color:"#f50"}},xt={key:1,class:"custom-tree-title"},kt={class:"user-list-box bg-white"},bt=me(()=>m("div",{class:"table-title"},"数据权限用户列表",-1)),Ct={style:{display:"flex","flex-direction":"row","align-items":"center","justify-content":"space-between"}},It=["src"],Nt={key:0,class:"table_container"},Ot=["onClick"],wt=["onClick"],St=["onClick"],Dt=["onClick"],Pt={class:"text-hide"},Tt={key:1,class:"table_container"},Rt=["onClick"],Et=["onClick"],Ut=["onClick"],$t=["onClick"],Kt={class:"text-hide"},Lt={key:0},Vt={style:{color:"#f50"}},jt={key:1},zt=Ae({__name:"index",setup(v){const f=p(),g=Be(),I=p(!1),k=He({companyName:""}),T=p(""),fe=()=>{let e=[];k.companyName&&(e=Z.map(t=>t.name.indexOf(k.companyName)>-1?H(t.parentId,K.value):null).filter((t,n,d)=>t&&d.indexOf(t)===n)),B.value=e,T.value=k.companyName,X.value=!0};let U=null;const $=p(!1),he=(e,t)=>{U=e[0],de({companyId:U}).then(n=>{n.code==200&&!n.data?$.value=!1:$.value=!0}),z()},j=p(""),se=()=>{U&&z()},ve=()=>{x.value.length&&re.confirm({title:"确认提示",icon:_(ut),content:"确认要取消授权吗？",onOk:ge,onCancel(){console.log("Cancel")}})},ge=()=>{const e={companyInfoId:U,userIds:x.value};Ve(e).then(t=>{ae.success("取消关联成功"),w.value=[],x.value=[],z()})},le=p([{title:"用户姓名",dataIndex:"userName",ellipsis:!0,width:150},{title:"手机号",dataIndex:"mobile",width:120},{title:"归属公司",dataIndex:"companyName",ellipsis:!0,width:320}]),R=p({pageNum:1,pageSize:10}),G=p(0),W=p([]),oe=(e,t,n,{currentDataSource:d})=>{R.value.pageNum=e==null?void 0:e.current,R.value.pageSize=e==null?void 0:e.pageSize,z()},z=async()=>{const e={companyInfoId:U,searchName:j.value,pageNum:R.value.pageNum,pageSize:R.value.pageSize},t=await je(e);W.value=t.records,G.value=t.total},J=e=>{g.push({path:"/system/user/detail",state:{pdata:V.cloneDeep(e)}})},M=e=>{g.push({path:"/system/company/detail/list",state:{pdata:V.cloneDeep(e)}})},x=p([]),w=p([]),ye=(e,t,n,d)=>{t?(x.value.push(e.id),w.value.push(e)):(x.value=x.value.filter(c=>c!==e.id),w.value=w.value.filter(c=>c.id!==e.id))},xe=(e,t,n)=>{const d=n.map(c=>c.id);e?(x.value=x.value.concat(d),w.value=w.value.concat(n)):(x.value=x.value.filter(c=>!d.includes(c)),w.value=w.value.filter(c=>!d.includes(c.id)))},ke=Fe(()=>({selectedRowKeys:x,onSelect:ye,onSelectAll:xe})),A=p(!1),be=()=>{A.value=!0},Ce=(e,t)=>{const n=t.node.dataRef.id;de({companyId:n}).then(d=>{if(d.code==200&&!d.data){ae.error("该公司没有授权用户的权限！");const S=JSON.parse(JSON.stringify(N.value.checked));var c=S.indexOf(n);c>-1&&S.splice(c,1),setTimeout(()=>{N.value.checked=S},10)}})},Ie=()=>{N.value.checked&&N.value.checked.length>0?g.push({path:"/system/permission/detail",state:{pdata:V.cloneDeep({authCompanyIds:N.value.checked})}}):ae.error("请选择需要授权用户的公司")},Ne=()=>{N.value={},F.value=[],b.value=""},b=p(""),B=p([]),X=p(!0),Oe=e=>{B.value=e,X.value=!1},H=(e,t)=>{let n;for(let d=0;d<t.length;d++){const c=t[d];c.children&&(c.children.some(S=>S.parentId===e)?n=c.id:H(e,c.children)&&(n=H(e,c.children)))}return n},N=p({}),Y=p(!0),F=p([1]),we=e=>{F.value=e,Y.value=!1};Qe(()=>b.value,e=>{let t=[];e&&(t=Z.map(n=>n.name.indexOf(e)>-1?H(n.parentId,ee.value):null).filter((n,d,c)=>n&&c.indexOf(n)===d)),F.value=t,Y.value=!0});const K=p([]);let Z=[];const ee=p([]),Se=async()=>{const e=await ze();K.value=e,ee.value=e,Z=ce(JSON.parse(JSON.stringify(K.value)));let t=[];ie(K.value,t),B.value=t},ce=e=>V.flattenDeep(V.map(e,t=>{const n=t.children||[];return delete t.children,[t].concat(ce(n))}));function ie(e,t){for(let n of e)n.children&&n.children.length>0&&(t.push(n.id),ie(n.children,t))}const E=e=>{const t=e||[];let n=[];for(let d of t){const c={companyName:d.companyName,id:d.companyInfoId};n.push(c)}return n};return qe(()=>{Se()}),(e,t)=>{const n=Ge,d=We,c=Xe,S=Ye,De=Ze,Pe=et,D=tt,Te=at,Re=dt,ue=nt,Ee=st,Ue=lt,$e=re;return u(),h(L,null,[m("div",_t,[_(Pe,{model:a(k),name:"formRef",ref:"formRef","label-col":{style:{width:"100px"}},autocomplete:"off"},{default:o(()=>[_(De,{gutter:24},{default:o(()=>[_(c,{span:8},{default:o(()=>[_(d,{label:"公司名称："},{default:o(()=>[_(n,{value:a(k).companyName,"onUpdate:value":t[0]||(t[0]=s=>a(k).companyName=s),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),_(c,{span:8},{default:o(()=>[_(S,{onClick:fe,type:"primary",loading:a(I)},{default:o(()=>[C("查询")]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1},8,["model"])]),m("div",{class:"content",ref_key:"permissionTableRef",ref:f},[m("div",mt,[m("div",ft,[ht,m("div",{class:"handle",onClick:be},[C(" 授权用户 "),_(a(rt))])]),m("div",vt,[_(Te,{showIcon:!1,"expanded-keys":a(B),"auto-expand-parent":a(X),onExpand:Oe,"tree-data":a(K),fieldNames:{children:"children",title:"name",key:"id"},onSelect:he},{title:o(({name:s})=>[_(D,null,{title:o(()=>[C(i(s),1)]),default:o(()=>[s&&s.indexOf(a(T))>-1?(u(),h("span",gt,[C(i(s.substring(0,s.indexOf(a(T))))+" ",1),m("span",yt,i(a(T)),1),C(" "+i(s.substring(s.indexOf(a(T))+a(T).length)),1)])):(u(),h("span",xt,i(s),1))]),_:2},1024)]),_:1},8,["expanded-keys","auto-expand-parent","tree-data"])])]),m("div",kt,[bt,m("div",Ct,[_(n,{style:{width:"282px"},value:a(j),"onUpdate:value":t[1]||(t[1]=s=>Q(j)?j.value=s:null),placeholder:"请输入",onPressEnter:se},{suffix:o(()=>[m("img",{src:a(Le),style:{width:"16px",height:"16px",cursor:"pointer"},onClick:se},null,8,It)]),_:1},8,["value"]),a($)?(u(),P(Re,{key:0},{default:o(()=>[_(S,{type:"primary",onClick:ve,disabled:!(a(x).length>0)},{default:o(()=>[C("取消关联")]),_:1},8,["disabled"])]),_:1})):pe("",!0)]),a($)?(u(),h("div",Nt,[_(ue,{columns:a(le),"data-source":a(W),loading:a(I),"row-key":"id",onChange:oe,rowSelection:a(ke),pagination:{current:a(R).pageNum,total:a(G),showTotal:s=>`共 ${s} 条`,size:"small",showQuickJumper:!0}},{bodyCell:o(({text:s,record:l,index:Ke,column:y})=>[y.dataIndex==="companyName"?(u(),P(D,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:r=>a(f)},{title:o(()=>[(u(!0),h(L,null,q(E(l==null?void 0:l.companyInfoUser),(r,O)=>(u(),h("span",{class:"light-name",key:O,onClick:te=>M(r)},i(r.companyName),9,Ot))),128))]),default:o(()=>[(u(!0),h(L,null,q(E(l==null?void 0:l.companyInfoUser),(r,O)=>(u(),h("span",{class:"light-name",key:O,onClick:te=>M(r)},i(r.companyName)+" "+i(E(l==null?void 0:l.companyInfoUser).length-1==O?"":"，"),9,wt))),128))]),_:2},1032,["getPopupContainer"])):y.dataIndex==="userName"?(u(),P(D,{key:1,placement:"topLeft",getPopupContainer:r=>a(f)},{title:o(()=>[m("span",{class:"light-name",onClick:r=>J(l)},i(l[y.dataIndex]),9,St)]),default:o(()=>[m("span",{class:"light-name",onClick:r=>J(l)},i(l[y.dataIndex]),9,Dt)]),_:2},1032,["getPopupContainer"])):(u(),P(D,{key:2},{title:o(()=>[C(i(l[y.dataIndex]),1)]),default:o(()=>[m("span",Pt,i(l[y.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","loading","rowSelection","pagination"])])):a($)?pe("",!0):(u(),h("div",Tt,[_(ue,{columns:a(le),"data-source":a(W),loading:a(I),onChange:oe,pagination:{current:a(R).pageNum,total:a(G),showTotal:s=>`共 ${s} 条`,size:"small",showQuickJumper:!0}},{bodyCell:o(({text:s,record:l,index:Ke,column:y})=>[y.dataIndex==="companyName"?(u(),P(D,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:r=>a(f)},{title:o(()=>[(u(!0),h(L,null,q(E(l==null?void 0:l.companyInfoUser),(r,O)=>(u(),h("span",{class:"light-name",key:O,onClick:te=>M(r)},i(r.companyName),9,Rt))),128))]),default:o(()=>[(u(!0),h(L,null,q(E(l==null?void 0:l.companyInfoUser),(r,O)=>(u(),h("span",{class:"light-name",key:O,onClick:te=>M(r)},i(r.companyName)+" "+i(E(l==null?void 0:l.companyInfoUser).length-1==O?"":"，"),9,Et))),128))]),_:2},1032,["getPopupContainer"])):y.dataIndex==="userName"?(u(),P(D,{key:1,placement:"topLeft",getPopupContainer:r=>a(f)},{title:o(()=>[m("span",{class:"light-name",onClick:r=>J(l)},i(l[y.dataIndex]),9,Ut)]),default:o(()=>[m("span",{class:"light-name",onClick:r=>J(l)},i(l[y.dataIndex]),9,$t)]),_:2},1032,["getPopupContainer"])):(u(),P(D,{key:2},{title:o(()=>[C(i(l[y.dataIndex]),1)]),default:o(()=>[m("span",Kt,i(l[y.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","loading","pagination"])]))])],512),_($e,{visible:a(A),"onUpdate:visible":t[4]||(t[4]=s=>Q(A)?A.value=s:null),title:"添加权限",okText:"授权用户",onOk:Ie,onCancel:Ne,width:"600px"},{default:o(()=>[_(Ee,{value:a(b),"onUpdate:value":t[2]||(t[2]=s=>Q(b)?b.value=s:null),style:{"margin-bottom":"8px"},placeholder:"请输入"},null,8,["value"]),_(Ue,{checkStrictly:!0,checkable:"",selectable:!1,checkedKeys:a(N),"onUpdate:checkedKeys":t[3]||(t[3]=s=>Q(N)?N.value=s:null),"auto-expand-parent":a(Y),"expanded-keys":a(F),onExpand:we,"tree-data":a(ee),fieldNames:{children:"children",title:"name",key:"id"},onCheck:Ce},{title:o(({name:s})=>[s&&s.indexOf(a(b))>-1?(u(),h("span",Lt,[C(i(s.substring(0,s.indexOf(a(b))))+" ",1),m("span",Vt,i(a(b)),1),C(" "+i(s.substring(s.indexOf(a(b))+a(b).length)),1)])):(u(),h("span",jt,i(s),1))]),_:1},8,["checkedKeys","auto-expand-parent","expanded-keys","tree-data"])]),_:1},8,["visible"])],64)}}});const Ft=it(zt,[["__scopeId","data-v-5ea4985c"]]);export{Ft as default};
