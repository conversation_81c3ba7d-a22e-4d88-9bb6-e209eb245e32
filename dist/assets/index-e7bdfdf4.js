import{d as f,o as p,a as t,v as a,g as o,i as d,y as l,b as c,u as r,a1 as i,n as y,_ as h}from"./index-db94d997.js";import{C as m,a as v}from"./CaretUpOutlined-7e71a64b.js";const S={class:"card"},w={class:"left_title"},g={key:0},k={class:"card_show_btn"},B={key:0,class:"card_content"},C=f({__name:"index",props:{title:{type:String,default:""},itemKey:{type:String,default:""},defaultShow:{type:Boolean,default:!1},changeKey:{type:Function,default:()=>{}},isHideSwitch:{type:Boolean,default:!1},isHideTitle:{type:Boolean,default:!1}},emits:["changeKey"],setup(n,{emit:u}){const e=n,_=()=>{u("changeKey",e.itemKey,e.defaultShow)};return p(()=>{}),(s,b)=>(t(),a("div",S,[o("div",{class:"card_title",style:y({"margin-bottom":e.defaultShow?"24px":"0px"})},[o("div",w,[n.isHideTitle?l("",!0):(t(),a("span",g,d(e.title),1)),e.isHideSwitch?l("",!0):(t(),a("div",{key:1,class:"switch",onClick:_},[o("div",null,d(e.defaultShow?"收起":"展开"),1),e.defaultShow?(t(),c(r(v),{key:1,style:{color:"#29cca0"}})):(t(),c(r(m),{key:0,style:{color:"#29cca0"}}))]))]),o("div",k,[o("div",null,[i(s.$slots,"btnRender",{},void 0,!0)]),i(s.$slots,"titleRight",{},void 0,!0)])],4),e.defaultShow?(t(),a("div",B,[i(s.$slots,"default",{},void 0,!0)])):l("",!0),o("div",null,[e.defaultShow?i(s.$slots,"footer",{key:0},void 0,!0):l("",!0)])]))}});const H=h(C,[["__scopeId","data-v-c8942b95"]]);export{H as _};
