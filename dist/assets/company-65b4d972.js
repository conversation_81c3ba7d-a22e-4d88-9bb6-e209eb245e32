import{_ as oa}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as sa,r as u,k as p,w as ia,o as ua,a as R,v as H,g as x,f as n,e as o,ai as pa,h as C,y as Q,b as G,z as $,c7 as ra,bc as da,at as ca,bI as ma,ee as _a,av as va,bJ as fa,bf as ya,bU as ga,aw as ha,bg as Ya,ax as ba,bh as ka,p as wa,j as Ca,_ as xa}from"./index-db94d997.js";import{a as Ma}from"./index-0f6c6517.js";import{R as Ta,D as La}from"./dayjs-a8e42122.js";import{_ as Da}from"./index-39334618.js";import{_ as Ia}from"./index-42d7fb9b.js";import"./CForm-ffa1b2bc.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const U=M=>(wa("data-v-64c206dc"),M=M(),Ca(),M),Sa={class:"wrapper"},Na={class:"cform-main bg-white"},ja=U(()=>x("span",{class:"weight4566"},"日维度",-1)),za=U(()=>x("span",{class:"weight4566"},"月维度",-1)),Ra=U(()=>x("span",{class:"weight4566"},"预算维度",-1)),Ua=U(()=>x("span",{style:{"margin-left":"5px"}},"全选",-1)),Ea=sa({__name:"company",setup(M){const a=u({dateType:1,dateDay:[p().subtract(10,"day").format("YYYY-MM-DD"),p().format("YYYY-MM-DD")],dateMon:[p().subtract(10,"month").format("YYYY-MM"),p().format("YYYY-MM")],monType:1,budget:p().format("YYYY"),companyList:[],pageNum:1,pageSize:10}),X=(s,{attrs:e})=>e.vnodes,T=u(!1),J=u(!1),Z=()=>{T.value?a.value.companyList=_.value.map(s=>s.value):a.value.companyList=[]};ia(()=>a.value.companyList.length,s=>{T.value=s===_.value.length,J.value=s>0&&s<_.value.length});const aa=()=>{a.value={dateType:1,dateDay:[p().subtract(10,"day").format("YYYY-MM-DD"),p().format("YYYY-MM-DD")],dateMon:[p().subtract(10,"month").format("YYYY-MM"),p().format("YYYY-MM")],monType:1,budget:p().format("YYYY"),companyList:[],pageNum:1,pageSize:10},W()},W=()=>{B()},A=u(0),E=u([]),K=u(0),P=u(),O=u({}),r=[{title:"产权公司",dataIndex:"ownerCompanyName",width:120,search:!1},{title:"省",dataIndex:"provinceName",width:120,search:!1},{title:"装机容量(万kW)",dataIndex:"capins",width:120,search:!1,formatMoney:!0},{title:"实际电量(万kWh)",key:"actualCapinList",width:120,search:!1,formatMoney:!0,align:"center",children:[]},{title:"上年同期电量(万kWh)",key:"lastYearCapinList",width:120,search:!1,align:"center",formatMoney:!0,children:[]},{title:"JYK电量(万kWh)",key:"jykcapinList",width:120,search:!1,align:"center",formatMoney:!0,hideInTable:!0,children:[]}],B=async()=>{var s,e,v,f,h,Y,y,D,I,b,S,N,k,g,j,z;(s=P.value)==null||s.setLoading(!0);try{const i={pageNum:a.value.pageNum,pageSize:a.value.pageSize,dateType:a.value.dateType,companyList:a.value.companyList};a.value.dateType===1&&(i.dayStart=((e=a.value)==null?void 0:e.dateDay[0])||"",i.dayEnd=((v=a.value)==null?void 0:v.dateDay[1])||""),a.value.dateType===2&&(i.monStart=((f=a.value)==null?void 0:f.dateMon[0])||"",i.monEnd=((h=a.value)==null?void 0:h.dateMon[1])||"",i.monType=a.value.monType),a.value.dateType===3&&(i.budget=((Y=a.value)==null?void 0:Y.budget)||"");const l=await Ma(i);O.value=i||{};const d=r.findIndex(t=>t.key==="actualCapinList");r[d].children=((I=(D=(y=l.result)==null?void 0:y[0])==null?void 0:D.actualCapinList)==null?void 0:I.map((t,c)=>({title:t.date,dataIndex:"actualCapinList_"+(c+1),align:"left",width:100})))||[];const la=r.findIndex(t=>t.key==="lastYearCapinList");r[la].children=((N=(S=(b=l.result)==null?void 0:b[0])==null?void 0:S.lastYearCapinList)==null?void 0:N.map((t,c)=>({title:t.date,dataIndex:"lastYearCapinList_"+(c+1),align:"left",width:100})))||[];const V=r.findIndex(t=>t.key==="jykcapinList");r[V].children=((j=(g=(k=l.result)==null?void 0:k[0])==null?void 0:g.jykcapinList)==null?void 0:j.map((t,c)=>({title:t.date,dataIndex:"jykcapinList_"+(c+1),align:"left",width:100})))||[],a.value.dateType===3?r[V].hideInTable=!1:r[V].hideInTable=!0,A.value=new Date().getTime(),E.value=l.result.map(t=>{var c,q,F;return(c=t==null?void 0:t.actualCapinList)==null||c.forEach((m,w)=>{t["actualCapinList_"+(w+1)]=m.capins?$(m.capins):""}),(q=t==null?void 0:t.lastYearCapinList)==null||q.forEach((m,w)=>{t["lastYearCapinList_"+(w+1)]=m.capins?$(m.capins):""}),(F=t==null?void 0:t.jykcapinList)==null||F.forEach((m,w)=>{t["jykcapinList_"+(w+1)]=m.capins?$(m.capins):""}),t}),K.value=l.total||0}finally{(z=P.value)==null||z.setLoading(!1)}},L=u(!1),ea=()=>{let s={...O.value,queryParameterDesc:[]},e={reportType:4,reportParam:JSON.stringify(s)};L.value=!0,ra(e).then(v=>{da.info("数据导出中，稍后请去导出中心查看"),L.value=!1}).catch(v=>{L.value=!1})},_=u([]),ta=()=>{ca({}).then(s=>{_.value=(s==null?void 0:s.map(e=>({label:e.companyName,value:e.companyCode})))||[]})},na=({current:s,pageSize:e})=>{a.value.pageNum=s||1,a.value.pageSize=e||10,B()};return ua(async()=>{ta(),await B()}),(s,e)=>{const v=Ta,f=ma,h=_a,Y=va,y=Da,D=La,I=fa,b=ya,S=ga,N=Ia,k=ha,g=Ya,j=ba,z=ka,i=oa;return R(),H("div",Sa,[x("div",Na,[n(z,{ref:"formRef",name:"basic",autocomplete:"off"},{default:o(()=>[n(b,{label:"时间选择",name:"stationCode",style:{"margin-bottom":"14px"}},{default:o(()=>[n(I,{value:a.value.dateType,"onUpdate:value":e[5]||(e[5]=l=>a.value.dateType=l)},{default:o(()=>[n(f,{value:1,style:{"margin-bottom":"10px"}},{default:o(()=>[ja,n(v,{value:a.value.dateDay,"onUpdate:value":e[0]||(e[0]=l=>a.value.dateDay=l),"value-format":"YYYY-MM-DD",disabled:a.value.dateType!==1,"allow-clear":!1},null,8,["value","disabled"])]),_:1}),n(f,{value:2,style:{"margin-bottom":"10px"}},{default:o(()=>[za,n(y,null,{default:o(()=>[n(v,{value:a.value.dateMon,"onUpdate:value":e[1]||(e[1]=l=>a.value.dateMon=l),picker:"month","value-format":"YYYY-MM",disabled:a.value.dateType!==2,"allow-clear":!1},null,8,["value","disabled"]),n(Y,{value:a.value.monType,"onUpdate:value":e[2]||(e[2]=l=>a.value.monType=l),disabled:a.value.dateType!==2,onClick:e[3]||(e[3]=pa(()=>{},["prevent"]))},{default:o(()=>[n(h,{value:1},{default:o(()=>[C("自然月份")]),_:1}),n(h,{value:2},{default:o(()=>[C("结账月份")]),_:1})]),_:1},8,["value","disabled"])]),_:1})]),_:1}),n(f,{value:3,style:{"margin-bottom":"10px"}},{default:o(()=>[Ra,n(D,{value:a.value.budget,"onUpdate:value":e[4]||(e[4]=l=>a.value.budget=l),picker:"year","value-format":"YYYY",disabled:a.value.dateType!==3,"allow-clear":!1},null,8,["value","disabled"])]),_:1})]),_:1},8,["value"])]),_:1}),n(b,{label:"产权公司",name:"stationCode"},{default:o(()=>[n(j,null,{default:o(()=>[n(k,{span:16},{default:o(()=>[n(Y,{value:a.value.companyList,"onUpdate:value":e[8]||(e[8]=l=>a.value.companyList=l),style:{width:"382px"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:_.value,mode:"multiple","allow-clear":!0,"filter-option":(l,d)=>((d==null?void 0:d.label)??"").toLowerCase().includes(l.toLowerCase())},{dropdownRender:o(({menuNode:l})=>[_.value&&_.value.length>0?(R(),H("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:e[7]||(e[7]=d=>d.preventDefault())},[n(S,{checked:T.value,"onUpdate:checked":e[6]||(e[6]=d=>T.value=d),indeterminate:J.value,onChange:Z},null,8,["checked","indeterminate"]),Ua],32)):Q("",!0),n(N,{style:{margin:"4px 0"}}),n(X,{vnodes:l},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1}),n(k,{span:8},{default:o(()=>[n(y,{style:{float:"right"}},{default:o(()=>[n(g,{onClick:aa},{default:o(()=>[C("重置")]),_:1}),n(g,{type:"primary",onClick:W},{default:o(()=>[C("查询")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},512)]),(R(),G(i,{columns:r,ref_key:"tableRef",ref:P,key:A.value,paginationAllData:!0,pagination:{current:a.value.pageNum,pageSize:a.value.pageSize,total:K.value,showTotal:l=>`共 ${l} 条`,size:"small",showQuickJumper:!0},"data-source":E.value,"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},bordered:!1,search:!1,onTableChange:na},{tableHeader:o(()=>[n(y,null,{default:o(()=>[E.value.length>0?(R(),G(g,{key:0,type:"primary",loading:L.value,onClick:ea},{default:o(()=>[C("导出")]),_:1},8,["loading"])):Q("",!0)]),_:1})]),_:1},8,["pagination","data-source"]))])}}});const qa=xa(Ea,[["__scopeId","data-v-64c206dc"]]);export{qa as default};
