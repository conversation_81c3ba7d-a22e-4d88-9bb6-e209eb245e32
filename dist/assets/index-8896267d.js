import{_ as A}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as O,r as l,I as R,T as v,o as M,D as q,au as B,at as V,a as w,v as Y,f as i,e as n,u as C,b as K,h as m,y as P,i as T,g as W,q as Q,bg as j,bn as G,_ as H}from"./index-db94d997.js";import{b as J,e as U}from"./index-23cd6eea.js";import{_ as $}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const X={class:"areaPrice"},Z={class:"text-hide"},F=O({__name:"index",setup(ee){const c=l(),_=l([]),f=l([]),I=R();l([]),l([]);let d="",y="";const D=[{title:"电站编号",dataIndex:"stationUniqueId",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:3,fixed:"left"},{title:"产权公司",key:"ownerCompanyCodeList",dataIndex:"ownerCompanyCodeList",valueType:"multipleSelect",maxTagCount:3,width:160,valueEnum:f,render:!0,resizable:!0,order:1,fixed:"left"},{title:"省",dataIndex:"provinceName",width:120,resizable:!0,search:!1},{title:"市",dataIndex:"cityName",width:120,resizable:!0,search:!1},{title:"区",dataIndex:"areaName",width:120,resizable:!0,search:!1},{title:"详细地址",dataIndex:"projectLocation",width:160,resizable:!0,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:_,valueTreeLoad:({id:e})=>h(e),onSelect:(e,t,a)=>{var r,u;const o=((u=(r=a==null?void 0:a.triggerNode)==null?void 0:r.props)==null?void 0:u.level)||1;d=["provinceCode","cityCode","areaCode","town","vil"][o-1],y=e},render:!0,width:150,resizable:!0,hideInTable:!0},{title:"电站类型",dataIndex:"businessStationType",valueType:"select",valueEnum:v("EAM_POWER_STATION_TYPE_CONDITION"),resizable:!0,width:120},{title:"电站状态",dataIndex:"stationStatus",valueType:"select",valueEnum:v("POWER_STATION_STATE"),resizable:!0,width:120},{title:"并网时间",dataIndex:"datongrd",width:120,dateFormat:"YYYY-MM-DD",resizable:!0,search:!1},{title:"装机容量(kW)",dataIndex:"capins",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"组件数量(块)",dataIndex:"componentQuantity",width:120,resizable:!0,search:!1},{title:"操作",key:"action",dataIndex:"action",search:!1,width:100,render:!0,align:"center",fixed:"right"}];M(async()=>{N(),h()}),q(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&E()});const h=e=>new Promise((t,a)=>{B({pid:e||"0"}).then(o=>{console.log("行政区res=",o);let s=x(o);_.value=s,console.log(_.value),t(!0)}).catch(()=>{a()})}),x=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,x(t.subDistrict))}),e),N=()=>{V({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(o=>({label:o.companyName,value:o.companyCode}));f.value=a})},S=(e,t)=>{I.push({path:"/pAssetManage/archivesManage/assetInfo/detail",state:{pdata:Q.cloneDeep({...e,companyCode:e.ownerCompanyCode})}})},k=()=>{var t;let e=(t=c.value)==null?void 0:t.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),U(e)},b=l([]),z=e=>{b.value=e||[],console.log("dataSource=",e)},E=()=>{var e;(e=c.value)==null||e.reload()},L=(e,t)=>{let a={};return new Promise(o=>{t&&t.hasOwnProperty("cityTree")&&!(t!=null&&t.cityTree)&&(d="",y=""),d&&(a={[d]:y});const s={...a,...e,noJoin:!0,delStatus:0};o(s)})};return(e,t)=>{const a=j,o=$,s=G,r=A;return w(),Y("div",X,[i(r,{columns:D,ref_key:"actionRef",ref:c,request:C(J),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:z,"before-query-params":L},{tableHeader:n(()=>[i(o,null,{default:n(()=>[C(b).length>0?(w(),K(a,{key:0,type:"primary",onClick:k},{default:n(()=>[m("导出")]),_:1})):P("",!0)]),_:1})]),ownerCompanyCodeListRender:n(({column:u,record:p,index:g})=>[i(s,null,{title:n(()=>[m(T(p.ownerCompanyName),1)]),default:n(()=>[W("span",Z,T(p.ownerCompanyName),1)]),_:2},1024)]),actionRender:n(({column:u,record:p,index:g})=>[i(o,null,{default:n(()=>[i(a,{size:"small",type:"link",onClick:()=>S(p,g)},{default:n(()=>[m(" 查看详情 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const pe=H(F,[["__scopeId","data-v-7b808188"]]);export{pe as default};
