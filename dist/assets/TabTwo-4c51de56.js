import{d as be,g as Me,a as fe,b as Le,n as me,o as Fe}from"./index-23cd6eea.js";import{d as ie,t as ce,o as re,a as n,v as p,u as l,F as k,x as N,f as t,e,h as b,i as S,g as T,y,X as ue,bn as Ae,_ as de,b as d,n as qe,Z as Ye,a1 as ye,K as J,w as _e,l as ge,be as he,bf as we,aw as Se,ax as Te,bU as Ke,bV as Ge,bI as We,bJ as Qe,bh as ke,r as W,bg as Ve,av as Xe,ay as De,p as ze,j as je}from"./index-db94d997.js";import{D as $e}from"./dayjs-a8e42122.js";import{_ as Ne}from"./index-0b09ecf1.js";import{_ as Je}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{P as Ce,X as ne,D as se,w as Be}from"./weiZhi-78534cab.js";import{C as Ze,a as el}from"./CaretUpOutlined-7e71a64b.js";import{_ as ll}from"./index-39334618.js";const tl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD8SURBVHgB7VTREYIwDG2dwA3sBjICI7CBbgAbwAa6ASP0nIAVdALYQDaIyfl61BoEv/Xd5aDp62uSpjXm52A/TRLRlj8F2w6uG9vVWjuYb8BCjq2jCT3bPRq3wlkrVmGxiJSIMt7oiDlBtSTWgHiKhWa4NbjNHKEAoU78EpFHCQ4zorkmKGl4RYwg5vGfinZYu9UWOoXsk3GXcLJ4o00SqFa32Dcq8+8+CRdhp7tX2D3UUEtZ5npNNNdODeUINUzFQlcURsNiK7xmdNa6QiOHNHuJKD4oCJU03ZzSrAE9b0RLE8LNoaiNnLZ26XFw/NmzZXANbBd+HEbzR8ADeNeNPYeFZJ8AAAAASUVORK5CYII=",al="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAC8SURBVHgB7VLBDcIwDLQr/nQDwgiMkAmADdiMUQITZAY2YINgCwcqyzKtSNVPTzqlru3ruTHAEiil9ExoBRK7MsfUdtAYq+CMgt6aeLmuFshq9BKf6Mh0BkMsSO5o9VaHHFyIW4lvxCcxDUXlOUnubvWaIyMiN8QqSgzCKhalxgZ/ubyxU+95nFy+yPr/6d4NOGAXVBgHzs6us1+CVZSOA4xE8z3UDvc0IsI0BE8wwZ/4uNE3PFkI8QFz4AW8WIHmwe9gQAAAAABJRU5ErkJggg==",ol={key:0,class:"pics"},ul={class:"preview-mask-box"},nl={class:"preview-mask"},sl=["onClick"],il=["onClick"],rl=["src"],dl=["src"],pl={key:2,class:"fileTableAction"},fl=["src"],_l=["src"],cl=["src"],vl=["src"],ml=["src"],yl=["src"],bl={key:1},Al=ie({__name:"index",props:{pictures:{default:[]},companyCode:{default:""},fileNameNew:{default:""}},emits:["showBigImg"],setup(H,{emit:C}){const r=H,{pictures:q,fileNameNew:i}=ce(r);console.log("PreviewPic_companyCode",r.companyCode);const o=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],O=["mp4","avi","rtmp","wmv","mpg","mov","rm","swf","flv"],v=g=>{if(!g)return"";var R=g.match(/\.([^.]+)$/);return R?R[1].toUpperCase():""},M=g=>{const R=i.value&&v(g.fileName)?i.value+"."+v(g.fileName):g.fileName;let Y=ue({...g,companyCode:r.companyCode,fileName:R});C("showBigImg",Y)},D=g=>{const R=i.value&&v(g.fileName)?i.value+"."+v(g.fileName):g.fileName;return ue({...g,companyCode:r.companyCode,fileName:R})};return re(()=>{}),(g,R)=>{var K;const Y=Ae;return n(),p("div",null,[((K=l(q))==null?void 0:K.length)>0?(n(),p("div",ol,[(n(!0),p(k,null,N(l(q),(B,s)=>(n(),p("div",{key:s,class:"img-box"},[t(Y,null,{title:e(()=>[b(S(l(i)&&v(B.fileName)?l(i)+"."+v(B.fileName):B.fileName),1)]),default:e(()=>[T("div",ul,[T("div",nl,[o.includes(v(B.fileName))?(n(),p("img",{key:0,src:tl,class:"eye",alt:"",onClick:()=>M(B)},null,8,sl)):y("",!0),T("img",{src:al,class:"pic-close",alt:"",onClick:()=>l(be)({fileId:B.id,fileName:l(i)&&v(B.fileName)?l(i)+"."+v(B.fileName):B.fileName})},null,8,il)])])]),_:2},1024),o.includes(v(B.fileName))?(n(),p("img",{key:0,src:D({...B,companyCode:r.companyCode}),class:"back-img",alt:""},null,8,rl)):O.includes(v(B.fileName))?(n(),p("video",{key:1,src:D({...B,companyCode:r.companyCode}),class:"back-img",alt:""},null,8,dl)):(n(),p("div",pl,[v(B.fileName)=="PDF"?(n(),p("img",{key:0,class:"back-img",src:l(Ce)},null,8,fl)):v(B.fileName)=="XLS"?(n(),p("img",{key:1,class:"back-img",src:l(ne)},null,8,_l)):v(B.fileName)=="XLSX"?(n(),p("img",{key:2,class:"back-img",src:l(ne)},null,8,cl)):v(B.fileName)=="DOC"?(n(),p("img",{key:3,class:"back-img",src:l(se)},null,8,vl)):v(B.fileName)=="DOCX"?(n(),p("img",{key:4,class:"back-img",src:l(se)},null,8,ml)):(n(),p("img",{key:5,src:l(Be)},null,8,yl))]))]))),128))])):(n(),p("div",bl))])}}});const Ie=de(Al,[["__scopeId","data-v-9ccef7d3"]]),F=(H,C)=>H.filter(r=>r.fileSource==C),gl={class:"card"},hl={class:"left_title"},wl={key:0},Sl={key:1,class:"title_color"},Tl={class:"right_title"},kl=ie({__name:"CollapseCard",props:{title:{type:String,default:""},title2:{type:String,default:""},borderBottom:{type:Boolean,default:!1},itemKey:{type:String,default:""},defaultShow:{type:Boolean,default:!1},hasDefaultMargin:{type:Boolean,default:!1},changeKey:{type:Function,default:()=>{}},isHideSwitch:{type:Boolean,default:!1},isHideTitle:{type:Boolean,default:!1}},emits:["changeKey"],setup(H,{emit:C}){const r=H,q=()=>{C("changeKey",r.itemKey,r.defaultShow)};return re(()=>{}),(i,o)=>(n(),p("div",gl,[T("div",{class:"card_title",style:qe({"margin-bottom":r.hasDefaultMargin?"0px":"24px"})},[T("div",hl,[H.isHideTitle?y("",!0):(n(),p("span",wl,S(r.title),1)),H.isHideTitle?y("",!0):(n(),p("span",Sl,S(r.title2),1))]),T("div",Tl,[r.isHideSwitch?y("",!0):(n(),p("div",{key:0,class:"switch",onClick:q},[T("div",null,S(r.defaultShow?"收起":"展开"),1),r.defaultShow?(n(),d(l(el),{key:1,style:{color:"#29cca0"}})):(n(),d(l(Ze),{key:0,style:{color:"#29cca0"}}))]))])],4),r.defaultShow?(n(),p("div",{key:0,class:Ye(r.borderBottom?"card_content":"")},[ye(i.$slots,"default",{},void 0,!0)],2)):y("",!0),T("div",null,[r.defaultShow?ye(i.$slots,"footer",{key:0},void 0,!0):y("",!0)])]))}});const Q=de(kl,[["__scopeId","data-v-7c6126a7"]]),Nl={class:"sec_content"},Cl={class:"sec_content"},Bl={class:"sec_content"},Il={class:"sec_content"},El={class:"sec_content"},Ul={class:"sec_content"},Ol=ie({__name:"TabOne",props:{stationCode:{default:""},pdata:{default:{}}},setup(H){const C=H,{pdata:r,stationCode:q}=ce(C);console.log("TabOne_pdata=",r.value);let i=J({}),o=J({}),O=J({defaultShow_2:!0});_e(r,(s,u)=>{console.log("newVal=",s),console.log("oldVal=",u)});const v=(s,u)=>{console.log("itemKey=",s),console.log("defaultShow=",u),O["defaultShow_"+s]=!u};let M=J({src:"",visiblePic:!1});const D=s=>{M.visiblePic=s},g=s=>{M.src=s,M.visiblePic=!0},R=()=>{const s={stationUniqueId:q.value};Me(s).then(u=>{var c,f,_,Z,A,V;u&&((c=Object.keys(u))==null||c.forEach(X=>{o[X]=u[X]}),o.roofType=(f=u==null?void 0:u.roofType)==null?void 0:f.split(","),o.topWay=(_=u==null?void 0:u.topWay)==null?void 0:_.split(","),o.aroundShade=(Z=u==null?void 0:u.aroundShade)==null?void 0:Z.split(","),o.roofShade=(A=u==null?void 0:u.roofShade)==null?void 0:A.split(","),o.pitchedRoofBrackerType=(V=u==null?void 0:u.pitchedRoofBrackerType)==null?void 0:V.split(","))})},Y=()=>{if(o.roofType){let s=o.roofType.map(u=>{var c;return(c=i.eamRoofType[parseInt(u)-1])==null?void 0:c.label});return s?"-"+s.join(","):""}else return""},K=()=>{var u;const s={stationCode:q.value,firstLabel:"EAMSTATION_SURVEY",companyCode:(u=r.value)==null?void 0:u.companyCode};fe(s).then(c=>{o.eamHouseSouthDip=F(c,"1"),o.eamHouseEastDip=F(c,"2"),o.eamHouseWestDip=F(c,"33"),o.eamHouseInnerDip=F(c,"3"),o.electricityMeterFile=F(c,"5"),o.houseAppearanceFile=F(c,"6"),o.houseSketchFile=F(c,"9"),o.houseOmnibearingFile=F(c,"7")})},B=()=>{const s=JSON.parse(localStorage.getItem("dictYutai")||"{}");s&&(i.eamRoofType=s==null?void 0:s.EAM_ROOF_TYPE,i.eamHouseType=s==null?void 0:s.EAM_HOUSE_TYPE,i.eamHouseTowardType=s==null?void 0:s.EAM_HOUSE_TOWARD_TYPE,i.eamTopType=s==null?void 0:s.EAM_TOP_WAY,i.eamAroundShade=s==null?void 0:s.EAM_AROUND_SHADE,i.eamRoofShade=s==null?void 0:s.EAM_ROOF_SHADE,i.eamElectricityMeter=s==null?void 0:s.EAM_ELECTRICITY_METER,i.eamElectricityCurrent=s==null?void 0:s.EAM_ELECTRICITY_CURRENT,i.eamElecPositionOld=s==null?void 0:s.EAM_ELEC_POSITION_OLD,i.eamIncomingLineMaterial=s==null?void 0:s.EAM_INCOMING_LINE_MATERIAL,i.eamRafterDitchType=s==null?void 0:s.EAM_RAFTER_DITCH_TYPE,i.eamStructureType=s==null?void 0:s.EAM_STRUCTURE_TYPE,i.eamTileType=s==null?void 0:s.EAM_TILE_TYPE,i.eamGirderType=s==null?void 0:s.EAM_GIRDER_TYPE,i.eamTileWeightType=s==null?void 0:s.EAM_TILE_WEIGHT_TYPE,i.eamPictchedRoofBrackerType=s==null?void 0:s.EAM_PICTCHED_ROOF_BRACKER_TYPE,i.eamHouseWaterproofer=s==null?void 0:s.EAM_HOUSE_WATERPROOFER,i.eamHousePanel=s==null?void 0:s.EAM_HOUSE_PANEL,i.eamBaseSupport=s==null?void 0:s.EAM_BASE_SUPPORT,i.eamHouseRoofSupport=s==null?void 0:s.EAM_HOUSE_ROOF_SUPPORT,i.eamInverterPosition=s==null?void 0:s.EAM_INVERTER_POSITION,i.eamMeterBoxPosition=s==null?void 0:s.EAM_METER_BOX_POSITION,i.eamSupportGroundPosition=s==null?void 0:s.EAM_SUPPORT_GROUND_POSITION,i.eamMeterBoxGroundPosition=s==null?void 0:s.EAM_METER_BOX_GROUND_POSITION)};return re(()=>{B(),q.value&&(R(),K())}),ge(()=>{o={}}),_e(q,(s,u)=>{s&&(R(),K())}),(s,u)=>{const c=he,f=we,_=Se,Z=$e,A=Te,V=Ke,X=Ge,I=We,E=Qe,G=Ie,te=ke,z=Ne;return n(),p("div",null,[t(te,{ref:"formRef1",model:l(o),"label-col":{span:6},"wrapper-col":{span:18}},{default:e(()=>[t(Q,{itemKey:"1",defaultShow:!0,isHideSwitch:!0,hasDefaultMargin:!1,borderBottom:!0,onChangeKey:v},{default:e(()=>[T("div",Nl,[t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"勘察人",name:"surveyPersonNam",rules:[{required:!0}]},{default:e(()=>[t(c,{value:l(o).surveyPersonNam,"onUpdate:value":u[0]||(u[0]=a=>l(o).surveyPersonNam=a),required:"",disabled:""},null,8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"勘察时间",name:"surveyDate",rules:[{required:!0}]},{default:e(()=>[t(Z,{style:{width:"100%"},value:l(o).surveyDate,"onUpdate:value":u[1]||(u[1]=a=>l(o).surveyDate=a),"value-format":"YYYY-MM-DD HH:MM:SS",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1}),t(Q,{title:"基本信息",itemKey:"2",defaultShow:l(O).defaultShow_2,isHideSwitch:l(O).isHideSwitch_2,borderBottom:!0,onChangeKey:v},{default:e(()=>[T("div",Cl,[t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"屋顶类型",name:"roofType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(X,{value:l(o).roofType,"onUpdate:value":u[2]||(u[2]=a=>l(o).roofType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamRoofType,a=>(n(),d(V,{value:a.value,key:a.value,style:{"line-height":"32px"}},{default:e(()=>[b(S(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"屋顶整体情况",name:"houseType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).houseType,"onUpdate:value":u[4]||(u[4]=a=>l(o).houseType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamHouseType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).houseTypeOther,"onUpdate:value":u[3]||(u[3]=m=>l(o).houseTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"房屋高度",rules:[{required:!0}]},{default:e(()=>[t(A,{class:"seconds-class",gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{name:"houseFloor"},{default:e(()=>[t(c,{value:l(o).houseFloor,"onUpdate:value":u[5]||(u[5]=a=>l(o).houseFloor=a),disabled:""},{addonAfter:e(()=>[b("层数")]),_:1},8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{name:"houseHeight"},{default:e(()=>[t(c,{value:l(o).houseHeight,"onUpdate:value":u[6]||(u[6]=a=>l(o).houseHeight=a),disabled:""},{addonAfter:e(()=>[b("总高")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"房屋建筑年限",name:"houseYear",rules:[{required:!0}]},{default:e(()=>[t(c,{value:l(o).houseYear,"onUpdate:value":u[7]||(u[7]=a=>l(o).houseYear=a),disabled:""},{addonAfter:e(()=>[b("年")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"房屋偏向",name:"houseTowardType",rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).houseTowardType,"onUpdate:value":u[9]||(u[9]=a=>l(o).houseTowardType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamHouseTowardType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.value===l(o).houseTowardType&&a.value!="3"?(n(),d(c,{key:0,value:l(o).houseTowardNum,"onUpdate:value":u[8]||(u[8]=m=>l(o).houseTowardNum=m),style:{width:"90px"},disabled:""},{addonAfter:e(()=>[b("°")]),_:1},8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"地理位置",rules:[{required:!0}]},{default:e(()=>[t(A,{class:"seconds-class",gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{name:"longitudeNum"},{default:e(()=>[t(c,{value:l(o).longitudeNum,"onUpdate:value":u[10]||(u[10]=a=>l(o).longitudeNum=a),disabled:""},{addonAfter:e(()=>[b("经度")]),_:1},8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{name:"latitudeNum"},{default:e(()=>[t(c,{value:l(o).latitudeNum,"onUpdate:value":u[11]||(u[11]=a=>l(o).latitudeNum=a),disabled:""},{addonAfter:e(()=>[b("纬度")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"海拔",name:"locationHeight",rules:[{required:!0}]},{default:e(()=>[t(c,{value:l(o).locationHeight,"onUpdate:value":u[12]||(u[12]=a=>l(o).locationHeight=a),disabled:""},{addonAfter:e(()=>[b("米")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"上至屋顶通道",name:"topWay","label-col":{span:3}},{default:e(()=>[t(X,{value:l(o).topWay,"onUpdate:value":u[14]||(u[14]=a=>l(o).topWay=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamTopType,a=>(n(),d(V,{value:a.value,key:a.value,style:{"line-height":"32px"}},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).topWayOther,"onUpdate:value":u[13]||(u[13]=m=>l(o).topWayOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"周边障碍物","label-col":{span:3}},{default:e(()=>[t(A,{class:"seconds-class"},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{name:"aroundShade"},{default:e(()=>[t(X,{value:l(o).aroundShade,"onUpdate:value":u[18]||(u[18]=a=>l(o).aroundShade=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamAroundShade,a=>(n(),d(V,{value:a.value,key:a.value,style:{"line-height":"32px"}},{default:e(()=>[b(S(a.label)+" ",1),a.label==="带电线路"?(n(),p(k,{key:0},[t(c,{value:l(o).wireVoltage,"onUpdate:value":u[15]||(u[15]=m=>l(o).wireVoltage=m),style:{width:"90px"},disabled:""},null,8,["value"]),b(" KV ")],64)):y("",!0),a.label==="变压器"?(n(),p(k,{key:1},[t(c,{value:l(o).transformerVoltage,"onUpdate:value":u[16]||(u[16]=m=>l(o).transformerVoltage=m),style:{width:"90px"},disabled:""},null,8,["value"]),b(" kV ")],64)):y("",!0),a.label==="其它"?(n(),d(c,{key:2,value:l(o).aroundShadeOther,"onUpdate:value":u[17]||(u[17]=m=>l(o).aroundShadeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),t(A,null,{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"屋面遮挡物",name:"roofShade","label-col":{span:3}},{default:e(()=>[t(X,{value:l(o).roofShade,"onUpdate:value":u[20]||(u[20]=a=>l(o).roofShade=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamRoofShade,a=>(n(),d(V,{value:a.value,key:a.value,style:{"line-height":"32px"}},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).roofShelterOther,"onUpdate:value":u[19]||(u[19]=m=>l(o).roofShelterOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:"配电信息",title2:"(全额上网类型中，并网方式为T接到公共电网时，此项可不填)",itemKey:"3",defaultShow:l(O).defaultShow_3,isHideSwitch:l(O).isHideSwitch_3,borderBottom:!0,onChangeKey:v},{default:e(()=>[T("div",Bl,[t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"电表位置",name:"electricityMeter"},{default:e(()=>[t(E,{value:l(o).electricityMeter,"onUpdate:value":u[21]||(u[21]=a=>l(o).electricityMeter=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamElectricityMeter,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"原电表参数",name:"electricitCurrent"},{default:e(()=>[t(E,{value:l(o).electricitCurrent,"onUpdate:value":u[22]||(u[22]=a=>l(o).electricitCurrent=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamElectricityCurrent,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"原电表位置",name:"electricityPositionOld"},{default:e(()=>[t(E,{value:l(o).electricityPositionOld,"onUpdate:value":u[24]||(u[24]=a=>l(o).electricityPositionOld=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamElecPositionOld,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).elePositionOldOther,"onUpdate:value":u[23]||(u[23]=m=>l(o).elePositionOldOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"进户线",name:"incomingLineMaterial"},{default:e(()=>[t(E,{value:l(o).incomingLineMaterial,"onUpdate:value":u[25]||(u[25]=a=>l(o).incomingLineMaterial=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamIncomingLineMaterial,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"线径",name:"incomingLineDiameter"},{default:e(()=>[t(c,{value:l(o).incomingLineDiameter,"onUpdate:value":u[26]||(u[26]=a=>l(o).incomingLineDiameter=a),disabled:""},{addonAfter:e(()=>[b("mm²")]),_:1},8,["value"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"进户线长度",name:"incomingLineLength"},{default:e(()=>[t(c,{value:l(o).incomingLineLength,"onUpdate:value":u[27]||(u[27]=a=>l(o).incomingLineLength=a),disabled:""},{addonAfter:e(()=>[b("米")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:`屋面信息${Y()}`,itemKey:"4",defaultShow:l(O).defaultShow_4,isHideSwitch:l(O).isHideSwitch_4,borderBottom:!0,onChangeKey:v},{default:e(()=>[T("div",Il,[t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("1")?(n(),d(f,{key:0,label:"屋面防水层",name:"roofWaterProof","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).roofWaterProof,"onUpdate:value":u[29]||(u[29]=a=>l(o).roofWaterProof=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamHouseWaterproofer,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).roofWaterProofother,"onUpdate:value":u[28]||(u[28]=m=>l(o).roofWaterProofother=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("1")?(n(),d(f,{key:0,label:"屋面板类型",name:"roofPanelType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).roofPanelType,"onUpdate:value":u[31]||(u[31]=a=>l(o).roofPanelType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamHousePanel,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).roofPanelTypeOther,"onUpdate:value":u[30]||(u[30]=m=>l(o).roofPanelTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("1")?(n(),d(f,{key:0,label:"底座固定形式",name:"footMountForm","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).footMountForm,"onUpdate:value":u[33]||(u[33]=a=>l(o).footMountForm=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamBaseSupport,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).footMountFormOther,"onUpdate:value":u[32]||(u[32]=m=>l(o).footMountFormOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("1")?(n(),d(f,{key:0,label:"平屋顶支架方案",name:"roofFlatSupport","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).roofFlatSupport,"onUpdate:value":u[35]||(u[35]=a=>l(o).roofFlatSupport=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamHouseRoofSupport,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).roofFlatSupportOther,"onUpdate:value":u[34]||(u[34]=m=>l(o).roofFlatSupportOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"屋面坡度",name:"roofSlope",rules:[{required:!0}]},{default:e(()=>[t(c,{value:l(o).roofSlope,"onUpdate:value":u[36]||(u[36]=a=>l(o).roofSlope=a),disabled:""},{addonAfter:e(()=>[b("度")]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"椽沟及平台",name:"rafterDitchType","label-col":{span:3}},{default:e(()=>[t(E,{value:l(o).rafterDitchType,"onUpdate:value":u[38]||(u[38]=a=>l(o).rafterDitchType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamRafterDitchType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).rafterDitchTypeOther,"onUpdate:value":u[37]||(u[37]=m=>l(o).rafterDitchTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"结构类型",name:"structureType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).structureType,"onUpdate:value":u[40]||(u[40]=a=>l(o).structureType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamStructureType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).structureTypeOther,"onUpdate:value":u[39]||(u[39]=m=>l(o).structureTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"瓦片类型",name:"tileType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).tileType,"onUpdate:value":u[42]||(u[42]=a=>l(o).tileType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamTileType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).tileTypeOther,"onUpdate:value":u[41]||(u[41]=m=>l(o).tileTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"主梁类型",name:"girderType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).girderType,"onUpdate:value":u[44]||(u[44]=a=>l(o).girderType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamGirderType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).girderTypeOther,"onUpdate:value":u[43]||(u[43]=m=>l(o).girderTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"瓦片承重形式",name:"tileWeightType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).tileWeightType,"onUpdate:value":u[46]||(u[46]=a=>l(o).tileWeightType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamTileWeightType,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).tileWeightTypeOther,"onUpdate:value":u[45]||(u[45]=m=>l(o).tileWeightTypeOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[l(o).roofType.includes("2")?(n(),d(f,{key:0,label:"斜屋顶支架方案",name:"pitchedRoofBrackerType","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(X,{value:l(o).pitchedRoofBrackerType,"onUpdate:value":u[47]||(u[47]=a=>l(o).pitchedRoofBrackerType=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamPictchedRoofBrackerType,a=>(n(),d(V,{value:a.value,key:a.value,style:{"line-height":"32px"}},{default:e(()=>[b(S(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})):y("",!0)]),_:1})]),_:1})])]),_:1},8,["title","defaultShow","isHideSwitch"]),t(Q,{title:"安装预估信息",itemKey:"5",defaultShow:l(O).defaultShow_5,isHideSwitch:l(O).isHideSwitch_5,borderBottom:!0,onChangeKey:v},{default:e(()=>[T("div",El,[t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"逆变器位置",name:"inverterPosition","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).inverterPosition,"onUpdate:value":u[49]||(u[49]=a=>l(o).inverterPosition=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamInverterPosition,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).inverterPositionOther,"onUpdate:value":u[48]||(u[48]=m=>l(o).inverterPositionOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"电表箱位置",name:"meterBoxPosition","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).meterBoxPosition,"onUpdate:value":u[51]||(u[51]=a=>l(o).meterBoxPosition=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamMeterBoxPosition,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).meterBoxPositionOther,"onUpdate:value":u[50]||(u[50]=m=>l(o).meterBoxPositionOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"支架接地",name:"supportGroundPosition","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).supportGroundPosition,"onUpdate:value":u[53]||(u[53]=a=>l(o).supportGroundPosition=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamSupportGroundPosition,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).supportGroundPositionOther,"onUpdate:value":u[52]||(u[52]=m=>l(o).supportGroundPositionOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:24},{default:e(()=>[t(f,{label:"电表箱接地",name:"meterBoxGroundPosition","label-col":{span:3},rules:[{required:!0}]},{default:e(()=>[t(E,{value:l(o).meterBoxGroundPosition,"onUpdate:value":u[55]||(u[55]=a=>l(o).meterBoxGroundPosition=a),disabled:""},{default:e(()=>[(n(!0),p(k,null,N(l(i).eamMeterBoxGroundPosition,a=>(n(),d(I,{value:a.value,key:a.value},{default:e(()=>[b(S(a.label)+" ",1),a.label==="其它"?(n(),d(c,{key:0,value:l(o).meterBoxGroundPositionOther,"onUpdate:value":u[54]||(u[54]=m=>l(o).meterBoxGroundPositionOther=m),style:{width:"90px"},disabled:""},null,8,["value"])):y("",!0)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:"勘察照片列表",itemKey:"6",defaultShow:l(O).defaultShow_6,isHideSwitch:l(O).isHideSwitch_6,borderBottom:!1,onChangeKey:v},{default:e(()=>[T("div",Ul,[t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"房屋整体南侧视角",name:"eamHouseSouthDip",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).eamHouseSouthDip,onShowBigImg:g,fileNameNew:"房屋整体南侧视角"},null,8,["pictures"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"房屋周边东侧视角",name:"eamHouseEastDip",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).eamHouseEastDip,onShowBigImg:g,fileNameNew:"房屋周边东侧视角"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"房屋周边西侧视角",name:"eamHouseWestDip",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).eamHouseWestDip,onShowBigImg:g,fileNameNew:"房屋周边西侧视角"},null,8,["pictures"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"房屋内部整体照片",name:"eamHouseInnerDip",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).eamHouseInnerDip,onShowBigImg:g,fileNameNew:"房屋内部整体照片"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"电表参数",name:"electricityMeterFile",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).electricityMeterFile,onShowBigImg:g,fileNameNew:"电表参数"},null,8,["pictures"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"屋顶全貌",name:"houseAppearanceFile",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).houseAppearanceFile,onShowBigImg:g,fileNameNew:"屋顶全貌"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),t(A,{gutter:24},{default:e(()=>[t(_,{span:12},{default:e(()=>[t(f,{label:"屋顶平面草图",name:"houseSketchFile",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).houseSketchFile,onShowBigImg:g,fileNameNew:"屋顶平面草图"},null,8,["pictures"])]),_:1})]),_:1}),t(_,{span:12},{default:e(()=>[t(f,{label:"屋顶方位角",name:"houseOmnibearingFile",rules:[{required:!0}]},{default:e(()=>[t(G,{pictures:l(o).houseOmnibearingFile,onShowBigImg:g,fileNameNew:"屋顶方位角"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"])]),_:1},8,["model"]),t(z,{style:{display:"none"},preview:{visible:l(M).visiblePic,onVisibleChange:D},src:l(M).src},null,8,["preview","src"])])}}});const yt=de(Ol,[["__scopeId","data-v-b1274314"]]),Pl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKeSURBVHgBvZY9bBMxFMefnaNRUopKWYAy3MBHgoSaAq3ElMuAYCRiJ2TuwIwYSCYYGdjT0hmVEYRQ04ElQfQQUnoRDCeqVhTxEamFSIE7855THwmXXBOa8JMc39mO/+fn5/fMYA9i5YLBAQzBWBIY6CCwIAyg5gKYjIH5yxEL72azZtA8LEiAMXYXHw3oAQFQBCHy1ky22JOQvloYjzqQE8Buwb/A4EGdi7w9na21N7cQWy3ozGFL+JiA/WGKnyJtXcraPiFaScRhywMQ8cTqIZFSK+Oqlcw1QBEiEXHlHkvkinY3fhmGgBAiRQ6iSTX2R1kxdzwBkyOj0A9r9W+wuFVpb2zOXeSnSgUyl9HaNzt2FOaOTZHL9lVun5iBWGSiXQfnJotpWohl5KgO3LFfQj+kj5yEsdABfwfnSQ1FujpA5UJG1vfWy7D4qQLPz11Hcx6UbRuNHbj89jGuPIFmnoJAhDA4C/C0TPUpvKh9gHj0sHwnkfvrJRQueYKT4VEobX+UY7uBGjpHq413G1De2YJtp9HWtvbjK1Rx01vZxNXR2AB0Dv8JDddlq4j8N2qPnnx5L2ta3cKZq75x19AJqARQI2ew8UHv1Kvsbu2aKlN95nmVMunDzTew9Ln5IZ0+gsDtMTU8rEU8vUanAdgn63h0wtd3SAvL89YLlLM013VX1IQK2nALy/zpK9AP9L+NxndfOyVGqRB/NU9xzoAhQAnRungzJb0OTZeHYcF5Tlb0I9MvZkYYNDindf7GiidEhDH9oh1NGBwmpXT14gmZmAndkEgPSEym8tZ7Q1tksKaz9gim332ZkS4nOEfrfaHZ3IXY60dJ5ro56Oe6hRuv9sSvvwdnMTE6mLMw+iYo0ntBGEOXoKiCB55KNwHFbyB1B4SUJz4fAAAAAElFTkSuQmCC",Hl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKDSURBVHgBtZbNbhMxEMfHjpUqhZQSPoRAVCtRpIRTCyoS4pAtvED6AAXyBMATkFwQN7hwTikPQF8gNL0SoMspKwHSig+BkAihICJKd82Myy67SbNx2u1Ptep4bf814/GMGQwh36yZHMCUjBWBgQESG8IAOh6AxRhYW6589Ppi2Yrbh8UJMMbuYNcEDSRAA6Ss2nPlhpaQsV6bHHehIoHdhN3A4EGXy6ozW+5Eh0Pk12sGc9kT7M7A3rDkH7lgXyo7fUJkScZlqwmIBGLdlJz3LeP+KLkrQRFiJuOpM1Yoi/4d/Gp41qmxg7B4vAATqTTosOFuwuMvLfj4+2dkXEo5TwEilBr7r+yzeKwAVw9NwcrXN6BD6ci0Cr17H5rRD9t7N8TZZzVyl9m7cEKklcjDT6+Csfx4DsVPq379+3uwf7WDb+QBWtMLusxUd1Gk2HXQJItuPIkbqpY+ANpwXhRornYANH98Vm1kpDQ5SzbSdgQ1DIHnNzlowpXDU8r3OsxlT8RZa4jY1VL96TFkokC7HD8j9/K08y4SdWRdKXdG9VfabyN35q5xOU6nQ8HgYMcADfKZnHIR0eq2+y7nINBYS+BlbeDtNXUW1NFCaqNCNYt7nrcG+wwVRkF5qPB8qQE92WFjaxNKR6dHirr6t35rqSBS9VVRh66rogvN8IRlTJBEVuglVRLx10TgvEL/gnpUeLF0H+VvQZJgtW1duHFb6fljY1h+UdWC5LCopPs/AiELK6GXkgsJialSHn438PBXe7bspLH8ksmwW+hxgnuE3wvbwwPIv1wuMs+rwCjPLTx4+/y1tZ31h3AOC6OLNYtjOaFMHyRhTF2SsgpeeGqDBHz+At//7rvRS+m/AAAAAElFTkSuQmCC",pe=H=>(ze("data-v-217aab5e"),H=H(),je(),H),xl={class:"sec_content"},Rl={class:"sec_content"},Ml={style:{display:"flex"}},Ll={class:"left_items"},Fl=pe(()=>T("img",{src:Pl,alt:""},null,-1)),ql=pe(()=>T("span",{class:"span_label"},"组件数量：",-1)),Yl={class:"span_text"},Kl={class:"left_items"},Gl=pe(()=>T("img",{src:Hl,alt:""},null,-1)),Wl=pe(()=>T("span",{class:"span_label"},"装机容量：",-1)),Ql={class:"span_text"},Vl={class:"sec_content"},Xl={class:"sec_content"},Dl={class:"sec_content"},zl={class:"table_wrap"},jl={key:0},$l={class:"fileTableAction"},Jl=["src"],Zl=["src"],et=["src"],lt=["src"],tt=["src"],at=["src"],ot=["src"],ut={class:"ellipsis"},nt={key:1},st={key:2},it=ie({__name:"TabTwo",props:{stationCode:{default:""},pdata:{default:{}}},setup(H){const C=H,{pdata:r}=ce(C),q=W(),i=W(),o=W([]),O=W(!1);let v=J({});const M=W(""),D=W("");let g=J({defaultShow_1:!0});const R=W([]),Y=(h,w)=>{console.log("itemKey=",h),console.log("defaultShow=",w),g["defaultShow_"+h]=!w};let K=J({src:"",visiblePic:!1});const B=h=>{K.visiblePic=h},s=h=>{K.src=h,K.visiblePic=!0},u=()=>{const h={stationCode:C.stationCode,firstLabel:"BOM_CONFIG_IMAGE_FILE_LIST"};fe(h).then(w=>{v.componentArrange=F(w,"1"),v.componentBracket=F(w,"2"),v.Wiring=F(w,"3"),v.WiringOnce=F(w,"4")})},c=()=>{const h={stationCode:C.stationCode,firstLabel:"BOM_IMAGE_FILE_LIST",secondLabel:"1"};O.value=!0,fe(h).then(w=>{G.value=w,O.value=!1}).catch(w=>{console.log(w),O.value=!1})},f=()=>{const h=JSON.parse(localStorage.getItem("dictYutai")||"{}");h&&h.APPLICATIONSCENARIO&&(R.value=h.APPLICATIONSCENARIO)};re(()=>{var h,w,P,U,L,ee,le;if((h=r==null?void 0:r.value)!=null&&h.from&&((w=r==null?void 0:r.value)==null?void 0:w.from)=="/technicalInfo"){const ae={stationUniqueId:(P=r==null?void 0:r.value)==null?void 0:P.stationUniqueId};Le(ae).then(x=>{v.applicationScenario=x==null?void 0:x.result[0].applicationScenario,v.applicationScenarioNum=x==null?void 0:x.result[0].applicationScenarioNum,M.value=x==null?void 0:x.result[0].componentQuantity,D.value=x==null?void 0:x.result[0].installedCapacity})}else v.applicationScenario=(U=r==null?void 0:r.value)==null?void 0:U.applicationScenario,v.applicationScenarioNum=(L=r==null?void 0:r.value)==null?void 0:L.applicationScenarioNum,M.value=(ee=r==null?void 0:r.value)==null?void 0:ee.componentQuantity,D.value=(le=r==null?void 0:r.value)==null?void 0:le.installedCapacity;f(),C!=null&&C.stationCode&&(u(),c())}),_e(r,h=>{var w,P,U,L;v.applicationScenario=(w=r==null?void 0:r.value)==null?void 0:w.applicationScenario,v.applicationScenarioNum=(P=r==null?void 0:r.value)==null?void 0:P.applicationScenarioNum,M.value=(U=r==null?void 0:r.value)==null?void 0:U.componentQuantity,D.value=(L=r==null?void 0:r.value)==null?void 0:L.installedCapacity}),ge(()=>{v={}});const _=W([{title:"物料编码",dataIndex:"materialCode",width:120,search:!1,resizable:!0},{title:"物料名称",dataIndex:"materialName",resizable:!0,search:!1,width:150},{title:"配置数量",dataIndex:"configureNum",resizable:!0,search:!1,width:120},{title:"确认数量",dataIndex:"confirmNum",search:!1,width:120,resizable:!0},{title:"单位",dataIndex:"unit",width:80,search:!1,resizable:!0,formatMoney:!0},{title:"物料组",dataIndex:"itemGroup",width:100,search:!1,resizable:!0,formatMoney:!0},{title:"BOM类型",dataIndex:"bomType",width:100,search:!1,resizable:!0,formatMoney:!0}]),Z=W([{title:"物料编码",dataIndex:"materialCode",width:120,search:!1,resizable:!0},{title:"物料名称",dataIndex:"materialName",resizable:!0,search:!1,width:150},{title:"配置数量",dataIndex:"configureNum",resizable:!0,search:!1,width:120},{title:"确认数量",dataIndex:"confirmNum",search:!1,width:120,resizable:!0},{title:"单位",dataIndex:"unit",width:80,search:!1,resizable:!0,formatMoney:!0},{title:"物料组",dataIndex:"itemGroup",width:100,search:!1,resizable:!0,formatMoney:!0},{title:"BOM类型",dataIndex:"bomType",width:100,search:!1,resizable:!0,formatMoney:!0}]),A=async()=>{const h={stationUniqueId:C==null?void 0:C.stationCode,source:1};Fe({...h}).then(w=>{console.log("导出成功")})},V=(h,w)=>new Promise(P=>{let U={stationUniqueId:C==null?void 0:C.stationCode,source:1};U={...U,...h},P(U)}),X=(h,w)=>new Promise(P=>{let U={stationUniqueId:C==null?void 0:C.stationCode,source:2};U={...U,...h},P(U)}),I=h=>{o.value=h||[]},E=W([{title:"序号",dataIndex:"id",width:60},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"附件名称",dataIndex:"fileName",ellipsis:!0,width:200},{title:"上传人",dataIndex:"createBy",ellipsis:!0,width:90},{title:"操作",key:"action",width:90}]);let G=W([]);const te=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],z=h=>{if(!h)return"";var w=h.match(/\.([^.]+)$/);return w?w[1].toUpperCase():""},a=async h=>{var P;const w=ue({...h,companyCode:(P=r.value)==null?void 0:P.companyCode});s(w)},m=async h=>{var w;be({fileId:h.id,companyCode:(w=r.value)==null?void 0:w.companyCode})};return(h,w)=>{const P=Ie,U=we,L=Se,ee=Te,le=Ve,ae=ll,x=Je,Ee=Xe,Ue=he,Oe=Ae,Pe=De,He=ke,xe=Ne;return n(),p("div",null,[t(He,{ref:"formRef1",model:l(v),"label-col":{span:6},"wrapper-col":{span:18}},{default:e(()=>[t(Q,{title:"设计图列表",itemKey:"1",defaultShow:l(g).defaultShow_1,isHideSwitch:l(g).isHideSwitch_1,borderBottom:!0,onChangeKey:Y},{default:e(()=>[T("div",xl,[t(ee,{gutter:24},{default:e(()=>[t(L,{span:12},{default:e(()=>[t(U,{label:"组件排布图",name:"componentArrange",rules:[{required:!0}]},{default:e(()=>[t(P,{pictures:l(v).componentArrange,onShowBigImg:s,fileNameNew:"组件排布图"},null,8,["pictures"])]),_:1})]),_:1}),t(L,{span:12},{default:e(()=>[t(U,{label:"组件支架图",name:"componentBracket",rules:[{required:!0}]},{default:e(()=>[t(P,{pictures:l(v).componentBracket,onShowBigImg:s,fileNameNew:"组件支架图"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),t(ee,{gutter:24},{default:e(()=>[t(L,{span:12},{default:e(()=>[t(U,{label:"组串接线图",name:"Wiring",rules:[{required:!0}]},{default:e(()=>[t(P,{pictures:l(v).Wiring,onShowBigImg:s,fileNameNew:"组串接线图"},null,8,["pictures"])]),_:1})]),_:1}),t(L,{span:12},{default:e(()=>[t(U,{label:"一次接线图",name:"WiringOnce",rules:[{required:!0}]},{default:e(()=>[t(P,{pictures:l(v).WiringOnce,onShowBigImg:s,fileNameNew:"一次接线图"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:"BOM清单",itemKey:"2",defaultShow:l(g).defaultShow_2,isHideSwitch:l(g).isHideSwitch_2,borderBottom:!0,onChangeKey:Y},{default:e(()=>[T("div",Rl,[t(x,{columns:l(_),ref_key:"actionRef1",ref:q,request:l(me),"label-col":{style:{width:"125px"}},"wrapper-col":{span:16},scroll:{x:500},search:!1,allDataSource:!0,hidenBoxShadow:!0,noPadding:!0,onGetDataSource:I,"before-query-params":V},{tableHeaderLeft:e(()=>[T("div",Ml,[T("div",Ll,[Fl,ql,T("span",Yl,S(l(M)),1)]),T("div",Kl,[Gl,Wl,T("span",Ql,S(l(D))+"W",1)])])]),tableHeader:e(()=>[t(ae,null,{default:e(()=>[l(o).length>0?(n(),d(le,{key:0,type:"primary",onClick:A},{default:e(()=>[b("导出")]),_:1})):y("",!0)]),_:1})]),_:1},8,["columns","request"])])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:"其他BOM",itemKey:"3",defaultShow:l(g).defaultShow_3,isHideSwitch:l(g).isHideSwitch_3,borderBottom:!0,onChangeKey:Y},{default:e(()=>[T("div",Vl,[t(x,{columns:l(Z),ref_key:"actionRef2",ref:i,request:l(me),"label-col":{style:{width:"125px"}},"wrapper-col":{span:16},scroll:{x:500},search:!1,allDataSource:!0,hidenBoxShadow:!0,noPadding:!0,"before-query-params":X},null,8,["columns","request"])])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:"特殊方案和应用场景",itemKey:"4",defaultShow:l(g).defaultShow_4,isHideSwitch:l(g).isHideSwitch_4,borderBottom:!0,onChangeKey:Y},{default:e(()=>[T("div",Xl,[t(ee,{gutter:24},{default:e(()=>[t(L,{span:12},{default:e(()=>[t(U,{label:"应用场景",name:"applicationScenario"},{default:e(()=>[t(Ee,{value:l(v).applicationScenario,"onUpdate:value":w[0]||(w[0]=$=>l(v).applicationScenario=$),options:l(R),disabled:""},null,8,["value","options"])]),_:1})]),_:1}),t(L,{span:12},{default:e(()=>[t(U,{label:"应用场景块数",name:"applicationScenarioNum"},{default:e(()=>[t(Ue,{value:l(v).applicationScenarioNum,"onUpdate:value":w[1]||(w[1]=$=>l(v).applicationScenarioNum=$),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),t(Q,{title:"附件上传",itemKey:"5",defaultShow:l(g).defaultShow_5,isHideSwitch:l(g).isHideSwitch_5,borderBottom:!1,onChangeKey:Y},{default:e(()=>[T("div",Dl,[T("div",zl,[t(Pe,{columns:l(E),"data-source":l(G),pagination:!1,loading:l(O)},{bodyCell:e(({column:$,text:j,record:oe,index:Re})=>{var ve;return[$.dataIndex=="fileName"?(n(),p("div",jl,[T("div",$l,[te.includes(z(j))?(n(),p("img",{key:0,src:l(ue)({...oe,companyCode:(ve=l(r).value)==null?void 0:ve.companyCode})},null,8,Jl)):z(j)=="PDF"?(n(),p("img",{key:1,src:l(Ce)},null,8,Zl)):z(j)=="XLS"?(n(),p("img",{key:2,src:l(ne)},null,8,et)):z(j)=="XLSX"?(n(),p("img",{key:3,src:l(ne)},null,8,lt)):z(j)=="DOC"?(n(),p("img",{key:4,src:l(se)},null,8,tt)):z(j)=="DOCX"?(n(),p("img",{key:5,src:l(se)},null,8,at)):(n(),p("img",{key:6,src:l(Be)},null,8,ot)),t(Oe,{placement:"topLeft"},{title:e(()=>[b(S(j),1)]),default:e(()=>[T("div",ut,S(j),1)]),_:2},1024)])])):$.dataIndex=="id"?(n(),p("div",nt,S(Re+1),1)):y("",!0),$.key=="action"?(n(),p("div",st,[t(ae,null,{default:e(()=>[te.includes(z(oe.fileName))?(n(),d(le,{key:0,size:"small",type:"link",onClick:()=>a(oe)},{default:e(()=>[b(" 查看 ")]),_:2},1032,["onClick"])):y("",!0),t(le,{size:"small",type:"link",onClick:()=>m(oe)},{default:e(()=>[b(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):y("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"])]),_:1},8,["model"]),t(xe,{style:{display:"none"},preview:{visible:l(K).visiblePic,onVisibleChange:B},src:l(K).src},null,8,["preview","src"])])}}});const bt=de(it,[["__scopeId","data-v-217aab5e"]]);export{Q as C,yt as T,Ie as _,bt as a,F as f};
