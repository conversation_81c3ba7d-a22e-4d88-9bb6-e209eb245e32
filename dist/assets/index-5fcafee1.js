import{a2 as e}from"./index-db94d997.js";function o(t){return e({url:"/web/stationInfoExamine/v1/page",method:"POST",isTable:!0,data:t})}function a(t){return e({url:"/web/stationInfoExamine/v1/update",method:"POST",data:t})}function i(t){return e({url:"/web/stationInfoExamine/v1/save",method:"POST",data:t})}function r(t){return e({url:"/web/stationInfoExamineDetail/v1/page",method:"GET",isTable:!0,data:t})}function u(t){return e({url:"/web/workflowRole/v1/roleList",method:"GET",data:t})}function s(t){return e({url:"/web/workflowRole/v1/userListByRole",method:"GET",data:t})}export{i as a,u as b,s as c,r as d,o as g,a as u};
