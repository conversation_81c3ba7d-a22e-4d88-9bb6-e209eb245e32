import{_ as X}from"./CForm-ffa1b2bc.js";import{_ as Z}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ee,I as ae,r as c,K as te,o as ne,D as oe,at as se,a as h,v as k,g as v,f as t,e as a,h as p,u as o,b as le,y as w,i as b,S as F,s as re,F as ie,bc as z,bi as L,q as ce,bg as de,bn as ue,bf as me,aw as pe,ax as _e,bh as fe}from"./index-db94d997.js";import{g as ye,f as he,e as ve,c as be}from"./index-ca482e6a.js";import{D as ge}from"./dayjs-a8e42122.js";import{E as xe}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as Ce}from"./index-39334618.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const ke={class:"areaPrice"},we={class:"text-hide"},Se={key:0,class:"status_tag"},Ie={class:"tag_one"},Me={key:1,class:"status_tag"},Pe={class:"tag_two"},Oe=ee({__name:"index",setup(De){const N=ae(),_=c(),g=c([]),u=c(!1),f=c(!1),y=c(),m=te({companyCodeList:[],monthKey:""}),x=c([{label:"已确认",value:1},{label:"待确认",value:0}]),R=c([{label:"调取中",value:0},{label:"调取成功",value:1}]),E=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:160,valueEnum:g,fixed:"left",render:!0},{title:"月份",dataIndex:"monthKey",valueType:"date",dateFormat:"YYYY-MM",resizable:!0,fixed:"left",width:120},{title:"组件数量",dataIndex:"componentQuantity",resizable:!0,search:!1,width:120},{title:"装机容量(万kW)",dataIndex:"capins",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"农户收益",dataIndex:"farmerEarnings",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"季度支付调整",dataIndex:"seasonPayAdjust",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"特殊支付调整",dataIndex:"specialPayAdjust",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月应付预测",dataIndex:"monthPayForecast",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"数据状态",dataIndex:"dataStatus",valueType:"select",valueEnum:R,resizable:!0,search:!1,width:120},{title:"确认状态",key:"confirmStatus",dataIndex:"confirmStatus",valueType:"select",valueEnum:x,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"操作",key:"action",search:!1,render:!0,align:"center",width:130}];ne(()=>{q()}),oe(()=>{_.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&C()});const T=()=>{y.value.validateFields().then(e=>{f.value=!0;let n={...e};he(n).then(s=>{console.log("应付预测"),y.value.resetFields(),f.value=!1,u.value=!1,z.info("预测成功"),C()}).catch(s=>{f.value=!1})})},Y=()=>{y.value.resetFields(),u.value=!1},q=()=>{se({}).then(e=>{console.log("产权公司res=",e);let s=(e||[]).map(i=>({label:i.companyName,value:i.companyCode}));g.value=s})},K=()=>{var n;let e=(n=_.value)==null?void 0:n.getInitialFormStateNew();ve(e)},O=()=>{u.value=!0},B=()=>{console.log("onCancel")},V=e=>{L.confirm({title:"确认提示",icon:t(xe),content:"确认后不可修改，是否确认？",onOk(){return new Promise((n,s)=>{let i={id:e==null?void 0:e.id};be(i).then(d=>{z.info("确认成功"),n(d),C()}).catch(d=>{console.log("err=",d),s()})})},onCancel:()=>B()})},$=e=>{N.push({path:"/financeManage/farmerIncomeManage/monthPayablePrediction/detail",state:{pdata:ce.cloneDeep(e)}})},S=c([]),A=e=>{S.value=e||[]},C=()=>{var e;(e=_.value)==null||e.reload()},U=(e,n)=>{let s={};return new Promise(i=>{const d={...s,...e,noJoin:!0,delStatus:0};i(d)})};return(e,n)=>{const s=de,i=Ce,d=ue,j=Z,Q=X,I=me,M=pe,P=_e,G=ge,H=fe,J=L;return h(),k(ie,null,[v("div",ke,[t(j,{columns:E,ref_key:"actionRef",ref:_,request:o(ye),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:A,"before-query-params":U},{tableHeader:a(()=>[t(i,null,{default:a(()=>[t(s,{onClick:O},{default:a(()=>[p("应付预测")]),_:1}),o(S).length>0?(h(),le(s,{key:0,type:"primary",onClick:K},{default:a(()=>[p("导出")]),_:1})):w("",!0)]),_:1})]),companyCodeListRender:a(({column:l,record:r,index:D})=>[t(d,null,{title:a(()=>[p(b(r.companyName),1)]),default:a(()=>[v("span",we,b(r.companyName),1)]),_:2},1024)]),confirmStatusRender:a(({column:l,record:r,index:D})=>[r[l.dataIndex]===1?(h(),k("span",Se,[v("span",Ie,b(o(F)(r[l.dataIndex],o(x))),1)])):w("",!0),r[l.dataIndex]===0?(h(),k("span",Me,[v("span",Pe,b(o(F)(r[l.dataIndex],o(x))),1)])):w("",!0)]),actionRender:a(({column:l,record:r,index:D})=>[t(i,null,{default:a(()=>[t(s,{type:"link",size:"small",onClick:W=>V(r),disabled:r.confirmStatus===1},{default:a(()=>[p("确认")]),_:2},1032,["onClick","disabled"]),t(s,{type:"link",size:"small",onClick:W=>$(r)},{default:a(()=>[p("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])]),t(J,{visible:o(u),"onUpdate:visible":n[2]||(n[2]=l=>re(u)?u.value=l:null),title:"应付预测","confirm-loading":o(f),destroyOnClose:!0,onOk:T,onCancel:Y,okText:"预测"},{default:a(()=>[t(H,{model:o(m),ref_key:"formRef",ref:y,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:a(()=>[t(P,{span:24},{default:a(()=>[t(M,{span:24},{default:a(()=>[t(I,{label:"产权公司",name:"companyCodeList",required:""},{default:a(()=>[t(Q,{value:o(m).companyCodeList,"onUpdate:value":n[0]||(n[0]=l=>o(m).companyCodeList=l),options:o(g)},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),t(P,{span:24},{default:a(()=>[t(M,{span:24},{default:a(()=>[t(I,{label:"时间选择",name:"monthKey",required:""},{default:a(()=>[t(G,{placeholder:"请选择",style:{width:"100%"},value:o(m).monthKey,"onUpdate:value":n[1]||(n[1]=l=>o(m).monthKey=l),picker:"month","value-format":"YYYY-MM"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});export{Oe as default};
