import{_ as xe}from"./CForm-ffa1b2bc.js";import{_ as he}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ye,I as ve,r,K as j,o as ke,D as Se,at as Ie,a as x,v as Ce,g as L,f as o,e as a,h as f,b as v,i as z,u as d,y as F,s as W,F as we,q as Re,bc as U,bW as De,bg as Le,be as ze,bn as Te,bf as Ee,aw as Pe,ax as Fe,bh as Ue,bi as Ne,av as $e,_ as Be}from"./index-db94d997.js";import{g as Me,u as Oe,a as qe,b as Ae,c as Ke}from"./index-5fcafee1.js";import{a as G,w as Ve,m as je}from"./dictLocal-9822709a.js";import{_ as We}from"./index-4a280682.js";import{_ as Ge}from"./index-39334618.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const He={class:"areaPrice"},Je={class:"areaPrice_table"},Qe={class:"text-hide"},Xe={class:"text-hide"},Ye=ye({__name:"index",setup(Ze){const H=ve(),T=r(),J=["provinceCodeList","cityCodeList","areaCodeList"],Q=r([]),N=r([]),m=r(!1),k=r(!1),u=j({companyCodeList:[],examineRate:""}),_=r(!1),S=r(!1),h=j({}),y=r(),I=r(),C=r(!1),$=r([]),B=r([]),X=[{title:"检查单号",dataIndex:"relationCode",search:!1,resizable:!0,width:100},{title:"检查电站数量",dataIndex:"stationNum",search:!1,resizable:!0,width:100},{title:"创建时间",dataIndex:"createTime",search:!1,resizable:!0,width:150},{title:"检查时间",dataIndex:"examineDate",search:!1,resizable:!0,width:150},{title:"存在问题",key:"value",dataIndex:"existingProblem",search:!1,resizable:!0,render:!0,width:150},{title:"整改建议",key:"value",dataIndex:"rectificationSuggestion",search:!1,resizable:!0,render:!0,width:150},{title:"整改情况",key:"value",dataIndex:"correctSituation",search:!1,resizable:!0,render:!0,width:150},{title:"检查单状态",key:"examineStatus",dataIndex:"examineStatus",render:!0,search:!1,resizable:!0,width:100},{title:"审批意见",dataIndex:"approveIdea",search:!1,resizable:!0,width:100},{title:"检查员",dataIndex:"examineUser",search:!1,resizable:!0,width:100},{title:"审核人",dataIndex:"approveUser",search:!1,resizable:!0,width:100},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:190}];ke(()=>{Z()}),Se(()=>{ee(),T.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&w()});const Y=()=>{u.examineRate=u.examineRate||u.examineRate===0?Math.ceil(u.examineRate):""},Z=()=>{Ie({}).then(e=>{let n=(e||[]).map(i=>({label:i.companyName,value:i.companyCode}));N.value=n})},ee=()=>{Ae().then(e=>{$.value=e==null?void 0:e.map(t=>({label:t.roleName,value:t.id}))})},te=e=>{Ke({roleId:e}).then(t=>{B.value=t==null?void 0:t.map(n=>({label:n.userName,value:n.userId}))})},ae=e=>{console.log(`selected ${e}`),te(e)},ne=e=>{H.push({path:"/pAssetManage/assetManage/checkAsset/detail",state:{pdata:Re.cloneDeep(e)}})},oe=e=>{e.isEdit=!0,e.existingProblemTem=e.existingProblem,e.rectificationSuggestionTem=e.rectificationSuggestion,e.correctSituationTem=e.correctSituation},le=e=>{e.isEdit=!1,e.existingProblem=e.existingProblemTem,e.rectificationSuggestion=e.rectificationSuggestionTem,e.correctSituation=e.correctSituationTem},se=e=>{console.log("编辑");let t={id:e.id,existingProblem:e.existingProblem,rectificationSuggestion:e.rectificationSuggestion,correctSituation:e.correctSituation};C.value=!0,Oe(t).then(n=>{C.value=!1,U.info("保存成功"),w(!0)}).catch(n=>{C.value=!1})},ie=()=>{var e;m.value=!0,(e=y.value)==null||e.resetFields()},M=r({}),de=e=>{var t;_.value=!0,M.value=e,(t=I.value)==null||t.resetFields()},re=()=>{var e;m.value=!1,(e=y.value)==null||e.resetFields()},ce=()=>{console.log("创建检查单"),y.value.validateFields().then(e=>{let t=e.examineRate||e.examineRate===0?e.examineRate/100:"",n={...e,examineRate:t};k.value=!0,qe(n).then(i=>{var c;m.value=!1,k.value=!1,U.info("保存成功"),(c=y.value)==null||c.resetFields(),w()}).catch(i=>{k.value=!1})})},ue=()=>{var e;_.value=!1,(e=I.value)==null||e.resetFields()},fe=()=>{console.log("提审"),I.value.validateFields().then(e=>{let t=M.value.id;const n=Ve[0].value,i=je[0].value;let c={assigneeId:e.assigneeId,relationId:t,businessKey:n,processKey:i};S.value=!0,De(c).then(p=>{_.value=!1,S.value=!1,U.info("保存成功"),w(!0)}).catch(p=>{S.value=!1})})},pe=r([]),me=e=>{pe.value=e||[]},w=e=>{var t;(t=T.value)==null||t.reload(e)},_e=e=>{const t=new Map;return e.forEach(n=>{const i=J[n.level-1];if(t.has(i))t.get(i).push(n.value);else{let c=[];c.push(n.value),t.set(i,c)}}),Object.fromEntries(t)},O=(e,t,n)=>!e||!t?[]:(e.forEach(i=>{t.find(p=>i.value===p)&&n.push(i),i.children&&i.children.length>0&&O(i.children,t,n)}),n),ge=(e,t)=>new Promise(n=>{const i=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[0]:"",c=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[1]:"";let p={delStatus:0,noJoin:!0,startTime:i,endTime:c};const E=O(Q.value,e==null?void 0:e.cityTree,[]);let P=_e(E);e==null||delete e.datongrd;const g={...p,...e,...P,delStatus:0};n(g)});return(e,t)=>{const n=Le,i=Ge,c=ze,p=Te,E=he,P=xe,g=Ee,R=Pe,D=Fe,be=We,q=Ue,A=Ne,K=$e;return x(),Ce(we,null,[L("div",He,[L("div",Je,[o(E,{columns:X,ref_key:"actionRef",ref:T,request:d(Me),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},search:!1,onGetDataSource:me,"before-query-params":ge},{tableHeader:a(()=>[o(i,null,{default:a(()=>[o(n,{type:"primary",onClick:ie},{default:a(()=>[f("创建检查单")]),_:1})]),_:1})]),valueRender:a(({column:s,record:l,index:V})=>[l.isEdit?(x(),v(c,{key:0,value:l[s.dataIndex],"onUpdate:value":b=>l[s.dataIndex]=b,placeholder:"请输入"},null,8,["value","onUpdate:value"])):(x(),v(p,{key:1},{title:a(()=>[f(z(l[s.dataIndex]),1)]),default:a(()=>[L("span",Qe,z(l[s.dataIndex]),1)]),_:2},1024))]),examineStatusRender:a(({column:s,record:l,index:V})=>[o(p,null,{title:a(()=>[f(z(l[s.dataIndex]&&d(G)[l[s.dataIndex]-1].label),1)]),default:a(()=>[L("span",Xe,z(l[s.dataIndex]&&d(G)[l[s.dataIndex]-1].label),1)]),_:2},1024)]),actionRender:a(({column:s,record:l,index:V})=>[o(i,null,{default:a(()=>[l.isEdit?F("",!0):(x(),v(n,{key:0,type:"link",size:"small",onClick:b=>oe(l),disabled:!(l.examineStatus=="3"||!l.examineStatus)},{default:a(()=>[f("编辑")]),_:2},1032,["onClick","disabled"])),l.isEdit?(x(),v(n,{key:1,type:"link",size:"small",onClick:b=>le(l),disabled:!(l.examineStatus=="3"||!l.examineStatus)},{default:a(()=>[f("取消")]),_:2},1032,["onClick","disabled"])):F("",!0),l.isEdit?(x(),v(n,{key:2,type:"link",size:"small",onClick:b=>se(l),loading:d(C)},{default:a(()=>[f("保存")]),_:2},1032,["onClick","loading"])):F("",!0),o(n,{type:"link",size:"small",onClick:b=>ne(l)},{default:a(()=>[f("检查")]),_:2},1032,["onClick"]),o(n,{type:"link",size:"small",onClick:b=>de(l),disabled:!(l.examineStatus=="3"||!l.examineStatus)},{default:a(()=>[f("提审")]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1},8,["request"])])]),o(A,{visible:d(m),"onUpdate:visible":t[2]||(t[2]=s=>W(m)?m.value=s:null),title:"新建检查单","confirm-loading":d(k),destroyOnClose:!0,onOk:ce,onCancel:re},{default:a(()=>[o(q,{model:d(u),ref_key:"formRef1",ref:y,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:a(()=>[o(D,{span:24},{default:a(()=>[o(R,{span:24},{default:a(()=>[o(g,{label:"产权公司",name:"companyCodeList",required:""},{default:a(()=>[o(P,{value:d(u).companyCodeList,"onUpdate:value":t[0]||(t[0]=s=>d(u).companyCodeList=s),options:d(N)},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),o(D,{span:24},{default:a(()=>[o(R,{span:24},{default:a(()=>[o(g,{label:"检查比例",name:"examineRate",required:""},{default:a(()=>[o(be,{placeholder:"请选择",style:{width:"100%"},value:d(u).examineRate,"onUpdate:value":t[1]||(t[1]=s=>d(u).examineRate=s),controls:!1,min:0,max:100,onBlur:Y},{addonAfter:a(()=>[f("%")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"]),o(A,{visible:d(_),"onUpdate:visible":t[5]||(t[5]=s=>W(_)?_.value=s:null),title:"提审","confirm-loading":d(S),okText:"提交审批",onOk:fe,onCancel:ue},{default:a(()=>[o(q,{model:d(h),ref_key:"formRef2",ref:I,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:a(()=>[o(D,{span:24},{default:a(()=>[o(R,{span:24},{default:a(()=>[o(g,{label:"审批角色",name:"aaa"},{default:a(()=>[o(K,{value:d(h).aaa,"onUpdate:value":t[3]||(t[3]=s=>d(h).aaa=s),options:d($),placeholder:"请选择",style:{width:"100%"},onChange:ae},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),o(D,{span:24},{default:a(()=>[o(R,{span:24},{default:a(()=>[o(g,{label:"审批人员",name:"assigneeId"},{default:a(()=>[o(K,{value:d(h).assigneeId,"onUpdate:value":t[4]||(t[4]=s=>d(h).assigneeId=s),options:d(B),placeholder:"请选择"},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const ut=Be(Ye,[["__scopeId","data-v-f6b5b2c0"]]);export{ut as default};
