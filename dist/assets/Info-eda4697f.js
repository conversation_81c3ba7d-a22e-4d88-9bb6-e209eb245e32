import{_ as g}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as M,r as n,o as S,D,au as N,a as b,v as k,f as I,e as h,u as x,b as F,h as K,y as E,bg as q,_ as B}from"./index-db94d997.js";import{a as O,b as V}from"./index-19ad01ac.js";import{_ as Y}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const L={class:"areaPrice"},P=M({__name:"Info",setup(R){var _;const f=n(((_=history.state)==null?void 0:_.pdata)||{}),l=n();n([]);const d=n([]);n([{label:"已确认",value:1},{label:"待确认",value:0}]);let c="",u="";const w=[{title:"电站编号",dataIndex:"stationCode",resizable:!0,fixed:"left",width:120},{title:"业主名称",dataIndex:"stationName",resizable:!0,fixed:"left",width:120},{title:"产权公司",dataIndex:"companyName",width:150,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:d,valueTreeLoad:({id:t})=>m(t),onSelect:(t,e,a)=>{var s,i;const o=((i=(s=a==null?void 0:a.triggerNode)==null?void 0:s.props)==null?void 0:i.level)||1;c=["prvCode","cityCode","distCode","town","vil"][o-1],u=t},render:!0,width:150,resizable:!0,hideInTable:!0},{title:"省",dataIndex:"prvName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"distName",search:!1,resizable:!0,width:100},{title:"装机容量",dataIndex:"capins",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"月份",dataIndex:"monthKey",valueType:"date",dateFormat:"YYYY-MM",resizable:!0,search:!1,width:120},{title:"本月结账电量",dataIndex:"monthSettleEq",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月总收入",dataIndex:"monthTotalIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"主营业务收入",dataIndex:"monthBusinessIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他业务收入",dataIndex:"monthOtherIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月总成本",dataIndex:"monthTotalCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月折旧成本",dataIndex:"monthDepreciationCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月运维成本",dataIndex:"monthOperationCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"职工薪酬",dataIndex:"monthEmpFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他费用",dataIndex:"monthOtherFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"财务费用",dataIndex:"monthFinanceFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"投资收益",dataIndex:"monthInvestIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月营业利润",dataIndex:"monthTotalProfit",resizable:!0,search:!1,formatMoney:!0,width:120}];S(()=>{m()}),D(()=>{l.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&C()});const m=t=>new Promise((e,a)=>{N({pid:t||"0"}).then(o=>{console.log("行政区res=",o);let r=y(o);d.value=r,console.log(d.value),e(!0)}).catch(()=>{a()})}),y=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,y(e.subDistrict))}),t),v=()=>{var e;let t=(e=l.value)==null?void 0:e.getInitialFormStateNew();V(t)},p=n([]),z=t=>{p.value=t||[]},C=()=>{var t;(t=l.value)==null||t.reload()},T=(t,e)=>{let a={};return new Promise(o=>{var s,i;e&&e.hasOwnProperty("cityTree")&&!(e!=null&&e.cityTree)&&(c="",u=""),c&&(a={[c]:u});const r={...a,...t,delStatus:0,companyCode:(s=f.value)==null?void 0:s.companyCode,monthKey:(i=f.value)==null?void 0:i.monthKey};o(r)})};return(t,e)=>{const a=q,o=Y,r=g;return b(),k("div",L,[I(r,{columns:w,ref_key:"actionRef",ref:l,request:x(O),onGetDataSource:z,"before-query-params":T},{tableHeader:h(()=>[I(o,null,{default:h(()=>[x(p).length>0?(b(),F(a,{key:0,type:"primary",onClick:v},{default:h(()=>[K("导出")]),_:1})):E("",!0)]),_:1})]),_:1},8,["request"])])}}});const te=B(P,[["__scopeId","data-v-bbe2290a"]]);export{te as default};
