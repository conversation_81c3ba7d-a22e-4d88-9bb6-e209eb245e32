import{a2 as de,bP as se,d as ce,r as _,K as re,o as me,D as _e,at as pe,w as M,a as g,v as R,g as d,f as l,e as o,u as i,y as S,h as f,b as I,i as y,z as U,e9 as V,m as L,F as fe,au as he,q as ue,bU as ye,av as ve,bf as ge,bI as xe,bg as ke,bh as we,bn as be,ay as Ie,p as De,j as Ce,_ as Fe}from"./index-db94d997.js";import{D as Te,R as ze}from"./dayjs-a8e42122.js";import{_ as Ye}from"./index-42d7fb9b.js";import{_ as Me}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";function Se(h){return de({url:"/web/incomeForecas/v1/page",method:"POST",isTable:!0,data:h})}function Pe(h){return se({url:"/web/incomeForecas/v1/export",method:"POST",data:h})}const P=h=>(De("data-v-58bec5d9"),h=h(),Ce(),h),je={class:"statements_container"},Ke={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ne={style:{flex:"1","padding-right":"5%"}},Re=P(()=>d("span",{style:{"margin-left":"5px"}},"全选",-1)),Ue={style:{flex:"1"}},Ve=P(()=>d("div",{style:{flex:"1"}},null,-1)),Le=P(()=>d("div",{class:"item_title"},"加权方式",-1)),Be={style:{display:"flex","flex-direction":"row","align-items":"center"}},qe={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},Ae={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Ee={style:{flex:"1",display:"flex","align-items":"center"}},Je={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Oe={style:{flex:"1",display:"flex","align-items":"center","padding-left":"5%"}},We={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},$e={style:{display:"flex","flex-direction":"row","align-items":"center"}},Qe={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},Ge={class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},He={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},Xe={class:"text-hide"},Ze={class:"text-hide"},et={class:"text-hide"},tt={class:"text-hide"},at=ce({__name:"index",setup(h){const k=_(),D=_(!1),B=_([]),w=_({}),b=_([]),j=_([]),C=_(0),T=_(1),K=_(10),x=_([]),t=re({weiType:"1",companyCodeList:"",time1:[],time2:"",time3:"",companyId:[],companyId_checked:!1,companyId_indeterminate:!1,provinceId:[],provinceId_checked:!1,provinceId_indeterminate:!1,checked1:!0,checked2:!1,checked3:!1}),q=_([{title:"产权公司",dataIndex:"companyName",resizable:!0,width:150},{title:"装机容量(kW)",dataIndex:"capacity",width:150,formatMoney:!0,resizable:!0},{title:"月份",dataIndex:"monthKey",width:150,resizable:!0},{title:"JYKJ电量(kWh)",dataIndex:"jykjEq",width:150,formatMoney:!0,resizable:!0},{title:"加权电价",dataIndex:"weOngrienPri",width:150,formatDecimal:4,resizable:!0},{title:"本月租金",dataIndex:"monthRent",width:150,formatMoney:!0,resizable:!0},{title:"本月预计收入",dataIndex:"forecastIncome",width:150,formatMoney:!0,resizable:!0}]),A=(e,a)=>{a.width=e},E=(e,{attrs:a})=>a.vnodes,J=(e,a)=>{t[e]=a.map(s=>s.value)},O=(e,a,s)=>{var v;((v=e==null?void 0:e.target)==null?void 0:v.checked)?J(a,s):t[a]=[],t[a+"_indeterminate"]=!1},W=(e,a,s,c)=>{a.length===0?(t[c+"_indeterminate"]=!1,t[c+"_checked"]=!1):a.length===s.length?(t[c+"_indeterminate"]=!1,t[c+"_checked"]=!0):(t[c+"_indeterminate"]=!0,t[c+"_checked"]=!1)},$=_({yearKey:[{required:!0}],time1:[{validator:async(e,a)=>{if((!a||a.length===0)&&t.checked1)return Promise.reject("请选择时间周期")},trigger:"change"}],time2:[{validator:async(e,a)=>{if((!a||a.length===0)&&t.checked2)return Promise.reject("请选择日期")},trigger:"change"}],time3:[{validator:async(e,a)=>{if((!a||a.length===0)&&t.checked3)return Promise.reject("请选择日期")},trigger:"change"}]}),Q=e=>{N(e)},G=e=>{console.log(e)},H=()=>{k.value.resetFields(),w.value={},b.value=[],j.value=[],t.companyId_checked=!1,t.companyId_indeterminate=!1,t.provinceId_checked=!1,t.provinceId_indeterminate=!1},X=e=>{T.value=e.current,K.value=e.pageSize,C.value=e.total;const a={...t,pageNum:e.current,pageSize:e.pageSize};N(a)},Z=()=>{he({pid:0}).then(e=>{if(e&&e.length>0){let a=e.map(s=>({label:s.name,value:s.code}));B.value=a}})},N=e=>{let a="",s="",c="";if(e.checked1?(a="1",s=e.time1[0],c=e.time1[1]):e.checked2?(a="2",s=e.time2,c=e.time2):e.checked3&&(a="3",s=e.time3,c=e.time3),t.yearKey){let v=ue.cloneDeep(e.companyId)||[],r={weiType:a,companyCodeList:v,startTime:s,endTime:c,pageNum:T.value,pageSize:K.value,yearKey:e.yearKey};console.log(e,"params====",r,"company--",v),D.value=!0,Se(r).then(u=>{D.value=!1,w.value=r,console.log("公司维度res=",u),b.value=u||[],ee(u,1)}).catch(u=>{D.value=!1})}},ee=(e,a)=>{a===1?(b.value=(e==null?void 0:e.result)||[],C.value=(e==null?void 0:e.total)||0):a===2&&(j.value=(e==null?void 0:e.result)||[],C.value=(e==null?void 0:e.total)||0)};me(()=>{te(),Z()}),_e(()=>{console.log("onActivated")});const te=()=>{pe({}).then(e=>{console.log("产权公司res=",e);let s=(e||[]).map(c=>({label:c.companyName,value:c.companyCode}));x.value=s})};M(()=>t.checked1,e=>{e&&(t.checked2=!1,t.checked3=!1,t.weiType="1",t.time2="",t.time3="",k.value.clearValidate(["time2","time3"]))}),M(()=>t.checked2,e=>{e&&(t.checked1=!1,t.checked3=!1,t.weiType="2",t.time1=[],t.time3="",k.value.clearValidate(["time1","time3"]))}),M(()=>t.checked3,e=>{e&&(t.checked1=!1,t.checked2=!1,t.weiType="3",t.time1=[],t.time2="",k.value.clearValidate(["time1","time2"]))});const ae=()=>{console.log("pageParams=",w.value),w.value.yearKey&&Pe(w.value).then(e=>{console.log("公司维度导出res:",e)})};return(e,a)=>{const s=ye,c=Ye,v=ve,r=ge,u=Te,z=xe,ne=ze,Y=ke,ie=Me,oe=we,F=be,le=Ie;return g(),R(fe,null,[d("div",je,[l(oe,{ref_key:"formRef",ref:k,name:"formRef",model:i(t),rules:i($),onFinish:Q,onFinishFailed:G},{default:o(()=>[d("div",Ke,[d("div",Ne,[l(r,{label:"产权公司",name:"companyId"},{default:o(()=>[l(v,{value:i(t).companyId,"onUpdate:value":a[3]||(a[3]=n=>i(t).companyId=n),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:i(x),mode:"multiple","filter-option":(n,m)=>((m==null?void 0:m.label)??"").toLowerCase().includes(n.toLowerCase()),onChange:a[4]||(a[4]=(n,m)=>W(n,m,i(x),"companyId"))},{dropdownRender:o(({menuNode:n})=>[i(x)&&i(x).length>0?(g(),R("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:a[2]||(a[2]=m=>m.preventDefault())},[l(s,{checked:i(t).companyId_checked,"onUpdate:checked":a[0]||(a[0]=m=>i(t).companyId_checked=m),indeterminate:i(t).companyId_indeterminate,onChange:a[1]||(a[1]=m=>O(m,"companyId",i(x)))},null,8,["checked","indeterminate"]),Re],32)):S("",!0),l(c,{style:{margin:"4px 0"}}),l(E,{vnodes:n},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1})]),d("div",Ue,[l(r,{label:"年份",name:"yearKey"},{default:o(()=>[l(u,{value:i(t).yearKey,"onUpdate:value":a[5]||(a[5]=n=>i(t).yearKey=n),style:{width:"100%"},picker:"year",valueFormat:"YYYY",placeholder:"请选择"},null,8,["value"])]),_:1})]),Ve]),Le,d("div",Be,[d("div",qe,[d("div",Ae,[l(r,{name:"checked1"},{default:o(()=>[l(z,{checked:i(t).checked1,"onUpdate:checked":a[6]||(a[6]=n=>i(t).checked1=n)},{default:o(()=>[f("发电量加权：")]),_:1},8,["checked"])]),_:1})]),l(r,{name:"time1",style:{flex:"1",display:"flex"}},{default:o(()=>[l(ne,{value:i(t).time1,"onUpdate:value":a[7]||(a[7]=n=>i(t).time1=n),style:{width:"100%"},disabled:!i(t).checked1,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})]),d("div",Ee,[d("div",Je,[l(r,{name:"checked2"},{default:o(()=>[l(z,{checked:i(t).checked2,"onUpdate:checked":a[8]||(a[8]=n=>i(t).checked2=n)},{default:o(()=>[f("装机容量加权：")]),_:1},8,["checked"])]),_:1})]),l(r,{name:"time2",style:{flex:"1",display:"flex"}},{default:o(()=>[l(u,{value:i(t).time2,"onUpdate:value":a[9]||(a[9]=n=>i(t).time2=n),style:{width:"100%"},disabled:!i(t).checked2,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})]),d("div",Oe,[d("div",We,[l(r,{name:"checked3"},{default:o(()=>[l(z,{checked:i(t).checked3,"onUpdate:checked":a[10]||(a[10]=n=>i(t).checked3=n)},{default:o(()=>[f("净资产额加权：")]),_:1},8,["checked"])]),_:1})]),l(r,{name:"time3",style:{flex:"1"}},{default:o(()=>[l(u,{value:i(t).time3,"onUpdate:value":a[11]||(a[11]=n=>i(t).time3=n),style:{width:"100%"},disabled:!i(t).checked3,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})])]),d("div",$e,[d("div",Qe,[l(ie,null,{default:o(()=>[l(Y,{onClick:H},{default:o(()=>[f("重置")]),_:1}),l(Y,{type:"primary","html-type":"submit"},{default:o(()=>[f("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),d("div",Ge,[d("div",He,[i(b).length>0?(g(),I(Y,{key:0,type:"primary",onClick:ae},{default:o(()=>[f("导出")]),_:1})):S("",!0)]),l(le,{style:{"margin-bottom":"24px"},columns:i(q),"data-source":i(b),pagination:{current:i(T),total:i(C),showTotal:n=>`共 ${n} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0},scroll:{y:500},loading:i(D),onChange:X,onResizeColumn:A},{bodyCell:o(({column:n,text:m,record:p,index:lt})=>[n!=null&&n.formatMoney&&!n.render?(g(),I(F,{key:0},{title:o(()=>[f(y(i(U)(p[n.dataIndex])),1)]),default:o(()=>[d("span",Xe,y(i(U)(p[n.dataIndex])),1)]),_:2},1024)):n!=null&&n.formatFixed&&!n.render?(g(),I(F,{key:1},{title:o(()=>[f(y(p[n.dataIndex]&&i(V)(p[n.dataIndex])),1)]),default:o(()=>[d("span",Ze,y(p[n.dataIndex]&&i(V)(p[n.dataIndex])),1)]),_:2},1024)):(n!=null&&n.formatDecimal||(n==null?void 0:n.formatDecimal)==0)&&!n.render?(g(),I(F,{key:2},{title:o(()=>[f(y(i(L)(p[n.dataIndex],n==null?void 0:n.formatDecimal)),1)]),default:o(()=>[d("span",et,y(i(L)(p[n.dataIndex],n==null?void 0:n.formatDecimal)),1)]),_:2},1024)):n.render?S("",!0):(g(),I(F,{key:3},{title:o(()=>[f(y(p[n.dataIndex]),1)]),default:o(()=>[d("span",tt,y(p[n.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","pagination","loading"])])],64)}}});const _t=Fe(at,[["__scopeId","data-v-58bec5d9"]]);export{_t as default};
