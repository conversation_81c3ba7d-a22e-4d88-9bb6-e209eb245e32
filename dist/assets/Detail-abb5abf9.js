import{_ as Q}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as Y,k as W,I as X,r as u,o as Z,D as ee,au as ae,at as te,a as f,v as ne,f as d,e as n,u as v,h as c,i as y,g as w,b as g,S as L,bc as $,c7 as se,bg as le,bn as oe,be as ie,av as re,_ as ue}from"./index-db94d997.js";import{a as de,u as ce}from"./index-a42d8557.js";import{q as pe}from"./dayjs-a8e42122.js";import{_ as ye}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const me={class:"areaPrice"},fe={class:"text-hide"},_e={class:"text-hide"},he={class:"text-hide"},ge={class:"text-hide"},ve=Y({__name:"Detail",setup(xe){var P;W.extend(pe);const q=X(),x=u(((P=history.state)==null?void 0:P.pdata)||{}),_=u(),E=u([]),D=u([]),b=u(!1),k=u(!1);u(!1);const z=u([{label:"正常",value:1},{label:"异常",value:0}]),B=u([{label:"支付",value:2},{label:"不予支付",value:1}]);let I="",T="";const F=[{title:"电站编号",dataIndex:"stationCode",resizable:!0,fixed:"left",width:120,order:1},{title:"业主名称",dataIndex:"stationName",resizable:!0,fixed:"left",width:120,order:2},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:160,valueEnum:E,render:!0,order:4},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:D,valueTreeLoad:({id:e})=>K(e),onSelect:(e,a,l)=>{var p,h;const o=((h=(p=l==null?void 0:l.triggerNode)==null?void 0:p.props)==null?void 0:h.level)||1;I=["provinceCode","cityCode","areaCode","town","vil"][o-1],T=e},render:!0,width:120,resizable:!0,hideInTable:!0,order:3},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:100},{title:"季度",key:"monthKey",dataIndex:"monthKey",valueType:"quarterDate",resizable:!0,width:120,render:!0,order:5,search:!1},{title:"系统农户收益预测",dataIndex:"systemFarmerEarnings",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"分享金额-正泰",dataIndex:"shareMoney",width:120,formatMoney:!0,resizable:!0,search:!1},{title:"偏差金额",dataIndex:"offsetMoney",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"偏差情况",dataIndex:"offsetStatus",valueType:"select",valueEnum:z,resizable:!0,width:120,order:7},{title:"发电状态",dataIndex:"electricityStatus",valueType:"select",valueEnum:z,resizable:!0,width:120,order:6},{title:"调整金额-正泰",dataIndex:"adjustMoney",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"季度支付调整",dataIndex:"seasonPayAdjust",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"原因备注",key:"causeRemark",dataIndex:"causeRemark",resizable:!0,search:!1,width:150,render:!0},{title:"支付建议",key:"paySuggest",dataIndex:"paySuggest",valueType:"select",valueEnum:B,resizable:!0,width:120,render:!0,order:8},{title:"操作",key:"action",search:!1,width:120,render:!0,align:"center",fixed:"right"}];Z(()=>{j(),K()}),ee(()=>{_.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&N()});const O=e=>{e.isEdit=!0,e.causeRemarkTem=e.causeRemark,e.paySuggestTem=e.paySuggest},U=e=>{e.isEdit=!1,e.causeRemark=e.causeRemarkTem,e.paySuggest=e.paySuggestTem},V=e=>{e.isEdit=!1;let a={id:e==null?void 0:e.id,causeRemark:e==null?void 0:e.causeRemark,paySuggest:e==null?void 0:e.paySuggest};b.value=!0,ce(a).then(l=>{console.log("保存成功res=",l),b.value=!1,$.info("保存成功"),N()}).catch(l=>{b.value=!1})},K=e=>new Promise((a,l)=>{ae({pid:e||"0"}).then(o=>{console.log("行政区res=",o);let i=R(o);D.value=i,console.log(D.value),a(!0)}).catch(()=>{l()})}),R=e=>(e.forEach(a=>{a.label=a.name,a.value=a.code,a.isLeaf=a.level>=3,a.subDistrict&&a.subDistrict&&(a.children=a.subDistrict,R(a.subDistrict))}),e),j=()=>{te({}).then(e=>{console.log("产权公司res=",e);let l=(e||[]).map(o=>({label:o.companyName,value:o.companyCode}));E.value=l})},J=()=>{q.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/quarterPayableConfirm/remarkDataImport",query:{templateType:9,fileType:".csv,.xls,.xlsx",fileSize:30}})},M=e=>{var a;if(e===1)console.log("导出中心"),q.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/quarterPayableConfirm/dataExport",query:{reportType:1,exportColumns:JSON.stringify([])}});else if(e===2){console.log("异常电站导出");let o={...(a=_==null?void 0:_.value)==null?void 0:a.getInitialFormStateNew(),queryParameterDesc:[]},i={reportType:1,reportParam:JSON.stringify(o)};k.value=!0,se(i).then(p=>{$.info("数据导出中，稍后请去导出中心查看"),k.value=!1}).catch(p=>{k.value=!1})}else e===3&&(console.log("异常批量备注"),J())},A=u([]),G=e=>{A.value=e||[]},N=()=>{var e;(e=_.value)==null||e.reload()},H=(e,a)=>{let l={};return new Promise(o=>{var C,s,t,m,r,S;a&&a.hasOwnProperty("cityTree")&&!(a!=null&&a.cityTree)&&(I="",T=""),I&&(l={[I]:T});const i=((C=e==null?void 0:e.monthKey)==null?void 0:C.quarter())||"",p=((s=e==null?void 0:e.monthKey)==null?void 0:s.year())||"",h={...l,...e,noJoin:!0,delStatus:0,companyCode:(t=x.value)==null?void 0:t.companyCode,monthKey:(m=x.value)==null?void 0:m.monthKey,quarter:i||((r=x.value)==null?void 0:r.quarter),yearKey:p||((S=x.value)==null?void 0:S.yearKey)};o(h)})};return(e,a)=>{const l=le,o=ye,i=oe,p=ie,h=re,C=Q;return f(),ne("div",me,[d(C,{columns:F,ref_key:"actionRef",ref:_,request:v(de),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:G,"before-query-params":H},{tableHeader:n(()=>[d(o,null,{default:n(()=>[d(l,{onClick:a[0]||(a[0]=s=>M(2)),loading:v(k)},{default:n(()=>[c("异常电站导出")]),_:1},8,["loading"]),d(l,{type:"primary",onClick:a[1]||(a[1]=s=>M(3))},{default:n(()=>[c("异常批量备注")]),_:1})]),_:1})]),companyCodeListRender:n(({column:s,record:t,index:m})=>[d(i,null,{title:n(()=>[c(y(t.companyName),1)]),default:n(()=>[w("span",fe,y(t.companyName),1)]),_:2},1024)]),causeRemarkRender:n(({column:s,record:t,index:m})=>[t.isEdit?(f(),g(p,{key:0,value:t[s.dataIndex],"onUpdate:value":r=>t[s.dataIndex]=r,placeholder:"请输入"},null,8,["value","onUpdate:value"])):(f(),g(i,{key:1},{title:n(()=>[c(y(t[s.dataIndex]),1)]),default:n(()=>[w("span",_e,y(t[s.dataIndex]),1)]),_:2},1024))]),paySuggestRender:n(({column:s,record:t,index:m})=>{var r;return[t.isEdit?(f(),g(h,{key:0,value:t[s.dataIndex],"onUpdate:value":S=>t[s.dataIndex]=S,options:((r=s.valueEnum)==null?void 0:r.value)||s.valueEnum,placeholder:"请选择"},null,8,["value","onUpdate:value","options"])):(f(),g(i,{key:1},{title:n(()=>[c(y(v(L)(t[s.dataIndex],s.valueEnum))+">",1)]),default:n(()=>[w("span",he,y(v(L)(t[s.dataIndex],s.valueEnum)),1)]),_:2},1024))]}),monthKeyRender:n(({column:s,record:t,index:m})=>[d(i,null,{title:n(()=>[c(y(t.yearKey&&t.quarter?t.yearKey+"年"+t.quarter+"季度":""),1)]),default:n(()=>[w("span",ge,y(t.yearKey&&t.quarter?t.yearKey+"年"+t.quarter+"季度":""),1)]),_:2},1024)]),actionRender:n(({column:s,record:t,index:m})=>[t.isEdit?(f(),g(o,{key:1},{default:n(()=>[d(l,{type:"link",size:"small",onClick:r=>U(t)},{default:n(()=>[c("取消")]),_:2},1032,["onClick"]),d(l,{type:"link",size:"small",onClick:r=>V(t),loading:v(b)},{default:n(()=>[c("保存")]),_:2},1032,["onClick","loading"])]),_:2},1024)):(f(),g(o,{key:0},{default:n(()=>[d(l,{type:"link",size:"small",onClick:r=>O(t)},{default:n(()=>[c("编辑")]),_:2},1032,["onClick"])]),_:2},1024))]),_:1},8,["request"])])}}});const Re=ue(ve,[["__scopeId","data-v-a3afead8"]]);export{Re as default};
