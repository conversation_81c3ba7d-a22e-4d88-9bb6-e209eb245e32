import{_ as re}from"./index-914bbc8b.js";import{_ as de}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ce,I as ue,r,K as me,bL as pe,o as fe,D as _e,at as he,a as u,v as p,g as m,f as s,e as a,h as f,u as l,b as be,y as ge,i as b,s as O,F as ye,bW as xe,bc as P,bi as U,q as ve,bg as Ie,bn as we,av as Ce,bf as Me,aw as ke,ax as ze,bh as Fe}from"./index-db94d997.js";import{g as De,e as Re,c as Se}from"./index-895935b7.js";import{b as Le,c as Ne}from"./index-5fcafee1.js";import{m as Be}from"./dictLocal-9822709a.js";import{E as Te}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as Ee}from"./index-39334618.js";import"./index-326d414f.js";import"./icon-831229e8.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Ve={class:"costManage"},Oe={class:"text-hide"},Pe={key:0,class:"status_tag"},Ue=m("span",{class:"tag_one"},"已确认",-1),qe=[Ue],Ke={key:1,class:"status_tag"},$e=m("span",{class:"tag_two"},"待确认",-1),Ye=[$e],Ae={key:0,class:"status_tag"},He={class:"tag_two"},We={key:1,class:"status_tag"},Ge={class:"tag_one"},Je={key:2,class:"status_tag"},Qe={class:"tag_three"},je={key:3,class:"status_tag"},Xe=m("span",{class:"tag_two"},"待提交",-1),Ze=[Xe],ht=ce({__name:"index",setup(et){const v=ue(),g=r(),F=r([]),_=r(!1),y=r(!1),h=me({}),I=r(),D=r([]),R=r([]),q=r({}),w=r(!1),K=r([]),C=r([{label:"审批中",value:1},{label:"审批通过",value:2},{label:"审批驳回",value:3}]),$=r([{label:"已确认",value:1},{label:"待确认",value:0}]),Y=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:160,valueEnum:F,fixed:"left",render:!0},{title:"月份",dataIndex:"monthKey",valueType:"date",dateFormat:"YYYY-MM",resizable:!0,fixed:"left",width:120},{title:"材料费",dataIndex:"materialFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"外购电力费",dataIndex:"buyElectricityFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"过网费",dataIndex:"passNetFee",resizable:!0,search:!1,formatMoney:!0,width:100},{title:"职工薪酬",dataIndex:"empFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"折旧费",dataIndex:"deprecitionFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"修理费",dataIndex:"fixFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"委托运行费",dataIndex:"entrustRunFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他费用",dataIndex:"otherFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"主营业务成本",dataIndex:"mainBusinessCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他业务成本",dataIndex:"otherBusinessCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"税金及附加",dataIndex:"taxAndAdd",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"销售费用",dataIndex:"salesFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"管理费用",dataIndex:"managementFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"研发费用",dataIndex:"developFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"财务费用",dataIndex:"financeFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他收益",dataIndex:"otherIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"投资收益",dataIndex:"investIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:'净敞口套期收益(损失以"-"号填列)',dataIndex:"netOpenHedgingIncome",resizable:!0,search:!1,formatMoney:!0,width:230},{title:'公允价值变动收益(损失以"-"号填列)',dataIndex:"fairValueChangeIncome",resizable:!0,search:!1,formatMoney:!0,width:240},{title:'信用减值损失(损失以"-"号填列)',dataIndex:"creditSubValueLoss",resizable:!0,search:!1,formatMoney:!0,width:230},{title:'资产减值损失(损失以"-"号填列)',dataIndex:"assetSubValueLoss",resizable:!0,search:!1,formatMoney:!0,width:230},{title:'资产处理收益(损失以"-"号填列)',dataIndex:"assetDisposeIncome",resizable:!0,search:!1,formatMoney:!0,width:230},{title:"确认状态",key:"confirmFlag",dataIndex:"confirmFlag",valueType:"select",valueEnum:$,resizable:!0,search:!1,render:!0,align:"center",width:100},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:160}];pe(),fe(()=>{A(),J()}),_e(()=>{g.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&M()});const A=()=>{he({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(d=>({label:d.companyName,value:d.companyCode}));F.value=o})},H=()=>{v.push({path:"/dataImport",query:{templateType:15,fileType:".csv,.xls,.xlsx",fileSize:30}})},W=()=>{var t;let e=(t=g.value)==null?void 0:t.getInitialFormStateNew();Re(e)},G=()=>{v.push({path:"/financeManage/costManage/station/costConfirm/config"})},J=()=>{Le().then(e=>{D.value=e==null?void 0:e.map(t=>({label:t.roleName,value:t.id}))})},Q=e=>{Ne({roleId:e}).then(t=>{R.value=t==null?void 0:t.map(o=>({label:o.userName,value:o.userId}))})},j=e=>{console.log(`selected ${e}`),Q(e)},X=()=>{var e;_.value=!1,(e=I.value)==null||e.resetFields()},Z=()=>{console.log("收入确认-提交审核"),I.value.validateFields().then(e=>{let t=q.value.id;const o="businessKeyIncomeConfirm",d=Be[0].value;let c={assigneeId:e.assigneeId,relationId:t,businessKey:o,processKey:d};y.value=!0,xe(c).then(k=>{_.value=!1,y.value=!1,P.info("保存成功"),M()}).catch(k=>{y.value=!1})})},ee=()=>{console.log("onCancel")},te=e=>{U.confirm({title:"确认提示",icon:s(Te),content:"确认后不可修改，是否确认？",onOk(){return new Promise((t,o)=>{let d={id:e==null?void 0:e.id};Se(d).then(c=>{P.info("确认成功"),t(c),M()}).catch(c=>{console.log("err=",c),o()})})},onCancel:()=>ee()})},ae=e=>{v.push({path:"/financeManage/costManage/station/costConfirm/info",state:{pdata:ve.cloneDeep(e)}})},S=r([]),se=e=>{S.value=e||[]},M=()=>{var e;(e=g.value)==null||e.reload()},ne=(e,t)=>{let o={};return new Promise(d=>{const c={...o,...e,noJoin:!0,delStatus:0};d(c)})};return(e,t)=>{const o=Ie,d=Ee,c=we,k=de,L=Ce,N=Me,B=ke,T=ze,oe=Fe,le=U,ie=re;return u(),p(ye,null,[m("div",Ve,[s(k,{columns:Y,ref_key:"actionRef",ref:g,request:l(De),"label-col":{style:{width:"80px"}},"wrapper-col":{span:16},onGetDataSource:se,"before-query-params":ne},{tableHeader:a(()=>[s(d,null,{default:a(()=>[s(o,{onClick:H},{default:a(()=>[f("导入表单")]),_:1}),l(S).length>0?(u(),be(o,{key:0,onClick:W},{default:a(()=>[f("导出")]),_:1})):ge("",!0),s(o,{type:"primary",onClick:G},{default:a(()=>[f("模式配置")]),_:1})]),_:1})]),companyCodeListRender:a(({column:n,record:i,index:z})=>[s(c,null,{title:a(()=>[f(b(i.companyName),1)]),default:a(()=>[m("span",Oe,b(i.companyName),1)]),_:2},1024)]),confirmFlagRender:a(({column:n,record:i,index:z})=>[i[n.dataIndex]===1?(u(),p("span",Pe,qe)):(u(),p("span",Ke,Ye))]),approveStatusRender:a(({column:n,record:i,index:z})=>{var x,E,V;return[i[n.dataIndex]=="1"?(u(),p("span",Ae,[m("span",He,b((x=l(C)[i[n.dataIndex]-1])==null?void 0:x.label),1)])):i[n.dataIndex]=="2"?(u(),p("span",We,[m("span",Ge,b((E=l(C)[i[n.dataIndex]-1])==null?void 0:E.label),1)])):i[n.dataIndex]=="3"?(u(),p("span",Je,[m("span",Qe,b((V=l(C)[i[n.dataIndex]-1])==null?void 0:V.label),1)])):(u(),p("span",je,Ze))]}),actionRender:a(({column:n,record:i,index:z})=>[s(d,null,{default:a(()=>[s(o,{type:"link",size:"small",onClick:x=>te(i),disabled:i.confirmFlag===1},{default:a(()=>[f("确认")]),_:2},1032,["onClick","disabled"]),s(o,{type:"link",size:"small",onClick:x=>ae(i)},{default:a(()=>[f("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])]),s(le,{visible:l(_),"onUpdate:visible":t[2]||(t[2]=n=>O(_)?_.value=n:null),title:"提交审批","confirm-loading":l(y),okText:"提交审批",onOk:Z,onCancel:X},{default:a(()=>[s(oe,{model:l(h),ref_key:"formRef2",ref:I,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:a(()=>[s(T,{span:24},{default:a(()=>[s(B,{span:24},{default:a(()=>[s(N,{label:"审批角色",name:"aaa"},{default:a(()=>[s(L,{value:l(h).aaa,"onUpdate:value":t[0]||(t[0]=n=>l(h).aaa=n),options:l(D),placeholder:"请选择",style:{width:"100%"},onChange:j},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),s(T,{span:24},{default:a(()=>[s(B,{span:24},{default:a(()=>[s(N,{label:"审批人员",name:"assigneeId"},{default:a(()=>[s(L,{value:l(h).assigneeId,"onUpdate:value":t[1]||(t[1]=n=>l(h).assigneeId=n),options:l(R),placeholder:"请选择"},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"]),s(ie,{visible:l(w),"onUpdate:visible":t[3]||(t[3]=n=>O(w)?w.value=n:null),rows:l(K),title:"电站收入确认审批"},null,8,["visible","rows"])],64)}}});export{ht as default};
