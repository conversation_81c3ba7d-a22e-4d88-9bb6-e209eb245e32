import{d as g,c9 as h,r as k,o as y,a as v,v as w,g as c,f as t,e as l,h as i,u as C,s as M,eT as I,F as L,eU as T,eV as U,bg as R,bY as S}from"./index-db94d997.js";import{_ as b}from"./index-39334618.js";const B={style:{width:"100%",height:"100%","flex-direction":"column",display:"flex","justify-content":"center","align-items":"center"}},V={style:{"margin-top":"100px",width:"500px",display:"flex","flex-direction":"row","align-items":"center"}},D=g({__name:"UploadMenu",setup(F){const{showModal:u,setFileState:p}=h(),s=k("");y(()=>{});const d=()=>{m()},r=()=>{T().then(a=>{})},m=()=>{p({title:"上传",importUrl:"/user/menuInfo/importMenuInfo",accept:".doc,.docx,.pdf,.xls",upLoadSuccess:n=>{console.log("上传成功",n)},downloadText:"上传附件",onClickDownload:()=>{console.log("点击下载")},onClickResultOk:n=>{console.log("导入结果点击关闭调用",n)},visible:!0,isTemplate:!1,fileSize:30,data:{}}),u()},_=()=>{let o={logotypeList:s.value.split(",")};U(o).then(e=>{console.log("删除菜单res=",e)})};return(a,o)=>{const e=R,n=b,f=S;return v(),w(L,null,[c("div",B,[c("div",null,[t(n,null,{default:l(()=>[t(e,{onClick:r},{default:l(()=>[i("下载模板")]),_:1}),t(e,{type:"primary",onClick:d},{default:l(()=>[i("上传")]),_:1})]),_:1})]),c("div",V,[t(f,{value:C(s),"onUpdate:value":o[0]||(o[0]=x=>M(s)?s.value=x:null),placeholder:"输入指定菜单的资源标识","auto-size":{minRows:2,maxRows:5}},null,8,["value"]),t(e,{type:"primary",onClick:_,style:{"margin-left":"24px"}},{default:l(()=>[i("删除指定菜单")]),_:1})])]),t(I)],64)}}});export{D as default};
