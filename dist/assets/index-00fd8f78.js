import{_ as B}from"./index-6d6caf4c.js";import{d as M,r,K as H,I as V,o as j,D as z,a as G,v as J,g,f as t,e as n,u as o,ez as K,h as x,F as Q,bi as w,q as Y,eA as X,eB as Z,be as $,bf as ee,bY as ae,bI as te,bJ as oe,bh as le,p as se,j as ne,eC as re,bc as de,_ as ue}from"./index-db94d997.js";import{_ as h}from"./index-07f7e8bf.js";import{E as ie}from"./ExclamationCircleOutlined-de0528d4.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-39334618.js";const ce=c=>(se("data-v-34d7a157"),c=c(),ne(),c),me={class:"areaPrice"},pe=ce(()=>g("div",{style:{width:"100%"}},[g("span",{class:"header_title"},"角色列表")],-1)),_e=M({__name:"index",setup(c){h.SHOW_PARENT;const i=r();r([]),r([]),r(!1);const m=r(!1),p=r(!1),l=H({roleName:"",roleCode:"",roleDesc:"",menuInfoIds:[],status:0}),_=r(),C=V(),y=[{title:"角色名称",dataIndex:"roleName",width:120,rightWidth:160},{title:"角色编码",dataIndex:"roleCode",width:120,rightWidth:130},{title:"状态",dataIndex:"status",valueType:"select",valueEnum:[{label:"启用",value:0},{label:"禁用",value:1}],width:120},{title:"创建人",dataIndex:"createUser",width:120,rightWidth:130},{title:"最近修改时间",dataIndex:"updateTime",search:!1,width:100,rightWidth:130}],b=r([]),D=e=>{let a={id:e.id};console.log("删除params=",a),i.value.setLoading(!0),re(a).then(d=>{i.value.setLoading(!1),console.log("删除成功"),de.info("删除成功"),f()}).catch(d=>{i.value.setLoading(!1)})},S=e=>{console.log("delItem",e),w.confirm({title:"删除提示",icon:t(ie),content:"确认要删除该角色吗？",onOk:()=>D(e),onCancel(){console.log("Cancel")}})},R=e=>{console.log("toDetail",e),C.push({path:"/system/role/detail",state:{pdata:Y.cloneDeep(e)}})},k=e=>{console.log("addItem",e),m.value=!0},N=()=>{_.value.validateFields().then(e=>{console.log("values=",e);let a={...e};p.value=!0,X(a).then(d=>{p.value=!1,console.log("新增角色res=",d),m.value=!1,_.value.resetFields(),f()}).catch(d=>{p.value=!1})})},q=()=>{_.value.resetFields()},L=()=>{Z().then(e=>{console.log("菜单列表res=",e),b.value=e||[]})};j(()=>{L()}),z(()=>{i.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&f()});const A=r([]),F=e=>{A.value=e||[],console.log("dataSource=",e)},f=()=>{var e;(e=i.value)==null||e.reload()},O=(e,a)=>new Promise(d=>{const u={...{},...e,delStatus:0};d(u)});return(e,a)=>{const d=B,v=$,u=ee,P=ae,T=h,I=te,U=oe,E=le,W=w;return G(),J(Q,null,[g("div",me,[t(d,{columns:y,ref_key:"actionRef",ref:i,request:o(K),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:F,"before-query-params":O,isAdd:!0,isDelete:!0,isSee:!0,addText:"新建角色",onAdd:k,onDelete:S,onSee:R},{tableHeader:n(()=>[pe]),_:1},8,["request"])]),t(W,{visible:m.value,"onUpdate:visible":a[5]||(a[5]=s=>m.value=s),title:"新建角色",onOk:N,onCancel:q,"confirm-loading":p.value,width:"446px"},{default:n(()=>[t(E,{model:o(l),name:"formRef",ref_key:"formRef",ref:_,"label-col":{style:{width:"100px"}},autocomplete:"off"},{default:n(()=>[t(u,{name:"roleName",label:"角色名称：",required:""},{default:n(()=>[t(v,{value:o(l).roleName,"onUpdate:value":a[0]||(a[0]=s=>o(l).roleName=s),placeholder:"请输入",maxlength:50},null,8,["value"])]),_:1}),t(u,{name:"roleCode",label:"角色编码：",required:""},{default:n(()=>[t(v,{value:o(l).roleCode,"onUpdate:value":a[1]||(a[1]=s=>o(l).roleCode=s),placeholder:"请输入",maxlength:50},null,8,["value"])]),_:1}),t(u,{name:"roleDesc",label:"角色描述：",required:""},{default:n(()=>[t(P,{value:o(l).roleDesc,"onUpdate:value":a[2]||(a[2]=s=>o(l).roleDesc=s),placeholder:"请输入"},null,8,["value"])]),_:1}),t(u,{name:"menuInfoIds",label:"PC权限：",required:""},{default:n(()=>[t(T,{value:o(l).menuInfoIds,"onUpdate:value":a[3]||(a[3]=s=>o(l).menuInfoIds=s),treeDefaultExpandAll:"",style:{width:"100%"},"tree-data":b.value,"tree-checkable":"","allow-clear":"","show-arrow":"",maxTagCount:3,"show-checked-strategy":o(h).SHOW_ALL,placeholder:"请选择","tree-node-filter-prop":"label","field-names":{children:"children",label:"menuName",value:"id"}},null,8,["value","tree-data","show-checked-strategy"])]),_:1}),t(u,{name:"status",label:"角色状态：",required:""},{default:n(()=>[t(U,{value:o(l).status,"onUpdate:value":a[4]||(a[4]=s=>o(l).status=s)},{default:n(()=>[t(I,{value:0},{default:n(()=>[x("启用")]),_:1}),t(I,{value:1},{default:n(()=>[x("禁用")]),_:1})]),_:1},8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const De=ue(_e,[["__scopeId","data-v-34d7a157"]]);export{De as default};
