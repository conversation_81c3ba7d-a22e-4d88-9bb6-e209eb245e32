import{cv as w,bl as y,bb as g,cw as E,r as m,w as T,cx as h,aT as v,V,a3 as _,aa as A,c as C}from"./index-db94d997.js";function B(){if(!arguments.length)return[];var e=arguments[0];return w(e)?e:[e]}let p;const N=e=>{var o;if(!y)return 0;if(p!==void 0)return p;const t=document.createElement("div");t.className=`${e}-scrollbar__wrap`,t.style.visibility="hidden",t.style.width="100px",t.style.position="absolute",t.style.top="-9999px",document.body.appendChild(t);const n=t.offsetWidth;t.style.overflow="scroll";const s=document.createElement("div");s.style.width="100%",t.appendChild(s);const u=s.offsetWidth;return(o=t.parentNode)==null||o.removeChild(t),p=n-u,p};function R(e,o){if(!y)return;if(!o){e.scrollTop=0;return}const t=[];let n=o.offsetParent;for(;n!==null&&e!==n&&e.contains(n);)t.push(n),n=n.offsetParent;const s=o.offsetTop+t.reduce((i,c)=>i+c.offsetTop,0),u=s+o.offsetHeight,r=e.scrollTop,l=r+e.clientHeight;s<r?e.scrollTop=s:u>l&&(e.scrollTop=u-e.clientHeight)}const O=e=>/([\uAC00-\uD7AF\u3130-\u318F])+/gi.test(e);function U(e,{beforeFocus:o,afterFocus:t,beforeBlur:n,afterBlur:s}={}){const u=g(),{emit:r}=u,l=E(),i=m(!1),c=a=>{v(o)&&o(a)||i.value||(i.value=!0,r("focus",a),t==null||t())},f=a=>{var d;v(n)&&n(a)||a.relatedTarget&&((d=l.value)!=null&&d.contains(a.relatedTarget))||(i.value=!1,r("blur",a),s==null||s())},b=()=>{var a,d;(a=l.value)!=null&&a.contains(document.activeElement)&&l.value!==document.activeElement||(d=e.value)==null||d.focus()};return T(l,a=>{a&&a.setAttribute("tabindex","-1")}),h(l,"focus",c,!0),h(l,"blur",f,!0),h(l,"click",b,!0),{isFocused:i,wrapperRef:l,handleFocus:c,handleBlur:f}}function W({afterComposition:e,emit:o}){const t=m(!1),n=l=>{o==null||o("compositionstart",l),t.value=!0},s=l=>{var i;o==null||o("compositionupdate",l);const c=(i=l.target)==null?void 0:i.value,f=c[c.length-1]||"";t.value=!O(f)},u=l=>{o==null||o("compositionend",l),t.value&&(t.value=!1,V(()=>e(l)))};return{isComposing:t,handleComposition:l=>{l.type==="compositionend"?u(l):s(l)},handleCompositionStart:n,handleCompositionUpdate:s,handleCompositionEnd:u}}const x=Symbol("emptyValuesContextKey"),F=["",void 0,null],S=void 0,k=_({emptyValues:Array,valueOnClear:{type:[String,Number,Boolean,Function],default:void 0,validator:e=>v(e)?!e():!e}}),D=(e,o)=>{const t=g()?A(x,m({})):m({}),n=C(()=>e.emptyValues||t.value.emptyValues||F),s=C(()=>v(e.valueOnClear)?e.valueOnClear():e.valueOnClear!==void 0?e.valueOnClear:v(t.value.valueOnClear)?t.value.valueOnClear():t.value.valueOnClear!==void 0?t.value.valueOnClear:o!==void 0?o:S),u=r=>n.value.includes(r);return n.value.includes(s.value),{emptyValues:n,valueOnClear:s,isEmptyValue:u}};export{U as a,D as b,B as c,W as d,x as e,N as g,R as s,k as u};
