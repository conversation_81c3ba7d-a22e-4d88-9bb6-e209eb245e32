import{C as ee,f as Z,_ as Fe,T as Nt,a as $t}from"./TabTwo-4c51de56.js";import{P as ke,X as de,D as ce,w as Ie}from"./weiZhi-78534cab.js";import{c as Lt,a as se,f as Pt,h as Tt,i as xt,j as Bt,d as Re,k as At,l as Dt,m as Ot,b as Et}from"./index-23cd6eea.js";import{f as e,bo as oe,aa as Ht,r as N,F as Ae,bp as Ut,bq as Ft,br as Rt,bs as ye,bt as Ce,d as he,bu as tt,bv as Kt,bw as it,bx as rt,l as Oe,ao as Mt,b4 as dt,c as qt,by as ct,bz as Xt,bA as Yt,bB as jt,bC as _t,bD as He,bE as pt,bF as Vt,bG as Jt,bH as Gt,K as ue,o as Pe,a as n,v as s,e as t,u as a,g as C,x as Ue,b as be,h as D,i as I,X as me,y as j,be as ft,bf as Ke,aw as Me,ax as qe,bI as zt,bJ as Qt,ay as Xe,av as at,bn as Ye,bg as je,bh as Ve,_ as Ne,p as lt,j as ot,t as Wt,w as mt,bK as Zt,bL as ea,S as We,T as Ze,z as ta,U as aa,s as la,bM as oa,bN as na}from"./index-db94d997.js";import{D as ht}from"./dayjs-a8e42122.js";import{_ as Je}from"./index-39334618.js";import{_ as Ge}from"./index-0b09ecf1.js";import"./index.vue_vue_type_style_index_0_lang-447fd504.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./CaretUpOutlined-7e71a64b.js";import"./customParseFormat-ed0c33ac.js";function Ee(h){return h!=null}var sa=function(o){var i=o.itemPrefixCls,d=o.component,k=o.span,l=o.labelStyle,m=o.contentStyle,T=o.bordered,v=o.label,L=o.content,f=o.colon,B=d;if(T){var K;return e(B,{class:[(K={},oe(K,"".concat(i,"-item-label"),Ee(v)),oe(K,"".concat(i,"-item-content"),Ee(L)),K)],colSpan:k},{default:function(){return[Ee(v)&&e("span",{style:l},[v]),Ee(L)&&e("span",{style:m},[L])]}})}return e(B,{class:["".concat(i,"-item")],colSpan:k},{default:function(){return[e("div",{class:"".concat(i,"-item-container")},[v&&e("span",{class:["".concat(i,"-item-label"),oe({},"".concat(i,"-item-no-colon"),!f)],style:l},[v]),L&&e("span",{class:"".concat(i,"-item-content"),style:m},[L])])]}})};const et=sa;var ia=function(o){var i=function(K,x,b){var E=x.colon,U=x.prefixCls,G=x.bordered,_=b.component,F=b.type,W=b.showLabel,V=b.showContent,y=b.labelStyle,M=b.contentStyle;return K.map(function(H,c){var u,r,w=H.props||{},O=w.prefixCls,X=O===void 0?U:O,z=w.span,re=z===void 0?1:z,ie=w.labelStyle,pe=ie===void 0?w["label-style"]:ie,_e=w.contentStyle,fe=_e===void 0?w["content-style"]:_e,P=w.label,A=P===void 0?(u=H.children)===null||u===void 0||(r=u.label)===null||r===void 0?void 0:r.call(u):P,g=Ut(H),Q=Ft(H),ae=Rt(H),le=H.key;return typeof _=="string"?e(et,{key:"".concat(F,"-").concat(String(le)||c),class:Q,style:ae,labelStyle:ye(ye({},y),pe),contentStyle:ye(ye({},M),fe),span:re,colon:E,component:_,itemPrefixCls:X,bordered:G,label:W?A:null,content:V?g:null},null):[e(et,{key:"label-".concat(String(le)||c),class:Q,style:ye(ye(ye({},y),ae),pe),span:1,colon:E,component:_[0],itemPrefixCls:X,bordered:G,label:A},null),e(et,{key:"content-".concat(String(le)||c),class:Q,style:ye(ye(ye({},M),ae),fe),span:re*2-1,component:_[1],itemPrefixCls:X,bordered:G,content:g},null)]})},d=o.prefixCls,k=o.vertical,l=o.row,m=o.index,T=o.bordered,v=Ht(yt,{labelStyle:N({}),contentStyle:N({})}),L=v.labelStyle,f=v.contentStyle;return k?e(Ae,null,[e("tr",{key:"label-".concat(m),class:"".concat(d,"-row")},[i(l,o,{component:"th",type:"label",showLabel:!0,labelStyle:L.value,contentStyle:f.value})]),e("tr",{key:"content-".concat(m),class:"".concat(d,"-row")},[i(l,o,{component:"td",type:"content",showContent:!0,labelStyle:L.value,contentStyle:f.value})])]):e("tr",{key:m,class:"".concat(d,"-row")},[i(l,o,{component:T?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0,labelStyle:L.value,contentStyle:f.value})])};const da=ia;Ce.any;var ca=function(){return{prefixCls:String,label:Ce.any,labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0},span:{type:Number,default:1}}},vt=he({compatConfig:{MODE:3},name:"ADescriptionsItem",props:ca(),slots:["label"],setup:function(o,i){var d=i.slots;return function(){var k;return(k=d.default)===null||k===void 0?void 0:k.call(d)}}}),gt={xxxl:3,xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};function ua(h,o){if(typeof h=="number")return h;if(rt(h)==="object")for(var i=0;i<ct.length;i++){var d=ct[i];if(o[d]&&h[d]!==void 0)return h[d]||gt[d]}return 3}function ut(h,o,i){var d=h;return(o===void 0||o>i)&&(d=Yt(h,{span:i}),jt(o===void 0,"Descriptions","Sum of column `span` in a line not match `column` of Descriptions.")),d}function ra(h,o){var i=Xt(h),d=[],k=[],l=o;return i.forEach(function(m,T){var v,L=(v=m.props)===null||v===void 0?void 0:v.span,f=L||1;if(T===i.length-1){k.push(ut(m,L,l)),d.push(k);return}f<l?(l-=f,k.push(m)):(k.push(ut(m,f,l)),d.push(k),l=o,k=[])}),d}var _a=function(){return{prefixCls:String,bordered:{type:Boolean,default:void 0},size:{type:String,default:"default"},title:Ce.any,extra:Ce.any,column:{type:[Number,Object],default:function(){return gt}},layout:String,colon:{type:Boolean,default:void 0},labelStyle:{type:Object,default:void 0},contentStyle:{type:Object,default:void 0}}},yt=Symbol("descriptionsContext"),Le=he({compatConfig:{MODE:3},name:"ADescriptions",props:_a(),slots:["title","extra"],Item:vt,setup:function(o,i){var d=i.slots,k=tt("descriptions",o),l=k.prefixCls,m=k.direction,T,v=N({});Kt(function(){T=it.subscribe(function(f){rt(o.column)==="object"&&(v.value=f)})}),Oe(function(){it.unsubscribe(T)}),Mt(yt,{labelStyle:dt(o,"labelStyle"),contentStyle:dt(o,"contentStyle")});var L=qt(function(){return ua(o.column,v.value)});return function(){var f,B,K,x,b=o.size,E=o.bordered,U=E===void 0?!1:E,G=o.layout,_=G===void 0?"horizontal":G,F=o.colon,W=F===void 0?!0:F,V=o.title,y=V===void 0?(f=d.title)===null||f===void 0?void 0:f.call(d):V,M=o.extra,H=M===void 0?(B=d.extra)===null||B===void 0?void 0:B.call(d):M,c=(K=d.default)===null||K===void 0?void 0:K.call(d),u=ra(c,L.value);return e("div",{class:[l.value,(x={},oe(x,"".concat(l.value,"-").concat(b),b!=="default"),oe(x,"".concat(l.value,"-bordered"),!!U),oe(x,"".concat(l.value,"-rtl"),m.value==="rtl"),x)]},[(y||H)&&e("div",{class:"".concat(l.value,"-header")},[y&&e("div",{class:"".concat(l.value,"-title")},[y]),H&&e("div",{class:"".concat(l.value,"-extra")},[H])]),e("div",{class:"".concat(l.value,"-view")},[e("table",null,[e("tbody",null,[u.map(function(r,w){return e(da,{key:w,index:w,colon:W,prefixCls:l.value,vertical:_==="vertical",bordered:U,row:r},null)})])])])])}}});Le.install=function(h){return h.component(Le.name,Le),h.component(Le.Item.name,Le.Item),h};const pa=Le;var fa=function(){return{prefixCls:String,color:String,dot:Ce.any,pending:{type:Boolean,default:void 0},position:Ce.oneOf(pt("left","right","")).def(""),label:Ce.any}};const De=he({compatConfig:{MODE:3},name:"ATimelineItem",props:_t(fa(),{color:"blue",pending:!1}),slots:["dot","label"],setup:function(o,i){var d=i.slots,k=tt("timeline",o),l=k.prefixCls;return function(){var m,T,v,L,f,B=o.color,K=B===void 0?"":B,x=o.pending,b=o.label,E=b===void 0?(m=d.label)===null||m===void 0?void 0:m.call(d):b,U=o.dot,G=U===void 0?(T=d.dot)===null||T===void 0?void 0:T.call(d):U,_=He((v={},oe(v,"".concat(l.value,"-item"),!0),oe(v,"".concat(l.value,"-item-pending"),x),v)),F=He((L={},oe(L,"".concat(l.value,"-item-head"),!0),oe(L,"".concat(l.value,"-item-head-custom"),G),oe(L,"".concat(l.value,"-item-head-").concat(K),!0),L)),W=/blue|red|green|gray/.test(K||"")?void 0:K;return e("li",{class:_},[E&&e("div",{class:"".concat(l.value,"-item-label")},[E]),e("div",{class:"".concat(l.value,"-item-tail")},null),e("div",{class:F,style:{borderColor:W,color:W}},[G]),e("div",{class:"".concat(l.value,"-item-content")},[(f=d.default)===null||f===void 0?void 0:f.call(d)])])}}});var ma=function(){return{prefixCls:String,pending:Ce.any,pendingDot:Ce.any,reverse:{type:Boolean,default:void 0},mode:Ce.oneOf(pt("left","alternate","right",""))}};const Be=he({compatConfig:{MODE:3},name:"ATimeline",props:_t(ma(),{reverse:!1,mode:""}),slots:["pending","pendingDot"],setup:function(o,i){var d=i.slots,k=tt("timeline",o),l=k.prefixCls,m=k.direction,T=function(L,f){var B=L.props||{};return o.mode==="alternate"?B.position==="right"?"".concat(l.value,"-item-right"):B.position==="left"||f%2===0?"".concat(l.value,"-item-left"):"".concat(l.value,"-item-right"):o.mode==="left"?"".concat(l.value,"-item-left"):o.mode==="right"||B.position==="right"?"".concat(l.value,"-item-right"):""};return function(){var v,L,f,B,K=o.pending,x=K===void 0?(v=d.pending)===null||v===void 0?void 0:v.call(d):K,b=o.pendingDot,E=b===void 0?(L=d.pendingDot)===null||L===void 0?void 0:L.call(d):b,U=o.reverse,G=o.mode,_=typeof x=="boolean"?null:x,F=Vt((f=d.default)===null||f===void 0?void 0:f.call(d)),W=x?e(De,{pending:!!x,dot:E||e(Jt,null,null)},{default:function(){return[_]}}):null;W&&F.push(W);var V=U?F.reverse():F,y=V.length,M="".concat(l.value,"-item-last"),H=V.map(function(r,w){var O=w===y-2?M:"",X=w===y-1?M:"";return Gt(r,{class:He([!U&&x?O:X,T(r,w)])})}),c=V.some(function(r){var w,O;return!!((w=r.props)!==null&&w!==void 0&&w.label||(O=r.children)!==null&&O!==void 0&&O.label)}),u=He(l.value,(B={},oe(B,"".concat(l.value,"-pending"),!!x),oe(B,"".concat(l.value,"-reverse"),!!U),oe(B,"".concat(l.value,"-").concat(G),!!G&&!c),oe(B,"".concat(l.value,"-label"),c),oe(B,"".concat(l.value,"-rtl"),m.value==="rtl"),B));return e("ul",{class:u},[H])}}});Be.Item=De;Be.install=function(h){return h.component(Be.name,Be),h.component(De.name,De),h};const ha={class:"sec_content"},va={class:"table_wrap"},ga={class:"sec_content"},ya={class:"sec_content"},ba={class:"table_wrap"},Ca={key:0},wa={class:"fileTableAction"},Sa=["src"],ka=["src"],Ia=["src"],Na=["src"],$a=["src"],La=["src"],Pa=["src"],Ta={class:"ellipsis"},xa={key:1},Ba={key:2},Aa={class:"sec_content"},Da={class:"table_wrap"},Oa={key:0},Ea={key:1},Ha={key:2},Ua={key:3},Fa={class:"sec_content"},Ra={class:"table_wrap"},Ka={key:0},Ma={key:1},qa={key:2},Xa={class:"sec_content"},Ya={class:"table_wrap"},ja={key:0},Va={class:"fileTableAction"},Ja=["src"],Ga=["src"],za=["src"],Qa=["src"],Wa=["src"],Za=["src"],el=["src"],tl={class:"ellipsis"},al={key:1},ll={key:2},ol={class:"sec_content"},nl={class:"table_wrap"},sl={key:0},il={class:"fileTableAction"},dl=["src"],cl=["src"],ul=["src"],rl=["src"],_l=["src"],pl=["src"],fl=["src"],ml={class:"ellipsis"},hl={key:1},vl={key:2},gl=he({__name:"TabThree",props:{stationCode:{type:String,require:!0}},setup(h){var nt;const o=h;let i=ue({});const d=N((nt=history.state)==null?void 0:nt.pdata),k=N(!1),l=N(!1),m=N(!1),T=N(!1),v=N(!1),L=N(!1);let f=ue({defaultShow_1:!0}),B=ue({});const K=N([]),x=($,p)=>{console.log("itemKey=",$),console.log("defaultShow=",p),f["defaultShow_"+$]=!p};let b=ue({src:"",visiblePic:!1});const E=$=>{b.visiblePic=$},U=$=>{b.src=$,b.visiblePic=!0},G=()=>{const $=JSON.parse(localStorage.getItem("dictYutai")||"{}");$&&(B.RevenueSharingModel=$==null?void 0:$.REVENUE_SHARING_MODEL,K.value=$.SETTLEMENTBANK)},_=$=>{if(!$)return;const p=JSON.parse(localStorage.getItem("dictYutai")||"{}");return p?p.COMMERCIAL_INFO_CONTRACT_STATUS.filter(te=>te.value==$)[0].label:void 0},F=()=>{const $={stationCode:o.stationCode};Lt($).then(p=>{console.log(p),i.contractNo=p==null?void 0:p.contractNo,i.signDate=p==null?void 0:p.signDate,i.firstPartyName=p==null?void 0:p.firstPartyName,i.secondPartyName=p==null?void 0:p.secondPartyName})},W=()=>{var p;const $={stationCode:o.stationCode,firstLabel:"COMMERCIAL_INFO",companyCode:(p=d.value)==null?void 0:p.companyCode};se($).then(q=>{i.contractOriginal=Z(q,"1"),i.accountImage=Z(q,"2")})},V=()=>{const $={stationUniqueId:o.stationCode};k.value=!0,Pt($).then(p=>{console.log(p),_e.value=p,k.value=!1}).catch(p=>{console.log(p),k.value=!1})},y=()=>{const $={stationCode:o.stationCode};Tt($).then(p=>{console.log(p),i.sharingAccountList=p})},M=()=>{var p;const $={stationCode:o.stationCode,firstLabel:"COMMERCIAL_INFO",secondLabel:"7",companyCode:(p=d.value)==null?void 0:p.companyCode};l.value=!0,se($).then(q=>{console.log(q),fe.value=q,l.value=!1}).catch(q=>{console.log(q),l.value=!1})},H=()=>{const $={stationCode:o.stationCode};m.value=!0,xt($).then(p=>{console.log(p),P.value=p,m.value=!1}).catch(p=>{console.log(p),m.value=!1})},c=()=>{var p;const $={stationCode:o.stationCode,firstLabel:"COMMERCIAL_INFO",secondLabel:"3",companyCode:(p=d.value)==null?void 0:p.companyCode};se($).then(q=>{A.value=q[0]}).catch(q=>{console.log(q)})},u=()=>{const $={stationCode:o.stationCode};T.value=!0,Bt($).then(p=>{g.value=p,T.value=!1}).catch(p=>{console.log(p),T.value=!1})},r=()=>{var p;const $={stationCode:o.stationCode,firstLabel:"COMMERCIAL_INFO",secondLabel:"5",companyCode:(p=d.value)==null?void 0:p.companyCode};v.value=!0,se($).then(q=>{Q.value=q,v.value=!1}).catch(q=>{console.log(q),v.value=!1})},w=()=>{var p;const $={stationCode:o.stationCode,firstLabel:"COMMERCIAL_INFO",secondLabel:"6",companyCode:(p=d.value)==null?void 0:p.companyCode};L.value=!0,se($).then(q=>{ae.value=q,L.value=!1}).catch(q=>{console.log(q),L.value=!1})};Pe(()=>{var $;i.shareIncomeType=($=d==null?void 0:d.value)==null?void 0:$.shareIncomeType,G(),o!=null&&o.stationCode&&(F(),W(),V(),y(),M(),H(),c(),u(),r(),w())}),Oe(()=>{i={},A.value={}});const O=N([{title:"序号",dataIndex:"id",width:60},{title:"开始月",dataIndex:"startMonth",ellipsis:!0,width:150},{title:"有效期(月)",dataIndex:"lifespan",ellipsis:!0,width:150},{title:"每板租金(元)",dataIndex:"rentPerBoard",ellipsis:!0,width:150}]),X=N([{title:"序号",dataIndex:"id",width:60},{title:"文件名称",dataIndex:"fileName",ellipsis:!0,width:150},{title:"上传用户",dataIndex:"createBy",ellipsis:!0,width:150},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"操作",key:"action",width:80}]),z=N([{title:"序号",key:"id",dataIndex:"id",width:60},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"附件名称",dataIndex:"fileName",ellipsis:!0,width:150},{title:" 业务员",dataIndex:"personName",ellipsis:!0,width:150},{title:"合同状态",key:"contractStatus",dataIndex:"contractStatus",ellipsis:!0,width:150},{title:"是否审核",key:"reviewed",dataIndex:"reviewed",ellipsis:!0,width:150},{title:"分享标准",dataIndex:"sharingStandards",ellipsis:!0,width:150},{title:"操作",key:"action",width:80}]),re=N([{title:"序号",dataIndex:"id",width:60},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"附件名称",dataIndex:"fileName",ellipsis:!0,width:150},{title:"补充协议",dataIndex:"replenishAgreement",ellipsis:!0,width:150},{title:" 业务员",dataIndex:"salerName",ellipsis:!0,width:150},{title:"合同状态",dataIndex:"contractStatus",ellipsis:!0,width:150},{title:"是否审核",dataIndex:"reviewed",ellipsis:!0,width:150},{title:"分享标准",dataIndex:"sharingStandards",ellipsis:!0,width:150},{title:"操作",key:"action",width:80}]),ie=N([{title:"序号",dataIndex:"id",width:60},{title:"文件名称",dataIndex:"fileName",ellipsis:!0,width:200},{title:"上传用户",dataIndex:"createBy",ellipsis:!0,width:100},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"操作",key:"action",width:80}]),pe=N([{title:"序号",dataIndex:"id",width:60},{title:"文件名称",dataIndex:"fileName",ellipsis:!0,width:200},{title:"上传用户",dataIndex:"createBy",ellipsis:!0,width:100},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"操作",key:"action",width:80}]);let _e=N([]),fe=N([]),P=N([]);const A=N({});let g=N([]),Q=N([]),ae=N([]);const le=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],J=$=>{if(!$)return"";var p=$.match(/\.([^.]+)$/);return p?p[1].toUpperCase():""},ze=async $=>{var q;const p=me({...$,companyCode:(q=d.value)==null?void 0:q.companyCode});U(p)},Te=async $=>{var p;Re({fileId:$.id,companyCode:(p=d.value)==null?void 0:p.companyCode})};return($,p)=>{const q=ft,ne=Ke,te=Me,bt=ht,ve=qe,st=Fe,Ct=zt,wt=Qt,$e=Xe,St=at,Qe=Ye,we=je,xe=Je,kt=Ve,It=Ge;return n(),s("div",null,[e(kt,{ref:"formRef1",model:a(i),"label-col":{span:5},"wrapper-col":{span:19}},{default:t(()=>[e(ee,{title:"基本信息",itemKey:"1",defaultShow:a(f).defaultShow_1,isHideSwitch:a(f).isHideSwitch_1,borderBottom:!0,onChangeKey:x},{default:t(()=>[C("div",ha,[e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"合同编号",name:"contractNo",rules:[{required:!0}]},{default:t(()=>[e(q,{value:a(i).contractNo,"onUpdate:value":p[0]||(p[0]=S=>a(i).contractNo=S),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(te,{span:12},{default:t(()=>[e(ne,{label:"签署日期",name:"signDate",rules:[{required:!0}]},{default:t(()=>[e(bt,{style:{width:"100%"},value:a(i).signDate,"onUpdate:value":p[1]||(p[1]=S=>a(i).signDate=S),"value-format":"YYYY-MM-DD HH:MM:SS",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"甲方",name:"firstPartyName",rules:[{required:!0}]},{default:t(()=>[e(q,{value:a(i).firstPartyName,"onUpdate:value":p[2]||(p[2]=S=>a(i).firstPartyName=S),disabled:""},null,8,["value"])]),_:1})]),_:1}),e(te,{span:12},{default:t(()=>[e(ne,{label:"乙方",name:"secondPartyName",rules:[{required:!0}]},{default:t(()=>[e(q,{value:a(i).secondPartyName,"onUpdate:value":p[3]||(p[3]=S=>a(i).secondPartyName=S),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"合同原件",name:"contractOriginal"},{default:t(()=>[e(st,{pictures:a(i).contractOriginal,onShowBigImg:U,fileNameNew:"合同原件"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"收益分成模式",name:"shareIncomeType",rules:[{required:!0}]},{default:t(()=>[e(wt,{value:a(i).shareIncomeType,"onUpdate:value":p[4]||(p[4]=S=>a(i).shareIncomeType=S),disabled:""},{default:t(()=>[(n(!0),s(Ae,null,Ue(a(B).RevenueSharingModel,S=>(n(),be(Ct,{value:S.value,key:S.value},{default:t(()=>[D(I(S.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(ve,{gutter:24},{default:t(()=>[e(te,{span:24},{default:t(()=>[C("div",va,[e($e,{columns:a(O),"data-source":a(_e),pagination:!1,loading:a(k),bordered:""},{bodyCell:t(({column:S,text:Y,record:R,index:ge})=>[]),_:1},8,["columns","data-source","loading"])])]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"收益分享账号信息",itemKey:"2",defaultShow:a(f).defaultShow_2,isHideSwitch:a(f).isHideSwitch_2,borderBottom:!0,onChangeKey:x},{default:t(()=>[C("div",ga,[(n(!0),s(Ae,null,Ue(a(i).sharingAccountList,(S,Y)=>(n(),s("div",{key:Y},[e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"结算银行",name:"settlementBank"},{default:t(()=>[e(St,{value:S.settlementBank,"onUpdate:value":R=>S.settlementBank=R,options:a(K),disabled:""},null,8,["value","onUpdate:value","options"])]),_:2},1024)]),_:2},1024),e(te,{span:12},{default:t(()=>[e(ne,{label:"开户行",name:"bankDeposit"},{default:t(()=>[e(q,{value:S.bankDeposit,"onUpdate:value":R=>S.bankDeposit=R,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"账户名",name:"accountName"},{default:t(()=>[e(q,{value:S.accountName,"onUpdate:value":R=>S.accountName=R,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(te,{span:12},{default:t(()=>[e(ne,{label:"身份证号",name:"idCard"},{default:t(()=>[e(q,{value:S.idCard,"onUpdate:value":R=>S.idCard=R,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"账号",name:"bankNo"},{default:t(()=>[e(q,{value:S.bankNo,"onUpdate:value":R=>S.bankNo=R,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(te,{span:12},{default:t(()=>[e(ne,{label:"联行行号",name:"interBankNo"},{default:t(()=>[e(q,{value:S.interBankNo,"onUpdate:value":R=>S.interBankNo=R,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"组件数量",name:"componentQuantity"},{default:t(()=>[e(q,{value:S.componentQuantity,"onUpdate:value":R=>S.componentQuantity=R,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024)]))),128)),e(ve,{gutter:24},{default:t(()=>[e(te,{span:12},{default:t(()=>[e(ne,{label:"账号影像",name:"accountImage"},{default:t(()=>[e(st,{pictures:a(i).accountImage,onShowBigImg:U,fileNameNew:"账号影像"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"合同附件上传",itemKey:"3",defaultShow:a(f).defaultShow_3,isHideSwitch:a(f).isHideSwitch_3,borderBottom:!0,onChangeKey:x},{default:t(()=>[C("div",ya,[C("div",ba,[e($e,{columns:a(X),"data-source":a(fe),pagination:!1,loading:a(l)},{bodyCell:t(({column:S,text:Y,record:R,index:ge})=>{var Se;return[S.dataIndex=="fileName"?(n(),s("div",Ca,[C("div",wa,[le.includes(J(Y))?(n(),s("img",{key:0,src:a(me)({...R,companyCode:(Se=a(d).value)==null?void 0:Se.companyCode})},null,8,Sa)):J(Y)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,ka)):J(Y)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,Ia)):J(Y)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,Na)):J(Y)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,$a)):J(Y)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,La)):(n(),s("img",{key:6,src:a(Ie)},null,8,Pa)),e(Qe,{placement:"topLeft"},{title:t(()=>[D(I(Y),1)]),default:t(()=>[C("div",Ta,I(Y),1)]),_:2},1024)])])):S.dataIndex=="id"?(n(),s("div",xa,I(ge+1),1)):j("",!0),S.key=="action"?(n(),s("div",Ba,[e(xe,null,{default:t(()=>[le.includes(J(R.fileName))?(n(),be(we,{key:0,size:"small",type:"link",onClick:()=>ze(R)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(we,{size:"small",type:"link",onClick:()=>Te(R)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"电子合同附件",itemKey:"4",defaultShow:a(f).defaultShow_4,isHideSwitch:a(f).isHideSwitch_4,borderBottom:!0,onChangeKey:x},{default:t(()=>[C("div",Aa,[C("div",Da,[e($e,{columns:a(z),"data-source":a(P),pagination:!1,loading:a(m),scroll:{x:1500}},{bodyCell:t(({column:S,text:Y,record:R,index:ge})=>[S.key=="id"?(n(),s("div",Oa,I(ge+1),1)):S.key=="contractStatus"?(n(),s("span",Ea,I(_(R.contractStatus)),1)):S.key=="reviewed"?(n(),s("div",Ha,I(R.reviewed?"是":"否"),1)):S.key=="action"?(n(),s("div",Ua,[e(xe,null,{default:t(()=>[e(we,{size:"small",type:"link",onClick:p[5]||(p[5]=()=>Te(a(A)))},{default:t(()=>[D(" 下载 ")]),_:1})]),_:1})])):j("",!0)]),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"补充协议附件",itemKey:"5",defaultShow:a(f).defaultShow_5,isHideSwitch:a(f).isHideSwitch_5,borderBottom:!0,onChangeKey:x},{default:t(()=>[C("div",Fa,[C("div",Ra,[e($e,{columns:a(re),"data-source":a(g),pagination:!1,loading:a(T),scroll:{x:1500}},{bodyCell:t(({column:S,text:Y,record:R,index:ge})=>[S.dataIndex=="id"?(n(),s("div",Ka,I(ge+1),1)):j("",!0),S.dataIndex=="reviewed"?(n(),s("div",Ma,I(R.reviewed?"是":"否"),1)):j("",!0),S.key=="action"?(n(),s("div",qa,[e(xe,null,{default:t(()=>[e(we,{size:"small",type:"link",onClick:()=>Te(R)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"代理商承诺函",itemKey:"6",defaultShow:a(f).defaultShow_6,isHideSwitch:a(f).isHideSwitch_6,borderBottom:!0,onChangeKey:x},{default:t(()=>[C("div",Xa,[C("div",Ya,[e($e,{columns:a(ie),"data-source":a(Q),pagination:!1,loading:a(v)},{bodyCell:t(({column:S,text:Y,record:R,index:ge})=>{var Se;return[S.dataIndex=="fileName"?(n(),s("div",ja,[C("div",Va,[le.includes(J(Y))?(n(),s("img",{key:0,src:a(me)({...R,companyCode:(Se=a(d).value)==null?void 0:Se.companyCode})},null,8,Ja)):J(Y)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,Ga)):J(Y)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,za)):J(Y)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,Qa)):J(Y)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,Wa)):J(Y)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,Za)):(n(),s("img",{key:6,src:a(Ie)},null,8,el)),e(Qe,{placement:"topLeft"},{title:t(()=>[D(I(Y),1)]),default:t(()=>[C("div",tl,I(Y),1)]),_:2},1024)])])):S.dataIndex=="id"?(n(),s("div",al,I(ge+1),1)):j("",!0),S.key=="action"?(n(),s("div",ll,[e(xe,null,{default:t(()=>[le.includes(J(R.fileName))?(n(),be(we,{key:0,size:"small",type:"link",onClick:()=>ze(R)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(we,{size:"small",type:"link",onClick:()=>Te(R)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"分享模式变更纸质合同附件",itemKey:"7",defaultShow:a(f).defaultShow_7,isHideSwitch:a(f).isHideSwitch_7,borderBottom:!1,onChangeKey:x},{default:t(()=>[C("div",ol,[C("div",nl,[e($e,{columns:a(pe),"data-source":a(ae),pagination:!1,loading:a(L)},{bodyCell:t(({column:S,text:Y,record:R,index:ge})=>{var Se;return[S.dataIndex=="fileName"?(n(),s("div",sl,[C("div",il,[le.includes(J(Y))?(n(),s("img",{key:0,src:a(me)({...R,companyCode:(Se=a(d).value)==null?void 0:Se.companyCode})},null,8,dl)):J(Y)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,cl)):J(Y)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,ul)):J(Y)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,rl)):J(Y)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,_l)):J(Y)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,pl)):(n(),s("img",{key:6,src:a(Ie)},null,8,fl)),e(Qe,{placement:"topLeft"},{title:t(()=>[D(I(Y),1)]),default:t(()=>[C("div",ml,I(Y),1)]),_:2},1024)])])):S.dataIndex=="id"?(n(),s("div",hl,I(ge+1),1)):j("",!0),S.key=="action"?(n(),s("div",vl,[e(xe,null,{default:t(()=>[le.includes(J(R.fileName))?(n(),be(we,{key:0,size:"small",type:"link",onClick:()=>ze(R)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(we,{size:"small",type:"link",onClick:()=>Te(R)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"])]),_:1},8,["model"]),e(It,{style:{display:"none"},preview:{visible:a(b).visiblePic,onVisibleChange:E},src:a(b).src},null,8,["preview","src"])])}}});const yl=Ne(gl,[["__scopeId","data-v-9134f6b0"]]),bl=h=>(lt("data-v-8e9e4f2e"),h=h(),ot(),h),Cl={class:"sec_content"},wl={key:0,class:"suc-status"},Sl={key:1,class:"red-status"},kl={key:0},Il=bl(()=>C("h3",{class:"second_title"},"其他",-1)),Nl={key:0,class:"suc-status"},$l={key:1,class:"red-status"},Ll={class:"sec_content"},Pl={class:"table_wrap"},Tl={key:0},xl={class:"fileTableAction"},Bl=["src"],Al=["src"],Dl=["src"],Ol=["src"],El=["src"],Hl=["src"],Ul=["src"],Fl={class:"ellipsis"},Rl={key:1},Kl={key:2},Ml=he({__name:"TabFour",props:{stationCode:{type:String,require:!0}},setup(h){var V;const o=h;let i=ue({});const d=N((V=history.state)==null?void 0:V.pdata),k=N(!1);let l=ue({defaultShow_1:!0}),m=N([]);const T=(y,M)=>{console.log("itemKey=",y),console.log("defaultShow=",M),l["defaultShow_"+y]=!M};let v=ue({src:"",visiblePic:!1});const L=y=>{v.visiblePic=y},f=y=>{v.src=y,v.visiblePic=!0},B=()=>{At({StationCode:o.stationCode}).then(y=>{i.ongridInfo=y})},K=()=>{var M;const y={stationCode:o.stationCode,firstLabel:"MERGE_NET",secondLabel:"1",companyCode:(M=d.value)==null?void 0:M.companyCode};se(y).then(H=>{i.ongridApplyForList=H})},x=()=>{var M;const y={stationCode:o.stationCode,firstLabel:"MERGE_NET_CONTRACT",secondLabel:"2",companyCode:(M=d.value)==null?void 0:M.companyCode};k.value=!0,se(y).then(H=>{k.value=!1,U.value=H,k.value=!1}).catch(H=>{console.log(H),k.value=!1})},b=()=>{const y=JSON.parse(localStorage.getItem("dictYutai")||"{}");y&&y.RELATEDTAIJITOW&&(m.value=y.RELATEDTAIJITOW)};Pe(()=>{b(),o!=null&&o.stationCode&&(B(),K(),x())}),Oe(()=>{i={}});const E=N([{title:"序号",dataIndex:"id",width:60},{title:"文件名称",dataIndex:"fileName",ellipsis:!0,width:200},{title:"上传用户",dataIndex:"createBy",ellipsis:!0,width:90},{title:"上传日期",dataIndex:"createTime",ellipsis:!0,width:150},{title:"操作",key:"action",width:90}]);let U=N([]);const G=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],_=y=>{if(!y)return"";var M=y.match(/\.([^.]+)$/);return M?M[1].toUpperCase():""},F=async y=>{var H;const M=me({...y,companyCode:(H=d.value)==null?void 0:H.companyCode});f(M)},W=async y=>{var M;Re({fileId:y.id,companyCode:(M=d.value)==null?void 0:M.companyCode})};return(y,M)=>{const H=ft,c=Ke,u=Me,r=qe,w=ht,O=at,X=Fe,z=Ye,re=je,ie=Je,pe=Xe,_e=Ve,fe=Ge;return n(),s("div",null,[e(_e,{ref:"formRef1",model:a(i),"label-col":{span:5},"wrapper-col":{span:19}},{default:t(()=>[e(ee,{title:"并网信息",itemKey:"1",defaultShow:a(l).defaultShow_1,isHideSwitch:a(l).isHideSwitch_1,borderBottom:!0,onChangeKey:T},{default:t(()=>[C("div",Cl,[(n(!0),s(Ae,null,Ue(a(i).ongridInfo,(P,A)=>(n(),s("div",{key:A},[e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"事项",name:"matter",rules:[{required:!0}]},{default:t(()=>[e(H,{value:P.matter,"onUpdate:value":g=>P.matter=g,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(u,{span:12},{default:t(()=>[e(c,{label:"状态",name:"status",rules:[{required:!0}]},{default:t(()=>[P.status?(n(),s("span",wl,"√")):(n(),s("span",Sl))]),_:2},1024)]),_:2},1024)]),_:2},1024),e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"操作时间",name:"powerContOpedate",rules:[{required:!0}]},{default:t(()=>[e(w,{style:{width:"100%"},value:P.powerContOpedate,"onUpdate:value":g=>P.powerContOpedate=g,"value-format":"YYYY-MM-DD hh:mm:ss",disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(u,{span:12},{default:t(()=>[e(c,{label:"泰极二类户",name:"relatedTaijiTow"},{default:t(()=>[e(O,{value:P.relatedTaijiTow,"onUpdate:value":g=>P.relatedTaijiTow=g,options:a(m),disabled:""},null,8,["value","onUpdate:value","options"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"开户行",name:"openBank"},{default:t(()=>[e(H,{value:P.openBank,"onUpdate:value":g=>P.openBank=g,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(u,{span:12},{default:t(()=>[e(c,{label:"户名",name:"accountName"},{default:t(()=>[e(H,{value:P.accountName,"onUpdate:value":g=>P.accountName=g,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"账号",name:"bankno"},{default:t(()=>[e(H,{value:P.bankno,"onUpdate:value":g=>P.bankno=g,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(u,{span:12},{default:t(()=>[e(c,{label:"合同日期",name:"ongridContSignDate",rules:[{required:!0}]},{default:t(()=>[e(w,{style:{width:"100%"},value:P.ongridContSignDate,"onUpdate:value":g=>P.ongridContSignDate=g,"value-format":"YYYY-MM-DD",disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"发电户号",name:"generatorNo"},{default:t(()=>[e(H,{value:P.generatorNo,"onUpdate:value":g=>P.generatorNo=g,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(u,{span:12},{default:t(()=>[e(c,{label:"用电户号",name:"usePowerNo"},{default:t(()=>[e(H,{value:P.usePowerNo,"onUpdate:value":g=>P.usePowerNo=g,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"有效发电开始日",name:"effPowerStartDate",rules:[{required:!0}]},{default:t(()=>[e(w,{style:{width:"100%"},value:P.effPowerStartDate,"onUpdate:value":g=>P.effPowerStartDate=g,"value-format":"YYYY-MM-DD hh:mm:ss",disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024)]),_:2},1024),P.matter=="其他"?(n(),s("div",kl,[Il,e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"操作时间",name:"powerContOpedate"},{default:t(()=>[e(w,{style:{width:"100%"},value:P.powerContOpedate,"onUpdate:value":g=>P.powerContOpedate=g,"value-format":"YYYY-MM-DD hh:mm:ss",disabled:""},null,8,["value","onUpdate:value"])]),_:2},1024)]),_:2},1024),e(u,{span:12},{default:t(()=>[e(c,{label:"状态",name:"status",rules:[{required:!0}]},{default:t(()=>[P.status?(n(),s("span",Nl,"√")):(n(),s("span",$l))]),_:2},1024)]),_:2},1024)]),_:2},1024)])):j("",!0)]))),128)),e(r,{gutter:24},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(c,{label:"并网申请表",name:"ongridApplyForList",rules:[{required:!0}]},{default:t(()=>[e(X,{pictures:a(i).ongridApplyForList,onShowBigImg:f,fileNameNew:"并网申请表"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"并网合同附件",itemKey:"2",defaultShow:a(l).defaultShow_2,isHideSwitch:a(l).isHideSwitch_2,borderBottom:!1,onChangeKey:T},{default:t(()=>[C("div",Ll,[C("div",Pl,[e(pe,{columns:a(E),"data-source":a(U),pagination:!1,loading:a(k)},{bodyCell:t(({column:P,text:A,record:g,index:Q})=>{var ae;return[P.dataIndex=="fileName"?(n(),s("div",Tl,[C("div",xl,[G.includes(_(A))?(n(),s("img",{key:0,src:a(me)({...g,companyCode:(ae=a(d).value)==null?void 0:ae.companyCode})},null,8,Bl)):_(A)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,Al)):_(A)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,Dl)):_(A)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,Ol)):_(A)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,El)):_(A)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,Hl)):(n(),s("img",{key:6,src:a(Ie)},null,8,Ul)),e(z,{placement:"topLeft"},{title:t(()=>[D(I(A),1)]),default:t(()=>[C("div",Fl,I(A),1)]),_:2},1024)])])):P.dataIndex=="id"?(n(),s("div",Rl,I(Q+1),1)):j("",!0),P.key=="action"?(n(),s("div",Kl,[e(ie,null,{default:t(()=>[G.includes(_(g.fileName))?(n(),be(re,{key:0,size:"small",type:"link",onClick:()=>F(g)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(re,{size:"small",type:"link",onClick:()=>W(g)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"])]),_:1},8,["model"]),e(fe,{style:{display:"none"},preview:{visible:a(v).visiblePic,onVisibleChange:L},src:a(v).src},null,8,["preview","src"])])}}});const ql=Ne(Ml,[["__scopeId","data-v-8e9e4f2e"]]),Xl={class:"sec_content"},Yl={class:"table_wrap"},jl={key:0},Vl={class:"sec_content"},Jl={class:"sec_content"},Gl={class:"sec_content"},zl={class:"sec_content"},Ql={class:"table_wrap"},Wl={key:0},Zl={class:"fileTableAction"},eo=["src"],to=["src"],ao=["src"],lo=["src"],oo=["src"],no=["src"],so=["src"],io={class:"ellipsis"},co={key:1},uo={key:2},ro=he({__name:"TabFive",props:{stationCode:{type:String,require:!0}},setup(h){var H;const o=h,i=N((H=history.state)==null?void 0:H.pdata),d=N(!1),k=N(!1);let l=ue({}),m=ue({defaultShow_1:!0});const T=(c,u)=>{console.log("itemKey=",c),console.log("defaultShow=",u),m["defaultShow_"+c]=!u};let v=ue({src:"",visiblePic:!1});const L=c=>{v.visiblePic=c},f=c=>{v.src=c,v.visiblePic=!0},B=()=>{const c={stationCode:o.stationCode};Dt(c).then(u=>{console.log(u),_.value=u,d.value=!1}).catch(u=>{console.log(u),d.value=!1})},K=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"PROJECT_INFO",secondLabel:"6",companyCode:(u=i.value)==null?void 0:u.companyCode};se(c).then(r=>{l.profitBankCard=r})},x=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"PROJECT_PHOTO_LIST",companyCode:(u=i.value)==null?void 0:u.companyCode};se(c).then(r=>{console.log("----工程信息总文件--：",r),l.phtopics1=Z(r,"101"),l.phtopics2=Z(r,"102"),l.phtopics3=Z(r,"103"),l.phtopics4=Z(r,"104"),l.phtopics5=Z(r,"105"),l.phtopics6=Z(r,"106"),l.phtopics7=Z(r,"107"),l.phtopics8=Z(r,"108"),l.phtopics9=Z(r,"109"),l.phtopics10=Z(r,"110"),l.phtopics11=Z(r,"111"),l.phtopics12=Z(r,"113"),l.phtopics13=Z(r,"115")})},b=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"PROJECT_RECORD_LIST",companyCode:(u=i.value)==null?void 0:u.companyCode};se(c).then(r=>{l.installRecordList=Z(r,"16"),l.checkRecordList=Z(r,"19")})},E=()=>{var u;const c={StationCode:o.stationCode,firstLabel:"PROJECT_OTHER",secondLabel:"20",companyCode:(u=i.value)==null?void 0:u.companyCode};k.value=!0,se(c).then(r=>{console.log(r),F.value=r,k.value=!1}).catch(r=>{console.log(r),k.value=!1})};Pe(()=>{o!=null&&o.stationCode&&(B(),K(),x(),b(),E())}),Oe(()=>{l={}});const U=N([{title:"序号",dataIndex:"id",width:60},{title:"物料名称",dataIndex:"materialName",ellipsis:!0,width:220},{title:"物料数量",dataIndex:"deviceNum",ellipsis:!0,width:100}]),G=N([{title:"序号",dataIndex:"id",width:60},{title:"附件名称",dataIndex:"fileName",ellipsis:!0,width:220},{title:"操作",key:"action",width:90}]);let _=N([]),F=N([]);const W=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],V=c=>{if(!c)return"";var u=c.match(/\.([^.]+)$/);return u?u[1].toUpperCase():""},y=async c=>{var r;const u=me({...c,companyCode:(r=i.value)==null?void 0:r.companyCode});f(u)},M=async c=>{var u;Re({fileId:c.id,companyCode:(u=i.value)==null?void 0:u.companyCode})};return(c,u)=>{const r=Xe,w=Fe,O=Ke,X=Me,z=qe,re=Ye,ie=je,pe=Je,_e=Ve,fe=Ge;return n(),s("div",null,[e(_e,{ref:"formRef1",model:a(l),"label-col":{span:7},"wrapper-col":{span:17}},{default:t(()=>[e(ee,{title:"主要设备信息表",itemKey:"1",defaultShow:a(m).defaultShow_1,isHideSwitch:a(m).isHideSwitch_1,borderBottom:!0,onChangeKey:T},{default:t(()=>[C("div",Xl,[C("div",Yl,[e(r,{columns:a(U),"data-source":a(_),pagination:!1,loading:a(d)},{bodyCell:t(({column:P,text:A,record:g,index:Q})=>[P.dataIndex=="id"?(n(),s("div",jl,I(Q+1),1)):j("",!0)]),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"施工资料登记",itemKey:"2",defaultShow:a(m).defaultShow_2,isHideSwitch:a(m).isHideSwitch_2,borderBottom:!0,onChangeKey:T},{default:t(()=>[C("div",Vl,[e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"施工交底文件",name:"profitBankCard",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).profitBankCard,onShowBigImg:f,fileNameNew:"施工交底文件"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"工程图片列表",itemKey:"3",defaultShow:a(m).defaultShow_3,isHideSwitch:a(m).isHideSwitch_3,borderBottom:!0,onChangeKey:T},{default:t(()=>[C("div",Jl,[e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"支架整体图一",name:"phtopics1",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics1,onShowBigImg:f,fileNameNew:"支架整体图一"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"支架整体图二",name:"phtopics2",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics2,onShowBigImg:f,fileNameNew:"支架整体图二"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"方针倾角测试照片/视频",name:"phtopics3",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics3,onShowBigImg:f,fileNameNew:"方针倾角测试照片/视频"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"方阵整体图一(全景)",name:"phtopics4",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics4,onShowBigImg:f,fileNameNew:"方阵整体图一(全景)"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"方阵整体图二",name:"phtopics5",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics5,onShowBigImg:f,fileNameNew:"方阵整体图二"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"方阵整体图三",name:"phtopics6",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics6,onShowBigImg:f,fileNameNew:"方阵整体图三"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"逆变器整体图",name:"phtopics7",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics7,onShowBigImg:f,fileNameNew:"逆变器整体图"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"光伏电缆走线",name:"phtopics8",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics8,onShowBigImg:f,fileNameNew:"光伏电缆走线"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"电表箱外部整体",name:"phtopics9",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics9,onShowBigImg:f,fileNameNew:"电表箱外部整体"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"电表箱内部",name:"phtopics10",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics10,onShowBigImg:f,fileNameNew:"电表箱内部"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"支架接地",name:"phtopics11",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics11,onShowBigImg:f,fileNameNew:"支架接地"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"接地点",name:"phtopics12",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics12,onShowBigImg:f,fileNameNew:"接地点"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"交流电缆走线",name:"phtopics13",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).phtopics13,onShowBigImg:f,fileNameNew:"交流电缆走线"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"工程信息记录列表",itemKey:"4",defaultShow:a(m).defaultShow_4,isHideSwitch:a(m).isHideSwitch_4,borderBottom:!0,onChangeKey:T},{default:t(()=>[C("div",Gl,[e(z,{gutter:24},{default:t(()=>[e(X,{span:12},{default:t(()=>[e(O,{label:"现场安装调试检测记录表",name:"installRecordList",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).installRecordList,onShowBigImg:f,fileNameNew:"现场安装调试检测记录表"},null,8,["pictures"])]),_:1})]),_:1}),e(X,{span:12},{default:t(()=>[e(O,{label:"自检表",name:"checkRecordList",rules:[{required:!0}]},{default:t(()=>[e(w,{pictures:a(l).checkRecordList,onShowBigImg:f,fileNameNew:"自检表"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"附件",itemKey:"5",defaultShow:a(m).defaultShow_5,isHideSwitch:a(m).isHideSwitch_5,borderBottom:!1,onChangeKey:T},{default:t(()=>[C("div",zl,[C("div",Ql,[e(r,{columns:a(G),"data-source":a(F),pagination:!1,loading:a(k)},{bodyCell:t(({column:P,text:A,record:g,index:Q})=>{var ae;return[P.dataIndex=="fileName"?(n(),s("div",Wl,[C("div",Zl,[W.includes(V(A))?(n(),s("img",{key:0,src:a(me)({...g,companyCode:(ae=a(i).value)==null?void 0:ae.companyCode})},null,8,eo)):V(A)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,to)):V(A)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,ao)):V(A)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,lo)):V(A)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,oo)):V(A)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,no)):(n(),s("img",{key:6,src:a(Ie)},null,8,so)),e(re,{placement:"topLeft"},{title:t(()=>[D(I(A),1)]),default:t(()=>[C("div",io,I(A),1)]),_:2},1024)])])):P.dataIndex=="id"?(n(),s("div",co,I(Q+1),1)):j("",!0),P.key=="action"?(n(),s("div",uo,[e(pe,null,{default:t(()=>[W.includes(V(g.fileName))?(n(),be(ie,{key:0,size:"small",type:"link",onClick:()=>y(g)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(ie,{size:"small",type:"link",onClick:()=>M(g)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"])]),_:1},8,["model"]),e(fe,{style:{display:"none"},preview:{visible:a(v).visiblePic,onVisibleChange:L},src:a(v).src},null,8,["preview","src"])])}}});const _o=Ne(ro,[["__scopeId","data-v-2d268a1c"]]),po={class:"sec_content"},fo={class:"sec_content"},mo={class:"sec_content"},ho={class:"table_wrap"},vo={key:0},go={class:"fileTableAction"},yo=["src"],bo=["src"],Co=["src"],wo=["src"],So=["src"],ko=["src"],Io=["src"],No={class:"ellipsis"},$o={key:1},Lo={key:2},Po={class:"sec_content"},To={class:"table_wrap"},xo={key:0},Bo={class:"fileTableAction"},Ao=["src"],Do=["src"],Oo=["src"],Eo=["src"],Ho=["src"],Uo=["src"],Fo=["src"],Ro={class:"ellipsis"},Ko={key:1},Mo={key:2},qo=he({__name:"TabSix",props:{stationCode:{default:""},pdata:{default:{}}},setup(h){const o=h,{pdata:i}=Wt(o),d=N(!1),k=N(!1);let l=ue({});const m=N([]);let T=ue({defaultShow_1:!0});const v=(c,u)=>{console.log("itemKey=",c),console.log("defaultShow=",u),T["defaultShow_"+c]=!u};let L=ue({src:"",visiblePic:!1});const f=c=>{L.visiblePic=c},B=c=>{L.src=c,L.visiblePic=!0},K=()=>{const c=JSON.parse(localStorage.getItem("dictYutai")||"{}");c&&c.HOUSE_TYPE&&(m.value=c.HOUSE_TYPE)},x=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"INFORMATION_INFO",companyCode:(u=i.value)==null?void 0:u.companyCode};se(c).then(r=>{console.log("----资料清单文件--：",r),l.idCard=Z(r,"1"),l.houseCertificate=Z(r,"2"),l.profitBankCard=Z(r,"20"),W.value=l.houseCertificate,k.value=!1}).catch(r=>{console.log(r),k.value=!1})},b=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"COMMERCIAL_INFO",companyCode:(u=i.value)==null?void 0:u.companyCode};se(c).then(r=>{l.residentContract=Z(r,"1"),l.profitShareBankCard=Z(r,"2")})},E=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"PROJECT_INFO",secondLabel:"6",companyCode:(u=i.value)==null?void 0:u.companyCode};se(c).then(r=>{l.constructionFile=r})},U=()=>{var u;const c={stationCode:o.stationCode,firstLabel:"MERGE_NET_CONTRACT",companyCode:(u=i.value)==null?void 0:u.companyCode};d.value=!0,se(c).then(r=>{console.log("资料管理-=:",r),F.value=r,d.value=!1}).catch(r=>{console.log(r),d.value=!1})};Pe(()=>{var c;l.houseFileType=(c=i.value)==null?void 0:c.houseFileType,o!=null&&o.stationCode&&(K(),x(),b(),E(),U(),console.log(i.value,"-------房屋类型",l.houseFileType))}),mt(i,(c,u)=>{var r;l.houseFileType=(r=i.value)==null?void 0:r.houseFileType}),Oe(()=>{l={}});const G=N([{title:"序号",dataIndex:"id",width:60},{title:"附件名称",dataIndex:"fileName",ellipsis:!0,width:220},{title:"操作",key:"action",width:80}]),_=N([{title:"序号",dataIndex:"id",width:60},{title:"创建日期",dataIndex:"createTime",ellipsis:!0,width:180},{title:"附件名称",dataIndex:"fileName",ellipsis:!0,width:220},{title:"上传人",dataIndex:"createBy",ellipsis:!0,width:100},{title:"操作",key:"action",width:90}]);let F=N([]),W=N([]);const V=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],y=c=>{if(!c)return"";var u=c.match(/\.([^.]+)$/);return u?u[1].toUpperCase():""},M=async c=>{var r;const u=me({...c,companyCode:(r=i.value)==null?void 0:r.companyCode});B(u)},H=async c=>{var u;Re({fileId:c.id,companyCode:(u=i.value)==null?void 0:u.companyCode})};return(c,u)=>{const r=at,w=Ke,O=Me,X=qe,z=Fe,re=Ye,ie=je,pe=Je,_e=Xe,fe=Ve,P=Ge;return n(),s("div",null,[e(fe,{ref:"formRef1",model:a(l),"label-col":{span:6},"wrapper-col":{span:18}},{default:t(()=>[e(ee,{itemKey:"1",defaultShow:!0,isHideSwitch:!0,borderBottom:!0,onChangeKey:v},{default:t(()=>[C("div",po,[e(X,{gutter:24},{default:t(()=>[e(O,{span:12},{default:t(()=>[e(w,{label:"房产证明类型",name:"houseFileType",rules:[{required:!0}]},{default:t(()=>[e(r,{value:a(l).houseFileType,"onUpdate:value":u[0]||(u[0]=A=>a(l).houseFileType=A),options:a(m),disabled:""},null,8,["value","options"])]),_:1})]),_:1})]),_:1})])]),_:1}),e(ee,{title:"资料清单",itemKey:"2",defaultShow:a(T).defaultShow_2,isHideSwitch:a(T).isHideSwitch_2,borderBottom:!0,onChangeKey:v},{default:t(()=>[C("div",fo,[e(X,{gutter:24},{default:t(()=>[e(O,{span:12},{default:t(()=>[e(w,{label:"身份证原件照片",name:"idCard",rules:[{required:!0}]},{default:t(()=>{var A;return[e(z,{pictures:a(l).idCard,onShowBigImg:B,companyCode:(A=a(i))==null?void 0:A.companyCode,fileNameNew:"身份证原件照片"},null,8,["pictures","companyCode"])]}),_:1})]),_:1}),e(O,{span:12},{default:t(()=>[e(w,{label:"房产证明文件",name:"houseCertificate",rules:[{required:!0}]},{default:t(()=>[e(z,{pictures:a(l).houseCertificate,onShowBigImg:B,fileNameNew:"房产证明文件"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(X,{gutter:24},{default:t(()=>[e(O,{span:12},{default:t(()=>[e(w,{label:"与居民的合同",name:"residentContract",rules:[{required:!0}]},{default:t(()=>[e(z,{pictures:a(l).residentContract,onShowBigImg:B,fileNameNew:"与居民的合同"},null,8,["pictures"])]),_:1})]),_:1}),e(O,{span:12},{default:t(()=>[e(w,{label:"收益分享银行卡",name:"profitShareBankCard",rules:[{required:!0}]},{default:t(()=>[e(z,{pictures:a(l).profitShareBankCard,onShowBigImg:B,fileNameNew:"收益分享银行卡"},null,8,["pictures"])]),_:1})]),_:1})]),_:1}),e(X,{gutter:24},{default:t(()=>[e(O,{span:12},{default:t(()=>[e(w,{label:"施工交底文件",name:"constructionFile",rules:[{required:!0}]},{default:t(()=>[e(z,{pictures:a(l).constructionFile,onShowBigImg:B,fileNameNew:"施工交底文件"},null,8,["pictures"])]),_:1})]),_:1}),e(O,{span:12},{default:t(()=>[e(w,{label:"电网收益银行卡",name:"profitBankCard",rules:[{required:!0}]},{default:t(()=>[e(z,{pictures:a(l).profitBankCard,onShowBigImg:B,fileNameNew:"电网收益银行卡"},null,8,["pictures"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"并网合同列表",itemKey:"3",defaultShow:a(T).defaultShow_3,isHideSwitch:a(T).isHideSwitch_3,borderBottom:!0,onChangeKey:v},{default:t(()=>[C("div",mo,[C("div",ho,[e(_e,{columns:a(G),"data-source":a(F),pagination:!1,loading:a(d)},{bodyCell:t(({column:A,text:g,record:Q,index:ae})=>{var le;return[A.dataIndex=="fileName"?(n(),s("div",vo,[C("div",go,[V.includes(y(g))?(n(),s("img",{key:0,src:a(me)({...Q,companyCode:(le=a(i))==null?void 0:le.companyCode})},null,8,yo)):y(g)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,bo)):y(g)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,Co)):y(g)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,wo)):y(g)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,So)):y(g)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,ko)):(n(),s("img",{key:6,src:a(Ie)},null,8,Io)),e(re,{placement:"topLeft"},{title:t(()=>[D(I(g),1)]),default:t(()=>[C("div",No,I(g),1)]),_:2},1024)])])):A.dataIndex=="id"?(n(),s("div",$o,I(ae+1),1)):j("",!0),A.key=="action"?(n(),s("div",Lo,[e(pe,null,{default:t(()=>{var J;return[V.includes(Q.fileName.substring(((J=Q.fileName)==null?void 0:J.lastIndexOf("."))+1))?(n(),be(ie,{key:0,size:"small",type:"link",onClick:()=>M(Q)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(ie,{size:"small",type:"link",onClick:()=>H(Q)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]}),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"]),e(ee,{title:"房产证明附件",itemKey:"4",defaultShow:a(T).defaultShow_4,isHideSwitch:a(T).isHideSwitch_4,borderBottom:!1,onChangeKey:v},{default:t(()=>[C("div",Po,[C("div",To,[e(_e,{columns:a(_),"data-source":a(W),pagination:!1,loading:a(k)},{bodyCell:t(({column:A,text:g,record:Q,index:ae})=>{var le;return[A.dataIndex=="fileName"?(n(),s("div",xo,[C("div",Bo,[V.includes(y(g))?(n(),s("img",{key:0,src:a(me)({...Q,companyCode:(le=a(i))==null?void 0:le.companyCode})},null,8,Ao)):y(g)=="PDF"?(n(),s("img",{key:1,src:a(ke)},null,8,Do)):y(g)=="XLS"?(n(),s("img",{key:2,src:a(de)},null,8,Oo)):y(g)=="XLSX"?(n(),s("img",{key:3,src:a(de)},null,8,Eo)):y(g)=="DOC"?(n(),s("img",{key:4,src:a(ce)},null,8,Ho)):y(g)=="DOCX"?(n(),s("img",{key:5,src:a(ce)},null,8,Uo)):(n(),s("img",{key:6,src:a(Ie)},null,8,Fo)),e(re,{placement:"topLeft"},{title:t(()=>[D(I(g),1)]),default:t(()=>[C("div",Ro,I(g),1)]),_:2},1024)])])):A.dataIndex=="id"?(n(),s("div",Ko,I(ae+1),1)):j("",!0),A.key=="action"?(n(),s("div",Mo,[e(pe,null,{default:t(()=>[V.includes(y(Q.fileName))?(n(),be(ie,{key:0,size:"small",type:"link",onClick:()=>M(Q)},{default:t(()=>[D(" 查看 ")]),_:2},1032,["onClick"])):j("",!0),e(ie,{size:"small",type:"link",onClick:()=>H(Q)},{default:t(()=>[D(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)])):j("",!0)]}),_:1},8,["columns","data-source","loading"])])])]),_:1},8,["defaultShow","isHideSwitch"])]),_:1},8,["model"]),e(P,{style:{display:"none"},preview:{visible:a(L).visiblePic,onVisibleChange:f},src:a(L).src},null,8,["preview","src"])])}}});const Xo=Ne(qo,[["__scopeId","data-v-18d27b52"]]),Yo="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJcSURBVHgBlVVLctpAEO0eqNjJiiPIm5RhRQJZZKecIOYCAZ2A+ATIJzA5gcQJrBsErw1l7UxlE46gnV2xpcnrkUSNBFjlrqI0n57Xb153D0wHzLkPOu9TNSbSLjH1SZNjNpi2GMeaONoMx4tDZ7m+8HEVXLSJA03UKZYAQDHnc/m5xfoWwH4duALYWwVzOE2xmGTM8yeV/dp+8hLb5/w+cCgjl4lnwlxrfbX54vl7gL27wNfMM7BZPrX0qA5UNwHmlK8xvADK/GEwudwBnq+DCWsOMIwehpMRvcG6q/C3yJCSHv0ZelE7RwV90eRZX1ZY3AUugzWi9kVTYQ/v0NbtBLf5l/LfFnRHMpdK2BktROCv3taWAGAmepkgAEM7HSKQX/rFIg30xrBzgspQuOp3s/Oc3e6YIYjoeeyawlrYl/N3SF4+0q5CVEeuYrNDkDE1mRVQWAInVpBG6VyfekbdRjycs+cZU4yPo4rNpOacULN1Kmd0PlfSTvVo0hlNaHnG9wLg2toc7kv/7pxR/dRoHJYj0z2QKUPp4cq8lMXTTE1LB7TSEpn0j0GhAqo9nOWao54j7oOZFCauQI8tfWa3XNFBknHX9LdIoZS/+fzj1mbHGeoVAOiys7akvLsKPODfnKZ8A59vO6YDL8QnpFdMoZ+1KT2e5CwL667Da0T5iWEkLWjX5SHL30xDwDUSDMZXFUCx3noxQ0J8Kt466Z46sAB9eEFg5qlpSeul2QOkUrfirZO5lIfoJ8VflFe/OJi8kPbkhbHPMx2xIiHS504JUv4FSGU8trLFoTfzP8oVECJZBfWuAAAAAElFTkSuQmCC",jo=h=>(lt("data-v-20eaf55e"),h=h(),ot(),h),Vo=jo(()=>C("img",{class:"img-node",src:Yo,alt:""},null,-1)),Jo={class:"item-title"},Go={class:"item-time"},zo=he({__name:"index",props:{lineData:{type:Array,default:[],required:!1}},setup(h){return(o,i)=>{const d=De,k=Be;return n(),be(k,{class:"timeline-page",mode:"left"},{default:t(()=>[(n(!0),s(Ae,null,Ue(h.lineData,(l,m)=>(n(),be(d,{key:m},{dot:t(()=>[Vo]),default:t(()=>[C("p",Jo,I((l==null?void 0:l.businessModuleString)||"")+I(l.createBy||"")+I((l==null?void 0:l.details)||""),1),C("p",Go,I(l.createTime),1)]),_:2},1024))),128))]),_:1})}}});const Qo=Ne(zo,[["__scopeId","data-v-20eaf55e"]]),Wo={class:"sec_content"},Zo=he({__name:"TabSev",props:{stationCode:{type:String,require:!0}},setup(h){const o=h,i=N([]);let d=ue({defaultShow_1:!0});const k=(m,T)=>{console.log("itemKey=",m),console.log("defaultShow=",T),d["defaultShow_"+m]=!T},l=()=>{Ot({stationCode:o.stationCode}).then(m=>{console.log("日志记录==",m),i.value=m})};return Pe(()=>{l()}),mt(()=>o.stationCode,m=>{}),(m,T)=>{const v=Qo;return n(),s("div",null,[e(ee,{title:"日志详情",itemKey:"1",defaultShow:a(d).defaultShow_1,isHideSwitch:a(d).isHideSwitch_1,borderBottom:!1,onChangeKey:k},{default:t(()=>[C("div",Wo,[e(v,{lineData:a(i)},null,8,["lineData"])])]),_:1},8,["defaultShow","isHideSwitch"])])}}});const en=Ne(Zo,[["__scopeId","data-v-007684e6"]]),tn=h=>(lt("data-v-7f946bb5"),h=h(),ot(),h),an={class:"assetInfoDetail"},ln={key:0,class:"basic_info common-card"},on=tn(()=>C("div",{class:"title"},"基础信息",-1)),nn={class:"basic_content"},sn={class:"content common-card"},dn=he({__name:"Detail",setup(h){var f,B;const o=N({});console.log("电站档案管理详情pdata=",o.value);const i=N("1"),d=Zt(window.location.href);console.log("obj=",d);const k=N((d==null?void 0:d.isHiddenBaseInfo)==="1"),l=(d==null?void 0:d.stationId)||((B=(f=history.state)==null?void 0:f.pdata)==null?void 0:B.stationUniqueId),m=N(l),T=K=>{console.log(K)};Pe(()=>{var K,x;if(console.log(o.value,"pdata--"),(K=v==null?void 0:v.query)!=null&&K.stationId)L();else{const b=(x=history.state)==null?void 0:x.pdata,E={...b,companyCode:b==null?void 0:b.ownerCompanyCode};o.value=E}});const v=ea(),L=()=>{var b;let x={stationUniqueId:(b=v==null?void 0:v.query)==null?void 0:b.stationId,pageNum:1,pageSize:10,delStatus:0};Et(x).then(E=>{if(console.log("res=",E),(E==null?void 0:E.code)===200&&E!=null&&E.data&&(E==null?void 0:E.data.length)>0){let U=E==null?void 0:E.data[0];o.value={...U,companyCode:U==null?void 0:U.ownerCompanyCode}}})};return(K,x)=>{const b=vt,E=pa,U=oa,G=na;return n(),s("div",an,[a(k)?j("",!0):(n(),s("div",ln,[on,C("div",nn,[e(E,{column:3},{default:t(()=>[e(b,{label:"电站编号"},{default:t(()=>{var _;return[D(I((_=a(o))==null?void 0:_.stationUniqueId),1)]}),_:1}),e(b,{label:"业主姓名"},{default:t(()=>{var _;return[D(I((_=a(o))==null?void 0:_.stationName),1)]}),_:1}),e(b,{label:"产权公司"},{default:t(()=>{var _;return[D(I((_=a(o))==null?void 0:_.ownerCompanyName),1)]}),_:1}),e(b,{label:"业主类型"},{default:t(()=>{var _;return[D(I(a(We)((_=a(o))==null?void 0:_.tyPower,a(Ze)("PROPRIETOR_TYPE"))),1)]}),_:1}),e(b,{label:"联系电话"},{default:t(()=>{var _,F;return[D(I((F=(_=a(o))==null?void 0:_.householdInfo)==null?void 0:F.nimMpHoh),1)]}),_:1}),e(b,{label:"身份证号"},{default:t(()=>{var _,F;return[D(I((F=(_=a(o))==null?void 0:_.householdInfo)==null?void 0:F.numIdHoh),1)]}),_:1}),e(b,{label:"业主年龄"},{default:t(()=>{var _,F;return[D(I((F=(_=a(o))==null?void 0:_.householdInfo)==null?void 0:F.age),1)]}),_:1}),e(b,{label:"装机容量"},{default:t(()=>{var _,F;return[D(I((_=a(o))!=null&&_.capins?a(ta)((F=a(o))==null?void 0:F.capins)+" kW":""),1)]}),_:1}),e(b,{label:"制单时间"},{default:t(()=>{var _;return[D(I(a(aa)((_=a(o))==null?void 0:_.operateDate)),1)]}),_:1}),e(b,{label:"上网类型"},{default:t(()=>{var _;return[D(I(a(We)((_=a(o))==null?void 0:_.netmode,a(Ze)("POWERSTA_NETMODE"))),1)]}),_:1}),e(b,{label:"电站类型"},{default:t(()=>{var _;return[D(I(a(We)((_=a(o))==null?void 0:_.businessStationType,a(Ze)("EAM_POWER_STATION_TYPE_CONDITION"))),1)]}),_:1}),e(b,{label:"项目公司"},{default:t(()=>{var _;return[D(I((_=a(o))==null?void 0:_.projectCompanyName),1)]}),_:1}),e(b,{label:"行政区划"},{default:t(()=>{var _,F,W;return[D(I(((_=a(o))==null?void 0:_.provinceName)||"")+I(((F=a(o))==null?void 0:F.cityName)||"")+I(((W=a(o))==null?void 0:W.areaName)||""),1)]}),_:1}),e(b,{label:"详细地址"},{default:t(()=>{var _;return[D(I((_=a(o))==null?void 0:_.projectLocation),1)]}),_:1}),e(b,{label:"组件数量"},{default:t(()=>{var _;return[D(I((_=a(o))==null?void 0:_.componentQuantity),1)]}),_:1})]),_:1})])])),C("div",sn,[e(G,{activeKey:a(i),"onUpdate:activeKey":x[0]||(x[0]=_=>la(i)?i.value=_:null),onChange:T},{default:t(()=>[e(U,{key:"1",tab:"勘查信息"},{default:t(()=>[e(Nt,{stationCode:a(m),pdata:a(o)},null,8,["stationCode","pdata"])]),_:1}),e(U,{key:"2",tab:"设计信息"},{default:t(()=>[e($t,{stationCode:a(m),pdata:a(o)},null,8,["stationCode","pdata"])]),_:1}),e(U,{key:"3",tab:"商务信息"},{default:t(()=>[e(yl,{stationCode:a(m)},null,8,["stationCode"])]),_:1}),e(U,{key:"4",tab:"并网信息"},{default:t(()=>[e(ql,{stationCode:a(m)},null,8,["stationCode"])]),_:1}),e(U,{key:"5",tab:"工程信息"},{default:t(()=>[e(_o,{stationCode:a(m)},null,8,["stationCode"])]),_:1}),e(U,{key:"6",tab:"资料归档"},{default:t(()=>[e(Xo,{stationCode:a(m),pdata:a(o)},null,8,["stationCode","pdata"])]),_:1}),e(U,{key:"7",tab:"日志记录"},{default:t(()=>[e(en,{stationCode:a(m)},null,8,["stationCode"])]),_:1})]),_:1},8,["activeKey"])])])}}});const wn=Ne(dn,[["__scopeId","data-v-7f946bb5"]]);export{wn as default};
