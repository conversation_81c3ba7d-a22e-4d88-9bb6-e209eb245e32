import{a2 as t,bP as n}from"./index-db94d997.js";function r(e){return t({url:"/web/incomeConfirm/v1/page",method:"POST",isTable:!0,data:e})}function i(e){return n({url:"/web/incomeConfirm/v1/exprot",method:"POST",data:e})}function m(e){return t({url:"/web/incomeConfirmDetail/v1/page",method:"POST",isTable:!0,data:e})}function a(e){return n({url:"/web/incomeConfirmDetail/v1/exprot",method:"POST",data:e})}function u(e){return t({url:"/web/incomePatternConfig/v1/getThisMonthIncomePattern",method:"POST",isTable:!0,data:e})}function c(e){return t({url:"/web/incomePatternConfig/v1/saveIncomePattern",method:"POST",data:e})}export{m as a,a as b,u as c,i as e,r as g,c as s};
