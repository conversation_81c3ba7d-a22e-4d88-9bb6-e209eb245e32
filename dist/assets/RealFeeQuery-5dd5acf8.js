import{_ as L}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as z,r as s,I as F,o as T,D as k,at as E,a as x,v as q,f as l,e as n,u as b,b as B,h as y,y as P,i as m,g,bg as O,bn as Q,_ as V}from"./index-db94d997.js";import{h as Y,i as j}from"./index-bad8f65c.js";import{_ as A}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const G={class:"areaPrice"},H={class:"text-hide"},J={class:"text-hide"},$=z({__name:"RealFeeQuery",setup(U){const d=s(),w=["provinceCodeList","cityCodeList","areaCodeList"],C=s([]),h=s([]);s(!1),F();const I=[{title:"电站编码",dataIndex:"stationCode",search:!0,resizable:!0,width:100},{title:"业主名称",dataIndex:"stationName",search:!0,resizable:!0,width:100},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,valueEnum:h,width:100,resizable:!0,render:!0},{title:"收款月份",dataIndex:"monthKey",valueType:"dateRange",dateFormat:"YYYY-MM",resizable:!0,width:100},{title:"操作时间",dataIndex:"createTime",search:!1,resizable:!0,width:100},{title:"实收电费",dataIndex:"fee",search:!1,resizable:!0,formatMoney:!0,width:100},{title:"调入电费",dataIndex:"intoMoney",search:!1,resizable:!0,formatMoney:!0,width:100},{title:"调出电费",dataIndex:"outMoney",search:!1,resizable:!0,formatMoney:!0,width:100},{title:"最终实收电费",dataIndex:"ultimatelyMoney",search:!1,resizable:!0,formatMoney:!0,width:100}];T(()=>{K()}),k(()=>{d.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&N()});const K=()=>{E({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));h.value=o})},M=()=>{var a;let e=(a=d.value)==null?void 0:a.getInitialFormStateNew();j(e)},f=s([]),D=e=>{f.value=e||[],console.log("dataSource=",e)},N=()=>{var e;(e=d.value)==null||e.reload()},R=e=>{const a=new Map;return e.forEach(o=>{const t=w[o.level-1];if(a.has(t))a.get(t).push(o.value);else{let r=[];r.push(o.value),a.set(t,r)}}),Object.fromEntries(a)},u=(e,a,o)=>!e||!a?[]:(e.forEach(t=>{a.find(i=>t.value===i)&&o.push(t),t.children&&t.children.length>0&&u(t.children,a,o)}),o),S=(e,a)=>new Promise(o=>{const t=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[0]:"",r=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[1]:"";let i={delStatus:0,noJoin:!0,startMonthKey:t,endMonthKey:r};const p=u(C.value,e==null?void 0:e.cityTree,[]);let c=R(p);e==null||delete e.monthKey;const _={...i,...e,...c};o(_)});return(e,a)=>{const o=O,t=A,r=Q,i=L;return x(),q("div",G,[l(i,{columns:I,ref_key:"actionRef",ref:d,request:b(Y),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:D,"before-query-params":S},{tableHeader:n(()=>[l(t,null,{default:n(()=>[b(f).length>0?(x(),B(o,{key:0,type:"primary",onClick:M},{default:n(()=>[y("导出")]),_:1})):P("",!0)]),_:1})]),companyCodeListRender:n(({column:p,record:c,index:_})=>[l(r,null,{title:n(()=>[y(m(c.companyName),1)]),default:n(()=>[g("span",H,m(c.companyName),1)]),_:2},1024)]),monthRender:n(({column:p,record:c,index:_})=>[l(r,null,{title:n(()=>[y(m(c.yKey),1)]),default:n(()=>[g("span",J,m(c.yKey),1)]),_:2},1024)]),_:1},8,["request"])])}}});const re=V($,[["__scopeId","data-v-ee5c0687"]]);export{re as default};
