import{_ as T}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as M,r as n,o as S,D as E,au as N,a as _,v as k,f as x,e as f,u as I,b as F,h as q,y as K,bg as B,_ as O}from"./index-db94d997.js";import{a as R,b as V}from"./index-ccc1a7be.js";import{_ as Y}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const L={class:"areaPrice"},Q=M({__name:"Info",setup(A){var b;const h=n(((b=history.state)==null?void 0:b.pdata)||{}),i=n();n([]);const d=n([]),w=n([{label:"是",value:1},{label:"否",value:0}]);let c="",u="";const v=[{title:"电站编号",dataIndex:"stationCode",resizable:!0,fixed:"left",width:120},{title:"业主名称",dataIndex:"stationName",resizable:!0,fixed:"left",width:120},{title:"产权公司",dataIndex:"companyName",width:150,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:d,valueTreeLoad:({id:t})=>m(t),onSelect:(t,e,a)=>{var s,l;const r=((l=(s=a==null?void 0:a.triggerNode)==null?void 0:s.props)==null?void 0:l.level)||1;c=["prvCode","cityCode","distCode","town","vil"][r-1],u=t},render:!0,width:150,resizable:!0,hideInTable:!0},{title:"省",dataIndex:"prvName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"distName",search:!1,resizable:!0,width:100},{title:"装机容量(kW)",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0,search:!1},{title:"组件数量",dataIndex:"componentQuantity",resizable:!0,search:!1,width:120},{title:"并网时间",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",resizable:!0,search:!1,width:120},{title:"月份",dataIndex:"monthKey",resizable:!0,search:!1,width:120},{title:"标杆电价",dataIndex:"sightcingBuelectrovalences",resizable:!0,search:!1,formatDecimal:4,width:120},{title:"补贴电价",dataIndex:"subsidyElectrovalences",resizable:!0,search:!1,formatDecimal:4,width:120},{title:"上网电价",dataIndex:"surfElectrovalences",resizable:!0,search:!1,formatDecimal:4,width:120},{title:"本月电量",dataIndex:"monthEq",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"租金单价",dataIndex:"rentUnit",resizable:!0,search:!1,width:120},{title:"本月租金",dataIndex:"monthRent",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月电费收入(含税)",dataIndex:"taxEqFeeMonth",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"本月电费收入(剔租金)",dataIndex:"eliminateEqFeeMonth",resizable:!0,search:!1,formatMoney:!0,width:160},{title:"月度运维费",dataIndex:"monthOperationFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"是否确认运维收入",dataIndex:"operationIncomeFlag",valueType:"select",valueEnum:w,resizable:!0,search:!1,width:150},{title:"本月收入(运维部分)",dataIndex:"monthOperationIncome",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"运维收入税金",dataIndex:"operationIncomeTaxes",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月收入(电费部分)",dataIndex:"monthIncomeEqFee",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"电费收入税金",dataIndex:"incomeEqFeeTaxes",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月收入总额",dataIndex:"monthIncomeSum",resizable:!0,search:!1,formatMoney:!0,width:120}];S(()=>{m()}),E(()=>{i.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&D()});const m=t=>new Promise((e,a)=>{N({pid:t||"0"}).then(r=>{console.log("行政区res=",r);let o=p(r);d.value=o,console.log(d.value),e(!0)}).catch(()=>{a()})}),p=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,p(e.subDistrict))}),t),z=()=>{var e;let t=(e=i.value)==null?void 0:e.getInitialFormStateNew();V(t)},y=n([]),g=t=>{y.value=t||[]},D=()=>{var t;(t=i.value)==null||t.reload()},C=(t,e)=>{let a={};return new Promise(r=>{var s,l;e&&e.hasOwnProperty("cityTree")&&!(e!=null&&e.cityTree)&&(c="",u=""),c&&(a={[c]:u});const o={...a,...t,noJoin:!0,delStatus:0,companyCode:(s=h.value)==null?void 0:s.companyCode,monthKey:(l=h.value)==null?void 0:l.monthKey};r(o)})};return(t,e)=>{const a=B,r=Y,o=T;return _(),k("div",L,[x(o,{columns:v,ref_key:"actionRef",ref:i,request:I(R),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:g,"before-query-params":C},{tableHeader:f(()=>[x(r,null,{default:f(()=>[I(y).length>0?(_(),F(a,{key:0,type:"primary",onClick:z},{default:f(()=>[q("导出")]),_:1})):K("",!0)]),_:1})]),_:1},8,["request"])])}}});const ae=O(Q,[["__scopeId","data-v-76fd4078"]]);export{ae as default};
