import{_ as te}from"./index-e7bdfdf4.js";import{_ as ae}from"./index-4fa991ee.js";import{a2 as J,d as oe,r as i,q as b,K as L,o as le,w as se,a as S,v as E,f as r,u as l,s as ne,e as n,h,y as D,g as _,i as z,bc as M,bg as ie,p as re,j as de,_ as ue}from"./index-db94d997.js";import{g as ce}from"./index-96df45ba.js";import{_ as fe}from"./index-07f7e8bf.js";import{_ as pe}from"./index-39334618.js";import"./CaretUpOutlined-7e71a64b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-4a280682.js";function U(c){return J({url:"/web/cockpitEconomicTargetDataConfig/v1/list",method:"POST",isTable:!0,data:c})}function G(c){return J({url:"/web/cockpitEconomicTargetDataConfig/v1/save",method:"POST",isTable:!0,data:c})}const ge=c=>(re("data-v-18a32619"),c=c(),de(),c),_e=ge(()=>_("div",{style:{color:"#000","font-family":"PingFang SC","font-size":"18px","font-style":"normal","font-weight":"500","line-height":"22px","margin-bottom":"16px"}},null,-1)),ve={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},ye={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},he={class:"table_wrap"},me={class:"item_table"},we={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},Te={key:0,style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-top":"24px"}},Ce={class:"table_wrap"},xe={class:"item_table"},be=oe({__name:"index",props:{typeTitle:{},reloadTable:{type:Function}},setup(c){const d=i(""),N=i([]),k=(()=>new Array(3).fill(1).map((t,e)=>({mKey:"指标"+(e+1),radiate:"",eqProportion:""})))(),Q=i(),m=i(k),w=i(),I=i(b.cloneDeep(k)),T=i([]),v=i(!1),y=i(!1),p=i(!1),g=i(!1),V=i(!1),q=i(!1);i("");const C=L({defaultShow_1:!0,defaultShow_2:!0,isHideSwitch_1:!1,isHideSwitch_2:!1}),O=L([{title:"",unit:"",dataIndex:"mKey",width:180,isEdit:!1,rule:{required:!0,message:""},dataType:"string"},{title:"指标名称",dataIndex:"radiate",isEdit:!0,dataType:"string"},{title:"指标数值",dataIndex:"eqProportion",isEdit:!0,dataType:"number"}]),P=L([{title:"省份",unit:"",dataIndex:"provinceName",width:160,isEdit:!1,rule:{required:!0,message:""},dataType:"string"},{title:"指标1名称",dataIndex:"targetNameOne",isEdit:!0,dataType:"string"},{title:"指标1数值",dataIndex:"targetValueOne",isEdit:!0,dataType:"number"},{title:"指标2名称",dataIndex:"targetNameTwo",isEdit:!0,dataType:"string"},{title:"指标2数值",dataIndex:"targetValueTwo",isEdit:!0,dataType:"number"},{title:"指标3名称",dataIndex:"targetNameThree",isEdit:!0,dataType:"string"},{title:"指标3数值",dataIndex:"targetValueThree",isEdit:!0,dataType:"number"}]),H=t=>{t===1?v.value=!0:t===2&&(y.value=!0)},K=t=>{t===1?(V.value=!1,v.value=!1,I.value=b.cloneDeep(m.value)):t===2&&(q.value=!1,y.value=!1,T.value=b.cloneDeep(w.value))},R=t=>{t===1?W(I.value):t===2&&(console.log("dataListClone2.value=",T.value),X(T.value)),console.log("infoData.value=",Q.value)},A=(t,e)=>{console.log("itemKey=",t),console.log("defaultShow=",e),C["defaultShow_"+t]=!e},j=(t,e)=>{let o=!0;return t.forEach(s=>{e.forEach(a=>{var u,x,f;(u=a==null?void 0:a.rule)!=null&&u.required&&(!s[a.dataIndex]||(x=a==null?void 0:a.rule)!=null&&x.noAllowZero&&s[a.dataIndex]==="0"||!s[a.dataIndex]&&!((f=a==null?void 0:a.rule)!=null&&f.noAllowZero)&&s[a.dataIndex]!="0")&&(o=!1)})}),o},W=t=>{if(!j(t,O))return;let o={};t.forEach((a,u)=>{o.targetType=1,o.id=a.id,o.assetCompanyCode=d.value,u===0?(o.targetNameOne=a.radiate,o.targetValueOne=a.eqProportion):u===1?(o.targetNameTwo=a.radiate,o.targetValueTwo=a.eqProportion):u===2&&(o.targetNameThree=a.radiate,o.targetValueThree=a.eqProportion)});const s=new Array(o);console.log("newList=",s),p.value=!0,G(s).then(a=>{p.value=!1,M.success({content:"保存成功"}),v.value=!1,m.value=t,B()}).catch(a=>{console.log("err=",a),p.value=!1})},X=t=>{if(!j(t,P))return;console.log("list=",t);const o=t.map(s=>({...s,assetCompanyCode:d.value,targetType:2}));g.value=!0,G(o).then(s=>{g.value=!1,M.success({content:"保存成功"}),y.value=!1,w.value=t,F()}).catch(s=>{console.log("err=",s),g.value=!1})},Y=()=>{B(),F()},$=t=>{if(!t||t.length<=0)return;let e=t[0];const o=new Array(3).fill(1).map((s,a)=>({id:e.id,mKey:"指标"+(a+1),radiate:a===0?e.targetNameOne:a===1?e.targetNameTwo:a===2?e.targetNameThree:"",eqProportion:a===0?e.targetValueOne:a===1?e.targetValueTwo:a===2?e.targetValueThree:""}));return console.log("处理接口返回数据结果list=",o),o},B=()=>{const t={targetType:1,assetCompanyCode:d.value};p.value=!0,U(t).then(e=>{console.log("国家级大屏查询res=",e),p.value=!1;const o=$(e==null?void 0:e.result);m.value=o||k,I.value=b.cloneDeep(m.value)}).catch(e=>{console.log("国家级大屏查询err=",e),p.value=!1})},F=()=>{const t={targetType:2,assetCompanyCode:d.value};g.value=!0,U(t).then(e=>{console.log("省级大屏：",e),g.value=!1,w.value=(e==null?void 0:e.result)||[],T.value=b.cloneDeep(w.value)}).catch(e=>{console.log("省级大屏查询err=",e),g.value=!1})},Z=(t,e)=>{t.forEach(o=>{o.code===e?N.value=new Array(o):o.children&&o.children.length>0&&Z(o.children,e)})},ee=async()=>{let e=await ce({ownOrOperation:1})||[];Z(e,"2df47652-7d34-42aa-80df-76391a55ec6b"),d.value="67dbc931-294f-4bc4-aea4-148699e97615"};return le(()=>{const t=history.state.pdata;console.log("pdata=",t),ee()}),se(d,()=>{Y()}),(t,e)=>{const o=fe,s=ie,a=pe,u=ae,x=te;return S(),E("div",null,[r(o,{value:l(d),"onUpdate:value":e[0]||(e[0]=f=>ne(d)?d.value=f:null),style:{width:"300px"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":l(N),placeholder:"请选择",allowClear:"","field-names":{label:"name",value:"code"}},null,8,["value","tree-data"]),_e,r(x,{title:"国家级大屏",itemKey:"1",defaultShow:l(C).defaultShow_1,isHideSwitch:l(C).isHideSwitch_1,onChangeKey:A},{btnRender:n(()=>[l(v)?D("",!0):(S(),E("div",ve,[r(s,{class:"btn add",type:"primary",onClick:e[1]||(e[1]=()=>H(1))},{default:n(()=>[h("编辑")]),_:1})]))]),footer:n(()=>[l(v)?(S(),E("div",ye,[r(a,null,{default:n(()=>[r(s,{onClick:e[2]||(e[2]=()=>K(1))},{default:n(()=>[h("取消")]),_:1}),r(s,{type:"primary",onClick:e[3]||(e[3]=()=>R(1))},{default:n(()=>[h("保存")]),_:1})]),_:1})])):D("",!0)]),default:n(()=>[_("div",he,[_("div",me,[r(u,{columns:l(O),dataListClone:l(I),dataList:l(m),isTableEdit:l(v),loading:l(p),startCheck:l(V),showAdd:!1},{salvageRateRender:n(({record:f})=>[_("div",null,z(f.salvageRate),1)]),_:1},8,["columns","dataListClone","dataList","isTableEdit","loading","startCheck"])])])]),_:1},8,["defaultShow","isHideSwitch"]),r(x,{title:"省级大屏",itemKey:"2",defaultShow:l(C).defaultShow_2,isHideSwitch:l(C).isHideSwitch_2,onChangeKey:A},{btnRender:n(()=>[l(y)?D("",!0):(S(),E("div",we,[r(s,{class:"btn add",type:"primary",onClick:e[4]||(e[4]=()=>H(2))},{default:n(()=>[h("编辑")]),_:1})]))]),footer:n(()=>[l(y)?(S(),E("div",Te,[r(a,null,{default:n(()=>[r(s,{onClick:e[5]||(e[5]=()=>K(2))},{default:n(()=>[h("取消")]),_:1}),r(s,{type:"primary",onClick:e[6]||(e[6]=()=>R(2))},{default:n(()=>[h("保存")]),_:1})]),_:1})])):D("",!0)]),default:n(()=>[_("div",Ce,[_("div",xe,[r(u,{columns:l(P),dataListClone:l(T),dataList:l(w),isTableEdit:l(y),loading:l(g),startCheck:l(q),showAdd:!1},{salvageRateRender:n(({record:f})=>[_("div",null,z(f.salvageRate),1)]),_:1},8,["columns","dataListClone","dataList","isTableEdit","loading","startCheck"])])])]),_:1},8,["defaultShow","isHideSwitch"])])}}});const He=ue(be,[["__scopeId","data-v-18a32619"]]);export{He as default};
