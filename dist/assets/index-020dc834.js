import{_ as L}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as N,r as c,I as S,k as g,o as j,D as R,at as E,a as q,v as B,g as h,f as l,e as o,h as _,i as p,u as M,q as V,bn as F,bg as K,_ as O}from"./index-db94d997.js";import{g as Q}from"./index-2196a4be.js";import{_ as A}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const G={class:"areaPrice"},J={class:"areaPrice_table"},$={class:"text-hide"},H={class:"text-hide"},U=N({__name:"index",setup(W){c(!1);const m=c(),x=["provinceCodeList","cityCodeList","areaCodeList"],b=c([]),u=c([]);c(!1);const w=S(),P=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:3,width:120,valueEnum:u,order:1,render:!0,fixed:"left"},{title:"时间周期",key:"year",dataIndex:"year",valueType:"date",dateFormat:"YYYY",defaultValue:g().format("YYYY"),search:!0,render:!0,width:120,order:2,fixed:"left"},{title:"1月",dataIndex:"january",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"2月",dataIndex:"february",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"3月",dataIndex:"march",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"4月",dataIndex:"april",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"5月",dataIndex:"may",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"6月",dataIndex:"june",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"7月",dataIndex:"july",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"8月",dataIndex:"august",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"9月",dataIndex:"september",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"10月",dataIndex:"october",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"11月",dataIndex:"november",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"12月",dataIndex:"december",search:!1,resizable:!0,formatPercentage:2,width:100},{title:"合计",dataIndex:"totall",search:!1,resizable:!0,formatPercentage:2,width:120},{title:"操作",key:"action",dataIndex:"action",search:!1,width:120,render:!0,fixed:"right"}];j(()=>{var e;I(),(e=m.value)==null||e.setFormParams({year:g().format("YYYY")},!0)}),R(()=>{m.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&Y()});const I=()=>{E({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));u.value=a})},C=async(e,r)=>{w.push({path:"/financeManage/electricQuantity/baseDisassemble/monthConfig/config",state:{pdata:V.cloneDeep(e)}})},z=c([]),D=e=>{z.value=e||[]},Y=()=>{var e;(e=m.value)==null||e.reload()},k=e=>{const r=new Map;return e.forEach(a=>{const t=x[a.level-1];if(r.has(t))r.get(t).push(a.value);else{let d=[];d.push(a.value),r.set(t,d)}}),Object.fromEntries(r)},y=(e,r,a)=>!e||!r?[]:(e.forEach(t=>{r.find(s=>t.value===s)&&a.push(t),t.children&&t.children.length>0&&y(t.children,r,a)}),a),T=(e,r)=>new Promise(a=>{const t=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[0]:"",d=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[1]:"";let s={delStatus:0,noJoin:!0,startTime:t,endTime:d};const f=y(b.value,e==null?void 0:e.cityTree,[]);let n=k(f);e==null||delete e.datongrd;const i={...s,...e,...n};a(i)});return(e,r)=>{const a=F,t=K,d=A,s=L;return q(),B("div",G,[h("div",J,[l(s,{columns:P,ref_key:"actionRef",ref:m,request:M(Q),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:D,"before-query-params":T,"default-query":!1},{companyCodeListRender:o(({column:f,record:n,index:i})=>[l(a,null,{title:o(()=>[_(p(n.companyName),1)]),default:o(()=>[h("span",$,p(n.companyName),1)]),_:2},1024)]),yearRender:o(({column:f,record:n,index:i})=>[l(a,null,{title:o(()=>[_(p(n.yKey),1)]),default:o(()=>[h("span",H,p(n.yKey),1)]),_:2},1024)]),actionRender:o(({column:f,record:n,index:i})=>[l(d,null,{default:o(()=>[l(t,{size:"small",type:"link",onClick:()=>C(n,i)},{default:o(()=>[_(" 参数配置 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])])}}});const de=O(U,[["__scopeId","data-v-7bde6f32"]]);export{de as default};
