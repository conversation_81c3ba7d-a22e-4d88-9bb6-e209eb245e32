import{_ as M}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as U,d as Y,I as V,r as u,K as $,o as z,D as A,at as G,a as O,v as E,g as v,f as t,e,u as l,h as p,i as b,q as H,av as Q,bf as J,aw as W,bg as X,ax as Z,bh as ee,bn as te,_ as ae}from"./index-db94d997.js";import{D as ne}from"./dayjs-a8e42122.js";import{_ as oe}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";function se(x){return U({url:"/web/budgetInfo/v1/list",method:"POST",isTable:!0,data:x})}const le={class:"budgetManage"},de={class:"form_container"},re={class:"text-hide"},ie=Y({__name:"index",setup(x){const g=V(),m=u(),f=u(),w=u([]),d=$({companyCode:[],yKey:""}),I=[{title:"项目",dataIndex:"budgetItem",width:150,search:!1,fixed:"left"},{title:"计量单位",dataIndex:"measuringUnit",width:120,search:!1},{title:"重庆公司下达预算",dataIndex:"chongqingGiveBudget",width:150,search:!1},{title:"1月",dataIndex:"january",width:120,search:!1},{title:"2月",dataIndex:"february",width:120,search:!1},{title:"3月",dataIndex:"march",width:120,search:!1},{title:"4月",dataIndex:"april",width:120,search:!1},{title:"5月",dataIndex:"may",width:120,search:!1},{title:"6月",dataIndex:"june",width:120,search:!1},{title:"7月",dataIndex:"july",width:120,search:!1},{title:"8月",dataIndex:"august",width:120,search:!1},{title:"9月",dataIndex:"september",width:120,search:!1},{title:"10月",dataIndex:"october",width:120,search:!1},{title:"11月",dataIndex:"november",width:120,search:!1},{title:"12月",dataIndex:"december",width:120,search:!1}],C=()=>{console.log("reset"),m.value.resetFields(),h()},k=()=>{m.value.validateFields().then(a=>{console.log("values=",a),h()})};z(()=>{D()}),A(()=>{f.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&h()});const D=()=>{G({}).then(a=>{console.log("产权公司res=",a);let i=(a||[]).map(o=>({label:o.companyName,value:o.companyCode}));w.value=i})},q=()=>{g.push({path:"/dataImport",query:{templateType:16,fileType:".csv,.xls,.xlsx",fileSize:30}})},S=a=>{g.push({path:"/financeManage/profitManage/station/info",state:{pdata:H.cloneDeep(a)}})},T=u([]),N=a=>{T.value=a||[]},h=()=>{var a;(a=f.value)==null||a.reload()},R=(a,r)=>{let i={...d};return new Promise(o=>{const c={...a,delStatus:0,...i};o(c)})};return(a,r)=>{const i=Q,o=J,c=W,B=ne,_=X,y=oe,L=Z,P=ee,F=te,K=M;return O(),E("div",le,[v("div",de,[t(P,{ref_key:"formRef",ref:m,model:l(d),name:"basic",autocomplete:"off"},{default:e(()=>[t(L,{span:24},{default:e(()=>[t(c,{span:8,style:{"padding-right":"20px"}},{default:e(()=>[t(o,{label:"产权公司",name:"companyCode",required:""},{default:e(()=>[t(i,{options:l(w),value:l(d).companyCode,"onUpdate:value":r[0]||(r[0]=s=>l(d).companyCode=s),placeholder:"请选择",allowClear:"",showArrow:"","show-search":"","filter-option":(s,n)=>((n==null?void 0:n.label)??"").toLowerCase().includes(s.toLowerCase()),style:{width:"100%"}},null,8,["options","value","filter-option"])]),_:1})]),_:1}),t(c,{span:8,style:{padding:"0px 10px"}},{default:e(()=>[t(o,{label:"年份",name:"yKey",required:""},{default:e(()=>[t(B,{value:l(d).yKey,"onUpdate:value":r[1]||(r[1]=s=>l(d).yKey=s),valueFormat:"YYYY",picker:"year",allowClear:"",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),t(c,{span:8,style:{"padding-left":"20px",display:"flex","justify-content":"flex-end"}},{default:e(()=>[t(y,{style:{"margin-bottom":"24px"}},{default:e(()=>[t(_,{onClick:C},{default:e(()=>[p("重置")]),_:1}),t(_,{type:"primary",onClick:k},{default:e(()=>[p("查询")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),t(K,{columns:I,ref_key:"actionRef",ref:f,request:l(se),"label-col":{style:{width:"80px"}},"wrapper-col":{span:16},onGetDataSource:N,"before-query-params":R,"default-query":!1,search:!1},{tableHeader:e(()=>[t(y,null,{default:e(()=>[t(_,{type:"primary",onClick:q},{default:e(()=>[p("上传")]),_:1})]),_:1})]),companyCodeListRender:e(({column:s,record:n,index:j})=>[t(F,null,{title:e(()=>[p(b(n.companyName),1)]),default:e(()=>[v("span",re,b(n.companyName),1)]),_:2},1024)]),actionRender:e(({column:s,record:n,index:j})=>[t(y,null,{default:e(()=>[t(_,{type:"link",size:"small",onClick:ce=>S(n)},{default:e(()=>[p("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const ge=ae(ie,[["__scopeId","data-v-1d353f9d"]]);export{ge as default};
