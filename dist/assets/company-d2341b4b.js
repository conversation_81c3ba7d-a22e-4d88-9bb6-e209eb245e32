import{_ as g}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as I,k as C,r as s,o as M,a as p,v as b,f as u,e as c,b as k,h as N,y as E,u as P,c7 as S,bc as T,at as Y,bg as q,_ as v}from"./index-db94d997.js";import{a as A}from"./index-4a5d1ec3.js";import{_ as D}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const F={class:"wrapper"},V=I({__name:"company",setup(W){const n=C().subtract(1,"month").format("YYYY-MM"),f=()=>{var e,a;(e=t.value)==null||e.setFormParams({datongrd:[n,n],ownerCompanyName:[],ownerCompanyName_indeterminate:!1,ownerCompanyName_checked:!1}),(a=t.value)==null||a.reload()},h=async e=>{var a,o;return{monStart:((a=e==null?void 0:e.datongrd)==null?void 0:a[0])||n,monEnd:((o=e==null?void 0:e.datongrd)==null?void 0:o[1])||n,companyList:(e==null?void 0:e.ownerCompanyName)||[],noJoin:!0,notFilterNull:!0}},m=s([]),t=s(),i=s([]),y=e=>{i.value=e||[]},_=[{title:"时间选择",dataIndex:"datongrd",valueType:"dateRange",search:!0,hideInTable:!0,dateFormat:"YYYY-MM",allowClear:!1,width:120,order:2},{title:"产权公司",dataIndex:"ownerCompanyName",key:"ownerCompanyName",valueType:"multipleSelect",maxTagCount:3,valueEnum:m,order:2,render:!0,width:120},{title:"装机容量(万kW)",dataIndex:"capins",formatMoney:!0,width:120,search:!1},{title:"户数",dataIndex:"number",width:120,search:!1},{title:"组件数量",dataIndex:"componentQuantity",width:120,search:!1},{title:"月份",dataIndex:"month",width:120,search:!1},{title:"资产原值",dataIndex:"assetValue",formatMoney:!0,width:120,search:!1},{title:"资产净值",dataIndex:"salvageValue",formatMoney:!0,width:120,search:!1},{title:"本期数",align:"center",width:120,children:[{title:"本月实际电量(万kWh)",dataIndex:"actualCapins",width:180,formatMoney:!0,search:!1},{title:"本月结账电量(万kWh)",dataIndex:"monthEq",width:180,formatMoney:!0,search:!1},{title:"本月农户收益",dataIndex:"farmerEarnings",formatMoney:!0,width:120,search:!1},{title:"主营业务收入",dataIndex:"monthIncomeSum",formatMoney:!0,width:120,search:!1},{title:"其他业务收入",dataIndex:"otherIncome",formatMoney:!0,width:120,search:!1}]},{title:"累计数",align:"center",width:120,children:[{title:"本月实际电量(万kWh)",dataIndex:"actualCapinsAll",width:180,formatMoney:!0,search:!1},{title:"本月结账电量(万kWh)",dataIndex:"monthEqAll",width:180,formatMoney:!0,search:!1},{title:"本月农户收益",dataIndex:"farmerEarningsAll",formatMoney:!0,width:120,search:!1},{title:"主营业务收入",dataIndex:"monthIncomeSumaAll",formatMoney:!0,width:120,search:!1},{title:"其他业务收入",dataIndex:"otherIncomeAll",formatMoney:!0,width:120,search:!1}]}],r=s(!1),w=()=>{var l;let a={...(l=t==null?void 0:t.value)==null?void 0:l.getInitialFormStateNew(),queryParameterDesc:[]},o={reportType:7,reportParam:JSON.stringify(a)};r.value=!0,S(o).then(d=>{T.info("数据导出中，稍后请去导出中心查看"),r.value=!1}).catch(d=>{r.value=!1})},x=()=>{Y({}).then(e=>{m.value=(e==null?void 0:e.map(a=>({label:a.companyName,value:a.companyCode})))||[]})};return M(async()=>{x(),t.value.setFormParams({datongrd:[n,n]},!1)}),(e,a)=>{const o=q,l=D,d=g;return p(),b("div",F,[u(d,{columns:_,request:P(A),ref_key:"actionRef",ref:t,"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},bordered:!0,"default-query":!0,"before-query-params":h,"on-cancel":f,onGetDataSource:y},{tableHeader:c(()=>[u(l,null,{default:c(()=>[i.value.length>0?(p(),k(o,{key:0,type:"primary",loading:r.value,onClick:w},{default:c(()=>[N("导出")]),_:1},8,["loading"])):E("",!0)]),_:1})]),_:1},8,["request"])])}}});const $=v(V,[["__scopeId","data-v-2f51c64b"]]);export{$ as default};
