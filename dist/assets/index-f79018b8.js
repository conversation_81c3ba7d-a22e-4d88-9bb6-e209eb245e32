import{cU as Ae,a3 as ee,aD as Z,aC as ve,a5 as X,a7 as pe,ct as me,r as L,c as v,d as N,ac as he,af as te,w as q,ap as qe,ao as be,K as ge,t as ye,cV as le,a as H,v as _e,a1 as A,Z as O,u as c,aj as Se,aT as Fe,aa as R,cF as J,cW as Re,o as we,l as We,cE as je,cl as ze,f as j,F as Te,V as Ie,b2 as ke,al as De,cX as Ue,cY as se,cZ as K,e as Y,b as Ge,aB as Ke,n as ie,h as Ye,i as ne,y as ue,g as de,c_ as Ze,c$ as Xe,aq as He,ar as Je}from"./index-db94d997.js";import{c as Q}from"./index-ec316fb4.js";var Qe=4;function ce(i){return Ae(i,Qe)}const et=ee({size:{type:String,values:me},disabled:Boolean}),tt=ee({...et,model:Object,rules:{type:Z(Object)},labelPosition:{type:String,values:["left","right","top"],default:"right"},requireAsteriskPosition:{type:String,values:["left","right"],default:"left"},labelWidth:{type:[String,Number],default:""},labelSuffix:{type:String,default:""},inline:Boolean,inlineMessage:Boolean,statusIcon:Boolean,showMessage:{type:Boolean,default:!0},validateOnRuleChange:{type:Boolean,default:!0},hideRequiredAsterisk:Boolean,scrollToError:Boolean,scrollIntoViewOptions:{type:[Object,Boolean]}}),lt={validate:(i,m,e)=>(ve(i)||X(i))&&pe(m)&&X(e)};function at(){const i=L([]),m=v(()=>{if(!i.value.length)return"0";const d=Math.max(...i.value);return d?`${d}px`:""});function e(d){const n=i.value.indexOf(d);return n===-1&&m.value,n}function u(d,n){if(d&&n){const f=e(n);i.value.splice(f,1,d)}else d&&i.value.push(d)}function t(d){const n=e(d);n>-1&&i.value.splice(n,1)}return{autoLabelWidth:m,registerLabelWidth:u,deregisterLabelWidth:t}}const $=(i,m)=>{const e=Q(m);return e.length>0?i.filter(u=>u.prop&&e.includes(u.prop)):i},rt="ElForm",ot=N({name:rt}),st=N({...ot,props:tt,emits:lt,setup(i,{expose:m,emit:e}){const u=i,t=[],d=he(),n=te("form"),f=v(()=>{const{labelPosition:a,inline:o}=u;return[n.b(),n.m(d.value||"default"),{[n.m(`label-${a}`)]:a,[n.m("inline")]:o}]}),C=a=>t.find(o=>o.prop===a),g=a=>{t.push(a)},s=a=>{a.prop&&t.splice(t.indexOf(a),1)},b=(a=[])=>{u.model&&$(t,a).forEach(o=>o.resetField())},y=(a=[])=>{$(t,a).forEach(o=>o.clearValidate())},E=v(()=>!!u.model),x=a=>{if(t.length===0)return[];const o=$(t,a);return o.length?o:[]},F=async a=>P(void 0,a),W=async(a=[])=>{if(!E.value)return!1;const o=x(a);if(o.length===0)return!0;let _={};for(const S of o)try{await S.validate("")}catch(I){_={..._,...I}}return Object.keys(_).length===0?!0:Promise.reject(_)},P=async(a=[],o)=>{const _=!Fe(o);try{const S=await W(a);return S===!0&&await(o==null?void 0:o(S)),S}catch(S){if(S instanceof Error)throw S;const I=S;return u.scrollToError&&M(Object.keys(I)[0]),await(o==null?void 0:o(!1,I)),_&&Promise.reject(I)}},M=a=>{var o;const _=$(t,a)[0];_&&((o=_.$el)==null||o.scrollIntoView(u.scrollIntoViewOptions))};return q(()=>u.rules,()=>{u.validateOnRuleChange&&F().catch(a=>qe())},{deep:!0}),be(le,ge({...ye(u),emit:e,resetFields:b,clearValidate:y,validateField:P,getField:C,addField:g,removeField:s,...at()})),m({validate:F,validateField:P,resetFields:b,clearValidate:y,scrollToField:M,fields:t}),(a,o)=>(H(),_e("form",{class:O(c(f))},[A(a.$slots,"default")],2))}});var it=Se(st,[["__file","form.vue"]]);const nt=["","error","validating","success"],ut=ee({label:String,labelWidth:{type:[String,Number],default:""},labelPosition:{type:String,values:["left","right","top",""],default:""},prop:{type:Z([String,Array])},required:{type:Boolean,default:void 0},rules:{type:Z([Object,Array])},error:String,validateStatus:{type:String,values:nt},for:String,inlineMessage:{type:[String,Boolean],default:""},showMessage:{type:Boolean,default:!0},size:{type:String,values:me}}),fe="ElLabelWrap";var dt=N({name:fe,props:{isAutoWidth:Boolean,updateAll:Boolean},setup(i,{slots:m}){const e=R(le,void 0),u=R(J);u||Re(fe,"usage: <el-form-item><label-wrap /></el-form-item>");const t=te("form"),d=L(),n=L(0),f=()=>{var s;if((s=d.value)!=null&&s.firstElementChild){const b=window.getComputedStyle(d.value.firstElementChild).width;return Math.ceil(Number.parseFloat(b))}else return 0},C=(s="update")=>{Ie(()=>{m.default&&i.isAutoWidth&&(s==="update"?n.value=f():s==="remove"&&(e==null||e.deregisterLabelWidth(n.value)))})},g=()=>C("update");return we(()=>{g()}),We(()=>{C("remove")}),je(()=>g()),q(n,(s,b)=>{i.updateAll&&(e==null||e.registerLabelWidth(s,b))}),ze(v(()=>{var s,b;return(b=(s=d.value)==null?void 0:s.firstElementChild)!=null?b:null}),g),()=>{var s,b;if(!m)return null;const{isAutoWidth:y}=i;if(y){const E=e==null?void 0:e.autoLabelWidth,x=u==null?void 0:u.hasLabel,F={};if(x&&E&&E!=="auto"){const W=Math.max(0,Number.parseInt(E,10)-n.value),M=(u.labelPosition||e.labelPosition)==="left"?"marginRight":"marginLeft";W&&(F[M]=`${W}px`)}return j("div",{ref:d,class:[t.be("item","label-wrap")],style:F},[(s=m.default)==null?void 0:s.call(m)])}else return j(Te,{ref:d},[(b=m.default)==null?void 0:b.call(m)])}}});const ct=N({name:"ElFormItem"}),ft=N({...ct,props:ut,setup(i,{expose:m}){const e=i,u=ke(),t=R(le,void 0),d=R(J,void 0),n=he(void 0,{formItem:!1}),f=te("form-item"),C=De().value,g=L([]),s=L(""),b=Ue(s,100),y=L(""),E=L();let x,F=!1;const W=v(()=>e.labelPosition||(t==null?void 0:t.labelPosition)),P=v(()=>{if(W.value==="top")return{};const l=se(e.labelWidth||(t==null?void 0:t.labelWidth)||"");return l?{width:l}:{}}),M=v(()=>{if(W.value==="top"||t!=null&&t.inline)return{};if(!e.label&&!e.labelWidth&&Ee)return{};const l=se(e.labelWidth||(t==null?void 0:t.labelWidth)||"");return!e.label&&!u.label?{marginLeft:l}:{}}),a=v(()=>[f.b(),f.m(n.value),f.is("error",s.value==="error"),f.is("validating",s.value==="validating"),f.is("success",s.value==="success"),f.is("required",xe.value||e.required),f.is("no-asterisk",t==null?void 0:t.hideRequiredAsterisk),(t==null?void 0:t.requireAsteriskPosition)==="right"?"asterisk-right":"asterisk-left",{[f.m("feedback")]:t==null?void 0:t.statusIcon,[f.m(`label-${W.value}`)]:W.value}]),o=v(()=>pe(e.inlineMessage)?e.inlineMessage:(t==null?void 0:t.inlineMessage)||!1),_=v(()=>[f.e("error"),{[f.em("error","inline")]:o.value}]),S=v(()=>e.prop?X(e.prop)?e.prop:e.prop.join("."):""),I=v(()=>!!(e.label||u.label)),z=v(()=>e.for||(g.value.length===1?g.value[0]:void 0)),T=v(()=>!z.value&&I.value),Ee=!!d,k=v(()=>{const l=t==null?void 0:t.model;if(!(!l||!e.prop))return K(l,e.prop).value}),D=v(()=>{const{required:l}=e,r=[];e.rules&&r.push(...Q(e.rules));const h=t==null?void 0:t.rules;if(h&&e.prop){const p=K(h,e.prop).value;p&&r.push(...Q(p))}if(l!==void 0){const p=r.map((w,V)=>[w,V]).filter(([w])=>Object.keys(w).includes("required"));if(p.length>0)for(const[w,V]of p)w.required!==l&&(r[V]={...w,required:l});else r.push({required:l})}return r}),Pe=v(()=>D.value.length>0),Le=l=>D.value.filter(h=>!h.trigger||!l?!0:ve(h.trigger)?h.trigger.includes(l):h.trigger===l).map(({trigger:h,...p})=>p),xe=v(()=>D.value.some(l=>l.required)),Me=v(()=>{var l;return b.value==="error"&&e.showMessage&&((l=t==null?void 0:t.showMessage)!=null?l:!0)}),ae=v(()=>`${e.label||""}${(t==null?void 0:t.labelSuffix)||""}`),B=l=>{s.value=l},Be=l=>{var r,h;const{errors:p,fields:w}=l;(!p||!w)&&console.error(l),B("error"),y.value=p?(h=(r=p==null?void 0:p[0])==null?void 0:r.message)!=null?h:`${e.prop} is required`:"",t==null||t.emit("validate",e.prop,!1,y.value)},Ve=()=>{B("success"),t==null||t.emit("validate",e.prop,!0,"")},Oe=async l=>{const r=S.value;return new Xe({[r]:l}).validate({[r]:k.value},{firstFields:!0}).then(()=>(Ve(),!0)).catch(p=>(Be(p),Promise.reject(p)))},re=async(l,r)=>{if(F||!e.prop)return!1;const h=Fe(r);if(!Pe.value)return r==null||r(!1),!1;const p=Le(l);return p.length===0?(r==null||r(!0),!0):(B("validating"),Oe(p).then(()=>(r==null||r(!0),!0)).catch(w=>{const{fields:V}=w;return r==null||r(!1,V),h?!1:Promise.reject(V)}))},U=()=>{B(""),y.value="",F=!1},oe=async()=>{const l=t==null?void 0:t.model;if(!l||!e.prop)return;const r=K(l,e.prop);F=!0,r.value=ce(x),await Ie(),U(),F=!1},Ne=l=>{g.value.includes(l)||g.value.push(l)},$e=l=>{g.value=g.value.filter(r=>r!==l)};q(()=>e.error,l=>{y.value=l||"",B(l?"error":"")},{immediate:!0}),q(()=>e.validateStatus,l=>B(l||""));const G=ge({...ye(e),$el:E,size:n,validateState:s,labelId:C,inputIds:g,isGroup:T,hasLabel:I,fieldValue:k,addInputId:Ne,removeInputId:$e,resetField:oe,clearValidate:U,validate:re});return be(J,G),we(()=>{e.prop&&(t==null||t.addField(G),x=ce(k.value))}),We(()=>{t==null||t.removeField(G)}),m({size:n,validateMessage:y,validateState:s,validate:re,clearValidate:U,resetField:oe}),(l,r)=>{var h;return H(),_e("div",{ref_key:"formItemRef",ref:E,class:O(c(a)),role:c(T)?"group":void 0,"aria-labelledby":c(T)?c(C):void 0},[j(c(dt),{"is-auto-width":c(P).width==="auto","update-all":((h=c(t))==null?void 0:h.labelWidth)==="auto"},{default:Y(()=>[c(I)?(H(),Ge(Ke(c(z)?"label":"div"),{key:0,id:c(C),for:c(z),class:O(c(f).e("label")),style:ie(c(P))},{default:Y(()=>[A(l.$slots,"label",{label:c(ae)},()=>[Ye(ne(c(ae)),1)])]),_:3},8,["id","for","class","style"])):ue("v-if",!0)]),_:3},8,["is-auto-width","update-all"]),de("div",{class:O(c(f).e("content")),style:ie(c(M))},[A(l.$slots,"default"),j(Ze,{name:`${c(f).namespace.value}-zoom-in-top`},{default:Y(()=>[c(Me)?A(l.$slots,"error",{key:0,error:y.value},()=>[de("div",{class:O(c(_))},ne(y.value),3)]):ue("v-if",!0)]),_:3},8,["name"])],6)],10,["role","aria-labelledby"])}}});var Ce=Se(ft,[["__file","form-item.vue"]]);const mt=He(it,{FormItem:Ce}),ht=Je(Ce);export{ht as E,mt as a};
