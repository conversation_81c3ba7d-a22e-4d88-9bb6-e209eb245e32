import{_ as Ne}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{bN as Te,d as J,bu as re,c1 as R,bz as Ee,bo as p,f as t,ax as L,aw as x,c2 as Pe,a1 as Ke,bt as k,c3 as _e,c4 as Ye,c5 as Be,c6 as ee,c as $e,r as K,I as Re,T as be,o as Le,D as Oe,at as Ge,K as je,k as he,a as ge,v as Ve,e as s,h as A,u as C,s as U,n as xe,g as te,ai as Ce,b as Ue,y as qe,i as q,c7 as Je,bc as Fe,bg as He,bI as Qe,bJ as We,bT as Xe,bn as Ze,_ as et}from"./index-db94d997.js";import{g as tt,e as at}from"./index-a08ebcd6.js";import{D as nt}from"./dayjs-a8e42122.js";import{_ as rt}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";var ot=Te.TabPane,lt=function(){return{prefixCls:String,title:k.any,extra:k.any,bordered:{type:Boolean,default:!0},bodyStyle:{type:Object,default:void 0},headStyle:{type:Object,default:void 0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},type:{type:String},size:{type:String},actions:k.any,tabList:{type:Array},tabBarExtraContent:k.any,activeTabKey:String,defaultActiveTabKey:String,cover:k.any,onTabChange:{type:Function}}},it=J({compatConfig:{MODE:3},name:"ACard",props:lt(),slots:["title","extra","tabBarExtraContent","actions","cover","customTab"],setup:function(a,S){var n=S.slots,T=re("card",a),y=T.prefixCls,N=T.direction,o=T.size,w=function(u){var d=u.map(function(f,P){return _e(f)&&!Ye(f)||!_e(f)?t("li",{style:{width:"".concat(100/u.length,"%")},key:"action-".concat(P)},[t("span",null,[f])]):null});return d},E=function(u){var d;(d=a.onTabChange)===null||d===void 0||d.call(a,u)},D=function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],d;return u.forEach(function(f){f&&Be(f.type)&&f.type.__ANT_CARD_GRID&&(d=!0)}),d};return function(){var v,u,d,f,P,B,m,e,r=a.headStyle,b=r===void 0?{}:r,h=a.bodyStyle,z=h===void 0?{}:h,I=a.loading,O=a.bordered,F=O===void 0?!0:O,G=a.type,M=a.tabList,H=a.hoverable,l=a.activeTabKey,we=a.defaultActiveTabKey,oe=a.tabBarExtraContent,le=oe===void 0?R((v=n.tabBarExtraContent)===null||v===void 0?void 0:v.call(n)):oe,ie=a.title,Q=ie===void 0?R((u=n.title)===null||u===void 0?void 0:u.call(n)):ie,se=a.extra,W=se===void 0?R((d=n.extra)===null||d===void 0?void 0:d.call(n)):se,de=a.actions,X=de===void 0?R((f=n.actions)===null||f===void 0?void 0:f.call(n)):de,ce=a.cover,ue=ce===void 0?R((P=n.cover)===null||P===void 0?void 0:P.call(n)):ce,j=Ee((B=n.default)===null||B===void 0?void 0:B.call(n)),i=y.value,Ie=(m={},p(m,"".concat(i),!0),p(m,"".concat(i,"-loading"),I),p(m,"".concat(i,"-bordered"),F),p(m,"".concat(i,"-hoverable"),!!H),p(m,"".concat(i,"-contain-grid"),D(j)),p(m,"".concat(i,"-contain-tabs"),M&&M.length),p(m,"".concat(i,"-").concat(o.value),o.value),p(m,"".concat(i,"-type-").concat(G),!!G),p(m,"".concat(i,"-rtl"),N.value==="rtl"),m),Se=z.padding===0||z.padding==="0px"?{padding:"24px"}:void 0,g=t("div",{class:"".concat(i,"-loading-block")},null),De=t("div",{class:"".concat(i,"-loading-content"),style:Se},[t(L,{gutter:8},{default:function(){return[t(x,{span:22},{default:function(){return[g]}})]}}),t(L,{gutter:8},{default:function(){return[t(x,{span:8},{default:function(){return[g]}}),t(x,{span:15},{default:function(){return[g]}})]}}),t(L,{gutter:8},{default:function(){return[t(x,{span:6},{default:function(){return[g]}}),t(x,{span:18},{default:function(){return[g]}})]}}),t(L,{gutter:8},{default:function(){return[t(x,{span:13},{default:function(){return[g]}}),t(x,{span:9},{default:function(){return[g]}})]}}),t(L,{gutter:8},{default:function(){return[t(x,{span:4},{default:function(){return[g]}}),t(x,{span:3},{default:function(){return[g]}}),t(x,{span:16},{default:function(){return[g]}})]}})]),fe=l!==void 0,ze=(e={size:"large"},p(e,fe?"activeKey":"defaultActiveKey",fe?l:we),p(e,"onChange",E),p(e,"class","".concat(i,"-head-tabs")),e),pe,ve=M&&M.length?t(Te,ze,{default:function(){return[M.map(function(c){var me=c.tab,V=c.slots,ye=V==null?void 0:V.tab;Pe(!V,"Card","tabList slots is deprecated, Please use `customTab` instead.");var Z=me!==void 0?me:n[ye]?n[ye](c):null;return Z=Ke(n,"customTab",c,function(){return[Z]}),t(ot,{tab:Z,key:c.key,disabled:c.disabled},null)})]},rightExtra:le?function(){return le}:null}):null;(Q||W||ve)&&(pe=t("div",{class:"".concat(i,"-head"),style:b},[t("div",{class:"".concat(i,"-head-wrapper")},[Q&&t("div",{class:"".concat(i,"-head-title")},[Q]),W&&t("div",{class:"".concat(i,"-extra")},[W])]),ve]));var Me=ue?t("div",{class:"".concat(i,"-cover")},[ue]):null,Ae=t("div",{class:"".concat(i,"-body"),style:z},[I?De:j]),ke=X&&X.length?t("ul",{class:"".concat(i,"-actions")},[w(X)]):null;return t("div",{class:Ie,ref:"cardContainerRef"},[pe,Me,j&&j.length?Ae:null,ke])}}});const Y=it;var st=function(){return{prefixCls:String,title:k.any,description:k.any,avatar:k.any}};const ae=J({compatConfig:{MODE:3},name:"ACardMeta",props:st(),slots:["title","description","avatar"],setup:function(a,S){var n=S.slots,T=re("card",a),y=T.prefixCls;return function(){var N=p({},"".concat(y.value,"-meta"),!0),o=ee(n,a,"avatar"),w=ee(n,a,"title"),E=ee(n,a,"description"),D=o?t("div",{class:"".concat(y.value,"-meta-avatar")},[o]):null,v=w?t("div",{class:"".concat(y.value,"-meta-title")},[w]):null,u=E?t("div",{class:"".concat(y.value,"-meta-description")},[E]):null,d=v||u?t("div",{class:"".concat(y.value,"-meta-detail")},[v,u]):null;return t("div",{class:N},[D,d])}}});var dt=function(){return{prefixCls:String,hoverable:{type:Boolean,default:!0}}};const ne=J({compatConfig:{MODE:3},name:"ACardGrid",__ANT_CARD_GRID:!0,props:dt(),setup:function(a,S){var n=S.slots,T=re("card",a),y=T.prefixCls,N=$e(function(){var o;return o={},p(o,"".concat(y.value,"-grid"),!0),p(o,"".concat(y.value,"-grid-hoverable"),a.hoverable),o});return function(){var o;return t("div",{class:N.value},[(o=n.default)===null||o===void 0?void 0:o.call(n)])}}});Y.Meta=ae;Y.Grid=ne;Y.install=function(_){return _.component(Y.name,Y),_.component(ae.name,ae),_.component(ne.name,ne),_};const ct={class:"areaPrice"},ut={class:"text-hide"},ft={class:"text-hide"},pt=J({__name:"index",setup(_){const a=K(),S=K([]),n=Re(),T=[{title:"电站编码",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:1,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:S,render:!0,resizable:!0,order:3,fixed:"left"},{title:"行政区划",key:"fullAddress",dataIndex:"fullAddress",width:150,render:!0,resizable:!0,search:!1},{title:"装机容量",dataIndex:"capins",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"光伏板数量",dataIndex:"componentQuantity",width:120,resizable:!0,search:!1},{title:"总折旧月份",dataIndex:"usrMonth",width:120,resizable:!0,search:!1},{title:"资产原值",dataIndex:"assetValue",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"入账月份",dataIndex:"monthKey",width:120,resizable:!0,search:!1},{title:"入账前已计提折旧额",dataIndex:"beforeAccountDepreciationCost",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"入账前已计提折旧月份",dataIndex:"beforeAccountDepreciationMonth",width:120,resizable:!0,search:!1},{title:"入账时资产价值",dataIndex:"accountAssetValue",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"剩余折旧月份",dataIndex:"residueDepreciationMonth",width:120,resizable:!0,search:!1},{title:"净残值率",dataIndex:"salvageRate",width:120,resizable:!0,search:!1},{title:"净残值",dataIndex:"salvageValue",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"月折旧额",dataIndex:"monthDepreciationCost",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"标杆电价",dataIndex:"sightcingBuelectrovalences",width:120,resizable:!0,formatDecimal:4,search:!1},{title:"补贴电价",dataIndex:"subsidyElectrovalences",width:120,formatDecimal:4,resizable:!0,search:!1},{title:"上网电价",dataIndex:"surfElectrovalences",width:120,formatDecimal:4,resizable:!0,search:!1},{title:"电站现状",dataIndex:"stationFlag",valueType:"select",valueEnum:be("STATION_FLAG"),resizable:!0,width:120},{title:"时间区间",dataIndex:"monthKey",valueType:"dateRange",width:120,dateFormat:"YYYY-MM"},{title:"形成方式",dataIndex:"formationType",valueType:"select",valueEnum:be("FORMATION_TYPE"),resizable:!0,width:120},{title:"变动时间",dataIndex:"updateTime",width:150,resizable:!0,search:!1}];Le(()=>{y()}),Oe(()=>{let e=a.value.getInitialFormStateNew();Object.prototype.hasOwnProperty.call(e,"delStatus")&&B()});const y=()=>{Ge({}).then(e=>{console.log("产权公司res=",e);let b=(e||[]).map(h=>({label:h.companyName,value:h.companyCode}));S.value=b})},N=()=>{n.push({path:"/pAssetManage/propertyManage/costAsset/dataImport",query:{templateType:7,fileType:".csv,.xls,.xlsx",fileSize:30}})},o=K(1),w=K(!1),E=je({display:"flex",height:"42px",lineHeight:"42px"}),D=K(he().format("YYYY-MM")),v=K(he().format("YYYY")),u=()=>{const e={isMonthOrYear:o.value,monthKey:o.value===1?D.value:v.value},r={reportType:0,reportParam:JSON.stringify(e)};Je(r).then(()=>{Fe.success("报告正在生成中，请到”异步导出中心下载"),w.value=!1})},d=()=>{var r;let e=(r=a.value)==null?void 0:r.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),at(e)},f=K([]),P=e=>{f.value=e||[],console.log("dataSource=",e)},B=()=>{var e;(e=a.value)==null||e.reload()},m=e=>{let r={};return new Promise(b=>{const h=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[0]:"",z=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[1]:"",I={...r,...e,startDate:h,endDate:z,noJoin:!0,delStatus:0};I==null||delete I.monthKey,b(I)})};return(e,r)=>{const b=He,h=nt,z=Qe,I=We,O=Y,F=Xe,G=rt,M=Ze,H=Ne;return ge(),Ve("div",ct,[t(H,{columns:T,ref_key:"actionRef",ref:a,request:C(tt),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:P,"before-query-params":m},{tableHeader:s(()=>[t(G,null,{default:s(()=>[t(b,{onClick:N},{default:s(()=>[A("批量导入")]),_:1}),t(F,{visible:C(w),"onUpdate:visible":r[5]||(r[5]=l=>U(w)?w.value=l:null),trigger:["click"]},{overlay:s(()=>[t(O,null,{default:s(()=>[t(I,{value:C(o),"onUpdate:value":r[3]||(r[3]=l=>U(o)?o.value=l:null),style:{padding:"5px 0 5px 15px"}},{default:s(()=>[t(z,{style:xe(C(E)),value:1},{default:s(()=>[A(" 月度盘点报告 "),t(h,{value:C(D),"onUpdate:value":r[1]||(r[1]=l=>U(D)?D.value=l:null),picker:"month","value-format":"YYYY-MM","allow-clear":!1,disabled:C(o)===2,style:{"margin-left":"10px"}},null,8,["value","disabled"])]),_:1},8,["style"]),t(z,{style:xe(C(E)),value:2},{default:s(()=>[A(" 年度盘点报告 "),t(h,{value:C(v),"onUpdate:value":r[2]||(r[2]=l=>U(v)?v.value=l:null),picker:"year","value-format":"YYYY","allow-clear":!1,disabled:C(o)===1,style:{"margin-left":"10px"}},null,8,["value","disabled"])]),_:1},8,["style"])]),_:1},8,["value"]),te("div",{class:"submit-container",onClick:r[4]||(r[4]=Ce(()=>{},["stop"]))},[t(b,{onClick:u,type:"primary"},{default:s(()=>[A("确认")]),_:1})])]),_:1})]),default:s(()=>[t(b,{onClick:r[0]||(r[0]=Ce(()=>{},["prevent"]))},{default:s(()=>[A("生成盘点报告")]),_:1})]),_:1},8,["visible"]),C(f).length>0?(ge(),Ue(b,{key:0,type:"primary",onClick:d},{default:s(()=>[A("导出")]),_:1})):qe("",!0)]),_:1})]),companyCodeListRender:s(({record:l})=>[t(M,null,{title:s(()=>[A(q(l.companyName),1)]),default:s(()=>[te("span",ut,q(l.companyName),1)]),_:2},1024)]),fullAddressRender:s(({record:l})=>[t(M,null,{title:s(()=>[A(q((l.provinceName||"")+(l.cityName||"")+(l.areaName||"")),1)]),default:s(()=>[te("span",ft,q((l.provinceName||"")+(l.cityName||"")+(l.areaName||"")),1)]),_:2},1024)]),_:1},8,["request"])])}}});const Tt=et(pt,[["__scopeId","data-v-f3a9c13f"]]);export{Tt as default};
