import{_ as N}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as q,r as l,bL as C,o as S,D as z,a as p,v as u,g as _,f as y,e as f,u as c,i as k,y as B,h as E,at as P,bg as T,_ as V}from"./index-db94d997.js";import{s as F,b as L}from"./index-705af6a9.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const M={class:"areaPrice"},R={class:"areaPrice_table"},W={key:0,style:{"margin-right":"15px"}},A=q({__name:"Info",setup(G){const i=l(),b=l([]),t=C(),h=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0},{title:"产权公司",dataIndex:"companyName",width:120,resizable:!0},{title:"省",dataIndex:"prvName",width:120,resizable:!0},{title:"市",dataIndex:"cityName",width:120,resizable:!0},{title:"区",dataIndex:"distName",width:120,resizable:!0},{title:"装机容量(kW)",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0},{title:"本月预测发电量(kWh)",dataIndex:"batteryDivinerEq",resizable:!0,formatMoney:!0,width:130}],x=()=>{var e;(e=i.value)==null||e.reload()},v=()=>{var r;let e=(r=i.value)==null?void 0:r.getInitialFormStateNew();L(e).then(o=>{console.log("导出成功")})},w=(e,r)=>new Promise(o=>{var s,n;let a={batteryDivinerEqDetailId:(s=t==null?void 0:t.query)==null?void 0:s.id,companyCode:(n=t==null?void 0:t.query)==null?void 0:n.companyCode};a={...a,...e},o(a)}),I=l([]),g=e=>{I.value=e||[]},D=()=>{P({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(a=>({label:a.companyName,value:a.companyCode}));b.value=o})};return S(()=>{D()}),z(()=>{i.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&x()}),(e,r)=>{const o=T,a=N;return p(),u("div",M,[_("div",R,[y(a,{search:!1,columns:h,ref_key:"actionRef",ref:i,request:c(F),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:g,"before-query-params":w},{tableHeader:f(()=>{var s,n,d,m;return[_("div",null,[(n=(s=c(t))==null?void 0:s.query)!=null&&n.createTime?(p(),u("span",W,"预测时间："+k((m=(d=c(t))==null?void 0:d.query)==null?void 0:m.createTime),1)):B("",!0),y(o,{type:"primary",onClick:v},{default:f(()=>[E("下载")]),_:1})])]}),_:1},8,["request"])])])}}});const X=V(A,[["__scopeId","data-v-8079eba9"]]);export{X as default};
