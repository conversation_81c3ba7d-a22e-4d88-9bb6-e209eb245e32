import{a2 as e,bP as o}from"./index-db94d997.js";const r=(t={})=>{const i=localStorage.getItem("companyData"),n=i?JSON.parse(i):[];return n&&n.length>0&&(t.companyCode=n[0].value),t};function a(t){return e({url:"/web/station/v1/pages",method:"POST",isTable:!0,data:t})}function l(t){return e({url:"/web/eam/v1/info",method:"GET",data:t})}function u(t){return e({url:"/web/bom/v1/list",method:"GET",data:t})}function c(t){return o({url:"/web/bom/v1/export",method:"GET",data:t})}function m(t){return e({url:"/web/stationCommercialInfo/v1/get",method:"GET",data:t})}function g(t){return e({url:"/web/stationIncomeList/v1/list",method:"POST",data:t})}function f(t){return e({url:"/web/stationShareAccountList/v1/list",method:"GET",data:t})}function d(t){return e({url:"/web/stationElectronicContractFile/v1/list",method:"GET",data:t})}function h(t){return e({url:"/web/stationReplenishAgreementFile/v1/list",method:"GET",data:t})}function b(t){return e({url:"/web/ongrid/v4/list",method:"GET",data:t})}function v(t){return e({url:"/web/engineering/v4/deviceInfo/list",method:"GET",data:t})}function w(t){return t=r(t),console.log("文件列表stationFile_data=",t),e({url:"/web/stationFile/v1/listV2",method:"POST",data:t})}function T(t){return t=r(t),console.log("下载stationFile_data=",t),o({url:"/web/stationFile/v1/downloadFileV2",method:"GET",data:t})}function S(t){return e({url:"/web/stationLog/v4/list",method:"GET",data:t})}function p(t){return o({url:"/web/station/v1/export",method:"POST",data:t})}export{w as a,a as b,m as c,T as d,p as e,g as f,l as g,f as h,d as i,h as j,b as k,v as l,S as m,u as n,c as o};
