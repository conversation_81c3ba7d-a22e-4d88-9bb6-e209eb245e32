import{_ as $}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as j,bP as k,d as G,I as H,r as i,T as J,o as Q,D as U,at as W,a as m,v as I,g as x,f as c,e as o,u as b,b as S,F as X,x as Z,ai as ee,i as w,h as v,y as te,au as ae,q as oe,bg as ne,bR as re,bS as le,bT as se,bn as ie,_ as ce}from"./index-db94d997.js";import{_ as de}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";function ue(r){return j({url:"/web/technicalData/v1/page",method:"POST",isTable:!0,data:r})}function pe(r){return k({url:"/web/technicalData/v1/exportFiled",method:"POST",data:r})}function _e(r){return k({url:"/web/technicalData/v1/exportPic",method:"POST",data:r})}const fe={class:"areaPrice"},me={class:"areaPrice_table"},he=["onClick"],ye={class:"text-hide"},xe=G({__name:"index",setup(r){const N=H();i(!1);const d=i(),h=i([]),g=i([]),z=i(["勘察资料"]),F=({id:e})=>C(e);let p="",y="";const L=[{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:h,valueTreeLoad:F,onSelect:(e,t,a)=>{var u,_;const n=((_=(u=a==null?void 0:a.triggerNode)==null?void 0:u.props)==null?void 0:_.level)||1;p=["provinceCode","cityCode","areaCode","town","vil"][n-1],y=e},render:!0,width:150,resizable:!0,hideInTable:!0,order:4},{title:"电站编号",dataIndex:"stationUniqueId",search:!0,resizable:!0,width:100,fixed:"left",order:2},{title:"业主名称",dataIndex:"stationName",search:!0,resizable:!0,fixed:"left",width:100,order:3},{title:"产权公司",key:"ownerCompanyCodeList",dataIndex:"ownerCompanyCodeList",valueType:"multipleSelect",maxTagCount:1,width:160,valueEnum:g,fixed:"left",order:1,render:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:120},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:120},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:120},{title:"详细地址",dataIndex:"projectLocation",search:!1,resizable:!0,width:120},{title:"并网时间",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",search:!1,resizable:!0,width:120},{title:"装机容量(kW)",dataIndex:"capins",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"屋顶类型",dataIndex:"roofType",valueType:"select2",valueEnum:J("EAM_ROOF_TYPE"),resizable:!0,search:!1,width:120},{title:"房屋建筑年限",dataIndex:"houseYear",resizable:!0,search:!1,width:120},{title:"屋面坡度",dataIndex:"roofSlope",resizable:!0,search:!1,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:100}],C=e=>new Promise((t,a)=>{ae({pid:e||"0"}).then(n=>{console.log("行政区res=",n);let l=T(n);h.value=l,console.log(h.value),t(!0)}).catch(()=>{a()})}),T=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,T(t.subDistrict))}),e);Q(()=>{M(),C()}),U(()=>{d.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&Y()});const E=(e,t)=>{console.log("type=",e),console.log("data=",t),e===0?P():e===1&&R()},M=()=>{W({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(n=>({label:n.companyName,value:n.companyCode}));g.value=a})},P=()=>{var t;let e=(t=d.value)==null?void 0:t.getInitialFormStateNew();pe(e)},R=()=>{var t;let e=(t=d.value)==null?void 0:t.getInitialFormStateNew();_e(e)},q=e=>{N.push({path:"/pAssetManage/assetManage/technicalInfo/detail",state:{pdata:oe.cloneDeep({...e,from:"/technicalInfo"})}})},D=i([]),O=e=>{D.value=e||[]},Y=()=>{var e;(e=d.value)==null||e.reload()},B=(e,t)=>{let a={};return new Promise(n=>{t&&t.hasOwnProperty("cityTree")&&!(t!=null&&t.cityTree)&&(p="",y=""),p&&(a={[p]:y});const l={...a,...e,noJoin:!0,delStatus:0};n(l)})};return(e,t)=>{const a=ne,n=re,l=le,u=se,_=de,V=ie,K=$;return m(),I("div",fe,[x("div",me,[c(K,{columns:L,ref_key:"actionRef",ref:d,request:b(ue),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:O,"before-query-params":B},{tableHeader:o(()=>[c(_,null,{default:o(()=>[b(D).length>0?(m(),S(u,{key:0,placement:"bottom"},{overlay:o(()=>[c(l,null,{default:o(()=>[(m(!0),I(X,null,Z(b(z),(f,s)=>(m(),S(n,null,{default:o(()=>[x("a",{onClick:ee(()=>E(s,f),["prevent"])},w(f),9,he)]),_:2},1024))),256))]),_:1})]),default:o(()=>[c(a,{type:"primary"},{default:o(()=>[v("导出")]),_:1})]),_:1})):te("",!0)]),_:1})]),ownerCompanyCodeListRender:o(({column:f,record:s,index:A})=>[c(V,null,{title:o(()=>[v(w(s.ownerCompanyName),1)]),default:o(()=>[x("span",ye,w(s.ownerCompanyName),1)]),_:2},1024)]),actionRender:o(({column:f,record:s,index:A})=>[c(a,{type:"link",size:"small",onClick:we=>q(s)},{default:o(()=>[v("查看详情")]),_:2},1032,["onClick"])]),_:1},8,["request"])])])}}});const Ne=ce(xe,[["__scopeId","data-v-ed1f2a26"]]);export{Ne as default};
