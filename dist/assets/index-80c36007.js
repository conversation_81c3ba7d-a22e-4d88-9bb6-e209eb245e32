import{_ as U}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as G,I as H,r as _,o as O,D as Q,at as j,a as i,v as b,g as m,f as p,e as s,h as o,b as f,u as r,i as u,y as I,q as E,bc as F,bg as K,av as W,bn as X,_ as Z}from"./index-db94d997.js";import{g as ee,u as te,e as ae}from"./index-0abb3d3a.js";import{a as k}from"./dictLocal-9822709a.js";import{_ as se}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const le={class:"areaPrice"},ne={class:"areaPrice_table"},de={class:"text-hide"},ie={class:"text-hide"},oe={key:0,class:"status_tag"},re={class:"tag_three"},ce={key:1,class:"status_tag"},pe={class:"tag_one"},ue={key:2,class:"status_tag"},_e={class:"tag_two"},me=G({__name:"index",setup(fe){const T=H(),h=_();_([]);const z=_([]),x=_(!1),y=_([{label:"出售成功",value:1},{label:"出售驳回",value:2}]),N=[{title:"出售单编号",dataIndex:"relationCode",search:!1,resizable:!0,width:100,fixed:"left"},{title:"电站编码",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:1,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:z,render:!0,resizable:!0,order:3},{title:"并网日期",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",search:!1,resizable:!0,width:150},{title:"结束日期",dataIndex:"endMonth",search:!1,resizable:!0,width:150},{title:"电站净值",dataIndex:"netValue",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"产品总价",dataIndex:"productPriceSum",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"产品售价",dataIndex:"productPrice",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"审批状态",key:"approveStatus",dataIndex:"approveStatus",valueType:"select",valueEnum:k,resizable:!0,render:!0,width:120},{title:"经办人",dataIndex:"createBy",search:!1,resizable:!0,render:!0,width:120},{title:"创建时间",dataIndex:"createTime",valueType:"dateRange",dateFormat:"YYYY-MM-DD",search:!1,hideInTable:!0,resizable:!0,width:150},{title:"创建时间",dataIndex:"createTime",valueType:"dateRange",resizable:!0,width:160},{title:"出售标志",key:"value",dataIndex:"sellStatus",valueType:"select",valueEnum:y,search:!1,resizable:!0,render:!0,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:190}];O(()=>{console.log("111-mmm"),R()}),Q(()=>{h.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&v()});const R=()=>{j({}).then(e=>{let n=(e||[]).map(c=>({label:c.companyName,value:c.companyCode}));z.value=n})},P=e=>{T.push({path:"/pAssetManage/propertyManage/assetSell/detail",state:{pdata:E.cloneDeep(e)}})},Y=e=>{e.isEdit=!0,e.sellStatusTem=e.sellStatus},L=e=>{e.isEdit=!1,e.sellStatus=e.sellStatusTem},B=e=>{console.log("编辑");let d={id:e.id,sellStatus:e.sellStatus};x.value=!0,te(d).then(n=>{x.value=!1,F.info("保存成功"),v(!0)}).catch(n=>{x.value=!1})},A=()=>{let e={};T.push({path:"/pAssetManage/propertyManage/assetSell/detail",state:{pdata:E.cloneDeep(e)}})},V=()=>{var d;let e=(d=h.value)==null?void 0:d.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),ae(e)},$=_([]),q=e=>{$.value=e||[]},v=e=>{var d;(d=h.value)==null||d.reload(e)},J=(e,d)=>{let n={};return new Promise(c=>{if(e!=null&&e.createTime){const g=(e==null?void 0:e.createTime.length)>0?e==null?void 0:e.createTime[0]:"",S=(e==null?void 0:e.createTime.length)>0?e==null?void 0:e.createTime[1]:"";n={delStatus:0,noJoin:!0,startTime:g,endTime:S},e.createTime&&delete e.createTime}const w={...n,...e,delStatus:0,noJoin:!0};c(w)})};return(e,d)=>{const n=K,c=se,w=W,g=X,S=U;return i(),b("div",le,[m("div",ne,[p(S,{columns:N,ref_key:"actionRef",ref:h,request:r(ee),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:q,"before-query-params":J},{tableHeader:s(()=>[p(c,null,{default:s(()=>[p(n,{onClick:A},{default:s(()=>[o("新建出售表单")]),_:1}),p(n,{type:"primary",onClick:V},{default:s(()=>[o("导出")]),_:1})]),_:1})]),valueRender:s(({column:a,record:t,index:C})=>[t.isEdit?(i(),f(w,{key:0,value:t[a.dataIndex],"onUpdate:value":l=>t[a.dataIndex]=l,style:{width:"100%"},options:r(y),placeholder:"请选择"},null,8,["value","onUpdate:value","options"])):(i(),f(g,{key:1},{title:s(()=>{var l;return[o(u(t[a.dataIndex]&&((l=r(y)[t[a.dataIndex]-1])==null?void 0:l.label)),1)]}),default:s(()=>{var l;return[m("span",de,u(t[a.dataIndex]&&((l=r(y)[t[a.dataIndex]-1])==null?void 0:l.label)),1)]}),_:2},1024))]),companyCodeListRender:s(({column:a,record:t,index:C})=>[p(g,null,{title:s(()=>[o(u(t.companyName),1)]),default:s(()=>[m("span",ie,u(t.companyName),1)]),_:2},1024)]),approveStatusRender:s(({column:a,record:t,index:C})=>{var l,D,M;return[t[a.dataIndex]=="1"?(i(),b("span",oe,[m("span",re,u(t[a.dataIndex]&&((l=r(k)[t[a.dataIndex]-1])==null?void 0:l.label)),1)])):t[a.dataIndex]=="2"?(i(),b("span",ce,[m("span",pe,u(t[a.dataIndex]&&((D=r(k)[t[a.dataIndex]-1])==null?void 0:D.label)),1)])):t[a.dataIndex]=="3"?(i(),b("span",ue,[m("span",_e,u(t[a.dataIndex]&&((M=r(k)[t[a.dataIndex]-1])==null?void 0:M.label)),1)])):I("",!0)]}),actionRender:s(({column:a,record:t,index:C})=>[p(c,null,{default:s(()=>[t.isEdit?I("",!0):(i(),f(n,{key:0,type:"link",size:"small",onClick:l=>Y(t),disabled:!(t.approveStatus=="2"&&t.sellStatus!="1")},{default:s(()=>[o("编辑")]),_:2},1032,["onClick","disabled"])),t.isEdit?(i(),f(n,{key:1,type:"link",size:"small",onClick:l=>L(t),disabled:!1},{default:s(()=>[o("取消")]),_:2},1032,["onClick"])):I("",!0),t.isEdit?(i(),f(n,{key:2,type:"link",size:"small",onClick:l=>B(t),loading:r(x)},{default:s(()=>[o("保存")]),_:2},1032,["onClick","loading"])):I("",!0),p(n,{type:"link",size:"small",onClick:l=>P(t),disabled:t.sellStatus=="1"},{default:s(()=>[o("修改出售单")]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1},8,["request"])])])}}});const Te=Z(me,[["__scopeId","data-v-9d904551"]]);export{Te as default};
