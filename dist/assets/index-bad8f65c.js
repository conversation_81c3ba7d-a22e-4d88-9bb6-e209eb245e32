import{a2 as t,bP as a}from"./index-db94d997.js";function i(e){return t({url:"/web/practicalProceedsEqFee/v1/page",method:"POST",isTable:!0,data:e})}function c(e){return a({url:"/web/practicalProceedsEqFee/v1/export",method:"POST",data:e})}function o(e){return t({url:"/web/practicalProceedsEqFeeDetail/v1/page",method:"POST",isTable:!0,data:e})}function n(e){return a({url:"/web/practicalProceedsEqFeeDetail/v1/export",method:"POST",data:e})}function s(e){return t({url:"/web/practicalProceedsEqFeeDetail/v1/update",method:"POST",data:e})}function u(e){return t({url:"/web/IntoOutInvoicesManage/v3/list",method:"POST",isTable:!0,data:e})}function l(e){return a({url:"/web/IntoOutInvoicesManage/v3/exportList",method:"POST",data:e})}function P(e){return t({url:"/web/IntoOutInvoicesManage/v3/create",method:"POST",data:e})}function d(e){return t({url:"/web/officialReceiptsEqFee/v1/page",method:"POST",isTable:!0,data:e})}function p(e){return a({url:"/web/officialReceiptsEqFee/v1/export",method:"POST",data:e})}export{o as a,n as b,u as c,P as d,c as e,l as f,i as g,d as h,p as i,s as u};
