import{_ as k}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as K,r as i,o as q,D as E,at as F,a as P,v as B,g as v,f as p,e as s,h as w,i as g,u as R,au as V,bg as O,bn as Q,_ as A}from"./index-db94d997.js";import{a as G,b as H}from"./index-ad1a77b9.js";import{_ as J}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const $={class:"areaPrice"},j={class:"areaPrice_table"},U={class:"text-hide"},W=K({__name:"Detail",setup(X){var x;const c=i(),f=i((x=history.state)==null?void 0:x.pdata),y=i([]),m=i([]),C=({id:t})=>h(t);let d="",_="";const D=[{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:y,valueTreeLoad:C,onSelect:(t,e,a)=>{var r,l;const o=((l=(r=a==null?void 0:a.triggerNode)==null?void 0:r.props)==null?void 0:l.level)||1;d=["provinceCode","cityCode","areaCode","town","vil"][o-1],_=t},render:!0,width:150,resizable:!0,hideInTable:!0,order:3},{title:"电站编号",dataIndex:"stationCode",search:!0,resizable:!0,width:100,fixed:"left",order:1},{title:"业主名称",dataIndex:"stationName",search:!0,resizable:!0,fixed:"left",width:100,order:2},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:160,valueEnum:m,fixed:"left",order:1,search:!1,render:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:120},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:120},{title:"区",dataIndex:"areaName",search:!1,resizable:!0,width:120},{title:"季度",dataIndex:"yearKeyQuarter",width:120,resizable:!0,search:!1},{title:"应付总额",dataIndex:"oughtPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"实付总额",dataIndex:"practicalPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"待支付金额",dataIndex:"stayPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付中金额",dataIndex:"underwayPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付成功金额",dataIndex:"paySucceedMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付失败金额",dataIndex:"payFailMoney",width:120,resizable:!0,formatMoney:!0,search:!1}],h=t=>new Promise((e,a)=>{V({pid:t||"0"}).then(o=>{console.log("行政区res=",o);let n=b(o);y.value=n,console.log(y.value),e(!0)}).catch(()=>{a()})}),b=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,b(e.subDistrict))}),t);q(()=>{I(),h()}),E(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&M()});const I=()=>{F({}).then(t=>{console.log("产权公司res=",t);let a=(t||[]).map(o=>({label:o.companyName,value:o.companyCode}));m.value=a})},T=()=>{var e;let t=(e=c.value)==null?void 0:e.getInitialFormStateNew();H(t)},S=i([]),z=t=>{S.value=t||[]},M=()=>{var t;(t=c.value)==null||t.reload()},N=(t,e)=>{let a={};return new Promise(o=>{if(e&&e.hasOwnProperty("cityTree")&&!(e!=null&&e.cityTree)&&(d="",_=""),d&&(a={[d]:_}),f.value){const{companyCode:r,yearKey:l,quarter:u}=f.value;a={...a,companyCodeList:[r],yearKey:l,quarter:u,noJoin:!0,delStatus:0}}const n={...a,...t};o(n)})};return(t,e)=>{const a=O,o=J,n=Q,r=k;return P(),B("div",$,[v("div",j,[p(r,{columns:D,ref_key:"actionRef",ref:c,request:R(G),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:z,"before-query-params":N},{tableHeader:s(()=>[p(o,null,{default:s(()=>[p(a,{type:"primary",onClick:T},{default:s(()=>[w("导出")]),_:1})]),_:1})]),companyCodeListRender:s(({column:l,record:u,index:L})=>[p(n,null,{title:s(()=>[w(g(u.companyName),1)]),default:s(()=>[v("span",U,g(u.companyName),1)]),_:2},1024)]),_:1},8,["request"])])])}}});const ie=A(W,[["__scopeId","data-v-bd8f20ab"]]);export{ie as default};
