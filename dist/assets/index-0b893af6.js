import{_ as z}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as N,r as y,I as K,o as P,D as S,at as v,a as h,v as L,f as s,e as t,u as x,b as R,h as p,y as T,i as m,g,q as B,bg as V,bn as E,_ as $}from"./index-db94d997.js";import{g as A,e as G}from"./index-ad1a77b9.js";import{_ as H}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const J={class:"areaPrice"},O={class:"text-hide"},Q={class:"text-hide"},Y=N({__name:"index",setup(j){const c=y(),_=y([]),b=K(),w=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:_,render:!0,resizable:!0,order:1,fixed:"left"},{title:"季度",key:"quarterKey",dataIndex:"quarterKey",valueType:"quarterDate",width:120,resizable:!0,render:!0,fixed:"left"},{title:"应付总额",dataIndex:"oughtPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"实付总额",dataIndex:"practicalPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"待支付金额",dataIndex:"stayPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付中金额",dataIndex:"underwayPayMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付成功金额",dataIndex:"paySucceedMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"支付失败金额",dataIndex:"payFailMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"操作",key:"action",dataIndex:"action",search:!1,width:100,render:!0,align:"center",fixed:"right"}];P(()=>{M()}),S(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&k()});const M=()=>{v({}).then(e=>{let n=(e||[]).map(o=>({label:o.companyName,value:o.companyCode}));_.value=n})},I=e=>{b.push({path:"/financeManage/farmerIncomeManage/farmerIncomeStatistics/detail",state:{pdata:B.cloneDeep(e)}})},q=()=>{var l;let e=(l=c.value)==null?void 0:l.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),G(e)},f=y([]),C=e=>{f.value=e||[]},k=()=>{var e;(e=c.value)==null||e.reload()},D=(e,l)=>{let n={};return new Promise(o=>{if(e!=null&&e.quarterKey){const d=new Date(e==null?void 0:e.quarterKey),i=d.getMonth(),a=d.getFullYear(),u=Math.floor(i/3)+1;n={yearKey:a,quarter:u}}const r={...n,...e,noJoin:!0,delStatus:0};r==null||delete r.quarterKey,o(r)})};return(e,l)=>{const n=V,o=H,r=E,d=z;return h(),L("div",J,[s(d,{columns:w,ref_key:"actionRef",ref:c,request:x(A),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:C,"before-query-params":D},{tableHeader:t(()=>[s(o,null,{default:t(()=>[x(f).length>0?(h(),R(n,{key:0,type:"primary",onClick:q},{default:t(()=>[p("导出")]),_:1})):T("",!0)]),_:1})]),companyCodeListRender:t(({column:i,record:a,index:u})=>[s(r,null,{title:t(()=>[p(m(a.companyName),1)]),default:t(()=>[g("span",O,m(a.companyName),1)]),_:2},1024)]),quarterKeyRender:t(({column:i,record:a,index:u})=>[s(r,null,{title:t(()=>[p(m(a.yearKey+"-"+a.quarter+"季度"),1)]),default:t(()=>[g("span",Q,m(a.yearKey+"-"+a.quarter+"季度"),1)]),_:2},1024)]),actionRender:t(({column:i,record:a,index:u})=>[s(o,null,{default:t(()=>[s(n,{type:"link",size:"small",onClick:F=>I(a),disabled:!1},{default:t(()=>[p("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const re=$(Y,[["__scopeId","data-v-75a2205d"]]);export{re as default};
