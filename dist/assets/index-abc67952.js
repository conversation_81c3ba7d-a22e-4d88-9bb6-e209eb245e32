import{d as Me,I as Te,r as d,K as G,o as Ne,D as Ee,at as Re,w as Ye,a as v,v as P,g as u,f as i,e as o,h as f,u as l,y as w,b as M,i as x,z as H,e9 as X,s as Be,F as Pe,au as Z,q as U,k as ee,bc as Ue,bg as Ae,bf as Ve,bU as je,av as qe,bh as Oe,bn as Se,ay as $e,aw as Qe,ax as Je,bi as Ke,p as We,j as Ge,_ as He}from"./index-db94d997.js";import{r as te,g as Xe,e as Ze}from"./const-3409a276.js";import{m as A}from"./dealTable-c35ad2da.js";import{D as et}from"./dayjs-a8e42122.js";import{_ as tt}from"./index-39334618.js";import{_ as at}from"./index-42d7fb9b.js";import{_ as nt}from"./index-4a280682.js";import"./customParseFormat-ed0c33ac.js";const T=I=>(We("data-v-a31725eb"),I=I(),Ge(),I),lt={class:"statements_container"},ot=T(()=>u("div",{class:"item_title"},"模式配置",-1)),st={style:{display:"flex","flex-direction":"row","align-items":"center"}},it={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},ct={style:{display:"flex","justify-content":"flex-end","flex-direction":"row","margin-bottom":"16px"}},dt=T(()=>u("div",{class:"item_title"},"查询条件",-1)),ut={style:{display:"flex","flex-direction":"row","align-items":"center"}},rt={style:{flex:"1","padding-right":"5%"}},pt={style:{flex:"1","padding-right":"5%"}},_t=T(()=>u("span",{style:{"margin-left":"5px"}},"全选",-1)),mt=T(()=>u("div",{style:{flex:"1","padding-right":"5%"}},null,-1)),ft={style:{display:"flex","flex-direction":"row","align-items":"center"}},yt={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},vt={class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},ht={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},gt={class:"text-hide"},xt={class:"text-hide"},bt={class:"text-hide"},kt={key:3},Ct=Me({__name:"index",setup(I){const ae=Te(),ne=[{label:"逆变器电量",value:1},{label:"调整后电量",value:2}],h=d(!1),V=d(),le=d(!1),_=G({}),N=d(),D=d(!1),oe=d([]),b=d({}),z=d([]),se=d([]),k=d(0),E=d(1),L=d(12),g=d([]),a=G({companyCodeList:"",companyId:[],companyId_checked:!1,companyId_indeterminate:!1}),R=d(3),j=d([{title:"产权公司",dataIndex:"companyName",resizable:!0,width:120,fixed:"left",customCell:(e,t,s)=>A(R.value,e,t)},{title:"收款年份",dataIndex:"year",resizable:!0,width:120,fixed:"left",customCell:(e,t,s)=>A(R.value,e,t)},{title:"装机容量(kW)",dataIndex:"capins",formatMoney:!0,resizable:!0,width:150,fixed:"left",customCell:(e,t,s)=>A(R.value,e,t)},{title:"指标",key:"targetType",dataIndex:"targetType",resizable:!0,width:150}]),q=d([{title:"操作",key:"action",resizable:!0,width:120,fixed:"right"}]),F=d([]),Y=d([]),ie=d([]),ce=e=>new Promise((t,s)=>{Z({pid:e||"0"}).then(c=>{let r=O(c);ie.value=r,t(!0)}).catch(()=>{s()})}),O=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,O(t.subDistrict))}),e),de=()=>{h.value=!0,console.log("立即配置")},ue=()=>{V.value.validateFields().then(()=>{h.value=!1})},re=()=>{h.value=!1},pe=e=>{console.log(e),ae.push({path:"/financeManage/electricQuantity/inverseComputation/detail",state:{pdata:U.cloneDeep({...e,..._,year:ee(a==null?void 0:a.year).format("YYYY")})}})},_e=(e,{attrs:t})=>t.vnodes,me=(e,t)=>{a[e]=t.map(s=>s.value)},fe=(e,t,s)=>{var r;((r=e==null?void 0:e.target)==null?void 0:r.checked)?me(t,s):a[t]=[],a[t+"_indeterminate"]=!1},ye=(e,t,s,c)=>{t.length===0?(a[c+"_indeterminate"]=!1,a[c+"_checked"]=!1):t.length===s.length?(a[c+"_indeterminate"]=!1,a[c+"_checked"]=!0):(a[c+"_indeterminate"]=!0,a[c+"_checked"]=!1)},ve=d({year:[{required:!0}]}),he=e=>{_.preMonth&&_.actualElectricityType?S():Ue.warning("请先完成模式配置")},ge=e=>{console.log(e)},xe=()=>{N.value.resetFields(),b.value={},z.value=[],a.companyId_checked=!1,a.companyId_indeterminate=!1,F.value=[],Y.value=[...j.value,...F.value,...q.value],k.value=0},be=(e,t,s)=>{E.value=e.current,L.value=e.pageSize,k.value=e.total,{...a,pageNum:e.current,pageSize:L.value},S()},ke=()=>{Z({pid:0}).then(e=>{if(e&&e.length>0){let t=e.map(s=>({label:s.name,value:s.code}));oe.value=t}})},S=e=>{if(a.year){const t=ee(a.year).format("YYYY"),c={companyCodeList:a!=null&&a.companyId?U.cloneDeep(a==null?void 0:a.companyId):[],preMonth:_.preMonth,actualElectricityType:_.actualElectricityType,pageNum:E.value,pageSize:L.value/3,year:t};console.log(c,"params====",a.companyId),D.value=!0,Xe(c).then(r=>{D.value=!1,b.value={...c,companyCodeList:a.companyId},console.log("公司维度res=",r==null?void 0:r.data),Ce(r,1)}).catch(()=>{D.value=!1})}},Ce=(e,t)=>{var s,c,r;t===1?(F.value=(s=e==null?void 0:e.result)==null?void 0:s.timeList.map((y,$)=>({title:y,dataIndex:y,formatMoney:!0,resizable:!0,width:120})),Y.value=[...j.value,...F.value,...q.value],z.value=((c=e==null?void 0:e.result)==null?void 0:c.dataList)||[],k.value=e!=null&&e.total?(e==null?void 0:e.total)*3:0):t===2&&(se.value=((r=e==null?void 0:e.result)==null?void 0:r.dataList)||[],k.value=e!=null&&e.total?(e==null?void 0:e.total)*3:0)};Ne(()=>{we(),ce(),ke()}),Ee(()=>{console.log("onActivated")});const we=()=>{Re({}).then(e=>{let s=(e||[]).map(c=>({label:c.companyName,value:c.companyCode}));g.value=s})};Ye(()=>a.checked1,e=>{e&&(a.checked2=!1,a.checked3=!1,a.time2="",a.time3="",N.value.clearValidate(["time2","time3"]))});const Ie=()=>{console.log("pageParams=",b.value);const e=a!=null&&a.companyId?U.cloneDeep(a==null?void 0:a.companyId):[],t={...b.value,companyCodeList:e};b.value.year&&Ze(t).then(s=>{console.log("公司维度导出res:",s)})};return(e,t)=>{const s=Ae,c=tt,r=et,y=Ve,$=je,De=at,Q=qe,J=Oe,B=Se,ze=$e,Le=nt,K=Qe,W=Je,Fe=Ke;return v(),P(Pe,null,[u("div",lt,[i(J,{ref_key:"formRef",ref:N,name:"formRef",model:l(a),rules:l(ve),onFinish:he,onFinishFailed:ge},{default:o(()=>[ot,u("div",st,[u("div",it,[u("div",ct,[i(c,null,{default:o(()=>[i(s,{type:"primary",onClick:de},{default:o(()=>[f("立即配置")]),_:1})]),_:1})])])]),dt,u("div",ut,[u("div",rt,[i(y,{label:"收款年份",name:"year"},{default:o(()=>[i(r,{value:l(a).year,"onUpdate:value":t[0]||(t[0]=n=>l(a).year=n),style:{width:"100%"},placeholder:"请选择",picker:"year"},null,8,["value"])]),_:1})]),u("div",pt,[i(y,{label:"产权公司",name:"companyId"},{default:o(()=>[i(Q,{value:l(a).companyId,"onUpdate:value":t[4]||(t[4]=n=>l(a).companyId=n),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:l(g),mode:"multiple","filter-option":(n,p)=>((p==null?void 0:p.label)??"").toLowerCase().includes(n.toLowerCase()),onChange:t[5]||(t[5]=(n,p)=>ye(n,p,l(g),"companyId"))},{dropdownRender:o(({menuNode:n})=>[l(g)&&l(g).length>0?(v(),P("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[3]||(t[3]=p=>p.preventDefault())},[i($,{checked:l(a).companyId_checked,"onUpdate:checked":t[1]||(t[1]=p=>l(a).companyId_checked=p),indeterminate:l(a).companyId_indeterminate,onChange:t[2]||(t[2]=p=>fe(p,"companyId",l(g)))},null,8,["checked","indeterminate"]),_t],32)):w("",!0),i(De,{style:{margin:"4px 0"}}),i(_e,{vnodes:n},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1})]),mt]),u("div",ft,[u("div",yt,[i(c,null,{default:o(()=>[i(s,{onClick:xe},{default:o(()=>[f("重置")]),_:1}),i(s,{type:"primary","html-type":"submit"},{default:o(()=>[f("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),u("div",vt,[u("div",ht,[i(c,null,{default:o(()=>[l(z).length>0?(v(),M(s,{key:0,type:"primary",onClick:Ie},{default:o(()=>[f("导出")]),_:1})):w("",!0)]),_:1})]),i(ze,{style:{"margin-bottom":"24px"},columns:l(Y),"data-source":l(z),pagination:{current:l(E),total:l(k),showTotal:n=>`共 ${n} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0,pageSizeOptions:["6","12","24","60","120"],pageSize:l(L)},scroll:{y:500},loading:l(D),onChange:be},{bodyCell:o(({column:n,text:p,record:m,index:wt})=>[n!=null&&n.formatMoney&&!n.render?(v(),M(B,{key:0},{title:o(()=>[f(x(l(H)(m[n.dataIndex])),1)]),default:o(()=>[u("span",gt,x(l(H)(m[n.dataIndex])),1)]),_:2},1024)):n!=null&&n.formatFixed&&!n.render?(v(),M(B,{key:1},{title:o(()=>[f(x(m[n.dataIndex]&&l(X)(m[n.dataIndex])),1)]),default:o(()=>[u("span",xt,x(m[n.dataIndex]&&l(X)(m[n.dataIndex])),1)]),_:2},1024)):w("",!0),n.key=="targetType"?(v(),M(B,{key:2},{title:o(()=>{var C;return[f(x((C=l(te)[m[n.dataIndex]-1])==null?void 0:C.label),1)]}),default:o(()=>{var C;return[u("span",bt,x((C=l(te)[m[n.dataIndex]-1])==null?void 0:C.label),1)]}),_:2},1024)):w("",!0),n.key=="action"?(v(),P("div",kt,[i(c,null,{default:o(()=>[i(s,{size:"small",type:"link",onClick:()=>pe(m)},{default:o(()=>[f(" 查看明细 ")]),_:2},1032,["onClick"])]),_:2},1024)])):w("",!0)]),_:1},8,["columns","data-source","pagination","loading"])]),i(Fe,{visible:l(h),"onUpdate:visible":t[8]||(t[8]=n=>Be(h)?h.value=n:null),title:"模式配置单","confirm-loading":l(le),destroyOnClose:!0,"ok-text":"提交",onOk:ue,onCancel:re},{default:o(()=>[i(J,{model:l(_),ref_key:"formRef1",ref:V,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:o(()=>[i(W,{span:24},{default:o(()=>[i(K,{span:24},{default:o(()=>[i(y,{label:"实际电量时间往前推",name:"preMonth",required:"","label-col":{span:8}},{default:o(()=>[i(Le,{value:l(_).preMonth,"onUpdate:value":t[6]||(t[6]=n=>l(_).preMonth=n),placeholder:"请输入",style:{width:"100%"},controls:!1},{addonAfter:o(()=>[f("个月")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),i(W,{span:24},{default:o(()=>[i(K,{span:24},{default:o(()=>[i(y,{label:"实际电量取值",name:"actualElectricityType",required:"","label-col":{span:6}},{default:o(()=>[i(Q,{value:l(_).actualElectricityType,"onUpdate:value":t[7]||(t[7]=n=>l(_).actualElectricityType=n),options:ne,style:{width:"100%"},placeholder:"请选择"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const Et=He(Ct,[["__scopeId","data-v-a31725eb"]]);export{Et as default};
