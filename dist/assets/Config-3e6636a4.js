import{_ as K}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as j,I as G,r as _,o as H,D as J,at as O,a as o,v as Q,f,e as n,u as s,b as d,h as r,i,S as z,g,k as v,q as C,bc as $,bg as X,av as Z,bn as ee,_ as ae}from"./index-db94d997.js";import{d as te,s as ne}from"./index-895935b7.js";import{D as le}from"./dayjs-a8e42122.js";import{_ as se}from"./index-39334618.js";import{_ as oe}from"./index-4a280682.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const de={class:"areaPrice"},ie={class:"text-hide"},ue={class:"text-hide"},re={class:"text-hide"},ce={class:"text-hide"},pe={class:"text-hide"},_e=j({__name:"Config",setup(me){const R=G(),D=_(),Y=_([]),c=_(!1),m=_([]),b=_([]),h=_(!1),k=_([{label:"发电量加权",value:1},{label:"装机容量加权",value:2},{label:"净资产额加权",value:3}]),S=[{title:"产权公司",valueType:"select",valueEnum:Y,dataIndex:"companyCode",width:150,fixed:"left",resizable:!0},{title:"月份",key:"month",valueType:"date",dataIndex:"monthKey",width:120,fixed:"left",resizable:!0,render:!0},{title:"开始时间",key:"day",dataIndex:"startTime",width:120,resizable:!0,render:!0},{title:"截止时间",key:"day",dataIndex:"endTime",width:120,resizable:!0,render:!0},{title:"周期天数",dataIndex:"cycleDayNum",width:120,resizable:!0},{title:"折旧费用",key:"select",dataIndex:"deprecitionFeeWeightedType",valueType:"select",valueEnum:k,width:120,resizable:!0,render:!0},{title:"运维费用",key:"select",dataIndex:"entrustRunFeeWeightedType",valueType:"select",valueEnum:k,width:120,resizable:!0,render:!0},{title:"财务费用",key:"select",dataIndex:"financeFeeWeightedType",valueType:"select",valueEnum:k,width:120,resizable:!0,render:!0},{title:"其他费用",key:"select",dataIndex:"otherFeeWeightedType",valueType:"select",valueEnum:k,width:120,resizable:!0,render:!0},{title:"配置时间",key:"createTime",dataIndex:"createTime",width:120,resizable:!0,render:!0}];H(()=>{N()}),J(()=>{D.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&q()});const F=(e,y)=>{let t;e!=null&&e.startTime&&(e!=null&&e.endTime)&&(t=v(v(e.endTime).format("YYYY-MM-DD")).diff(v(v(e.startTime).format("YYYY-MM-DD")),"day")+1),e.cycleDayNum=t},N=()=>{O({}).then(e=>{console.log("产权公司res=",e);let t=(e||[]).map(p=>({label:p.companyName,value:p.companyCode}));Y.value=t})},P=()=>{console.log("编辑"),c.value=!0,b.value=C.cloneDeep(m.value)},U=()=>{var e;console.log("取消"),c.value=!1,(e=D.value)==null||e.setDataSource(b.value)},W=()=>{console.log("保存"),c.value=!1,b.value=C.cloneDeep(m.value)},M=()=>{console.log("立即生成数据");let y=(m.value&&m.value.length>0?C.cloneDeep(m.value):[]).filter(t=>t.monthKey&&t.startTime&&t.endTime&&t.deprecitionFeeWeightedType&&t.entrustRunFeeWeightedType&&t.financeFeeWeightedType&&t.otherFeeWeightedType);console.log("params=",y),h.value=!0,ne(y).then(t=>{h.value=!1,$.info("操作成功"),W(),R.go(-1)}).catch(t=>{console.log("err=",t),h.value=!1})},B=e=>{m.value=e||[]},q=()=>{var e;(e=D.value)==null||e.reload()},L=(e,y)=>{let t={};return new Promise(p=>{const w={...t,...e,noJoin:!0,delStatus:0};p(w)})};return(e,y)=>{const t=X,p=se,w=Z,x=ee,E=le,V=oe,A=K;return o(),Q("div",de,[f(A,{columns:S,ref_key:"actionRef",ref:D,request:s(te),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:B,"before-query-params":L,search:!1,pagination:!1},{tableHeader:n(()=>[s(c)?(o(),d(p,{key:1},{default:n(()=>[f(t,{onClick:U},{default:n(()=>[r("取消")]),_:1}),f(t,{type:"primary",onClick:M,loading:s(h)},{default:n(()=>[r("立即生成数据")]),_:1},8,["loading"])]),_:1})):(o(),d(p,{key:0},{default:n(()=>[f(t,{onClick:P},{default:n(()=>[r("编辑")]),_:1}),f(t,{type:"primary",onClick:M,loading:s(h)},{default:n(()=>[r("立即生成数据")]),_:1},8,["loading"])]),_:1}))]),selectRender:n(({column:a,record:l,index:T})=>[s(c)?(o(),d(w,{key:0,value:l[a.dataIndex],"onUpdate:value":u=>l[a.dataIndex]=u,options:a.valueEnum,placeholder:"请选择",allowClear:"",showArrow:"","show-search":"","filter-option":(u,I)=>((I==null?void 0:I.label)??"").toLowerCase().includes(u.toLowerCase()),style:{width:"90%"}},null,8,["value","onUpdate:value","options","filter-option"])):(o(),d(x,{key:1},{title:n(()=>[r(i(s(z)(l[a.dataIndex],a.valueEnum)),1)]),default:n(()=>[g("span",ie,i(s(z)(l[a.dataIndex],a.valueEnum)),1)]),_:2},1024))]),createTimeRender:n(({column:a,record:l,index:T})=>[f(x,null,{title:n(()=>[r(i(s(v)().format("YYYY-MM-DD")),1)]),default:n(()=>[g("span",ue,i(s(v)().format("YYYY-MM-DD")),1)]),_:1})]),monthRender:n(({column:a,record:l,index:T})=>[s(c)?(o(),d(E,{key:0,value:l[a.dataIndex],"onUpdate:value":u=>l[a.dataIndex]=u,"value-format":"YYYY-MM",picker:"month",placeholder:"请选择",allowClear:"",style:{width:"90%"}},null,8,["value","onUpdate:value"])):(o(),d(x,{key:1},{title:n(()=>[r(i(l[a.dataIndex]),1)]),default:n(()=>[g("span",re,i(l[a.dataIndex]),1)]),_:2},1024))]),dayRender:n(({column:a,record:l,index:T})=>[s(c)?(o(),d(E,{key:0,value:l[a.dataIndex],"onUpdate:value":u=>l[a.dataIndex]=u,"value-format":"YYYY-MM-DD",placeholder:"请选择",allowClear:"",style:{width:"90%"},onChange:()=>F(l,a)},null,8,["value","onUpdate:value","onChange"])):(o(),d(x,{key:1},{title:n(()=>[r(i(l[a.dataIndex]),1)]),default:n(()=>[g("span",ce,i(l[a.dataIndex]),1)]),_:2},1024))]),numberRender:n(({column:a,record:l,index:T})=>[s(c)?(o(),d(V,{key:0,value:l[a.dataIndex],"onUpdate:value":u=>l[a.dataIndex]=u,controls:!1,placeholder:"请输入",min:0,precision:0,style:{width:"90%"}},null,8,["value","onUpdate:value"])):(o(),d(x,{key:1},{title:n(()=>[r(i(l[a.dataIndex]),1)]),default:n(()=>[g("span",pe,i(l[a.dataIndex]),1)]),_:2},1024))]),_:1},8,["request"])])}}});const we=ae(_e,[["__scopeId","data-v-a65c2264"]]);export{we as default};
