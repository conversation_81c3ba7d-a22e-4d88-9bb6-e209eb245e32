import{f as D,L as te,e8 as de,d as se,a as d,v as o,g as b,a1 as R,e as i,h,i as s,u as f,d0 as ie,y as g,b as n,S as Q,W as P,z as W,e9 as T,m as G,n as Y,Z as re,F as u,x as E,k as J,bn as ne,av as le,be as pe,ea as _e,eb as fe,ec as ye,ay as oe,_ as he}from"./index-db94d997.js";import{D as xe}from"./dayjs-a8e42122.js";import{_ as ve}from"./index-4a280682.js";function X(x){for(var _=1;_<arguments.length;_++){var l=arguments[_]!=null?Object(arguments[_]):{},k=Object.keys(l);typeof Object.getOwnPropertySymbols=="function"&&(k=k.concat(Object.getOwnPropertySymbols(l).filter(function(w){return Object.getOwnPropertyDescriptor(l,w).enumerable}))),k.forEach(function(w){Ie(x,w,l[w])})}return x}function Ie(x,_,l){return _ in x?Object.defineProperty(x,_,{value:l,enumerable:!0,configurable:!0,writable:!0}):x[_]=l,x}var M=function(_,l){var k=X({},_,l.attrs);return D(te,X({},k,{icon:de}),null)};M.displayName="PlusOutlined";M.inheritAttrs=!1;const be=M,ge={key:0},ke={key:0},Ce={key:0},we={class:"text-hide"},ue={class:"text-hide"},De={class:"text-hide"},Fe={class:"text-hide"},Pe={class:"text-hide"},Te={class:"text-hide"},Ye={key:1},Oe={key:0,class:"add_sty"},Ae=se({__name:"index",props:{columns:{},dataListClone:{},dataList:{},dataListSummary:{},pagination:{default:!1},summary:{type:Boolean,default:!1},average:{type:Boolean,default:!1},isTableEdit:{type:Boolean,default:!1},averageName:{default:"平均"},totalName:{default:"总计"},handleClickAddTable:{},loading:{type:Boolean,default:!1},startCheck:{type:Boolean,default:!1},showAdd:{type:Boolean,default:!0},bordered:{type:Boolean,default:!0},scroll:{}},emits:["handleClickAddTable"],setup(x,{emit:_}){const l=x,k=()=>{_("handleClickAddTable")},w=(t="YYYYMMDD")=>{let p=t.includes("YYYY"),r=t.includes("MM"),I=t.includes("DD");return p&&r&&I?"":p&&r&&!I?"month":p&&!r&&!I?"year":""},K=t=>{let p=0,r=t.dataIndex;return l.dataListClone.forEach(I=>{let C=Number(I[r])||0;p+=C}),t.formatFixed?T(p):t.formatPercentage?P(p):p.toFixed(2)},m=t=>{let p=0,r=t.dataIndex;l.dataListClone.forEach(O=>{let A=Number(O[r])||0;p+=A});let I=l.dataListClone.length,C=p/I;return t.formatFixed?T(C):t.formatPercentage?P(C):C.toFixed(2)},c=(t,p,r)=>{if(!(!p&&!r))return t&&t>J(r||null)||t&&t<J(p||null)};return(t,p)=>{const r=ne,I=le,C=xe,O=ve,A=pe,S=_e,B=fe,ee=ye,ae=oe;return d(),o("div",null,[b("div",null,[R(t.$slots,"tableHeader",{},void 0,!0)]),D(ae,{columns:t.columns,"data-source":t.dataListClone,bordered:t.bordered,pagination:t.pagination,loading:t.loading,scroll:{x:"max-content"}},{headerCell:i(({column:e,record:F})=>[e.showIconHelp?(d(),o("span",ge,[h(s(e.title)+" ",1),D(f(ie))])):g("",!0),e.tooltip?(d(),n(r,{key:1},{title:i(()=>[h(s(e.tooltip),1)]),default:i(()=>[h(" "+s(e.title),1)]),_:2},1024)):g("",!0)]),bodyCell:i(({column:e,text:F,record:a,index:v})=>{var N,U,j,Z,q,z,V,$,H;return[!t.isTableEdit||!e.isEdit?(d(),o("div",ke,[e.render?R(t.$slots,`${e.key}Render`,{key:1,record:a,index:v},void 0,!0):(d(),o("div",Ce,[(e==null?void 0:e.valueType)==="select"?(d(),n(r,{key:0},{title:i(()=>[h(s(f(Q)(a[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),default:i(()=>[b("span",we,s(f(Q)(a[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),_:2},1024)):e!=null&&e.formatPercentage?(d(),n(r,{key:1},{title:i(()=>[h(s(f(P)(a[e.dataIndex],e==null?void 0:e.formatPercentage)),1)]),default:i(()=>[b("span",ue,s(f(P)(a[e.dataIndex],e==null?void 0:e.formatPercentage)),1)]),_:2},1024)):e!=null&&e.formatMoney?(d(),n(r,{key:2},{title:i(()=>[h(s(a[e.dataIndex]&&f(W)(a[e.dataIndex]))+s(a[e.dataIndex]?e.unit:""),1)]),default:i(()=>[b("span",De,s(a[e.dataIndex]&&f(W)(a[e.dataIndex]))+s(a[e.dataIndex]?e.unit:""),1)]),_:2},1024)):e!=null&&e.formatFixed?(d(),n(r,{key:3},{title:i(()=>[h(s(a[e.dataIndex]&&f(T)(a[e.dataIndex]))+s(a[e.dataIndex]?e.unit:""),1)]),default:i(()=>[b("span",Fe,s(a[e.dataIndex]&&f(T)(a[e.dataIndex]))+s(a[e.dataIndex]?e.unit:""),1)]),_:2},1024)):e!=null&&e.formatDecimal?(d(),n(r,{key:4},{title:i(()=>[h(s(a[e.dataIndex]&&f(G)(a[e.dataIndex],e==null?void 0:e.formatDecimal))+s(a[e.dataIndex]?e.unit:""),1)]),default:i(()=>[b("span",Pe,s(a[e.dataIndex]&&f(G)(a[e.dataIndex],e==null?void 0:e.formatDecimal))+s(a[e.dataIndex]?e.unit:""),1)]),_:2},1024)):(d(),n(r,{key:5},{title:i(()=>[h(s(a[e.dataIndex])+s(a[e.dataIndex]?e.unit:""),1)]),default:i(()=>[b("span",Te,s(a[e.dataIndex])+s(a[e.dataIndex]?e.unit:""),1)]),_:2},1024))]))])):(d(),o("div",Ye,[e.valueType==="select"?(d(),n(I,{key:0,options:e.valueEnum,mode:e.valueMode,value:a[e.dataIndex],"onUpdate:value":y=>a[e.dataIndex]=y,placeholder:"请选择",showArrow:"","show-search":"","filter-option":(y,L)=>((L==null?void 0:L.label)??"").toLowerCase().includes(y.toLowerCase()),style:Y({width:"100%",borderColor:t.startCheck&&((N=e==null?void 0:e.rule)!=null&&N.required)&&!a[e.dataIndex]?"red !important":""})},null,8,["options","mode","value","onUpdate:value","filter-option","style"])):e.valueType==="date"?(d(),n(C,{key:1,format:e.dateFormat?e.dateFormat:"YYYY-MM-DD",valueFormat:e.dateFormat?e.dateFormat:"YYYY-MM-DD",picker:w(e.dateFormat),value:a[e.dataIndex],"onUpdate:value":y=>a[e.dataIndex]=y,"disabled-date":y=>c(y,e.minDate,e.maxDate),placeholder:"请选择",allowClear:"",style:Y({width:"100%",borderColor:t.startCheck&&((U=e==null?void 0:e.rule)!=null&&U.required)&&!a[e.dataIndex]?"red !important":""})},null,8,["format","valueFormat","picker","value","onUpdate:value","disabled-date","style"])):e.dataType=="number"?(d(),n(O,{key:2,controls:!1,type:e.dataType,value:a[e.dataIndex],"onUpdate:value":y=>a[e.dataIndex]=y,style:Y([{width:"100%"},{borderColor:t.startCheck&&((j=e==null?void 0:e.rule)!=null&&j.required)&&(!a[e.dataIndex]&&((Z=e==null?void 0:e.rule)!=null&&Z.noAllowZero)||(q=e==null?void 0:e.rule)!=null&&q.noAllowZero&&a[e.dataIndex]==="0"||!a[e.dataIndex]&&!((z=e==null?void 0:e.rule)!=null&&z.noAllowZero)&&a[e.dataIndex]!="0")?"red !important":""}]),placeholder:"请输入",precision:e.precision,min:e.min,max:e.max,class:re((V=e==null?void 0:e.rule)!=null&&V.isSwitchBg&&a.id?"switchBg":""),formatter:e.formatter,parser:e.parser,"addon-after":e==null?void 0:e.unit},null,8,["type","value","onUpdate:value","precision","min","max","class","formatter","parser","addon-after","style"])):(d(),n(A,{key:3,class:"edit_table_input",style:Y({borderColor:t.startCheck&&(($=e==null?void 0:e.rule)!=null&&$.required)&&(!a[e.dataIndex]||(H=e==null?void 0:e.rule)!=null&&H.noAllowZero&&a[e.dataIndex]==="0")?"red !important":""}),placeholder:"请输入",value:a[e.dataIndex],"onUpdate:value":y=>a[e.dataIndex]=y},null,8,["style","value","onUpdate:value"]))]))]}),summary:i(()=>[D(ee,{fixed:""},{default:i(()=>{var e,F;return[t.isTableEdit&&t.showAdd?(d(),n(B,{key:0,class:"add_summary_row"},{default:i(()=>[(d(!0),o(u,null,E(t.columns,(a,v)=>(d(),n(S,{key:v,align:"left"},{default:i(()=>[v==0?(d(),o("div",Oe,[D(f(be),{style:{fontSize:"14px",color:"#29CCA0"}}),b("div",{onClick:k},"添加")])):g("",!0)]),_:2},1024))),128))]),_:1})):g("",!0),l.summary&&((e=t.dataListClone)==null?void 0:e.length)>0?(d(),n(B,{key:1},{default:i(()=>[(d(!0),o(u,null,E(t.columns,(a,v)=>(d(),n(S,null,{default:i(()=>[a.summary||v===0?(d(),o(u,{key:0},[h(s(v===0?t.totalName:a.dataIndex!=="operation"?K(a):""),1)],64)):g("",!0)]),_:2},1024))),256))]),_:1})):g("",!0),l.average&&((F=t.dataListClone)==null?void 0:F.length)>0?(d(),n(B,{key:2},{default:i(()=>[(d(!0),o(u,null,E(t.columns,(a,v)=>(d(),n(S,null,{default:i(()=>[a.average||v===0?(d(),o(u,{key:0},[h(s(v===0?t.averageName:a.dataIndex!=="operation"?m(a):""),1)],64)):g("",!0)]),_:2},1024))),256))]),_:1})):g("",!0)]}),_:1})]),_:3},8,["columns","data-source","bordered","pagination","loading"])])}}});const Ee=he(Ae,[["__scopeId","data-v-4e7bfc62"]]);export{Ee as _};
