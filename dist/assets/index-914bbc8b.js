import{d as k,af as E,ao as A,bm as H,a1 as S,a3 as K,c as L,a as s,v as _,g as d,Z as m,u as t,n as M,b as l,e as p,aB as Z,aP as $,y as i,i as h,aj as G,aq as J,ar as Q,t as W,r as I,w as X,em as Y,aA as N,f as B,F as x,x as ee,h as y,ag as te,b6 as se,s as ae,bi as oe,p as ne,j as le,_ as ie}from"./index-db94d997.js";import{E as pe}from"./index-326d414f.js";import{i as re}from"./icon-831229e8.js";const ce=k({name:"ElTimeline",setup(u,{slots:r}){const e=E("timeline");return A("timeline",r),()=>H("ul",{class:[e.b()]},[S(r,"default")])}}),me=K({timestamp:{type:String,default:""},hideTimestamp:Boolean,center:Boolean,placement:{type:String,values:["top","bottom"],default:"bottom"},type:{type:String,values:["primary","success","warning","danger","info"],default:""},color:{type:String,default:""},size:{type:String,values:["normal","large"],default:"normal"},icon:{type:re},hollow:Boolean}),de=k({name:"ElTimelineItem"}),ue=k({...de,props:me,setup(u){const r=u,e=E("timeline-item"),f=L(()=>[e.e("node"),e.em("node",r.size||""),e.em("node",r.type||""),e.is("hollow",r.hollow)]);return(a,T)=>(s(),_("li",{class:m([t(e).b(),{[t(e).e("center")]:a.center}])},[d("div",{class:m(t(e).e("tail"))},null,2),a.$slots.dot?i("v-if",!0):(s(),_("div",{key:0,class:m(t(f)),style:M({backgroundColor:a.color})},[a.icon?(s(),l(t($),{key:0,class:m(t(e).e("icon"))},{default:p(()=>[(s(),l(Z(a.icon)))]),_:1},8,["class"])):i("v-if",!0)],6)),a.$slots.dot?(s(),_("div",{key:1,class:m(t(e).e("dot"))},[S(a.$slots,"dot")],2)):i("v-if",!0),d("div",{class:m(t(e).e("wrapper"))},[!a.hideTimestamp&&a.placement==="top"?(s(),_("div",{key:0,class:m([t(e).e("timestamp"),t(e).is("top")])},h(a.timestamp),3)):i("v-if",!0),d("div",{class:m(t(e).e("content"))},[S(a.$slots,"default")],2),!a.hideTimestamp&&a.placement==="bottom"?(s(),_("div",{key:1,class:m([t(e).e("timestamp"),t(e).is("bottom")])},h(a.timestamp),3)):i("v-if",!0)],2)],2))}});var D=G(ue,[["__file","timeline-item.vue"]]);const _e=J(ce,{TimelineItem:D}),fe=Q(D),ve=u=>(ne("data-v-5ab34d69"),u=u(),le(),u),ye={class:"timeline-title"},he=["title"],ge={key:5},be={class:"desc"},ke=ve(()=>d("span",{class:"label"},"驳回原因：",-1)),we={class:"msg"},Se=k({__name:"index",props:{visible:{type:Boolean,default:!1},rows:{default:{}},title:{default:""}},emits:["update:visible"],setup(u,{emit:r}){const e=u,{visible:f,rows:a,title:T}=W(e),w=I([]),g=I(!1);X(f,v=>{v&&P()});const C=(v,c=!1)=>{const n=v.split("_"),b=n.join("、");return c?b:n.length>2?`${n.slice(0,2).join("、")}等`:b},j=()=>{r("update:visible",!1)},z=()=>{r("update:visible",!1)},P=()=>{let v={processInstanceId:a.value.instanceId};Y(v).then(c=>{console.log("审批流记录res=",c),w.value=c||[],w.value.forEach(n=>{n.stepStatus!==3&&(n.type="primary")})})};return(v,c)=>{const n=pe,b=N("CaretBottom"),V=N("CaretTop"),U=$,F=fe,O=_e,R=oe;return s(),l(R,{visible:t(f),"onUpdate:visible":c[1]||(c[1]=o=>ae(f)?f.value=o:null),title:"审核记录",width:"900px",onCancel:z,onOk:j},{default:p(()=>[d("h3",ye,h(t(T)),1),B(O,{class:"timeline"},{default:p(()=>[(s(!0),_(x,null,ee(t(w),(o,q)=>(s(),l(F,{key:q,timestamp:o.stepTime,type:o.type,hollow:!0},{default:p(()=>[d("span",{title:C(o.stepUserName,!0)},h(C(o.stepUserName))+"（"+h(o.stepName)+"）",9,he),o.stepStatus==0?(s(),l(n,{key:0,type:"primary"},{default:p(()=>[y("发起审批")]),_:1})):i("",!0),o.stepStatus==1?(s(),l(n,{key:1,type:"warning"},{default:p(()=>[y(" 审核中 ")]),_:1})):i("",!0),o.stepStatus==2?(s(),l(n,{key:2,type:"primary"},{default:p(()=>[y(" 审核通过 ")]),_:1})):i("",!0),o.stepStatus==3?(s(),l(n,{key:3,type:"danger"},{default:p(()=>[y(" 审核驳回 ")]),_:1})):i("",!0),o.stepStatus==4?(s(),l(n,{key:4,type:"info"},{default:p(()=>[y("待流转")]),_:1})):i("",!0),o.msg?(s(),_("div",ge,[d("div",{class:"view-btn",onClick:c[0]||(c[0]=Te=>g.value=!t(g))},[y(" 查看驳回意见 "),B(U,null,{default:p(()=>[t(g)?(s(),l(V,{key:1})):(s(),l(b,{key:0}))]),_:1})]),te(d("div",be,[ke,d("span",we,h(o.msg),1)],512),[[se,t(g)]])])):i("",!0)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1},8,["visible"])}}});const Be=ie(Se,[["__scopeId","data-v-5ab34d69"]]);export{Be as _};
