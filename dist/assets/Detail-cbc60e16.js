import{d as te,I as ae,r as i,K as oe,o as ne,D as le,a as C,v as se,g as r,f as d,e as l,u as n,h,b as z,y as M,i as x,z as U,e9 as j,F as ie,au as re,be as de,bf as ce,bg as ue,bh as _e,bn as pe,ay as me,_ as fe}from"./index-db94d997.js";import{r as $,a as ye,b as ve}from"./const-3409a276.js";import{m as v}from"./dealTable-c35ad2da.js";import{_ as ge}from"./index-07f7e8bf.js";import{_ as he}from"./index-39334618.js";const xe={class:"statements_container"},be={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ce={style:{flex:"1","padding-right":"5%"}},we={style:{flex:"1","padding-right":"5%"}},Ne={style:{flex:"1","padding-right":"5%"}},Ie={style:{display:"flex","flex-direction":"row","align-items":"center"}},ze={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},ke={class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},De={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},Te={class:"text-hide"},Fe={class:"text-hide"},Se={class:"text-hide"},Me=te({__name:"Detail",setup(Be){var A;ae();const g=i(((A=history.state)==null?void 0:A.pdata)||{}),B=i(),w=i(!1);i([]);const N=i({}),k=i([]),D=i(0),T=i(1),L=i(4),c=oe({stationCode:"",stationName:""}),f=i(3),q=i([{title:"电站编号",dataIndex:"stationCode",resizable:!0,width:120,fixed:"left",customCell:(e,t,o)=>v(f.value,e,t)},{title:"业主名称",dataIndex:"stationName",resizable:!0,width:120,fixed:"left",customCell:(e,t,o)=>v(f.value,e,t)},{title:"省",dataIndex:"provinceName",resizable:!0,width:100,customCell:(e,t,o)=>v(f.value,e,t)},{title:"市",dataIndex:"cityName",resizable:!0,width:100,customCell:(e,t,o)=>v(f.value,e,t)},{title:"区",dataIndex:"areaName",resizable:!0,width:100,customCell:(e,t,o)=>v(f.value,e,t)},{title:"产权公司",dataIndex:"companyName",resizable:!0,width:120,customCell:(e,t,o)=>v(f.value,e,t)},{title:"装机容量",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0,customCell:(e,t,o)=>v(f.value,e,t)},{title:"指标",key:"targetType",dataIndex:"targetType",resizable:!0,width:120}]),P=i([]),R=i([]),V=i([]);let F="",E="";const H=(e,t,o)=>{var _,p;const s=((p=(_=o==null?void 0:o.triggerNode)==null?void 0:_.props)==null?void 0:p.level)||1;F=["provinceCode","cityCode","areaCode","town","vil"][s-1],E=e},J=e=>new Promise((t,o)=>{re({pid:e||"0"}).then(s=>{let u=K(s);V.value=u,t(!0)}).catch(()=>{o()})}),K=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,K(t.subDistrict))}),e),O=i({}),Q=e=>{I(e)},G=e=>{console.log(e)},W=()=>{B.value.resetFields(),I()},X=(e,t,o)=>{T.value=e.current,L.value=e.pageSize,D.value=e.total;const s={...c,pageNum:e.current,pageSize:e.pageSize};I(s)},I=e=>{var s,u,_,p;let t={};e!=null&&e.cityTree&&F&&(t={[F]:E});const o={...t,stationCode:c.stationCode?c.stationCode:null,stationName:c.stationName?e.stationName:null,pageNum:T.value,pageSize:L.value,companyCode:(s=g.value)==null?void 0:s.companyCode,preMonth:(u=g.value)==null?void 0:u.preMonth,year:(_=g.value)==null?void 0:_.year,actualElectricityType:(p=g.value)==null?void 0:p.targetType};console.log(e,"params====",o,g.value),w.value=!0,ye(o).then(y=>{w.value=!1,N.value=o,console.log("明细res=",y==null?void 0:y.data),Y(y,1)}).catch(y=>{w.value=!1})},Y=(e,t)=>{var o,s;t===1&&(P.value=(o=e==null?void 0:e.result)==null?void 0:o.timeList.map((u,_)=>({title:u,dataIndex:u,formatMoney:!0,resizable:!0,width:120})),R.value=[...q.value,...P.value],k.value=((s=e==null?void 0:e.result)==null?void 0:s.dataList)||[],D.value=(e==null?void 0:e.total)||0)};ne(()=>{var e;J(),(e=g.value)!=null&&e.companyCode&&I()}),le(()=>{console.log("onActivated")});const Z=()=>{console.log("pageParams=",N.value),N.value.yearKey&&ve(N.value).then(e=>{console.log("公司维度导出res:",e)})};return(e,t)=>{const o=de,s=ce,u=ge,_=ue,p=he,y=_e,S=pe,ee=me;return C(),se(ie,null,[r("div",xe,[d(y,{ref_key:"formRef",ref:B,name:"formRef",model:n(c),rules:n(O),onFinish:Q,onFinishFailed:G},{default:l(()=>[r("div",be,[r("div",Ce,[d(s,{label:"电站编号",name:"stationCode"},{default:l(()=>[d(o,{value:n(c).stationCode,"onUpdate:value":t[0]||(t[0]=a=>n(c).stationCode=a),placeholder:"请输入"},null,8,["value"])]),_:1})]),r("div",we,[d(s,{label:"业主名称",name:"stationName"},{default:l(()=>[d(o,{value:n(c).stationName,"onUpdate:value":t[1]||(t[1]=a=>n(c).stationName=a),placeholder:"请输入"},null,8,["value"])]),_:1})]),r("div",Ne,[d(s,{label:"行政区划",name:"cityTree"},{default:l(()=>[d(u,{value:n(c).cityTree,"onUpdate:value":t[2]||(t[2]=a=>n(c).cityTree=a),"show-search":"",style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择","allow-clear":"","tree-data":n(V),"tree-node-filter-prop":"label",onChange:H},null,8,["value","tree-data"])]),_:1})])]),r("div",Ie,[r("div",ze,[d(p,null,{default:l(()=>[d(_,{onClick:W},{default:l(()=>[h("重置")]),_:1}),d(_,{type:"primary","html-type":"submit"},{default:l(()=>[h("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),r("div",ke,[r("div",De,[d(p,null,{default:l(()=>[n(k).length>0?(C(),z(_,{key:0,type:"primary",onClick:Z},{default:l(()=>[h("导出")]),_:1})):M("",!0)]),_:1})]),d(ee,{style:{"margin-bottom":"24px"},columns:n(R),"data-source":n(k),pagination:{current:n(T),total:n(D),showTotal:a=>`共 ${a} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0},scroll:{y:500},loading:n(w),onChange:X},{bodyCell:l(({column:a,text:Le,record:m,index:Pe})=>[a!=null&&a.formatMoney&&!a.render?(C(),z(S,{key:0},{title:l(()=>[h(x(n(U)(m[a.dataIndex])),1)]),default:l(()=>[r("span",Te,x(n(U)(m[a.dataIndex])),1)]),_:2},1024)):a!=null&&a.formatFixed&&!a.render?(C(),z(S,{key:1},{title:l(()=>[h(x(m[a.dataIndex]&&n(j)(m[a.dataIndex])),1)]),default:l(()=>[r("span",Fe,x(m[a.dataIndex]&&n(j)(m[a.dataIndex])),1)]),_:2},1024)):M("",!0),a.key=="targetType"?(C(),z(S,{key:2},{title:l(()=>{var b;return[h(x((b=n($)[m[a.dataIndex]-1])==null?void 0:b.label),1)]}),default:l(()=>{var b;return[r("span",Se,x((b=n($)[m[a.dataIndex]-1])==null?void 0:b.label),1)]}),_:2},1024)):M("",!0)]),_:1},8,["columns","data-source","pagination","loading"])])],64)}}});const Ue=fe(Me,[["__scopeId","data-v-17b0fd41"]]);export{Ue as default};
