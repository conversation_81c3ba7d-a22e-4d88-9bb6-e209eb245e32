import{a2 as o,bP as e}from"./index-db94d997.js";function n(t){return o({url:"/web/costConfirm/v1/page",method:"POST",isTable:!0,data:t})}function s(t){return e({url:"/web/costConfirm/v1/exprot",method:"POST",data:t})}function i(t){return o({url:"/web/costConfirm/v1/confirm",method:"GET",data:t})}function a(t){return o({url:"/web/costConfirmDetail/v1/page",method:"POST",isTable:!0,data:t})}function u(t){return e({url:"/web/costConfirmDetail/v1/exprot",method:"POST",data:t})}function f(t){return o({url:"/web/costPatternConfig/v1/getThisMonthCostPattern",method:"POST",isTable:!0,data:t})}function m(t){return o({url:"/web/costPatternConfig/v1/saveCostPattern",method:"POST",data:t})}export{a,u as b,i as c,f as d,s as e,n as g,m as s};
