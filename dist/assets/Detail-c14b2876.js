import{_ as ye}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as be,I as Ie,r as _,K as xe,c as Q,o as Ce,D as ke,a as p,v as f,g as c,u as a,b as C,e as n,h as k,y as Y,f as i,i as m,F as E,x as L,s as we,eD as Ne,bi as Re,eE as De,eB as Se,q as W,bg as Pe,be as Ee,bf as Le,aw as Ue,ax as qe,bn as Be,bI as Fe,bJ as Te,bY as ze,bh as Ae,p as Me,j as $e,eF as Oe,_ as Ve}from"./index-db94d997.js";import{s as je}from"./search-5ccd1e6d.js";import{E as He}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as <PERSON>}from"./index-07f7e8bf.js";import{_ as Ge}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";const x=w=>(Me("data-v-4bbbda14"),w=w(),$e(),w),Je={class:"areaPrice"},Qe={class:"title"},Ye=x(()=>c("span",null,"基础信息",-1)),We={key:1,class:"f_item"},Xe=x(()=>c("span",{class:"label"},"角色名称：",-1)),Ze={class:"label-value"},et={class:"f_item"},tt=x(()=>c("span",{class:"label"},"角色编码：",-1)),at={class:"label-value"},nt={key:1,class:"f_item"},ot=x(()=>c("span",{class:"label"},"PC权限：",-1)),st={class:"label-value"},lt={key:1,class:"f_item"},it=x(()=>c("span",{class:"label"},"角色状态：",-1)),ct={class:"label-value"},dt={key:1,class:"f_item"},ut=x(()=>c("span",{class:"label"},"角色描述：",-1)),pt={class:"label-value"},rt={class:"light-name"},_t={class:"light-name"},ft={key:0,class:"footer"},mt=x(()=>c("div",{class:"table_title"},"已授权用户",-1)),ht={style:{display:"flex","flex-direction":"row","align-items":"center","justify-content":"space-between"}},vt=["src"],gt=["onClick"],yt=["onClick"],bt=["onClick"],It=["onClick"],xt=be({__name:"Detail",setup(w){var G;const M=Ie(),U=_();_(!1),_(!1),_(!1),_(!1);const N=_(!1),q=_(),B=_(((G=history.state)==null?void 0:G.pdata)||{}),s=xe({menuInfoIds:[]}),v=_(!1),R=_([]),D=_(""),F=_(),T=_();_([]);const X=[{title:"用户编码",dataIndex:"id",search:!1,resizable:!0,width:120},{title:"用户姓名",key:"userName",dataIndex:"userName",search:!1,resizable:!0,render:!0,width:120},{title:"手机号",dataIndex:"mobile",search:!1,resizable:!0,width:120},{title:"归属公司",key:"companyInfoUser",dataIndex:"companyInfoUser",search:!1,resizable:!0,width:120,render:!0},{title:"授权人",dataIndex:"authUser",search:!1,resizable:!0,width:120},{title:"授权时间",dataIndex:"authTime",search:!1,resizable:!0,width:120}],g=_([]),Z=(t,e,l,o)=>{e?g.value.push(t.id):g.value=g.value.filter(d=>d!==t.id)},ee=(t,e,l)=>{const o=l.map(d=>d.id);t?g.value=g.value.concat(o):g.value=g.value.filter(d=>!o.includes(d))},te=Q(()=>({selectedRowKeys:g,onSelect:Z,onSelectAll:ee})),$=()=>{var t;(t=U.value)==null||t.reload()},O=(t,e,l=[])=>(t.forEach(o=>{e.includes(o.id)&&l.push(o.menuName),o!=null&&o.children&&(o==null?void 0:o.children.length)>0&&O(o.children,e,l)}),l);Q(()=>{let t=[];return s.menuInfoIds&&s.menuInfoIds.length>0&&(t=O(R.value,s.menuInfoIds)),t});const ae=()=>{v.value=!0},ne=()=>{v.value=!1,q.value.resetFields()},V=(t,e=[])=>(t.forEach(l=>{l.children&&l.children.length>0?V(l.children,e):e.push(l.id)}),e),oe=()=>{let t=[],e=s.menuInfoIds;for(let l=e.length-1;l>=0;l--){const o=e[l];let d="";const y=(b,A)=>{A.forEach(h=>{h.id===b?d=h:h.children&&h.children.length>0&&y(b,h.children)})};if(y(o,R.value),d!=null&&d.children&&(d==null?void 0:d.children.length)>0){const b=V(d.children,[]);t=t.concat(b),e.splice(l,1)}}s.menuInfoIds=s.menuInfoIds.concat(t)},se=()=>{console.log("保存基础信息"),q.value.validateFields().then(t=>{oe();let e={...t,id:B.value.id,menuInfoIds:s.menuInfoIds};console.log("保存params=",e),N.value=!0,Oe(e).then(l=>{N.value=!1,v.value=!1}).catch(l=>{N.value=!1})})},le=()=>{Re.confirm({title:"确认提示",icon:i(He),content:"确认保存修改后的信息吗？",onOk:se,onCancel(){console.log("Cancel")}})},j=()=>{$()},ie=(t,e)=>new Promise(l=>{let o={rolesId:B.value.id,userName:D.value};o={...o,...t},e&&(e.hasOwnProperty("provinceCodeList"),delete o.provinceCodeList),l(o)}),ce=_([]),de=t=>{ce.value=t||[]},S=_([]),ue=()=>{let t={id:B.value.id};De(t).then(e=>{s.isEdit=e==null?void 0:e.isEdit,s.roleName=e==null?void 0:e.roleName,s.roleCode=e==null?void 0:e.roleCode,s.roleDesc=e==null?void 0:e.roleDesc,s.status=e==null?void 0:e.status;for(let o of e==null?void 0:e.menuInfoList)S.value.push(o.menuName);const l=e==null?void 0:e.menuInfoList.map(o=>o.id);s.menuInfoIds=l||[]})},pe=()=>{Se().then(t=>{console.log("菜单列表res=",t),R.value=t||[]})},z=t=>{const e=t||[];let l=[];for(let o of e){const d={companyName:o.companyName,id:o.companyInfoId};l.push(d)}return l},H=t=>{M.push({path:"/system/company/detail/list",state:{pdata:W.cloneDeep(t)}})},K=t=>{M.push({path:"/system/user/detail",state:{pdata:W.cloneDeep(t)}})};return Ce(()=>{pe(),ue()}),ke(()=>{U.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&$()}),(t,e)=>{const l=Pe,o=Ee,d=Le,y=Ue,b=qe,A=Ke,h=Be,J=Fe,re=Te,_e=ze,fe=Ae,me=Ge,he=ye;return p(),f("div",Je,[c("div",{class:"basic_info",ref_key:"autoRoleBox",ref:F},[c("div",Qe,[Ye,!a(v)&&a(s).isEdit?(p(),C(l,{key:0,type:"primary",onClick:ae},{default:n(()=>[k("编辑")]),_:1})):Y("",!0)]),i(fe,{model:a(s),name:"formRef",ref_key:"formRef",ref:q,"label-col":{style:{width:"100px",color:"red"}},autocomplete:"off"},{default:n(()=>[i(b,{span:24},{default:n(()=>[i(y,{span:12},{default:n(()=>[a(v)?(p(),C(d,{key:0,name:"roleName",label:"角色名称：",required:"",style:{"padding-right":"100px"}},{default:n(()=>[i(o,{value:a(s).roleName,"onUpdate:value":e[0]||(e[0]=r=>a(s).roleName=r),placeholder:"请输入",maxlength:50},null,8,["value"])]),_:1})):(p(),f("div",We,[Xe,c("span",Ze,m(a(s).roleName),1)]))]),_:1}),i(y,{span:12},{default:n(()=>[c("div",et,[tt,c("span",at,m(a(s).roleCode),1)])]),_:1})]),_:1}),i(b,{span:12},{default:n(()=>[i(y,{span:12},{default:n(()=>[a(v)?(p(),C(d,{key:0,name:"menuInfoIds",label:"PC权限",required:"",style:{"padding-right":"100px"}},{default:n(()=>[i(A,{value:a(s).menuInfoIds,"onUpdate:value":e[1]||(e[1]=r=>a(s).menuInfoIds=r),treeDefaultExpandAll:"",style:{width:"100%"},"tree-data":a(R),"tree-checkable":"","allow-clear":"","show-arrow":"",maxTagCount:3,placeholder:"请选择","tree-node-filter-prop":"label","field-names":{children:"children",label:"menuName",value:"id"}},null,8,["value","tree-data"])]),_:1})):(p(),f("div",nt,[ot,c("span",st,[i(h,{class:"tooltip-box",placement:"topRight",getPopupContainer:()=>a(F)},{title:n(()=>[(p(!0),f(E,null,L(a(S),(r,u)=>(p(),f("span",{class:"light-name",key:u},m(r),1))),128))]),default:n(()=>[(p(!0),f(E,null,L(a(S),(r,u)=>(p(),f("span",{class:"light-name",key:u},m(r)+" "+m(a(S).length-1==u?"":"，"),1))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1}),i(y,{span:12},{default:n(()=>[a(v)?(p(),C(d,{key:0,name:"status",label:"角色状态：",required:"",style:{"padding-right":"100px"}},{default:n(()=>[i(re,{value:a(s).status,"onUpdate:value":e[2]||(e[2]=r=>a(s).status=r)},{default:n(()=>[i(J,{value:0},{default:n(()=>[k("启用")]),_:1}),i(J,{value:1},{default:n(()=>[k("禁用")]),_:1})]),_:1},8,["value"])]),_:1})):(p(),f("div",lt,[it,c("span",ct,m(a(s).status===0?"启用":a(s).status===1?"禁用":""),1)]))]),_:1})]),_:1}),i(b,{span:24},{default:n(()=>[i(y,{span:12},{default:n(()=>[a(v)?(p(),C(d,{key:0,name:"roleDesc",label:"角色描述：",required:"",style:{"padding-right":"200px"}},{default:n(()=>[i(_e,{value:a(s).roleDesc,"onUpdate:value":e[3]||(e[3]=r=>a(s).roleDesc=r),"show-count":"",maxlength:200},null,8,["value"])]),_:1})):(p(),f("div",dt,[ut,c("span",pt,[i(h,{class:"tooltip-box",placement:"topRight",getPopupContainer:()=>a(F)},{title:n(()=>[c("span",rt,m(a(s).roleDesc),1)]),default:n(()=>[c("span",_t,m(a(s).roleDesc),1)]),_:1},8,["getPopupContainer"])])]))]),_:1})]),_:1})]),_:1},8,["model"]),a(v)?(p(),f("div",ft,[i(me,null,{default:n(()=>[i(l,{onClick:ne},{default:n(()=>[k("取消")]),_:1}),i(l,{onClick:le,type:"primary",loading:a(N)},{default:n(()=>[k("保存")]),_:1},8,["loading"])]),_:1})])):Y("",!0)],512),c("div",{class:"table_popup_container",ref_key:"areaPriceRef",ref:T},null,512),i(he,{columns:X,ref_key:"actionRef",ref:U,request:a(Ne),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},search:!1,onGetDataSource:de,"before-query-params":ie,rowSelection:a(te)},{tableHeaderLeft:n(()=>[mt,c("div",ht,[i(o,{style:{width:"282px"},value:a(D),"onUpdate:value":e[4]||(e[4]=r=>we(D)?D.value=r:null),placeholder:"请输入",onPressEnter:j},{suffix:n(()=>[c("img",{src:a(je),style:{width:"16px",height:"16px",cursor:"pointer"},onClick:j},null,8,vt)]),_:1},8,["value"])])]),tableHeader:n(()=>[]),companyInfoUserRender:n(({column:r,record:u,index:ve})=>[i(h,{class:"tooltip-box",placement:"topLeft",getPopupContainer:()=>a(T)},{title:n(()=>[(p(!0),f(E,null,L(z(u==null?void 0:u.companyInfoUser),(I,P)=>(p(),f("span",{class:"light-name",key:P,onClick:ge=>H(I)},m(I.companyName),9,gt))),128))]),default:n(()=>[(p(!0),f(E,null,L(z(u==null?void 0:u.companyInfoUser),(I,P)=>(p(),f("span",{class:"light-name",key:P,onClick:ge=>H(I)},m(I.companyName)+" "+m(z(u==null?void 0:u.companyInfoUser).length-1==P?"":"，"),9,yt))),128))]),_:2},1032,["getPopupContainer"])]),userNameRender:n(({column:r,record:u,index:ve})=>[i(h,{class:"tooltip-box",placement:"topLeft",getPopupContainer:()=>a(T)},{title:n(()=>[c("span",{class:"light-name",onClick:I=>K(u)},m(u[r.dataIndex]),9,bt)]),default:n(()=>[c("span",{class:"light-name",onClick:I=>K(u)},m(u[r.dataIndex]),9,It)]),_:2},1032,["getPopupContainer"])]),_:1},8,["request","rowSelection"])])}}});const Ut=Ve(xt,[["__scopeId","data-v-4bbbda14"]]);export{Ut as default};
