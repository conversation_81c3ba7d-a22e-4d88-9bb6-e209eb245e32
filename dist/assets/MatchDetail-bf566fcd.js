import{_ as oe}from"./index-e7bdfdf4.js";import{_ as se}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ne,c9 as le,r as u,bL as ie,K as de,o as ce,ce as re,a as s,v as r,f as p,e as o,u as i,b as m,h as _,y as T,i as f,g as v,z,bc as pe,bd as ue,cf as _e,bg as me,bn as fe,be as ye,ay as he,_ as ve}from"./index-db94d997.js";import{P as ke,X as F,D as L,w as ge}from"./weiZhi-78534cab.js";import{a as Ce,b as xe,u as be}from"./index-bad8f65c.js";import{D as Ie}from"./dayjs-a8e42122.js";import{_ as Te}from"./index-39334618.js";import{_ as we}from"./index-83ca18bc.js";import"./CaretUpOutlined-7e71a64b.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const De={class:"receivableDetail"},Pe={class:"text-hide"},Ee={class:"text-hide"},Se={class:"text-hide"},ze={style:{display:"flex"}},Fe={class:"table_wrap"},Le={key:0},Me={class:"fileTableAction"},Ke=["src"],Re=["src"],Ue=["src"],Ne=["src"],Be=["src"],qe=["src"],Xe=["src"],$e={class:"ellipsis"},Ge={key:1},Oe={key:2},je=ne({__name:"MatchDetail",setup(Ve){var E;const{showModal:M,setFileState:K}=le(),R=u([{key:"产权公司：",dataIndex:"projectName"},{key:"装机容量(kW)：",dataIndex:"capins",formatMoney:!0},{key:"电站数量：",dataIndex:"stationCount"}]);ie();const U=u([]),w=u([]),x=u(!1),k=u(!1);de({projectId:""});const c=u((E=history.state)==null?void 0:E.pdata),N=u([{title:"产权公司",dataIndex:"companyName",width:120,search:!1,resizable:!0},{title:"电站编号",dataIndex:"stationCode",width:120,search:!1,resizable:!0},{title:"业主名称",dataIndex:"stationName",resizable:!0,search:!1,width:100},{title:"收款月份",dataIndex:"monthKey",search:!1,width:120,resizable:!0},{title:"实收电费(元)",key:"fee",dataIndex:"fee",width:120,search:!1,resizable:!0,render:!0,formatMoney:!0},{title:"收款方式",key:"proceedsType",dataIndex:"proceedsType",search:!1,width:120,render:!0,resizable:!0},{title:"操作时间",dataIndex:"updateTime",search:!1,width:120,resizable:!0},{title:"操作",key:"action",search:!1,width:100,resizable:!0,render:!0}]),D=u(),B=()=>{var e;(e=D.value)==null||e.reload()},q=async()=>{var n,l;const e={companyCode:(n=c.value)==null?void 0:n.companyCode,monthKey:(l=c.value)==null?void 0:l.monthKey,delStatus:0};xe({...e}).then(d=>{console.log("导出成功")})},X=()=>{U.value=R.value.map(e=>({...e,value:c.value[e.dataIndex]}))},$=(e,n)=>new Promise(l=>{var b,C;let d={companyCode:(b=c.value)==null?void 0:b.companyCode,monthKey:(C=c.value)==null?void 0:C.monthKey};d={...d,...e},l(d)}),G=e=>{w.value=e||[]},O=e=>{console.log("编辑item=",e),e.isTableEdit=!0,e.feeTem=e.fee,e.proceedsTypeTem=e.proceedsType},j=e=>{e.isTableEdit=!1,e.fee=e.feeTem,e.proceedsType=e.proceedsTypeTem},V=e=>{let n={id:e.id,companyCode:e.companyCode,monthKey:e.monthKey,fee:e.fee,proceedsType:e.proceedsType};x.value=!0,be(n).then(l=>{x.value=!1,pe.info("保存成功"),B()}).catch(l=>{x.value=!1})};ce(()=>{X(),I()});const I=()=>{k.value=!0;let e={businessType:3,relationCode:c.value.id,delStatus:0,companyCode:c.value.companyCode};console.log("params=",e),re(e).then(n=>{console.log("文件列表：",n),k.value=!1,P.value=n}).catch(n=>{console.log("查询失败err:",n),k.value=!1})},Y=u([{title:"序号",dataIndex:"id",width:60},{title:"文档名称",dataIndex:"fileName",ellipsis:!0,width:300},{title:"操作",key:"action",width:80}]);let P=u([]);const H=["PNG","png","JPG","jpg","JPEG","jpeg","gif","GIF","BMP","bmp","WEBP","webp"],g=e=>{if(!e)return"";var n=e.match(/\.([^.]+)$/);return n?n[1].toUpperCase():""},J=()=>{W()},W=()=>{K({title:"上传",importUrl:"/web/oss/file/v1/uploadFile",accept:".doc,.docx,.pdf",upLoadSuccess:d=>{console.log("上传成功",d),I()},downloadText:"上传附件",onClickDownload:()=>{console.log("点击下载")},onClickResultOk:d=>{console.log("导入结果点击关闭调用",d)},visible:!0,isTemplate:!1,fileSize:30,data:{businessType:3,relationCode:c.value.id,companyCode:c.value.companyCode}}),M()},A=async e=>{ue({fileId:e.id,companyCode:c.value.companyCode})},Q=e=>{console.log(e),k.value=!0,_e({fileId:e.id,companyCode:c.value.companyCode}).then(n=>{console.log("删除成功res:",n),I()}).catch(n=>{console.log("删除失败err:",n),k.value=!1})};return(e,n)=>{const l=me,d=Te,b=Ie,C=fe,S=ye,Z=se,ee=we,ae=he,te=oe;return s(),r("div",De,[p(Z,{columns:i(N),ref_key:"actionRef",ref:D,request:i(Ce),"label-col":{style:{width:"125px"}},"wrapper-col":{span:16},search:!1,onGetDataSource:G,"before-query-params":$},{tableHeader:o(()=>[p(d,null,{default:o(()=>[i(w).length>0?(s(),m(l,{key:0,type:"primary",onClick:q},{default:o(()=>[_("导出")]),_:1})):T("",!0)]),_:1})]),monthKeyRender:o(({record:a,column:t,index:y})=>[a.isTableEdit?(s(),m(b,{key:0,value:a[t.dataIndex],"onUpdate:value":h=>a[t.dataIndex]=h,picker:"month","value-format":"YYYY-MM"},null,8,["value","onUpdate:value"])):(s(),m(C,{key:1},{title:o(()=>[_(f(a[t.dataIndex]),1)]),default:o(()=>[v("span",Pe,f(a[t.dataIndex]),1)]),_:2},1024))]),proceedsTypeRender:o(({record:a,column:t,index:y})=>[a.isTableEdit?(s(),m(S,{key:0,value:a[t.dataIndex],"onUpdate:value":h=>a[t.dataIndex]=h},null,8,["value","onUpdate:value"])):(s(),m(C,{key:1},{title:o(()=>[_(f(a[t.dataIndex]),1)]),default:o(()=>[v("span",Ee,f(a[t.dataIndex]),1)]),_:2},1024))]),feeRender:o(({record:a,column:t,index:y})=>[a.isTableEdit?(s(),m(S,{key:0,value:a[t.dataIndex],"onUpdate:value":h=>a[t.dataIndex]=h},null,8,["value","onUpdate:value"])):(s(),m(C,{key:1},{title:o(()=>[_(f(a[t.dataIndex]?i(z)(a[t.dataIndex]):""),1)]),default:o(()=>[v("span",Se,f(a[t.dataIndex]?i(z)(a[t.dataIndex]):""),1)]),_:2},1024))]),actionRender:o(({record:a,index:t})=>[a.isTableEdit?(s(),m(d,{key:1},{default:o(()=>[p(l,{size:"small",type:"link",onClick:y=>j(a)},{default:o(()=>[_("取消")]),_:2},1032,["onClick"]),p(l,{size:"small",type:"link",onClick:y=>V(a),loading:i(x)},{default:o(()=>[_("保存")]),_:2},1032,["onClick","loading"])]),_:2},1024)):(s(),m(l,{key:0,size:"small",type:"link",onClick:()=>O(a,t)},{default:o(()=>[_(" 编辑 ")]),_:2},1032,["onClick"]))]),_:1},8,["columns","request"]),p(te,{title:"附件",itemKey:"4",defaultShow:!0,isHideSwitch:!0,style:{"margin-top":"24px"}},{btnRender:o(()=>[v("div",ze,[p(l,{type:"primary",onClick:J},{default:o(()=>[_("上传")]),_:1})])]),default:o(()=>[v("div",Fe,[p(ae,{columns:i(Y),"data-source":i(P),pagination:!1,loading:i(k)},{bodyCell:o(({column:a,text:t,record:y,index:h})=>[a.dataIndex=="fileName"?(s(),r("div",Le,[v("div",Me,[H.includes(g(t))?(s(),r("img",{key:0,src:t},null,8,Ke)):g(t)=="PDF"?(s(),r("img",{key:1,src:i(ke)},null,8,Re)):g(t)=="XLS"?(s(),r("img",{key:2,src:i(F)},null,8,Ue)):g(t)=="XLSX"?(s(),r("img",{key:3,src:i(F)},null,8,Ne)):g(t)=="DOC"?(s(),r("img",{key:4,src:i(L)},null,8,Be)):g(t)=="DOCX"?(s(),r("img",{key:5,src:i(L)},null,8,qe)):(s(),r("img",{key:6,src:i(ge)},null,8,Xe)),v("div",$e,f(t),1)])])):a.dataIndex=="id"?(s(),r("div",Ge,f(h+1),1)):T("",!0),a.key=="action"?(s(),r("div",Oe,[p(d,null,{default:o(()=>[p(l,{size:"small",type:"link",onClick:()=>A(y)},{default:o(()=>[_(" 下载 ")]),_:2},1032,["onClick"]),p(ee,{title:"确认删除?",onConfirm:Ye=>Q(y)},{default:o(()=>[p(l,{size:"small",type:"link"},{default:o(()=>[_(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)])):T("",!0)]),_:1},8,["columns","data-source","loading"])])]),_:1})])}}});const ia=ve(je,[["__scopeId","data-v-83a45cc9"]]);export{ia as default};
