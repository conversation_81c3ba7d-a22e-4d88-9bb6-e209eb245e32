import{d as Se,r as C,I as Te,K as me,c as L,o as Pe,D as Ue,a as r,v as p,g as u,u as t,b,e as l,f as i,h as N,y as fe,i as _,F as k,x as I,q as ge,bi as Q,eu as Ee,bc as Y,ev as $e,ew as Me,ex as je,es as qe,er as Be,bg as Ve,be as Fe,bf as He,aw as We,ax as ze,av as Je,bn as Ke,bI as Xe,bJ as Ze,bh as Ge,p as Qe,j as Ye,_ as ea}from"./index-db94d997.js";import{g as aa}from"./index-4185a861.js";import{g as ta,a as he}from"./index-96df45ba.js";import{_ as D}from"./index-07f7e8bf.js";import{E as ee}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as sa}from"./index-39334618.js";const v=S=>(Qe("data-v-3e0886bd"),S=S(),Ye(),S),oa={class:"basic_info"},na={class:"title"},la=v(()=>u("span",null,"基础信息",-1)),ra={key:1,class:"f_item"},ia=v(()=>u("span",{class:"label"},"用户姓名：",-1)),da={class:"label-value"},pa={key:1,class:"f_item"},ua=v(()=>u("span",{class:"label"},"手机号：",-1)),ca={class:"label-value"},_a={style:{display:"flex"}},ma={key:1,class:"f_item"},fa=v(()=>u("span",{class:"label"},"账号密码：",-1)),ga={class:"label-value"},ha={key:1,class:"f_item"},ya=v(()=>u("span",{class:"label"},"归属公司：",-1)),va={class:"label-value"},Ca=["onClick"],ba=["onClick"],ka={key:1,class:"f_item"},Ia=v(()=>u("span",{class:"label"},"身份证号：",-1)),wa=v(()=>u("span",{class:"label-value"},"1*****************",-1)),Aa=[Ia,wa],Na={key:1,class:"f_item"},xa=v(()=>u("span",{class:"label"},"角色：",-1)),Oa={class:"label-value"},Ra=["onClick"],La=["onClick"],Da={key:1,class:"f_item"},Sa=v(()=>u("span",{class:"label"},"状态：",-1)),Ta={class:"label-value"},Pa={style:{display:"flex"}},Ua={key:1,class:"f_item"},Ea=v(()=>u("span",{class:"label"},"数据权限：",-1)),$a={class:"label-value"},Ma={style:{display:"flex"}},ja={key:1,class:"f_item"},qa=v(()=>u("span",{class:"label"},"产权区域数据权限：",-1)),Ba={class:"label-value"},Va={style:{display:"flex"}},Fa={key:1,class:"f_item"},Ha=v(()=>u("span",{class:"label"},"运维公司数据权限：",-1)),Wa={class:"label-value"},za={style:{display:"flex"}},Ja={key:1,class:"f_item"},Ka=v(()=>u("span",{class:"label"},"运维区域数据权限：",-1)),Xa={class:"label-value"},Za={key:0,class:"footer"},Ga=Se({__name:"Detail",setup(S){var ue;const A=C(),$=C(),M=Te(),ye=C(((ue=history.state)==null?void 0:ue.pdata)||{}),e=me({}),f=C(!1);C([]);const ae=C([]),T=C([]),te={userName:[{required:!0,message:"用户姓名不能为空",trigger:"change"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9_·]{1,50}$/,message:"请输入正确的用户名（支持中文、·、英文、_）",trigger:"change"}],mobile:[{required:!0,message:"手机号不能为空",trigger:"change"},{pattern:/^1[1-9]\d{9}$/,message:"请输入正确的手机号",trigger:"change"}],companyIds:[{required:!0,message:"归属公司不能为空",trigger:"change"}],rolesIds:[{required:!0,message:"角色不能为空",trigger:"change"}]},j=(s,a,n=[])=>(s.forEach(d=>{a.includes(d.id)&&n.push(d),d!=null&&d.children&&(d==null?void 0:d.children.length)>0&&j(d.children,a,n)}),n),q=(s,a,n=[])=>(s.forEach(d=>{a.includes(d.code)&&n.push(d),d!=null&&d.children&&(d==null?void 0:d.children.length)>0&&q(d.children,a,n)}),n),B=L(()=>{let s=[];return e.userAuthcompanyIds&&e.userAuthcompanyIds.length>0&&(s=j(O.value,e.userAuthcompanyIds)),s}),V=L(()=>{let s=[];return e.userOperationCompanyIds&&e.userOperationCompanyIds.length>0&&(s=j(P.value,e.userOperationCompanyIds)),s}),F=L(()=>{let s=[];return e.ownAreas&&e.ownAreas&&e.ownAreas.length>0&&(s=q(J.value,e.ownAreas)),s}),H=L(()=>{let s=[];return e.operationAreas&&e.operationAreas&&e.operationAreas.length>0&&(s=q(K.value,e.operationAreas)),s}),W=s=>{const a=s||[];let n=[];for(let d of a){const m={companyName:d.companyName,id:d.id};n.push(m)}return n},se=s=>{M.push({path:"/system/company/detail/list",state:{pdata:ge.cloneDeep(s)}})},oe=s=>{M.push({path:"/system/role/detail",state:{pdata:ge.cloneDeep(s)}})},ne=()=>{M.replace({path:"/system/permission/list"})},z=s=>{const a=s||[];let n=[];for(let d of a){const m={roleName:d.roleName,id:d.id};n.push(m)}return n},ve=L(()=>e.id!=localStorage.getItem("id")),Ce=()=>{f.value=!0,pe(e.rolesIds)},be=()=>{Q.confirm({title:"确认提示",icon:i(ee),content:"确认解锁该用户吗？",async onOk(){await Ee({mobile:e.mobile}),Y.success("解锁成功")},onCancel(){console.log("Cancel")}})},ke=()=>{f.value=!1,$.value.resetFields()},Ie=()=>{$.value.validate().then(s=>{s&&Q.confirm({title:"确认提示",icon:i(ee),content:"确认保存修改后的信息吗？",async onOk(){const a={id:e.id,userName:e.userName,mobile:e.mobile,companyIds:e.companyIds,rolesIds:e.rolesIds,status:e.status,userOperationCompanyIds:e.userOperationCompanyIds,operationAreas:e.operationAreas.join(",")};R.value||(a.userAuthcompanyIds=e.userAuthcompanyIds,a.ownAreas=e.ownAreas.join(",")),console.log(11,a),e.id==localStorage.getItem("id")&&(a.identityCard=e.identityCard),await $e(a)=="成功"&&(Y.success("编辑成功"),f.value=!1,re(!0))},onCancel(){console.log("Cancel")}})})},we=()=>{Q.confirm({title:"确认重置密码",icon:i(ee),content:"重置后，将会发送新的密码信息给到客户",onOk(){Me({userId:e.id}).then(s=>{Y.success("重置成功")})},onCancel(){console.log("Cancel")}})};let le=[];const re=(s=!1)=>{let a={id:ye.value.id};je(a).then(n=>{if(e.isEdit=n.isEdit,e.id=n.id,e.userName=n.userName,e.mobile=n.mobile,e.status=n.status==0?0:n.status==1?1:0,e.identityCard=e.id==localStorage.getItem("id")?n.identityCard:"1*****************",e.id==localStorage.getItem("id")){const g=[{required:!1,message:"",trigger:"change"},{pattern:/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,message:"请输入正确的身份证号",trigger:"change"}];te.identityCard=g}e.password="*********",e.userCompanyInfoList=n.userCompanyInfoList,e.rolesList=n.rolesList,e.userAuthcompanys=n.userAuthcompanys;let d=[];if(n.userCompanyInfoList)for(let g of n.userCompanyInfoList)d.push(g.id);e.companyIds=d;let m=[];if(n.rolesList)for(let g of n.rolesList)m.push(g.id);e.rolesIds=m;let h=[];if(n.userAuthcompanys)for(let g of n.userAuthcompanys)h.push(g.id);e.userAuthcompanyIds=h,le=h;let y=[];if(n.userOperationCompanys)for(let g of n.userOperationCompanys)y.push(g.id);e.userOperationCompanyIds=y,e.ownAreas=n.ownAreas.split(","),e.operationAreas=n.operationAreas.split(","),s&&(ie(1),ie(2)),n.isSystemRoles&&(R.value=!0,e.userAuthcompanyIds=[])})},Ae=()=>{aa().then(s=>{let a=s||[];ae.value=a})},Ne=()=>{qe().then(s=>{let a=s||[];T.value=a})},xe=(s,a)=>a.companyName.toLowerCase().indexOf(s.toLowerCase())>=0,Oe=(s,a)=>a.roleName.toLowerCase().indexOf(s.toLowerCase())>=0,O=C([]),P=C([]),ie=async s=>{const a=await ta({ownOrOperation:s});s==1?O.value=a:P.value=a,U(1),U(2),de(O.value)},de=s=>{s.forEach(a=>{le.includes(a.id)&&(a.disabled=!0),a!=null&&a.children&&(a==null?void 0:a.children.length)>0&&de(a.children)})},J=C([]),K=C([]),U=async(s,a=!1)=>{if(s==1){a&&(e.ownAreas=[]);const n=[];X(O.value,e.userAuthcompanyIds,n);const d={ownOrOperation:s,companyCodes:n.join(",")},m=await he(d);J.value=m}else if(s==2){a&&(e.operationAreas=[]);const n=[];X(P.value,e.userOperationCompanyIds,n);const d={ownOrOperation:s,companyCodes:n.join(",")},m=await he(d);K.value=m}},X=(s,a,n)=>{s.forEach(d=>{for(let m in a)a[m]==d.id&&n.push(d.code);if(d!=null&&d.children&&(d==null?void 0:d.children.length)>0)X(d.children,a,n);else return n})},R=C(!1),pe=s=>{let a=0;for(let n of s)n==w.safeRoles?a=1:n==w.systemRoles&&(a=2);a==0?(Z(0),R.value=!1):a==1?(Z(w.safeRoles),e.rolesIds=[],e.rolesIds=[w.safeRoles],R.value=!1):a==2&&(Z(w.systemRoles),e.rolesIds=[],e.rolesIds=[w.systemRoles],R.value=!0,e.userAuthcompanyIds=[])},Z=s=>{if(s>0)for(let a of T.value)a.id!=s&&(a.disabled=!0);else for(let a of T.value)a.disabled=!1},w=me({safeRoles:"",systemRoles:""}),Re=async()=>{const s=await Be();w.safeRoles=s.safeRoles,w.systemRoles=s.systemRoles};return Pe(()=>{re(!0),Ae(),Ne(),Re()}),Ue(()=>{}),(s,a)=>{const n=Ve,d=sa,m=Fe,h=He,y=We,g=ze,ce=Je,x=Ke,_e=Xe,Le=Ze,E=D,De=Ge;return r(),p("div",{class:"areaPrice",ref_key:"userManageDetailRef",ref:A},[u("div",oa,[u("div",na,[la,!t(f)&&t(e).isEdit?(r(),b(d,{key:0},{default:l(()=>[i(n,{type:"primary",onClick:Ce},{default:l(()=>[N("编辑")]),_:1}),i(n,{type:"primary",onClick:be},{default:l(()=>[N("解锁")]),_:1})]),_:1})):fe("",!0)]),i(De,{model:t(e),name:"formRef",ref_key:"formRef",ref:$,rules:te,"label-col":{style:{width:"130px",color:"red"}},autocomplete:"off"},{default:l(()=>[i(g,{span:24},{default:l(()=>[i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"userName",label:"用户姓名",style:{"padding-right":"100px"}},{default:l(()=>[i(m,{value:t(e).userName,"onUpdate:value":a[0]||(a[0]=o=>t(e).userName=o),placeholder:"请输入",maxlength:50,disabled:""},null,8,["value"])]),_:1})):(r(),p("div",ra,[ia,u("span",da,_(t(e).userName),1)]))]),_:1}),i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"mobile",label:"手机号",style:{"padding-right":"100px"}},{default:l(()=>[i(m,{value:t(e).mobile,"onUpdate:value":a[1]||(a[1]=o=>t(e).mobile=o),placeholder:"请输入"},null,8,["value"])]),_:1})):(r(),p("div",pa,[ua,u("span",ca,_(t(e).mobile),1)]))]),_:1})]),_:1}),i(g,{span:24},{default:l(()=>[i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"password",label:"账号密码",style:{"padding-right":"100px"}},{default:l(()=>[u("div",_a,[i(m,{value:t(e).password,"onUpdate:value":a[2]||(a[2]=o=>t(e).password=o),disabled:""},null,8,["value"]),i(n,{type:"primary",style:{"margin-left":"10px"},onClick:we},{default:l(()=>[N("重置密码")]),_:1})])]),_:1})):(r(),p("div",ma,[fa,u("span",ga,_(t(e).password),1)]))]),_:1}),i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"companyIds",label:"归属公司",style:{"padding-right":"100px"}},{default:l(()=>[i(ce,{placeholder:"请选择",value:t(e).companyIds,"onUpdate:value":a[3]||(a[3]=o=>t(e).companyIds=o),allowClear:"","show-search":"",mode:"multiple",options:t(ae),fieldNames:{label:"companyName",value:"id"},"filter-option":xe,maxTagCount:3},null,8,["value","options"])]),_:1})):(r(),p("div",ha,[ya,u("span",va,[i(x,{class:"tooltip-box",placement:"topRight",getPopupContainer:o=>t(A)},{title:l(()=>[(r(!0),p(k,null,I(W(t(e).userCompanyInfoList),(o,c)=>(r(),p("span",{class:"light-name",key:c,onClick:G=>se(o)},_(o.companyName),9,Ca))),128))]),default:l(()=>[(r(!0),p(k,null,I(W(t(e).userCompanyInfoList),(o,c)=>(r(),p("span",{class:"light-name",key:c,onClick:G=>se(o)},_(o.companyName)+" "+_(W(t(e).userCompanyInfoList).length-1==c?"":"，"),9,ba))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1})]),_:1}),i(g,{span:24},{default:l(()=>[i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"identityCard",label:"身份证号",style:{"padding-right":"100px"}},{default:l(()=>[i(m,{value:t(e).identityCard,"onUpdate:value":a[4]||(a[4]=o=>t(e).identityCard=o),disabled:t(ve),placeholder:"请输入"},null,8,["value","disabled"])]),_:1})):(r(),p("div",ka,Aa))]),_:1}),i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"rolesIds",label:"角色",style:{"padding-right":"100px"}},{default:l(()=>[i(ce,{placeholder:"请选择",value:t(e).rolesIds,"onUpdate:value":a[5]||(a[5]=o=>t(e).rolesIds=o),allowClear:"","show-search":"",mode:"multiple",options:t(T),fieldNames:{label:"roleName",value:"id"},"filter-option":Oe,maxTagCount:3,onChange:pe},null,8,["value","options"])]),_:1})):(r(),p("div",Na,[xa,u("span",Oa,[i(x,{class:"tooltip-box",placement:"topRight",getPopupContainer:o=>t(A)},{title:l(()=>[(r(!0),p(k,null,I(z(t(e).rolesList),(o,c)=>(r(),p("span",{class:"light-name",key:c,onClick:G=>oe(o)},_(o.roleName),9,Ra))),128))]),default:l(()=>[(r(!0),p(k,null,I(z(t(e).rolesList),(o,c)=>(r(),p("span",{class:"light-name",key:c,onClick:G=>oe(o)},_(o.roleName)+" "+_(z(t(e).rolesList).length-1==c?"":"，"),9,La))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1})]),_:1}),i(g,{span:12},{default:l(()=>[i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"status",label:"状态",style:{"padding-right":"100px"}},{default:l(()=>[i(Le,{value:t(e).status,"onUpdate:value":a[6]||(a[6]=o=>t(e).status=o)},{default:l(()=>[i(_e,{value:0},{default:l(()=>[N("启用")]),_:1}),i(_e,{value:1},{default:l(()=>[N("禁用")]),_:1})]),_:1},8,["value"])]),_:1})):(r(),p("div",Da,[Sa,u("span",Ta,_(t(e).status===0?"启用":t(e).status===1?"禁用":""),1)]))]),_:1})]),_:1}),i(g,{span:12},{default:l(()=>[i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"userAuthcompanyIds",label:"产权公司数据权限",style:{"padding-right":"100px"}},{default:l(()=>[u("div",Pa,[i(E,{value:t(e).userAuthcompanyIds,"onUpdate:value":a[7]||(a[7]=o=>t(e).userAuthcompanyIds=o),style:{width:"100%"},"tree-data":t(O),fieldNames:{label:"name",value:"id"},showCheckedStrategy:t(D).SHOW_ALL,"tree-checkable":"","show-arrow":"",maxTagCount:3,placeholder:"请选择","tree-node-filter-prop":"name",onChange:a[8]||(a[8]=o=>U(1,!0))},null,8,["value","tree-data","showCheckedStrategy"])])]),_:1})):(r(),p("div",Ua,[Ea,u("span",$a,[i(x,{class:"tooltip-box",placement:"topRight",getPopupContainer:o=>t(A)},{title:l(()=>[(r(!0),p(k,null,I(t(B),(o,c)=>(r(),p("span",{class:"light-name",key:c,onClick:ne},_(o.name),1))),128))]),default:l(()=>[(r(!0),p(k,null,I(t(B),(o,c)=>(r(),p("span",{class:"light-name",key:c,onClick:ne},_(o.name)+" "+_(t(B).length-1==c?"":"，"),1))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1}),i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"ownAreas",label:"产权区域数据权限",style:{"padding-right":"100px"}},{default:l(()=>[u("div",Ma,[i(E,{value:t(e).ownAreas,"onUpdate:value":a[9]||(a[9]=o=>t(e).ownAreas=o),style:{width:"100%"},"tree-data":t(J),fieldNames:{label:"name",value:"code"},showCheckedStrategy:t(D).SHOW_ALL,"tree-checkable":"","show-arrow":"",maxTagCount:3,placeholder:"请选择","tree-node-filter-prop":"name"},null,8,["value","tree-data","showCheckedStrategy"])])]),_:1})):(r(),p("div",ja,[qa,u("span",Ba,[i(x,{class:"tooltip-box",placement:"topRight",getPopupContainer:o=>t(A)},{title:l(()=>[(r(!0),p(k,null,I(t(F),(o,c)=>(r(),p("span",{class:"light-name",key:c},_(o.name),1))),128))]),default:l(()=>[(r(!0),p(k,null,I(t(F),(o,c)=>(r(),p("span",{class:"light-name",key:c},_(o.name)+" "+_(t(F).length-1==c?"":"，"),1))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1})]),_:1}),i(g,{span:12},{default:l(()=>[i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"userOperationCompanyIds",label:"运维公司数据权限",style:{"padding-right":"100px"}},{default:l(()=>[u("div",Va,[i(E,{value:t(e).userOperationCompanyIds,"onUpdate:value":a[10]||(a[10]=o=>t(e).userOperationCompanyIds=o),style:{width:"100%"},"tree-data":t(P),fieldNames:{label:"name",value:"id"},showCheckedStrategy:t(D).SHOW_ALL,"tree-checkable":"","show-arrow":"",maxTagCount:3,placeholder:"请选择","tree-node-filter-prop":"name",onChange:a[11]||(a[11]=o=>U(2,!0))},null,8,["value","tree-data","showCheckedStrategy"])])]),_:1})):(r(),p("div",Fa,[Ha,u("span",Wa,[i(x,{class:"tooltip-box",placement:"topRight",getPopupContainer:o=>t(A)},{title:l(()=>[(r(!0),p(k,null,I(t(V),(o,c)=>(r(),p("span",{class:"light-name",key:c},_(o.name),1))),128))]),default:l(()=>[(r(!0),p(k,null,I(t(V),(o,c)=>(r(),p("span",{class:"light-name",key:c},_(o.name)+" "+_(t(V).length-1==c?"":"，"),1))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1}),i(y,{span:12},{default:l(()=>[t(f)?(r(),b(h,{key:0,name:"operationAreas",label:"运维区域数据权限",style:{"padding-right":"100px"}},{default:l(()=>[u("div",za,[i(E,{value:t(e).operationAreas,"onUpdate:value":a[12]||(a[12]=o=>t(e).operationAreas=o),style:{width:"100%"},"tree-data":t(K),fieldNames:{label:"name",value:"code"},showCheckedStrategy:t(D).SHOW_ALL,"tree-checkable":"","show-arrow":"",maxTagCount:3,placeholder:"请选择","tree-node-filter-prop":"name"},null,8,["value","tree-data","showCheckedStrategy"])])]),_:1})):(r(),p("div",Ja,[Ka,u("span",Xa,[i(x,{class:"tooltip-box",placement:"topRight",getPopupContainer:o=>t(A)},{title:l(()=>[(r(!0),p(k,null,I(t(H),(o,c)=>(r(),p("span",{class:"light-name",key:c},_(o.name),1))),128))]),default:l(()=>[(r(!0),p(k,null,I(t(H),(o,c)=>(r(),p("span",{class:"light-name",key:c},_(o.name)+" "+_(t(H).length-1==c?"":"，"),1))),128))]),_:1},8,["getPopupContainer"])])]))]),_:1})]),_:1})]),_:1},8,["model"]),t(f)?(r(),p("div",Za,[i(d,null,{default:l(()=>[i(n,{onClick:ke},{default:l(()=>[N("取消")]),_:1}),i(n,{onClick:Ie,type:"primary"},{default:l(()=>[N("保存")]),_:1})]),_:1})])):fe("",!0)])],512)}}});const ot=ea(Ga,[["__scopeId","data-v-3e0886bd"]]);export{ot as default};
