import{_ as j}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as S,d as H,r as u,o as G,at as Q,a as d,v,f as n,e as o,g as F,h as f,y as g,b as I,u as i,s as J,F as K,k as D,bc as w,bd as W,bg as X,av as Z,bf as ee,bh as te,bi as ae,p as oe,j as se,_ as ne}from"./index-db94d997.js";import{D as le}from"./dayjs-a8e42122.js";import{_ as re}from"./index-39334618.js";import{_ as ie}from"./index-83ca18bc.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";function ue(r){return S({url:"/web/companyBusinessAnalysisReport/v1/page",method:"POST",isTable:!0,data:r})}function ce(r){return S({url:"/web/companyBusinessAnalysisReport/v1/save",method:"POST",data:r})}function de(r){return S({url:"/web/companyBusinessAnalysisReport/v1/delete",method:"GET",data:r})}const pe=r=>(oe("data-v-60e8ebbe"),r=r(),se(),r),me=pe(()=>F("div",{class:"pre_title"},"经营分析报表",-1)),_e={key:0},fe={key:1,style:{color:"#29cca0"}},ye={key:2,style:{color:"#f44"}},ve=H({__name:"index",setup(r){const b=u(),p=u(!1),y=u(!1),m=u({companyCode:void 0,reportTime:""}),x=u([]),q=e=>{const a=e.year(),s=e.month(),l=D().year(),C=D().month();let c=!1;return a>l||a===l&&s>=C?c=!0:c=!1,c},R=()=>{p.value=!1,b.value.resetFields()},A=()=>{b.value.validate().then(e=>{let a={...e,reportTimeType:2};y.value=!0,ce(a).then(s=>{w.info("保存成功"),y.value=!1,R(),h()}).catch(s=>{y.value=!1})})},L=()=>{p.value=!0},N=(e,a)=>new Promise(s=>{let l={};l={...l,...e,delStatus:0},s(l)}),P=u([{title:"报表名称",dataIndex:"reportName",search:!1,width:140},{title:"报表状态",dataIndex:"reportStatus",key:"reportStatus",search:!1,render:!0,width:100},{title:"创建时间",dataIndex:"createTime",search:!1,width:100},{title:"操作",key:"action",width:100,fixed:"right",resizable:!1,render:!0}]),T=u(),z=()=>{h()},h=()=>{var e;(e=T.value)==null||e.reload()},M=e=>{if(e.reportStatus===0||e.reportStatus===1||e.reportStatus===3){w.warning("文件未生成");return}const a={fileId:e.reportFileId};W(a)},k=u(!1),O=e=>{const a={id:e.id};k.value=!0,de(a).then(s=>{k.value=!1,w.success("删除成功"),h()}).catch(s=>{k.value=!1})};G(()=>{V()});const V=()=>{Q({}).then(e=>{let s=(e||[]).map(l=>({label:l.companyName,value:l.companyCode}));x.value=s})};return(e,a)=>{const s=X,l=re,C=ie,c=j,Y=Z,B=ee,$=le,E=te,U=ae;return d(),v(K,null,[n(c,{columns:i(P),ref_key:"actionRef",ref:T,"label-col":{span:6},"wrapper-col":{span:18},"before-query-params":N,"default-query":!0,search:!1,request:i(ue)},{tableHeaderLeft:o(()=>[me]),tableHeader:o(()=>[F("div",null,[n(l,null,{default:o(()=>[n(s,{onClick:z},{default:o(()=>[f("刷新列表")]),_:1}),n(s,{type:"primary",onClick:L},{default:o(()=>[f("生成报表")]),_:1})]),_:1})])]),reportStatusRender:o(({record:t})=>[t.reportStatus==0||t.reportStatus==1?(d(),v("div",_e,"生成中")):g("",!0),t.reportStatus==2?(d(),v("div",fe,"生成成功")):g("",!0),t.reportStatus==3?(d(),v("div",ye,"生成失败")):g("",!0)]),actionRender:o(({record:t})=>[n(l,null,{default:o(()=>[n(s,{size:"small",type:"link",disabled:t.reportStatus!=2,onClick:_=>M(t)},{default:o(()=>[f(" 下载 ")]),_:2},1032,["disabled","onClick"]),t.reportStatus===2||t.reportStatus===3?(d(),I(C,{key:0,title:"确认删除?",onConfirm:_=>O(t)},{default:o(()=>[n(s,{size:"small",type:"link"},{default:o(()=>[f(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])):(d(),I(s,{key:1,size:"small",type:"link",disabled:""},{default:o(()=>[f(" 删除 ")]),_:1}))]),_:2},1024)]),_:1},8,["columns","request"]),n(U,{visible:i(p),"onUpdate:visible":a[2]||(a[2]=t=>J(p)?p.value=t:null),title:"生成报表","confirm-loading":i(y),onOk:A,onCancel:R},{default:o(()=>[n(E,{ref_key:"formRef",ref:b,model:i(m),"label-col":{style:{width:"80px",marginRight:"5px"}},autocomplete:"off"},{default:o(()=>[n(B,{label:"项目公司",name:"companyCode",rules:[{required:!0,message:"请选择"}]},{default:o(()=>[n(Y,{options:i(x),value:i(m).companyCode,"onUpdate:value":a[0]||(a[0]=t=>i(m).companyCode=t),placeholder:"请选择",allowClear:"",showArrow:"","show-search":"","filter-option":(t,_)=>((_==null?void 0:_.label)??"").toLowerCase().includes(t.toLowerCase()),style:{width:"100%"}},null,8,["options","value","filter-option"])]),_:1}),n(B,{label:"月份",name:"reportTime",rules:[{required:!0,message:"请选择"}]},{default:o(()=>[n($,{value:i(m).reportTime,"onUpdate:value":a[1]||(a[1]=t=>i(m).reportTime=t),picker:"month","value-format":"YYYY-MM",placeholder:"请选择",style:{width:"100%"},"disabled-date":q},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const Te=ne(ve,[["__scopeId","data-v-60e8ebbe"]]);export{Te as default};
