import{_ as k}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as E,r as l,I as A,o as q,D as M,at as R,a as y,v as B,f as d,e as r,u as f,b as F,h as b,y as V,i as g,g as Y,bg as j,bn as O,_ as G}from"./index-db94d997.js";import{a as x,b as H}from"./index-62e1f9f0.js";import{_ as J}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const K={class:"areaPrice"},Q={class:"text-hide"},$=E({__name:"SumAnalysis",setup(U){const c=l(),w=["provinceCodeList","cityCodeList","areaCodeList"],C=l([]),m=l([]);A();const S=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:3},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:4},{title:"账期选择",key:"status",dataIndex:"paymentMonths",valueType:"select",valueEnum:[{label:"1个月",value:1},{label:"2个月",value:2},{label:"3个月",value:3}],resizable:!0,hideInTable:!0,width:100,order:1,rules:[{required:!0,message:"请选择"}]},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:120,valueEnum:m,render:!0,resizable:!0,order:5},{title:"省",dataIndex:"provinceName",width:120,resizable:!0,search:!1},{title:"市",dataIndex:"cityName",width:120,resizable:!0,search:!1},{title:"区",dataIndex:"areaName",width:120,resizable:!0,search:!1},{title:"收款月份",dataIndex:"collectionPeriod",valueType:"dateRange",dateFormat:"YYYY-MM",width:120,resizable:!0,search:!0,order:2,rules:[{required:!0,message:"请选择"}]},{title:"累计应收电费",dataIndex:"electricityReceivableSum",width:120,resizable:!0,search:!1},{title:"累计实收电费",dataIndex:"actualElectricitySum",width:120,search:!1,resizable:!0},{title:"累计待回款金额",dataIndex:"returnMoneySum",width:120,search:!1,resizable:!0}],I=()=>{c.value.reset()};q(()=>{D(),console.log("getAccumulationList=",x)}),M(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&z()});const D=()=>{R({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));m.value=o})},N=()=>{var a;let e=(a=c.value)==null?void 0:a.getInitialFormStateNew();H(e)},p=l([]),P=e=>{console.log(e),p.value=e||[],console.log("dataSource=",e)},z=()=>{var e;(e=c.value)==null||e.reload()},L=e=>{const a=new Map;return e.forEach(o=>{const t=w[o.level-1];if(a.has(t))a.get(t).push(o.value);else{let n=[];n.push(o.value),a.set(t,n)}}),Object.fromEntries(a)},u=(e,a,o)=>!e||!a?[]:(e.forEach(t=>{a.find(i=>t.value===i)&&o.push(t),t.children&&t.children.length>0&&u(t.children,a,o)}),o),T=(e,a)=>new Promise(o=>{const t=e!=null&&e.collectionPeriod&&(e==null?void 0:e.collectionPeriod.length)>0?e==null?void 0:e.collectionPeriod[0]:"",n=e!=null&&e.collectionPeriod&&(e==null?void 0:e.collectionPeriod.length)>0?e==null?void 0:e.collectionPeriod[1]:"";let i={delStatus:0,noJoin:!0,startData:t,endData:n};const _=u(C.value,e==null?void 0:e.cityTree,[]);let s=L(_);e==null||delete e.collectionPeriod;const h={...i,...e,...s};o(h)});return(e,a)=>{const o=j,t=J,n=O,i=k;return y(),B("div",K,[d(i,{columns:S,ref_key:"actionRef",ref:c,request:f(x),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:P,"before-query-params":T,"default-query":!1,onCancel:I},{tableHeader:r(()=>[d(t,null,{default:r(()=>[f(p).length>0?(y(),F(o,{key:0,type:"primary",onClick:N},{default:r(()=>[b("导出")]),_:1})):V("",!0)]),_:1})]),companyCodeListRender:r(({column:_,record:s,index:h})=>[d(n,null,{title:r(()=>[b(g(s.companyName),1)]),default:r(()=>[Y("span",Q,g(s.companyName),1)]),_:2},1024)]),_:1},8,["request"])])}}});const ce=G($,[["__scopeId","data-v-ca49e7a0"]]);export{ce as default};
