import{_ as J}from"./index-4c4cf647.js";import{b as K,h as Q}from"./index-23cd6eea.js";import{a as W,u as X,b as Z}from"./index-d38c0948.js";import{d as $,I as j,bL as ee,r as f,o as ae,a as v,v as Y,g as y,u as a,b as N,e as n,h,y as P,f as o,F as oe,bc as le,bg as te,be as ne,bf as se,aw as de,ax as ue,bY as ie,bh as me,p as pe,j as re,_ as _e}from"./index-db94d997.js";import{D as ce}from"./dayjs-a8e42122.js";import{_ as fe}from"./index-4a280682.js";import{_ as ve}from"./index-39334618.js";import"./index-e7bdfdf4.js";import"./CaretUpOutlined-7e71a64b.js";import"./weiZhi-78534cab.js";import"./customParseFormat-ed0c33ac.js";const he=k=>(pe("data-v-a01262e2"),k=k(),re(),k),ye={class:"card",style:{"margin-bottom":"24px"}},ke={class:"card_title",style:{"margin-bottom":"24px"}},ge=he(()=>y("div",{class:"left_title"},[y("span",null,"基础信息")],-1)),Ne={key:0,class:"card_show_btn"},be={class:"footer"},Ce=$({__name:"Detail",setup(k){var x,R;const b=j(),s=ee(),C=f(),w=f(),g=f(!1),d=f(((x=s==null?void 0:s.query)==null?void 0:x.type)||""),T=f(((R=s==null?void 0:s.query)==null?void 0:R.showEdit)||"1"),e=f({});ae(()=>{var i;(i=s==null?void 0:s.query)!=null&&i.id&&M()});const B=()=>{e.value.stationCode?(L(),S()):(e.value.stationName="",e.value.companyCode="",e.value.companyName="",e.value.householdName="",e.value.householdBankNum="",e.value.householdBankName="",e.value.householdOpenBank="")},L=()=>{let i={stationUniqueId:e.value.stationCode};K(i).then(l=>{var r;let t=l!=null&&l.records&&(l==null?void 0:l.records.length)>0?l.records[0]:{};e.value.stationName=t==null?void 0:t.stationName,e.value.companyCode=t==null?void 0:t.ownerCompanyCode,e.value.companyName=t==null?void 0:t.ownerCompanyName,e.value.householdName=((r=t==null?void 0:t.householdInfo)==null?void 0:r.nameHoh)||(t==null?void 0:t.stationName)})},S=()=>{let i={stationCode:e.value.stationCode};Q(i).then(l=>{let t=l&&l.length>0?l[0]:{};e.value.householdBankNum=t==null?void 0:t.bankNo,e.value.householdBankName=t==null?void 0:t.accountName,e.value.householdOpenBank=t==null?void 0:t.bankDeposit})},M=()=>{var l;let i={id:(l=s==null?void 0:s.query)==null?void 0:l.id};W(i).then(t=>{e.value=t||{}})},O=()=>{d.value="1"},q=()=>{var i,l;d.value==="1"&&((i=s==null?void 0:s.query)==null?void 0:i.type)!=="1"?d.value=(l=s==null?void 0:s.query)==null?void 0:l.type:b.go(-1)},V=()=>{C.value.validateFields().then(i=>{var p,_;const l=(p=s==null?void 0:s.query)!=null&&p.id?X:Z,t=((_=w.value)==null?void 0:_.getFileList())||[];console.log("fileList=",t);const r=t.map(c=>c.id);let m={...i,fileIdList:r,companyName:e.value.companyName,companyCode:e.value.companyCode,stationName:e.value.stationName,id:e.value.id};console.log("params=",m),g.value=!0,l(m).then(c=>{console.log("新建误转账信息res=",c),g.value=!1,le.info("保存成功"),b.go(-1)}).catch(c=>{g.value=!1})})};return(i,l)=>{var U,I,E,F;const t=te,r=ne,m=se,p=de,_=ue,c=ce,A=fe,z=ie,H=me,G=J,D=ve;return v(),Y(oe,null,[y("div",ye,[y("div",ke,[ge,a(T)==="1"?(v(),Y("div",Ne,[a(d)!=="0"&&a(d)!=="1"?(v(),N(t,{key:0,type:"primary",onClick:O},{default:n(()=>[h("编辑")]),_:1})):P("",!0)])):P("",!0)]),o(H,{model:a(e),name:"formRef",ref_key:"formRef",ref:C,autocomplete:"off","label-col":{style:{width:"180px",paddingRight:"8px"}}},{default:n(()=>[o(_,{span:24},{default:n(()=>[o(p,{span:12},{default:n(()=>[o(m,{label:"电站编号",name:"stationCode",required:""},{default:n(()=>[o(r,{placeholder:"请输入",value:a(e).stationCode,"onUpdate:value":l[0]||(l[0]=u=>a(e).stationCode=u),style:{width:"60%"},disabled:a(d)!=="0"&&a(d)!=="1",onBlur:B,onPressEnter:B},null,8,["value","disabled"])]),_:1})]),_:1}),o(p,{span:12},{default:n(()=>[o(m,{label:"产权公司",name:"companyName",required:""},{default:n(()=>[o(r,{placeholder:"请输入",value:a(e).companyName,"onUpdate:value":l[1]||(l[1]=u=>a(e).companyName=u),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),o(_,{span:24},{default:n(()=>[o(p,{span:12},{default:n(()=>[o(m,{label:"业主名称",name:"householdName",required:""},{default:n(()=>[o(r,{placeholder:"请输入",value:a(e).householdName,"onUpdate:value":l[2]||(l[2]=u=>a(e).householdName=u),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1}),o(p,{span:12},{default:n(()=>[o(m,{label:"业主卡号",name:"householdBankNum",required:""},{default:n(()=>[o(r,{placeholder:"请输入",value:a(e).householdBankNum,"onUpdate:value":l[3]||(l[3]=u=>a(e).householdBankNum=u),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),o(_,{span:24},{default:n(()=>[o(p,{span:12},{default:n(()=>[o(m,{label:"账户名",name:"householdBankName",required:""},{default:n(()=>[o(r,{placeholder:"请输入",value:a(e).householdBankName,"onUpdate:value":l[4]||(l[4]=u=>a(e).householdBankName=u),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1}),o(p,{span:12},{default:n(()=>[o(m,{label:"开户行",name:"householdOpenBank",required:""},{default:n(()=>[o(r,{placeholder:"请输入",value:a(e).householdOpenBank,"onUpdate:value":l[5]||(l[5]=u=>a(e).householdOpenBank=u),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),o(_,{span:24},{default:n(()=>[o(p,{span:12},{default:n(()=>[o(m,{label:"误转账发生时间",name:"errorPaymentTime",required:""},{default:n(()=>[o(c,{placeholder:"请选择",value:a(e).errorPaymentTime,"onUpdate:value":l[6]||(l[6]=u=>a(e).errorPaymentTime=u),controls:!1,"value-format":"YYYY-MM-DD",style:{width:"60%"},disabled:a(d)!=="0"&&a(d)!=="1"},null,8,["value","disabled"])]),_:1})]),_:1}),o(p,{span:12},{default:n(()=>[o(m,{label:"误转账金额",name:"errorFee",required:""},{default:n(()=>[o(A,{placeholder:"请输入",value:a(e).errorFee,"onUpdate:value":l[7]||(l[7]=u=>a(e).errorFee=u),controls:!1,style:{width:"60%"},precision:2,disabled:a(d)!=="0"&&a(d)!=="1"},{addonAfter:n(()=>[h("元")]),_:1},8,["value","disabled"])]),_:1})]),_:1})]),_:1}),o(_,{span:24},{default:n(()=>[o(p,{span:12},{default:n(()=>[o(m,{label:"金额归集到项目公司期间",name:"moneyCollectionTime",required:""},{default:n(()=>[o(c,{placeholder:"请选择",value:a(e).moneyCollectionTime,"onUpdate:value":l[8]||(l[8]=u=>a(e).moneyCollectionTime=u),controls:!1,"value-format":"YYYY-MM",picker:"month",style:{width:"60%"},disabled:a(d)!=="0"&&a(d)!=="1"},null,8,["value","disabled"])]),_:1})]),_:1})]),_:1}),o(_,{span:24},{default:n(()=>[o(p,{span:22},{default:n(()=>[o(m,{label:"错误转账事由",name:"reason",required:""},{default:n(()=>[o(z,{value:a(e).reason,"onUpdate:value":l[9]||(l[9]=u=>a(e).reason=u),placeholder:"请输入","auto-size":{minRows:2,maxRows:5},maxlength:25,showCount:"",disabled:a(d)!=="0"&&a(d)!=="1"},null,8,["value","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),o(G,{ref_key:"uploadRef",ref:w,businessType:22,relationCode:(I=(U=a(s))==null?void 0:U.query)==null?void 0:I.id,companyCode:(F=(E=a(s))==null?void 0:E.query)==null?void 0:F.companyCode,isEdit:a(d)==="0"||a(d)==="1"},null,8,["relationCode","companyCode","isEdit"]),y("div",be,[a(d)==="0"||a(d)==="1"?(v(),N(D,{key:0},{default:n(()=>[o(t,{onClick:q},{default:n(()=>[h("取消")]),_:1}),o(t,{type:"primary",onClick:V,loading:a(g)},{default:n(()=>[h("提交")]),_:1},8,["loading"])]),_:1})):(v(),N(D,{key:1},{default:n(()=>[o(t,{onClick:q},{default:n(()=>[h("返回")]),_:1})]),_:1}))])],64)}}});const Pe=_e(Ce,[["__scopeId","data-v-a01262e2"]]);export{Pe as default};
