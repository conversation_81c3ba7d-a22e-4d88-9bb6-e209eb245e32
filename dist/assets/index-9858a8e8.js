import{_ as ue}from"./index-914bbc8b.js";import{_ as me}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as pe,I as _e,r,K as fe,bL as he,o as ve,D as be,at as ge,a as c,v as m,g as u,f as l,e as t,u as o,b as z,h,y as S,i as p,S as V,s as W,F as ye,V as xe,bW as Ie,bc as we,q as ke,bg as Ce,bn as De,av as Me,bf as Fe,aw as ze,ax as Se,bh as Te,bi as Re}from"./index-db94d997.js";import{g as Ee,e as Ye}from"./index-ccc1a7be.js";import{b as Le,c as Ne}from"./index-5fcafee1.js";import{m as qe}from"./dictLocal-9822709a.js";import{_ as Be}from"./index-39334618.js";import"./index-326d414f.js";import"./icon-831229e8.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Pe={class:"areaPrice"},$e={class:"text-hide"},Ke={key:0,class:"status_tag"},Oe={class:"tag_one"},Ue={key:1,class:"status_tag"},Ve={class:"tag_two"},We={key:0,class:"status_tag"},Ae={class:"tag_two"},Ge={key:1,class:"status_tag"},He={class:"tag_one"},Je={key:2,class:"status_tag"},Qe={class:"tag_three"},je={key:3,class:"status_tag"},Xe=u("span",{class:"tag_two"},"待提交",-1),Ze=[Xe],fa=pe({__name:"index",setup(ea){const T=_e(),y=r(),R=r([]),_=r(!1),x=r(!1),v=fe({}),I=r(),E=r([]),C=r([]),Y=r({}),w=r(!1),L=r([]),D=r([{label:"审批中",value:1},{label:"审批通过",value:2},{label:"审批驳回",value:3}]),N=r([{label:"是",value:1},{label:"否",value:0}]),M=r([{label:"已确认",value:1},{label:"未确认",value:0}]),A=r([{label:"待审核",value:0},{label:"审核中",value:1},{label:"审核驳回",value:3},{label:"审核通过",value:2}]),G=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:160,valueEnum:R,fixed:"left",render:!0},{title:"月份",dataIndex:"monthKey",valueType:"date",dateFormat:"YYYY-MM",resizable:!0,fixed:"left",width:120},{title:"开始时间",dataIndex:"startTime",valueType:"date",dateFormat:"YYYY-MM-DD",resizable:!0,search:!1,width:120},{title:"截止时间",dataIndex:"endTime",valueType:"date",dateFormat:"YYYY-MM-DD",resizable:!0,search:!1,width:120},{title:"本月电量(kWh)",dataIndex:"monthEq",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"本月租金",dataIndex:"monthRent",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"是否扣除分享收益",dataIndex:"shareDeductIncomeFlag",valueType:"select",valueEnum:N,width:150,resizable:!0,search:!1},{title:"本月电费收入(含税)",dataIndex:"taxEqFeeMonth",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"本月电费收入(剔租金)",dataIndex:"eliminateEqFeeMonth",resizable:!0,search:!1,formatMoney:!0,width:160},{title:"月度运维费",dataIndex:"monthOperationFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"是否确认运维收入",dataIndex:"operationIncomeFlag",valueType:"select",valueEnum:N,resizable:!0,search:!1,width:150},{title:"本月收入(运维部分)",dataIndex:"monthOperationIncome",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"运维收入税金",dataIndex:"operationIncomeTaxes",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月收入(电费部分)",dataIndex:"monthIncomeEqFee",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"电费收入税金",dataIndex:"incomeEqFeeTaxes",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月收入总额",dataIndex:"monthIncomeSum",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"确认状态",key:"confirmFlag",dataIndex:"confirmFlag",valueType:"select",valueEnum:M,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"审核状态",key:"approveStatus",dataIndex:"approveStatus",valueType:"select",valueEnum:A,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:160}],H=he();ve(()=>{J(),X()}),be(()=>{y.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&B()});const J=()=>{ge({}).then(e=>{console.log("产权公司res=",e);let i=(e||[]).map(d=>({label:d.companyName,value:d.companyCode}));R.value=i})},Q=()=>{var a;let e=(a=y.value)==null?void 0:a.getInitialFormStateNew();Ye(e)},j=()=>{T.push({path:"/financeManage/incomeManage/station/incomeConfirm/config"})},X=()=>{Le().then(e=>{E.value=e==null?void 0:e.map(a=>({label:a.roleName,value:a.id}))})},Z=e=>{Ne({roleId:e}).then(a=>{C.value=a==null?void 0:a.map(i=>({label:i.userName,value:i.userId}))})},ee=e=>{console.log(`selected ${e}`),Z(e)},ae=e=>{L.value=e||{},xe(()=>{w.value=!0})},te=e=>{var a;_.value=!0,Y.value=e,C.value=[],(a=I.value)==null||a.resetFields()},ne=()=>{var e;_.value=!1,(e=I.value)==null||e.resetFields()},se=()=>{console.log("收入确认-提交审核"),I.value.validateFields().then(e=>{let a=Y.value.id;const i="businessKeyIncomeConfirm",d=qe[0].value;let f={assigneeId:e.assigneeId,relationId:a,businessKey:i,processKey:d};x.value=!0,Ie(f).then(b=>{_.value=!1,x.value=!1,we.info("保存成功"),B()}).catch(b=>{x.value=!1})})},oe=e=>{T.push({path:"/financeManage/incomeManage/station/incomeConfirm/info",state:{pdata:ke.cloneDeep(e)}})},q=r([]),le=e=>{q.value=e||[]},B=()=>{var e;(e=y.value)==null||e.reload()},ie=(e,a)=>{var f;let i={};const d=(f=H.query)==null?void 0:f.relationId;return d&&(i.id=d),new Promise(b=>{const k={...i,...e,noJoin:!0,delStatus:0};b(k)})};return(e,a)=>{const i=Ce,d=Be,f=De,b=me,k=Me,P=Fe,$=ze,K=Se,re=Te,de=Re,ce=ue;return c(),m(ye,null,[u("div",Pe,[l(b,{columns:G,ref_key:"actionRef",ref:y,request:o(Ee),"label-col":{style:{width:"80px"}},"wrapper-col":{span:16},onGetDataSource:le,"before-query-params":ie},{tableHeader:t(()=>[l(d,null,{default:t(()=>[o(q).length>0?(c(),z(i,{key:0,onClick:Q},{default:t(()=>[h("导出")]),_:1})):S("",!0),l(i,{type:"primary",onClick:j},{default:t(()=>[h("模式配置")]),_:1})]),_:1})]),companyCodeListRender:t(({column:n,record:s,index:F})=>[l(f,null,{title:t(()=>[h(p(s.companyName),1)]),default:t(()=>[u("span",$e,p(s.companyName),1)]),_:2},1024)]),confirmFlagRender:t(({column:n,record:s,index:F})=>[s[n.dataIndex]===1?(c(),m("span",Ke,[u("span",Oe,p(o(V)(s[n.dataIndex],o(M))),1)])):S("",!0),s[n.dataIndex]===0?(c(),m("span",Ue,[u("span",Ve,p(o(V)(s[n.dataIndex],o(M))),1)])):S("",!0)]),approveStatusRender:t(({column:n,record:s,index:F})=>{var g,O,U;return[s[n.dataIndex]=="1"?(c(),m("span",We,[u("span",Ae,p((g=o(D)[s[n.dataIndex]-1])==null?void 0:g.label),1)])):s[n.dataIndex]=="2"?(c(),m("span",Ge,[u("span",He,p((O=o(D)[s[n.dataIndex]-1])==null?void 0:O.label),1)])):s[n.dataIndex]=="3"?(c(),m("span",Je,[u("span",Qe,p((U=o(D)[s[n.dataIndex]-1])==null?void 0:U.label),1)])):(c(),m("span",je,Ze))]}),actionRender:t(({column:n,record:s,index:F})=>[l(d,null,{default:t(()=>[s.approveStatus===1?(c(),z(i,{key:0,type:"link",size:"small",onClick:g=>ae(s)},{default:t(()=>[h("查看审批单 ")]),_:2},1032,["onClick"])):(c(),z(i,{key:1,type:"link",size:"small",onClick:g=>te(s),disabled:s.approveStatus===2},{default:t(()=>[h("提交审核 ")]),_:2},1032,["onClick","disabled"])),l(i,{type:"link",size:"small",onClick:g=>oe(s)},{default:t(()=>[h("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])]),l(de,{visible:o(_),"onUpdate:visible":a[2]||(a[2]=n=>W(_)?_.value=n:null),title:"提交审批","confirm-loading":o(x),okText:"提交审批",onOk:se,onCancel:ne},{default:t(()=>[l(re,{model:o(v),ref_key:"formRef2",ref:I,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:t(()=>[l(K,{span:24},{default:t(()=>[l($,{span:24},{default:t(()=>[l(P,{label:"审批角色",name:"aaa"},{default:t(()=>[l(k,{value:o(v).aaa,"onUpdate:value":a[0]||(a[0]=n=>o(v).aaa=n),options:o(E),placeholder:"请选择",style:{width:"100%"},onChange:ee},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),l(K,{span:24},{default:t(()=>[l($,{span:24},{default:t(()=>[l(P,{label:"审批人员",name:"assigneeId"},{default:t(()=>[l(k,{value:o(v).assigneeId,"onUpdate:value":a[1]||(a[1]=n=>o(v).assigneeId=n),options:o(C),placeholder:"请选择"},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"]),l(ce,{visible:o(w),"onUpdate:visible":a[3]||(a[3]=n=>W(w)?w.value=n:null),rows:o(L),title:"电站收入确认审批"},null,8,["visible","rows"])],64)}}});export{fa as default};
