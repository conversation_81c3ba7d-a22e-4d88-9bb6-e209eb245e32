import{a2 as t,bP as s}from"./index-db94d997.js";function a(e){return t({url:"/web/assetSell/v1/page",method:"POST",isTable:!0,data:e})}function r(e){return t({url:"/web/assetSell/v1/save",method:"POST",data:e})}function u(e){return t({url:"/web/assetSell/v1/update",method:"POST",data:e})}function n(e){return t({url:"/web/assetSell/v1/getStationInfo",method:"POST",data:e})}function o(e){return t({url:"/web/assetSell/v1/refresh",method:"POST",data:e})}function S(e){return t({url:"/web/assetSell/v1/updateFlag",method:"POST",data:e})}function d(e){return s({url:"/web/assetSell/v1/export",method:"POST",data:e})}export{n as a,o as b,r as c,u as d,d as e,a as g,S as u};
