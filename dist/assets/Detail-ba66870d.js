import{_ as k}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as C,I,r as c,bL as N,o as T,D as q,a as u,v as S,g as z,f as l,e as o,u as p,b as M,h as _,y as P,at as B,bg as R,_ as F}from"./index-db94d997.js";import{a as L,e as V}from"./index-705af6a9.js";import{_ as E}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Q={class:"areaPrice"},W={class:"areaPrice_table"},$=C({__name:"Detail",setup(A){const d=I(),s=c(),f=c([]),r=N(),y=[{title:"产权公司",dataIndex:"companyName",width:120,resizable:!0},{title:"装机容量(kW)",dataIndex:"capacity",width:120,formatMoney:!0,resizable:!0},{title:"本月预测发电量(万kWh)",dataIndex:"power",width:120,formatMoney:!0,resizable:!0},{title:"预测时间",dataIndex:"createTime",resizable:!0,width:100},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",resizable:!1,width:60}],h=e=>{d.push({path:"/financeManage/electricQuantity/monthForecast/detail/info",query:{id:e==null?void 0:e.id,companyCode:e==null?void 0:e.companyCode,createTime:e==null?void 0:e.createTime}})},b=()=>{var e;(e=s.value)==null||e.reload()},x=()=>{var n;let e=(n=s.value)==null?void 0:n.getInitialFormStateNew();V(e).then(a=>{console.log("导出成功")})},g=(e,n)=>new Promise(a=>{var i;let t={pid:(i=r==null?void 0:r.query)==null?void 0:i.id};t={...t,...e},a(t)}),m=c([]),v=e=>{m.value=e||[]},w=()=>{B({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));f.value=a})};return T(()=>{w()}),q(()=>{s.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&b()}),(e,n)=>{const a=R,t=E,i=k;return u(),S("div",Q,[z("div",W,[l(i,{search:!1,columns:y,ref_key:"actionRef",ref:s,request:p(L),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:v,"before-query-params":g},{tableHeader:o(()=>[l(t,null,{default:o(()=>[p(m).length>0?(u(),M(a,{key:0,type:"primary",onClick:x},{default:o(()=>[_("下载")]),_:1})):P("",!0)]),_:1})]),actionRender:o(({column:G,record:D,index:H})=>[l(t,null,{default:o(()=>[l(a,{type:"link",size:"small",onClick:O=>h(D)},{default:o(()=>[_("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])])}}});const ae=F($,[["__scopeId","data-v-c82ec7b0"]]);export{ae as default};
