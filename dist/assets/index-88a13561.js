import{_ as G}from"./index-6d6caf4c.js";import{d as H,r as d,K as Q,I as X,o as Y,D as ee,a as I,v as ae,g,f as o,e as s,u as n,h as f,b as S,y as U,F as te,q as oe,bc as ne,be as se,bf as re,av as le,bU as pe,bV as ie,bI as de,bJ as ce,bh as me,bi as ue,p as ye,j as _e,_ as fe}from"./index-db94d997.js";import{g as ge,a as he,b as Ce}from"./index-4185a861.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";import"./index-39334618.js";const A=m=>(ye("data-v-6e1cb86c"),m=m(),_e(),m),be={class:"areaPrice"},Ie=A(()=>g("div",{style:{width:"100%",display:"flex","justify-content":"space-between"}},[g("span",{class:"header_title"},"公司列表")],-1)),ve=A(()=>g("span",{style:{width:"58px","word-wrap":"break-word","white-space":"pre-line","line-height":"14px"}},"统一社会信用代码",-1)),Te=H({__name:"index",setup(m){const h=d(),q=["provinceCodeList","cityCodeList","areaCodeList"],K=d([]),v=d([]),T=d([]),w=d([{label:"表内",value:1},{label:"表外",value:2},{label:"受托",value:3}]),u=d(!1),t=Q({companyName:"",ucCompanyCode:"",creditCode:"",parentId:null,parentOperationId:null,status:0,isTitleCompany:0,isOperationCompany:0,companyType:[],companyAssetType:void 0}),L=e=>{e.includes(1)?t.isTitleCompany=1:t.isTitleCompany=0,e.includes(2)?t.isOperationCompany=1:t.isOperationCompany=0},C=d(),P=X(),R=[{title:"公司名称",key:"companyName",dataIndex:"companyName",width:120},{title:"公司编码",lengthLabel:!0,key:"ucCompanyCode",dataIndex:"ucCompanyCode",width:120,rightWidth:150},{title:"统一社会信用代码",lengthLabel:!0,key:"creditCode",dataIndex:"creditCode",width:120,rightWidth:100},{title:"上级公司",key:"parentComanyName",dataIndex:"parentComanyName",goPageUrlType1:{goUrl:"/system/company/detail",fieldValue:"id",fieldValueReplace:"parentId"},width:120,rightWidth:160},{title:"公司资产类型",dataIndex:"companyAssetType",valueType:"select",search:!1,valueEnum:w,width:120},{title:"状态",dataIndex:"status",valueType:"select",valueEnum:[{label:"启用",value:0},{label:"禁用",value:1}],width:120},{title:"创建人",key:"createUser",dataIndex:"createUser",width:120,rightWidth:160},{title:"最近修改时间",dataIndex:"updateTime",search:!1,resizable:!0,width:100,rightWidth:130}],V={companyName:[{required:!0,message:"公司名称不能为空",trigger:"change"},{pattern:/^[\u4e00-\u9fa5a-zA-Z\s\p{P}\p{S}]+$/u,message:"请输入正确的公司名称（支持中文、英文、特殊符号）",trigger:"change"}],ucCompanyCode:[{required:!0,message:"公司编码不能为空",trigger:"change"}],creditCode:[{required:!0,message:"统一社会信用代码不能为空",trigger:"change"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9\s\p{P}\p{S}]+$/u,message:"请输入正确的统一社会信用代码",trigger:"change"}],status:[{required:!0,message:"",trigger:"change"}],companyType:[{required:!0,message:"请选择公司类型",trigger:"change"}],parentId:[{required:!0,message:"请选择上级权限公司",trigger:"change"}],parentOperationId:[{required:!0,message:"请选择上级运维公司",trigger:"change"}]},E=e=>{P.push({path:"/system/company/detail/list",state:{pdata:oe.cloneDeep(e)}})},j=e=>{u.value=!0},F=()=>{C.value.validate().then(async e=>{if(e){const a={companyName:t.companyName,ucCompanyCode:t.ucCompanyCode,creditCode:t.creditCode,parentId:t.parentId,parentOperationId:t.parentOperationId,status:t.status,isTitleCompany:t.isTitleCompany,isOperationCompany:t.isOperationCompany,companyAssetType:t.companyAssetType};await Ce(a)=="成功"&&(ne.success("新建成功"),x())}})},x=()=>{C.value.resetFields(),u.value=!1,k()};Y(()=>{N(1),N(2)}),ee(()=>{h.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&k()});const N=e=>{ge({ownOrOperation:e}).then(a=>{let l=a||[];e==1&&(v.value=l),e==2&&(T.value=l)})},O=(e,a)=>a.companyName.toLowerCase().indexOf(e.toLowerCase())>=0,W=d([]),B=e=>{W.value=e||[],console.log("dataSource=",e)},k=()=>{var e;(e=h.value)==null||e.reload()},z=e=>{const a=new Map;return e.forEach(l=>{const p=q[l.level-1];if(a.has(p))a.get(p).push(l.value);else{let i=[];i.push(l.value),a.set(p,i)}}),Object.fromEntries(a)},D=(e,a,l)=>!e||!a?[]:(e.forEach(p=>{a.find(c=>p.value===c)&&l.push(p),p.children&&p.children.length>0&&D(p.children,a,l)}),l),M=(e,a)=>new Promise(l=>{const p=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[0]:"",i=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[1]:"";let c={delStatus:0,noJoin:!0,startTime:p,endTime:i};const y=D(K.value,e==null?void 0:e.cityTree,[]);let b=z(y);e==null||delete e.yKey;const _={...c,...e,...b};l(_)});return(e,a)=>{const l=G,p=se,i=re,c=le,y=pe,b=ie,_=de,$=ce,J=me,Z=ue;return I(),ae(te,null,[g("div",be,[o(l,{columns:R,ref_key:"actionRef",ref:h,request:n(he),onGetDataSource:B,"before-query-params":M,isAdd:!0,isDelete:!1,isSee:!0,addText:"新建公司",onAdd:j,onSee:E},{tableHeader:s(()=>[Ie]),_:1},8,["request"])]),o(Z,{visible:u.value,"onUpdate:visible":a[8]||(a[8]=r=>u.value=r),title:"新建公司",onOk:F,onCancel:x,width:"600px"},{default:s(()=>[o(J,{model:n(t),rules:V,name:"formRef",ref_key:"formRef",ref:C,"label-col":{style:{width:"120px"}},autocomplete:"off"},{default:s(()=>[o(i,{name:"companyName",label:"公司名称"},{default:s(()=>[o(p,{value:n(t).companyName,"onUpdate:value":a[0]||(a[0]=r=>n(t).companyName=r),placeholder:"请输入"},null,8,["value"])]),_:1}),o(i,{name:"ucCompanyCode",label:"公司编码"},{default:s(()=>[o(p,{value:n(t).ucCompanyCode,"onUpdate:value":a[1]||(a[1]=r=>n(t).ucCompanyCode=r),placeholder:"请输入"},null,8,["value"])]),_:1}),o(i,{name:"creditCode"},{label:s(()=>[ve]),default:s(()=>[o(p,{value:n(t).creditCode,"onUpdate:value":a[2]||(a[2]=r=>n(t).creditCode=r),placeholder:"请输入"},null,8,["value"])]),_:1}),o(i,{name:"companyAssetType",label:"公司资产类型",required:!1},{default:s(()=>[o(c,{placeholder:"请选择",value:n(t).companyAssetType,"onUpdate:value":a[3]||(a[3]=r=>n(t).companyAssetType=r),options:w.value},null,8,["value","options"])]),_:1}),o(i,{name:"companyType",label:"公司类型"},{default:s(()=>[o(b,{value:n(t).companyType,"onUpdate:value":a[4]||(a[4]=r=>n(t).companyType=r),onChange:L},{default:s(()=>[o(y,{value:1},{default:s(()=>[f("产权公司")]),_:1}),o(y,{value:2},{default:s(()=>[f("运维公司")]),_:1})]),_:1},8,["value"])]),_:1}),n(t).companyType.includes(1)?(I(),S(i,{key:0,name:"parentId",label:"上级产权公司"},{default:s(()=>[o(c,{placeholder:"请选择",value:n(t).parentId,"onUpdate:value":a[5]||(a[5]=r=>n(t).parentId=r),allowClear:"","show-search":"",options:v.value,fieldNames:{label:"companyName",value:"id"},"filter-option":O},null,8,["value","options"])]),_:1})):U("",!0),n(t).companyType.includes(2)?(I(),S(i,{key:1,name:"parentOperationId",label:"上级运维公司"},{default:s(()=>[o(c,{placeholder:"请选择",value:n(t).parentOperationId,"onUpdate:value":a[6]||(a[6]=r=>n(t).parentOperationId=r),allowClear:"","show-search":"",options:T.value,fieldNames:{label:"companyName",value:"id"},"filter-option":O},null,8,["value","options"])]),_:1})):U("",!0),o(i,{name:"status",label:"角色状态"},{default:s(()=>[o($,{value:n(t).status,"onUpdate:value":a[7]||(a[7]=r=>n(t).status=r)},{default:s(()=>[o(_,{value:0},{default:s(()=>[f("启用")]),_:1}),o(_,{value:1},{default:s(()=>[f("禁用")]),_:1})]),_:1},8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"])],64)}}});const Ke=fe(Te,[["__scopeId","data-v-6e1cb86c"]]);export{Ke as default};
