import{_ as O}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{_ as U}from"./CForm-ffa1b2bc.js";import{d as V,I as $,r as m,K as A,w as j,o as G,D as H,at as J,a as f,v as Q,g as M,f as t,e,u as n,b,h as u,y as W,i as k,q as X,bf as Z,aw as ee,av as te,ax as ae,bg as oe,bh as ne,bn as le,_ as se}from"./index-db94d997.js";import{g as re,e as ie}from"./index-19ad01ac.js";import{D as de}from"./dayjs-a8e42122.js";import{_ as me}from"./index-39334618.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const ue={class:"profitManage"},ce={class:"form_container"},pe={class:"text-hide"},_e=V({__name:"index",setup(fe){const T=$(),y=m(),c=m(),h=m([]),z=m([{label:"月",value:1},{label:"年",value:2}]),o=A({companyCodeList:[],dateType:1,monthKey:"",yearKey:""});j(()=>o.dateType,a=>{a===1?o.yearKey="":o.monthKey=""});const S=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:160,valueEnum:h,fixed:"left",render:!0},{title:"装机容量",dataIndex:"capins",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"月份",dataIndex:"monthKey",valueType:"date",dateFormat:"YYYY-MM",resizable:!0,width:120},{title:"本月结账电量",dataIndex:"monthSettleEq",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月总收入",dataIndex:"monthTotalIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"主营业务收入",dataIndex:"monthBusinessIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他业务收入",dataIndex:"monthOtherIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月总成本",dataIndex:"monthTotalCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月折旧成本",dataIndex:"monthDepreciationCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月运维成本",dataIndex:"monthOperationCost",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"职工薪酬",dataIndex:"monthEmpFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"其他费用",dataIndex:"monthOtherFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"财务费用",dataIndex:"monthFinanceFee",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"投资收益",dataIndex:"monthInvestIncome",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"本月营业利润",dataIndex:"monthTotalProfit",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:100}],D=()=>{console.log("reset"),y.value.resetFields(),x()},w=()=>{y.value.validateFields().then(a=>{console.log("values=",a),x()})};G(()=>{F(),w()}),H(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&x()});const F=()=>{J({}).then(a=>{console.log("产权公司res=",a);let i=(a||[]).map(s=>({label:s.companyName,value:s.companyCode}));h.value=i})},Y=()=>{var l;let a=(l=c.value)==null?void 0:l.getInitialFormStateNew();ie(a)},K=a=>{T.push({path:"/financeManage/profitManage/station/info",state:{pdata:X.cloneDeep(a)}})},g=m([]),L=a=>{g.value=a||[]},x=()=>{var a;(a=c.value)==null||a.reload()},N=(a,l)=>{let i={...o};return new Promise(s=>{const d={...a,noJoin:!0,delStatus:0,...i};s(d)})};return(a,l)=>{const i=U,s=Z,d=ee,P=te,C=de,I=ae,p=oe,v=me,R=ne,q=le,B=O;return f(),Q("div",ue,[M("div",ce,[t(R,{ref_key:"formRef",ref:y,model:n(o),name:"basic",autocomplete:"off"},{default:e(()=>[t(I,{span:24},{default:e(()=>[t(d,{span:8,style:{"padding-right":"20px"}},{default:e(()=>[t(s,{label:"产权公司",name:"companyCodeList"},{default:e(()=>[t(i,{value:n(o).companyCodeList,"onUpdate:value":l[0]||(l[0]=r=>n(o).companyCodeList=r),options:n(h)},null,8,["value","options"])]),_:1})]),_:1}),t(d,{span:8,style:{"padding-right":"10px","padding-left":"10px"}},{default:e(()=>[t(s,{label:"日期维度",name:"dateType"},{default:e(()=>[t(P,{options:n(z),value:n(o).dateType,"onUpdate:value":l[1]||(l[1]=r=>n(o).dateType=r),placeholder:"请选择",showArrow:"",style:{width:"100%"}},null,8,["options","value"])]),_:1})]),_:1}),t(d,{span:8,style:{"padding-left":"20px"}},{default:e(()=>[n(o).dateType===2?(f(),b(s,{key:0,label:"年份",name:"year"},{default:e(()=>[t(C,{value:n(o).yearKey,"onUpdate:value":l[2]||(l[2]=r=>n(o).yearKey=r),valueFormat:"YYYY",picker:"year",allowClear:"",style:{width:"100%"}},null,8,["value"])]),_:1})):(f(),b(s,{key:1,label:"月份",name:"month"},{default:e(()=>[t(C,{value:n(o).monthKey,"onUpdate:value":l[3]||(l[3]=r=>n(o).monthKey=r),valueFormat:"YYYY-MM",picker:"month",allowClear:"",disabled:!n(o).dateType,style:{width:"100%"}},null,8,["value","disabled"])]),_:1}))]),_:1})]),_:1}),t(I,{span:24,style:{display:"flex","flex-direction":"row","justify-content":"flex-end"}},{default:e(()=>[t(v,null,{default:e(()=>[t(p,{onClick:D},{default:e(()=>[u("重置")]),_:1}),t(p,{type:"primary",onClick:w},{default:e(()=>[u("查询")]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),t(B,{columns:S,ref_key:"actionRef",ref:c,request:n(re),"label-col":{style:{width:"80px"}},"wrapper-col":{span:16},onGetDataSource:L,"before-query-params":N,"default-query":!1,search:!1},{tableHeader:e(()=>[t(v,null,{default:e(()=>[n(g).length>0?(f(),b(p,{key:0,type:"primary",onClick:Y},{default:e(()=>[u("导出")]),_:1})):W("",!0)]),_:1})]),companyCodeListRender:e(({column:r,record:_,index:E})=>[t(q,null,{title:e(()=>[u(k(_.companyName),1)]),default:e(()=>[M("span",pe,k(_.companyName),1)]),_:2},1024)]),actionRender:e(({column:r,record:_,index:E})=>[t(v,null,{default:e(()=>[t(p,{type:"link",size:"small",onClick:ye=>K(_)},{default:e(()=>[u("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const ke=se(_e,[["__scopeId","data-v-1fc20a91"]]);export{ke as default};
