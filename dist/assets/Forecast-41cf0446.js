import{_ as L}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as M,r as i,o as P,D as z,at as B,a as g,v as j,g as _,f as l,e as n,u as p,b as O,h,y as R,i as m,z as b,bg as V,bn as W,_ as A}from"./index-db94d997.js";import{g as G,e as H}from"./index-e683f274.js";import{_ as J}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const K={class:"areaPrice"},Q={class:"areaPrice_table"},$={class:"text-hide"},U={class:"text-hide"},X=M({__name:"Forecast",setup(Y){const s=i(),x=["provinceCodeList","cityCodeList","areaCodeList"],w=i([]),f=i([]),C=i([{label:"发电量加权",value:1},{label:"装机容量加权",value:2},{label:"净资产加权",value:3}]),F=[{title:"产权公司",key:"companyCodes",dataIndex:"companyCodes",valueType:"multipleSelect",maxTagCount:1,width:120,valueEnum:f,render:!0,rules:[{required:!0,message:"请选择"}]},{title:"加权电价选择",dataIndex:"weiType",valueType:"select",hideInTable:!0,width:120,valueEnum:C,rules:[{required:!0,message:"请选择"}]},{title:"本月电量预计(kWh)",dataIndex:"eq",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"加权上网电价(元/kWh)",dataIndex:"weOngrienPri",search:!1,resizable:!0,formatDecimal:4,width:120},{title:"本月应收电费预测(万元)",key:"monthEqFee",dataIndex:"monthEqFee",search:!1,resizable:!0,formatMoney:!0,width:120,render:!0},{title:"本年应收电费预测(万元)",dataIndex:"yearEqFee",search:!1,resizable:!0,formatMoney:!0,width:120}],q=()=>{s.value.reset()};P(()=>{D()}),z(()=>{let e=s.value.getInitialFormStateNew();Object.prototype.hasOwnProperty.call(e,"delStatus")&&k()});const D=()=>{B({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));f.value=o})},E=()=>{var a;let e=(a=s.value)==null?void 0:a.getInitialFormStateNew();H(e)},y=i([]),T=e=>{y.value=e||[]},k=()=>{var e;(e=s.value)==null||e.reload()},I=e=>{const a=new Map;return e.forEach(o=>{const t=x[o.level-1];if(a.has(t))a.get(t).push(o.value);else{let r=[];r.push(o.value),a.set(t,r)}}),Object.fromEntries(a)},u=(e,a,o)=>!e||!a?[]:(e.forEach(t=>{a.find(d=>t.value===d)&&o.push(t),t.children&&t.children.length>0&&u(t.children,a,o)}),o),N=e=>new Promise(a=>{const o=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[0]:"",t=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[1]:"";let r={delStatus:0,noJoin:!0,startTime:o,endTime:t};const d=u(w.value,e==null?void 0:e.cityTree,[]);let c=I(d);e==null||delete e.datongrd;const S={...r,...e,...c};a(S)});return(e,a)=>{const o=V,t=J,r=W,d=L;return g(),j("div",K,[_("div",Q,[l(d,{columns:F,ref_key:"actionRef",ref:s,request:p(G),"label-col":{style:{width:"110px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:T,"before-query-params":N,"default-query":!1,onCancel:q},{tableHeader:n(()=>[l(t,null,{default:n(()=>[p(y).length>0?(g(),O(o,{key:0,type:"primary",onClick:E},{default:n(()=>[h("导出")]),_:1})):R("",!0)]),_:1})]),companyCodesRender:n(({record:c})=>[l(r,null,{title:n(()=>[h(m(c.companyName),1)]),default:n(()=>[_("span",$,m(c.companyName),1)]),_:2},1024)]),monthEqFeeRender:n(({record:c})=>[l(r,null,{title:n(()=>[h(m(p(b)(c.monthEqFee/1e4)),1)]),default:n(()=>[_("span",U,m(p(b)(c.monthEqFee/1e4)),1)]),_:2},1024)]),_:1},8,["request"])])])}}});const se=A(X,[["__scopeId","data-v-29286bc8"]]);export{se as default};
