import{_ as W,a as X}from"./CForm-ffa1b2bc.js";import{_ as Z}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ee,k as te,I as ae,r as d,K as ne,o as oe,D as se,at as le,a as q,v as K,g as f,f as n,e as t,h as p,i as _,u as s,S as P,y as F,s as re,F as ie,bc as L,bi as R,q as ce,bg as me,bn as de,bf as ue,aw as pe,ax as _e,bh as fe}from"./index-db94d997.js";import{g as ye,r as he,c as ge}from"./index-a42d8557.js";import{q as be}from"./dayjs-a8e42122.js";import{E as xe}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as Ce}from"./index-39334618.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const ke={class:"areaPrice"},qe={class:"text-hide"},Ke={class:"text-hide"},we={key:0,class:"status_tag"},Se={class:"tag_one"},ve={key:1,class:"status_tag"},Ie={class:"tag_two"},$e=ee({__name:"index",setup(De){te.extend(be);const w=ae(),h=d(),g=d([]),u=d(!1),z=d(!1),y=d(),m=ne({companyCodeList:[],monthKey:""}),b=d([{label:"已确认",value:1},{label:"待确认",value:0}]),N=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",width:160,valueEnum:g,fixed:"left",render:!0},{title:"季度",key:"monthKey",dataIndex:"monthKey",valueType:"quarterDate",resizable:!0,fixed:"left",width:120,render:!0},{title:"系统农户收益预测",dataIndex:"systemFarmerEarnings",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"分享金额-正泰",dataIndex:"shareMoney",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"偏差金额",dataIndex:"offsetMoney",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"季度支付调整",dataIndex:"seasonPayAdjust",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"确认状态",key:"confirmStatus",dataIndex:"confirmStatus",valueType:"select",valueEnum:b,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:130}];oe(()=>{S()}),se(()=>{h.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&(S(),v())});const T=()=>{y.value.validateFields().then(e=>{const l=m.monthKey?m.monthKey.quarter():"",r=m.monthKey?m.monthKey.year():"";let i={...e,quarter:l,yearKey:r};he(i).then(c=>{console.log("调取农户收益res=",c),L.success("调取农户收益成功"),y.value.resetFields(),u.value=!1})})},O=()=>{y.value.resetFields(),u.value=!1},S=()=>{le({}).then(e=>{console.log("产权公司res=",e);let r=(e||[]).map(i=>({label:i.companyName,value:i.companyCode}));g.value=r})},E=()=>{w.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/quarterPayableConfirm/dataImport",query:{templateType:8,fileType:".csv,.xls,.xlsx",fileSize:30}})},$=()=>{u.value=!0},B=()=>{console.log("onCancel")},V=e=>{R.confirm({title:"确认提示",icon:n(xe),content:"确认后不可修改，是否确认？",onOk(){return new Promise((l,r)=>{let i={id:e==null?void 0:e.id};ge(i).then(c=>{L.info("确认成功"),l(c),v()}).catch(c=>{console.log("err=",c),r()})})},onCancel:()=>B()})},U=e=>{w.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/quarterPayableConfirm/detail",state:{pdata:ce.cloneDeep(e)}})},j=d([]),A=e=>{j.value=e||[]},v=()=>{var e;(e=h.value)==null||e.reload()},Q=(e,l)=>{let r={},i=e!=null&&e.monthKey?e==null?void 0:e.monthKey.year():"",c=e!=null&&e.monthKey?e==null?void 0:e.monthKey.quarter():"";return new Promise(x=>{const C={...r,...e,noJoin:!0,delStatus:0,yearKey:i,quarter:c};x(C)})};return(e,l)=>{const r=me,i=Ce,c=de,x=Z,C=W,I=ue,D=pe,M=_e,G=X,H=fe,J=R;return q(),K(ie,null,[f("div",ke,[n(x,{columns:N,ref_key:"actionRef",ref:h,request:s(ye),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:A,"before-query-params":Q},{tableHeader:t(()=>[n(i,null,{default:t(()=>[n(r,{onClick:E},{default:t(()=>[p("导入正泰支付单")]),_:1}),n(r,{type:"primary",onClick:$},{default:t(()=>[p("调取农户收益")]),_:1})]),_:1})]),companyCodeListRender:t(({column:o,record:a,index:k})=>[n(c,null,{title:t(()=>[p(_(a.companyName),1)]),default:t(()=>[f("span",qe,_(a.companyName),1)]),_:2},1024)]),monthKeyRender:t(({column:o,record:a,index:k})=>[n(c,null,{title:t(()=>[p(_(a.yearKey&&a.quarter?a.yearKey+"年"+a.quarter+"季度":""),1)]),default:t(()=>[f("span",Ke,_(a.yearKey&&a.quarter?a.yearKey+"年"+a.quarter+"季度":""),1)]),_:2},1024)]),confirmStatusRender:t(({column:o,record:a,index:k})=>[a[o.dataIndex]===1?(q(),K("span",we,[f("span",Se,_(s(P)(a[o.dataIndex],s(b))),1)])):F("",!0),a[o.dataIndex]===0?(q(),K("span",ve,[f("span",Ie,_(s(P)(a[o.dataIndex],s(b))),1)])):F("",!0)]),actionRender:t(({column:o,record:a,index:k})=>[n(i,null,{default:t(()=>[n(r,{type:"link",size:"small",onClick:Y=>V(a),disabled:a.confirmStatus===1},{default:t(()=>[p("确认")]),_:2},1032,["onClick","disabled"]),n(r,{type:"link",size:"small",onClick:Y=>U(a)},{default:t(()=>[p("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])]),n(J,{visible:s(u),"onUpdate:visible":l[2]||(l[2]=o=>re(u)?u.value=o:null),title:"调取农户收益","confirm-loading":s(z),destroyOnClose:!0,onOk:T,onCancel:O},{default:t(()=>[n(H,{model:s(m),ref_key:"formRef",ref:y,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:t(()=>[n(M,{span:24},{default:t(()=>[n(D,{span:24},{default:t(()=>[n(I,{label:"产权公司",name:"companyCodeList",required:""},{default:t(()=>[n(C,{value:s(m).companyCodeList,"onUpdate:value":l[0]||(l[0]=o=>s(m).companyCodeList=o),options:s(g)},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),n(M,{span:24},{default:t(()=>[n(D,{span:24},{default:t(()=>[n(I,{label:"季度选择",name:"monthKey",required:""},{default:t(()=>[n(G,{style:{width:"100%"},value:s(m).monthKey,"onUpdate:value":l[1]||(l[1]=o=>s(m).monthKey=o)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});export{$e as default};
