import{a2 as $,bP as H,d as me,r as f,K as xe,o as ve,D as ge,at as Ie,w as Y,a as y,v as w,g as d,f as l,e as o,u as s,h as m,y as L,b as k,i as g,S as B,z as K,e9 as Q,F as be,au as we,q as ke,bI as ze,bf as Ce,bU as Me,av as Se,be as De,bg as Fe,bh as Ne,bn as Te,ay as Ee,az as Pe,p as qe,j as Le,_ as Re}from"./index-db94d997.js";import{e as Ae}from"./empty1-348ef1fe.js";import{_ as Oe}from"./index-42d7fb9b.js";import{_ as Ue}from"./index-07f7e8bf.js";import{R as Ve}from"./dayjs-a8e42122.js";import{_ as je}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";function Ye(p){return $({url:"/web/statisticsAnalyse/v1/getIncomeConfirmVOList",method:"POST",isTable:!0,data:p})}function Be(p){return H({url:"/web/statisticsAnalyse/v1/exprotIncomeConfirmExcel",method:"POST",data:p})}function Ke(p){return $({url:"/web/statisticsAnalyse/v1/getIncomeConfirmDetailVOList",method:"POST",isTable:!0,data:p})}function Qe(p){return H({url:"/web/statisticsAnalyse/v1/exportIncomeConfirmDetail",method:"POST",data:p})}const I=p=>(qe("data-v-6f2045fa"),p=p(),Le(),p),$e={class:"statements_container"},He=I(()=>d("div",{class:"item_title"},"维度选择",-1)),Je={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ge={style:{flex:"1",display:"flex","align-items":"center"}},We={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Xe={style:{flex:"1",display:"flex","flex-direction":"row"}},Ze=I(()=>d("span",{style:{"margin-left":"5px"}},"全选",-1)),et=I(()=>d("div",{style:{flex:"1"}},null,-1)),tt=I(()=>d("div",{style:{flex:"1","padding-left":"24px"}},null,-1)),at={style:{display:"flex","flex-direction":"row","align-items":"center"}},st={style:{flex:"1",display:"flex","flex-direction":"row","align-items":"center"}},it={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row","align-items":"center"}},nt={style:{flex:"1",display:"flex","flex-direction":"row","align-items":"center"}},dt=I(()=>d("div",{class:"item_title"},"时间维度",-1)),ot={style:{display:"flex","flex-direction":"row","align-items":"center"}},lt={style:{flex:"1",display:"flex","align-items":"center"}},rt={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},ct={style:{display:"flex","flex-direction":"row",flex:"1"}},ht=I(()=>d("div",{style:{flex:"1",display:"flex","align-items":"center"}},null,-1)),ft={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"24px"}},pt={key:0,class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},_t={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},ut={class:"text-hide"},yt={class:"text-hide"},mt={class:"text-hide"},xt={class:"text-hide"},vt={key:1,class:"content_container"},gt={key:2,class:"content_container",style:{position:"relative"}},It=["src"],bt=I(()=>d("span",{class:"empty_text"}," 暂无数据，请选择正确查询信息 ",-1)),wt=me({__name:"index",setup(p){const z=f(),x=f(!1),T=f([]),c=f({}),C=f([]),M=f([]),S=f(0),D=f(1),E=f(10),b=f([]),i=xe({companyCodeList:"",time1:[],companyId:[],companyId_checked:!1,companyId_indeterminate:!1,provinceId:[],provinceId_checked:!1,provinceId_indeterminate:!1,checked4:!0,checked5:!1,stationName:"",stationCode:""}),R=f([{label:"是",value:1},{label:"否",value:0}]),J=f([{title:"产权公司",dataIndex:"companyName",resizable:!0,width:160},{title:"装机容量",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0},{title:"组件数量",dataIndex:"componentQuantity",search:!1,width:120,resizable:!0},{title:"月份",dataIndex:"monthKey",search:!1,width:120,resizable:!0},{title:"本月电量",dataIndex:"monthEq",search:!1,width:120,formatMoney:!0,resizable:!0},{title:"本月租金",dataIndex:"monthRent",search:!1,formatMoney:!0,resizable:!0,width:120},{title:"本月电费收入(含税)",dataIndex:"taxEqFeeMonth",search:!1,formatMoney:!0,resizable:!0,width:170},{title:"本月电费收入(剔租金)",dataIndex:"eliminateEqFeeMonth",search:!1,formatMoney:!0,resizable:!0,width:170},{title:"月度运维费",dataIndex:"monthOperationFee",search:!1,formatMoney:!0,resizable:!0,width:150},{title:"是否确认运维收入",dataIndex:"operationIncomeFlag",valueType:"select",valueEnum:R,search:!1,resizable:!0,width:150},{title:"本月收入(运维部分)",dataIndex:"monthOperationIncome",search:!1,formatMoney:!0,resizable:!0,width:170},{title:"运维收入税金",dataIndex:"operationIncomeTaxes",search:!1,formatMoney:!0,resizable:!0,width:150},{title:"本月收入(电费部分)",dataIndex:"monthIncomeEqFee",search:!1,formatMoney:!0,resizable:!0,width:170},{title:"电费收入税金",dataIndex:"incomeEqFeeTaxes",search:!1,formatMoney:!0,resizable:!0,width:150},{title:"本月收入总额",dataIndex:"monthIncomeSum",search:!1,formatMoney:!0,resizable:!0,width:150}]),G=(a,t)=>{t.width=a},W=f([{title:"电站编码",dataIndex:"stationCode",resizable:!0,width:120},{title:"业主姓名",dataIndex:"stationName",resizable:!0,width:120},{title:"产权公司",dataIndex:"companyName",resizable:!0,width:160},{title:"省",dataIndex:"prvName",resizable:!0,width:120},{title:"市",dataIndex:"cityName",resizable:!0,width:120},{title:"区",dataIndex:"distName",resizable:!0,width:120},{title:"装机容量",dataIndex:"capins",width:120,formatMoney:!0,resizable:!0},{title:"组件数量",dataIndex:"componentQuantity",search:!1,width:120,resizable:!0},{title:"并网时间",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",search:!1,width:120,resizable:!0},{title:"月份",dataIndex:"monthKey",search:!1,width:120,resizable:!0},{title:"标杆电价",dataIndex:"sightcingBuelectrovalences",search:!1,width:120,formatDecimal:4,resizable:!0},{title:"补贴电价",dataIndex:"subsidyElectrovalences",search:!1,width:120,formatDecimal:4,resizable:!0},{title:"上网电价",dataIndex:"surfElectrovalences",search:!1,width:120,formatDecimal:4,resizable:!0},{title:"本月电量",dataIndex:"monthEq",search:!1,width:120,formatMoney:!0,resizable:!0},{title:"租金单价",dataIndex:"rentUnit",search:!1,width:120,resizable:!0},{title:"本月租金",dataIndex:"monthRent",search:!1,formatMoney:!0,resizable:!0,width:120},{title:"本月电费收入(含税)",dataIndex:"taxEqFeeMonth",search:!1,formatMoney:!0,resizable:!0,width:160},{title:"本月电费收入(剔租金)",dataIndex:"eliminateEqFeeMonth",search:!1,formatMoney:!0,resizable:!0,width:170},{title:"月度运维费",dataIndex:"monthOperationFee",search:!1,formatMoney:!0,resizable:!0,width:120},{title:"是否确认运维收入",dataIndex:"operationIncomeFlag",valueType:"select",valueEnum:R,search:!1,resizable:!0,width:150},{title:"本月收入(运维部分)",dataIndex:"monthOperationIncome",search:!1,formatMoney:!0,resizable:!0,width:160},{title:"运维收入税金",dataIndex:"operationIncomeTaxes",search:!1,formatMoney:!0,resizable:!0,width:120},{title:"本月收入(电费部分)",dataIndex:"monthIncomeEqFee",search:!1,formatMoney:!0,resizable:!0,width:160},{title:"电费收入税金",dataIndex:"incomeEqFeeTaxes",search:!1,formatMoney:!0,resizable:!0,width:150},{title:"本月收入总额",dataIndex:"monthIncomeSum",search:!1,formatMoney:!0,resizable:!0,width:150}]),X=(a,{attrs:t})=>t.vnodes,Z=(a,t)=>{i[a]=t.map(r=>r.value)},ee=(a,t,r)=>{var h;((h=a==null?void 0:a.target)==null?void 0:h.checked)?Z(t,r):i[t]=[],i[t+"_indeterminate"]=!1},te=(a,t,r,n)=>{t.length===0?(i[n+"_indeterminate"]=!1,i[n+"_checked"]=!1):t.length===r.length?(i[n+"_indeterminate"]=!1,i[n+"_checked"]=!0):(i[n+"_indeterminate"]=!0,i[n+"_checked"]=!1)},ae=f({companyId:[{validator:async(a,t)=>{if((!t||t.length===0)&&i.checked4)return Promise.reject("请选择产权公司")},trigger:"change"}],provinceId:[{validator:async(a,t)=>{if((!t||t.length===0)&&i.checked5)return Promise.reject("请选择行政区划")},trigger:"change"}],time1:[{validator:async(a,t)=>{if(!t||t.length===0)return Promise.reject("请选择时间周期")},trigger:"change"}]}),se=a=>{U(a)},ie=a=>{console.log(a)},ne=()=>{z.value.resetFields(),c.value={},C.value=[],M.value=[],i.companyId_checked=!1,i.companyId_indeterminate=!1,i.provinceId_checked=!1,i.provinceId_indeterminate=!1},de=a=>{D.value=a.current,E.value=a.pageSize,S.value=a.total;const t={...i,pageNum:a.current,pageSize:a.pageSize};U(t)};let P="",A="";const oe=(a,t,r)=>{var v,F;const n=((F=(v=r==null?void 0:r.triggerNode)==null?void 0:v.props)==null?void 0:F.level)||1;P=["prvCode","cityCode","distCode","town","vil"][n-1],A=a},le=a=>new Promise((t,r)=>{we({pid:a||"0"}).then(n=>{console.log("行政区res=",n);let h=O(n);T.value=h,console.log(T.value),t(!0)}).catch(()=>{r()})}),O=a=>(a.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,O(t.subDistrict))}),a),U=a=>{let t=a==null?void 0:a.time1[0],r=a==null?void 0:a.time1[1];if(i.checked4){let h={companyCodeList:ke.cloneDeep(a.companyId)||[],startMonthKey:t,endMonthKey:r,pageNum:D.value,pageSize:E.value,checked4:i.checked4};console.log("params=",h),x.value=!0,Ye(h).then(v=>{x.value=!1,c.value=h,console.log("产权公司维度res=",v),V(v,1)}).catch(v=>{x.value=!1})}else if(i.checked5){let n={stationName:i.stationName,stationCode:i.stationCode,startMonthKey:t,endMonthKey:r,pageNum:D.value,pageSize:E.value,checked5:i.checked5};P&&(n[P]=A),console.log("params=",n),x.value=!0,Ke(n).then(h=>{x.value=!1,c.value=n,console.log("地区维度res=",h),V(h,2)}).catch(h=>{x.value=!1})}},V=(a,t)=>{t===1?(C.value=(a==null?void 0:a.result)||[],S.value=(a==null?void 0:a.total)||0):t===2&&(M.value=(a==null?void 0:a.result)||[],S.value=(a==null?void 0:a.total)||0)};ve(()=>{re(),le()}),ge(()=>{console.log("onActivated")});const re=()=>{Ie({}).then(a=>{console.log("产权公司res=",a);let r=(a||[]).map(n=>({label:n.companyName,value:n.companyCode}));b.value=r})};Y(()=>i.checked4,a=>{a&&(i.checked5=!1,i.checked6=!1,i.provinceId=[],z.value.clearValidate(["provinceId"]))}),Y(()=>i.checked5,a=>{a&&(i.checked4=!1,i.checked6=!1,i.companyId=[],z.value.clearValidate(["companyId"]))});const ce=()=>{console.log("pageParams=",c.value),c.value.checked4?Be(c.value).then(a=>{console.log("公司维度导出res:",a)}):c.value.checked5&&Qe(c.value).then(a=>{console.log("公司维度导出res:",a)})};return(a,t)=>{const r=ze,n=Ce,h=Me,v=Oe,F=Se,j=De,he=Ue,fe=Ve,q=Fe,pe=je,_e=Ne,N=Te,ue=Ee,ye=Pe;return y(),w(be,null,[d("div",$e,[l(_e,{ref_key:"formRef",ref:z,name:"formRef",model:s(i),rules:s(ae),onFinish:se,onFinishFailed:ie},{default:o(()=>[He,d("div",Je,[d("div",Ge,[d("div",We,[l(n,{name:"checked4"},{default:o(()=>[l(r,{checked:s(i).checked4,"onUpdate:checked":t[0]||(t[0]=e=>s(i).checked4=e)},{default:o(()=>[m("产权公司：")]),_:1},8,["checked"])]),_:1})]),d("div",Xe,[l(n,{name:"companyId",style:{flex:"1",display:"flex","padding-right":"24px"}},{default:o(()=>[l(F,{value:s(i).companyId,"onUpdate:value":t[4]||(t[4]=e=>s(i).companyId=e),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:s(b),mode:"multiple",disabled:!s(i).checked4,"filter-option":(e,_)=>((_==null?void 0:_.label)??"").toLowerCase().includes(e.toLowerCase()),onChange:t[5]||(t[5]=(e,_)=>te(e,_,s(b),"companyId"))},{dropdownRender:o(({menuNode:e})=>[s(b)&&s(b).length>0?(y(),w("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[3]||(t[3]=_=>_.preventDefault())},[l(h,{checked:s(i).companyId_checked,"onUpdate:checked":t[1]||(t[1]=_=>s(i).companyId_checked=_),indeterminate:s(i).companyId_indeterminate,onChange:t[2]||(t[2]=_=>ee(_,"companyId",s(b)))},null,8,["checked","indeterminate"]),Ze],32)):L("",!0),l(v,{style:{margin:"4px 0"}}),l(X,{vnodes:e},null,8,["vnodes"])]),_:1},8,["value","options","disabled","filter-option"])]),_:1}),et,tt])])]),d("div",at,[d("div",st,[d("div",it,[l(n,{name:"checked5"},{default:o(()=>[l(r,{checked:s(i).checked5,"onUpdate:checked":t[6]||(t[6]=e=>s(i).checked5=e)},{default:o(()=>[m("电站维度：")]),_:1},8,["checked"])]),_:1})]),d("div",nt,[l(n,{name:"stationName",style:{flex:"1","padding-right":"24px"}},{default:o(()=>[l(j,{placeholder:"请输入业主名称",disabled:!s(i).checked5,value:s(i).stationName,"onUpdate:value":t[7]||(t[7]=e=>s(i).stationName=e)},null,8,["disabled","value"])]),_:1}),l(n,{name:"stationCode",style:{flex:"1"}},{default:o(()=>[l(j,{placeholder:"请输入电站编码",disabled:!s(i).checked5,value:s(i).stationCode,"onUpdate:value":t[8]||(t[8]=e=>s(i).stationCode=e)},null,8,["disabled","value"])]),_:1}),l(n,{name:"station",style:{flex:"1","padding-left":"24px"}},{default:o(()=>[l(he,{value:s(i).cityCode,"onUpdate:value":t[9]||(t[9]=e=>s(i).cityCode=e),"show-search":"",style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择省市区划","allow-clear":"","tree-data":s(T),"tree-node-filter-prop":"label",onChange:oe,disabled:!s(i).checked5},null,8,["value","tree-data","disabled"])]),_:1})])])]),dt,d("div",ot,[d("div",lt,[d("div",rt,[l(n,{name:"checked1"},{default:o(()=>[m(" 时间选择： ")]),_:1})]),d("div",ct,[l(n,{name:"time1",style:{flex:"1",display:"flex","padding-right":"24px"}},{default:o(()=>[l(fe,{value:s(i).time1,"onUpdate:value":t[10]||(t[10]=e=>s(i).time1=e),style:{width:"100%"},picker:"month",valueFormat:"YYYY-MM"},null,8,["value"])]),_:1}),ht,d("div",ft,[l(pe,null,{default:o(()=>[l(q,{onClick:ne},{default:o(()=>[m("重置")]),_:1}),l(q,{type:"primary","html-type":"submit"},{default:o(()=>[m("查询")]),_:1})]),_:1})])])])])]),_:1},8,["model","rules"])]),s(c).checked4||s(c).checked5?(y(),w("div",pt,[d("div",_t,[s(c).checked4&&s(C).length>0||s(c).checked5&&s(M).length>0?(y(),k(q,{key:0,type:"primary",onClick:ce},{default:o(()=>[m("导出")]),_:1})):L("",!0)]),l(ue,{style:{"margin-bottom":"24px"},columns:s(c).checked4?s(J):s(c).checked5?s(W):[],"data-source":s(c).checked4?s(C):s(c).checked5?s(M):[],pagination:{current:s(D),total:s(S),showTotal:e=>`共 ${e} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0},scroll:{y:500},loading:s(x),onChange:de,onResizeColumn:G},{bodyCell:o(({column:e,text:_,record:u,index:Mt})=>[(e==null?void 0:e.valueType)==="select"&&!(e!=null&&e.render)?(y(),k(N,{key:0},{title:o(()=>[m(g(s(B)(u[e==null?void 0:e.dataIndex],e==null?void 0:e.valueEnum)),1)]),default:o(()=>[d("span",ut,g(s(B)(u[e==null?void 0:e.dataIndex],e==null?void 0:e.valueEnum)),1)]),_:2},1024)):e!=null&&e.formatMoney&&!(e!=null&&e.render)?(y(),k(N,{key:1},{title:o(()=>[m(g(s(K)(u[e==null?void 0:e.dataIndex])),1)]),default:o(()=>[d("span",yt,g(s(K)(u[e==null?void 0:e.dataIndex])),1)]),_:2},1024)):e!=null&&e.formatFixed&&!(e!=null&&e.render)?(y(),k(N,{key:2},{title:o(()=>[m(g(u[e==null?void 0:e.dataIndex]&&s(Q)(u[e==null?void 0:e.dataIndex])),1)]),default:o(()=>[d("span",mt,g(u[e==null?void 0:e.dataIndex]&&s(Q)(u[e==null?void 0:e.dataIndex])),1)]),_:2},1024)):e!=null&&e.render?L("",!0):(y(),k(N,{key:3},{title:o(()=>[m(g(u[e==null?void 0:e.dataIndex]),1)]),default:o(()=>[d("span",xt,g(u[e==null?void 0:e.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","pagination","loading"])])):s(x)?(y(),w("div",vt,[l(ye)])):(y(),w("div",gt,[d("img",{src:s(Ae),style:{width:"188px",height:"168px"}},null,8,It),bt]))],64)}}});const qt=Re(wt,[["__scopeId","data-v-6f2045fa"]]);export{qt as default};
