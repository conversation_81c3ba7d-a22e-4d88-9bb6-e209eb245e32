import{d as Q,t as ae,r as E,w as L,a as s,b as p,e as g,u as n,v as T,f,s as Z,y as v,g as te,bU as pe,av as le,K as G,aU as ve,o as _e,n as fe,F as B,x as he,aI as ie,bX as ge,h as J,i as ye,a1 as me,Z as we,be as xe,az as Ce,bf as ke,aw as be,bg as Fe,ax as Se,bh as Ie,_ as Ye}from"./index-db94d997.js";import{_ as Te}from"./index-42d7fb9b.js";import{D as ne,R as Ue}from"./dayjs-a8e42122.js";import{_ as ee}from"./index-07f7e8bf.js";const De=te("span",{style:{"margin-left":"5px"}},"全选",-1),Pe=Q({__name:"index",props:{options:{default:[]},maxTagCount:{default:1},model:{default:"multiple"},value:{default:[]}},emits:["update:value"],setup(U,{emit:F}){const r=U;let{options:y,value:_}=ae(r);const m=E(!1),C=E(!1),M=(l,{attrs:d})=>d.vnodes,c=()=>{const l=y.value.map(d=>d.value);F("update:value",l)},S=(l,d)=>{var D;((D=l==null?void 0:l.target)==null?void 0:D.checked)?c():F("update:value",[]),C.value=!1},h=(l,d)=>{F("update:value",l)};return L([_,y],()=>{_.value.length===0?(C.value=!1,m.value=!1):_.value.length===y.value.length?(C.value=!1,m.value=!0):(C.value=!0,m.value=!1),_.value&&_.value.length===1&&_.value[0]===void 0&&F("update:value",[])}),(l,d)=>{const k=pe,D=Te,P=le;return s(),p(P,{value:n(_),"onUpdate:value":d[3]||(d[3]=x=>Z(_)?_.value=x:_=x),style:{width:"100%"},"max-tagCount":l.maxTagCount,"show-search":"","show-arrow":"","allow-clear":"",placeholder:"请选择",options:n(y),mode:"multiple","filter-option":(x,i)=>((i==null?void 0:i.label)??"").toLowerCase().includes(x.toLowerCase()),onChange:d[4]||(d[4]=(x,i)=>h(x))},{dropdownRender:g(({menuNode:x})=>[n(y)&&n(y).length>0?(s(),T("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:d[2]||(d[2]=i=>i.preventDefault())},[f(k,{checked:n(m),"onUpdate:checked":d[0]||(d[0]=i=>Z(m)?m.value=i:null),indeterminate:n(C),onChange:d[1]||(d[1]=i=>S(i,n(y)))},null,8,["checked","indeterminate"]),De],32)):v("",!0),f(D,{style:{margin:"4px 0"}}),f(M,{vnodes:x},null,8,["vnodes"])]),_:1},8,["value","max-tagCount","options","filter-option"])}}}),Ee={class:"quarter_date_box"},Me=Q({__name:"index",props:{value:{default:""}},emits:["update:value"],setup(U,{emit:F}){const r=U,{value:y}=ae(r),_=(m,C)=>{F("update:value",m)};return(m,C)=>{const M=ne;return s(),T("div",Ee,[f(M,{value:n(y),picker:"quarter",format:"YYYY年Q季度",placeholder:"请选择",style:{width:"100%"},getPopupContainer:c=>c.parentNode,onChange:_},null,8,["value","getPopupContainer"])])}}});const Ne={class:"cform-main bg-white"},Ve=Q({__name:"CForm",props:{dataSource:{},getPageData:{type:Function},labelCol:{},wrapperCol:{},beforeQueryParams:{type:Function},showReset:{type:Boolean},showSubmit:{type:Boolean},submitText:{},onCancel:{}},setup(U,{expose:F}){const r=U,y=ee.SHOW_PARENT,_=a=>a&&a.hasOwnProperty("search")?a==null?void 0:a.search:!0,m=a=>{const t=a.length;let o=1;switch(t){case 1:o=.3;break;case 2:o=.8;break}return t>=3&&(o=1),o},C=a=>{const t=a.length;let o=24;switch(t){case 1:o=8;break;case 2:o=8;break}return t>=3&&(o=8),o},c=(r==null?void 0:r.dataSource.filter(a=>_(a)&&(a==null?void 0:a.dataIndex))).sort(function(a,t){return a.order-t.order});c.forEach((a,t)=>{if(a.isTop){let o=c.splice(t,1);c.unshift(...o)}});const S=E(!0),h=E();let l=G({}),d=E({});const{getPageData:k}=r;(()=>{c&&c.length>0&&c.forEach(a=>{(a.valueType==="multipleSelect"||a.valueType==="multipleTreeSelect")&&(l[a.dataIndex]=[]),a.defaultValue&&(l[a.dataIndex]=a.defaultValue)})})();const P=a=>new Promise(t=>{let o={...a};r!=null&&r.beforeQueryParams?r==null||r.beforeQueryParams(a).then(w=>{w&&(o=w),console.log("在点击查询之前添加参数",w,o),t(o)}).catch(()=>{t(o)}):t(o)}),x=a=>{P(a).then(t=>{d.value=t,t.pageNum=1,k&&k(t)})},i=()=>{var t;h==null||h.value.resetFields();const a=(t=h==null?void 0:h.value)==null?void 0:t.getFieldsValue();r!=null&&r.beforeQueryParams?r==null||r.beforeQueryParams({},a).then(o=>{o.pageNum=1,k&&k(o)}):k&&k()};F({getFormParams:()=>{var t;const a=(t=h.value)==null?void 0:t.getFieldsValue();return P(a)},validateFormParams:async()=>{var t;const a=await((t=h.value)==null?void 0:t.validate());return a?P(a):null},setFormParams:a=>new Promise(t=>{a&&Object.keys(a).forEach(o=>{l[o]=a[o]}),t(!0)}),getInitialFormState:()=>d.value,reset:()=>{var a;(a=h.value)==null||a.resetFields()}});const z=(a="YYYYMMDD")=>{let t=a.includes("YYYY"),o=a.includes("MM"),w=a.includes("DD");return t&&o&&w?"date":t&&o&&!w?"month":t&&!o&&!w?"year":""};let N=0;const b=G({data:[],value:[],fetching:!1}),oe=ve((a,t)=>{if(!t)return;N+=1;const o=N;b.data=[],b.fetching=!0;const w={pageNo:1,pageSize:1e3,projectName:t};a.request(w).then(V=>{if(o!==N)return;const R=V.data.map(I=>({label:`${I[a.dataIndex]}`,value:a.dataValue?I[a.dataValue]:I[a.dataIndex]}));b.data=R,b.fetching=!1})},300);return _e(()=>{}),L(b.value,()=>{b.data=[],b.fetching=!1}),L(l,()=>{console.log("formState=",l)}),(a,t)=>{const o=ee,w=Ue,V=ne,R=Me,I=le,re=Pe,ue=xe,se=Ce,A=ke,Y=be,H=Fe,q=Se,de=Ie;return s(),T("div",Ne,[f(de,{ref_key:"formRef",ref:h,model:n(l),name:"basic",autocomplete:"off",onFinish:x,"label-col":(S.value,r.labelCol),"wrapper-col":S.value?r.wrapperCol:{}},{default:g(()=>[te("div",{class:we(`ant-advanced-search-form ${S.value?"form-box":""}`)},[f(q,{span:24,class:"table-from-main",style:fe(`flex: ${m(n(c))}`)},{default:g(()=>[(s(!0),T(B,null,he(n(c),(e,ce)=>(s(),T(B,{key:ce},[(s(),p(Y,{key:0,span:8},{default:g(()=>[f(A,{style:{padding:"0 10px"},label:e.title,name:e.dataIndex,rules:e.rules},{default:g(()=>{var O,j,W,K,X;return[e.valueType==="treeSelectLoad"?(s(),p(o,{key:0,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":((O=e.valueEnum)==null?void 0:O.value)||e.valueEnum,placeholder:"请选择","load-data":e.valueTreeLoad,"dropdown-match-select-width":!1,"tree-data-simple-mode":"",allowClear:"",onChange:e.onSelect},null,8,["value","onUpdate:value","tree-data","load-data","onChange"])):v("",!0),e.valueType==="treeSelect"?(s(),p(o,{key:1,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,"show-search":"",style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择","allow-clear":"","tree-data":((j=e.valueEnum)==null?void 0:j.value)||e.valueEnum,"tree-node-filter-prop":"label",onChange:e.onSelect},null,8,["value","onUpdate:value","tree-data","onChange"])):v("",!0),e.valueType==="multipleTreeSelect"?(s(),p(o,{key:2,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,"show-search":"",style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择","allow-clear":"",multiple:"","tree-checkable":"","show-checked-strategy":n(y),"max-tagCount":e.maxTagCount||1,"tree-data":((W=e.valueEnum)==null?void 0:W.value)||e.valueEnum,"tree-node-filter-prop":"label",onChange:e.onSelect},null,8,["value","onUpdate:value","show-checked-strategy","max-tagCount","tree-data","onChange"])):v("",!0),e.valueType==="dateRange"?(s(),p(w,{key:3,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,allowClear:e.allowClear!==!1,style:{width:"100%"},format:e.dateFormat?e.dateFormat:"YYYY-MM-DD",valueFormat:e.dateFormat?e.dateFormat:"YYYY-MM-DD",picker:z(e.dateFormat)},null,8,["value","onUpdate:value","allowClear","format","valueFormat","picker"])):v("",!0),e.valueType==="date"?(s(),p(V,{key:4,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,format:e.dateFormat?e.dateFormat:"YYYY-MM-DD",valueFormat:e.dateFormat?e.dateFormat:"YYYY-MM-DD",picker:z(e.dateFormat),allowClear:"",style:{width:"100%"}},null,8,["value","onUpdate:value","format","valueFormat","picker"])):v("",!0),e.valueType==="quarterDate"?(s(),p(R,{key:5,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u},null,8,["value","onUpdate:value"])):v("",!0),e.valueType==="select"||e.valueType==="select2"||e.valueType==="select3"?(s(),p(I,{key:6,options:((K=e.valueEnum)==null?void 0:K.value)||e.valueEnum,mode:e.valueMode,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,placeholder:"请选择",allowClear:"",showArrow:"","show-search":"","filter-option":(u,$)=>(($==null?void 0:$.label)??"").toLowerCase().includes(u.toLowerCase()),style:{width:"100%"}},null,8,["options","mode","value","onUpdate:value","filter-option"])):v("",!0),e.valueType==="multipleSelect"?(s(),p(re,{key:7,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,options:((X=e.valueEnum)==null?void 0:X.value)||e.valueEnum},null,8,["value","onUpdate:value","options"])):v("",!0),e!=null&&e.valueType?v("",!0):(s(),p(ue,ie({key:8,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,placeholder:"请输入",allowClear:""},e.otherProps,{style:{width:"100%"}}),null,16,["value","onUpdate:value"])),(e==null?void 0:e.valueType)=="searchSelect"?(s(),p(I,{key:9,value:n(l)[e.dataIndex],"onUpdate:value":u=>n(l)[e.dataIndex]=u,"show-search":"",allowClear:"",placeholder:"请输入",style:{width:"100%"},"filter-option":!1,"not-found-content":"暂无数据",options:b.data,onSearch:u=>n(oe)(e,u)},ge({_:2},[b.fetching?{name:"notFoundContent",fn:g(()=>[f(se,{size:"small"})]),key:"0"}:void 0]),1032,["value","onUpdate:value","options","onSearch"])):v("",!0)]}),_:2},1032,["label","name","rules"])]),_:2},1024))],64))),128)),n(c).length%3==1&&S.value?(s(),p(Y,{key:0,span:8})):v("",!0),n(c).length%3==0&&S.value?(s(),T(B,{key:1},[f(Y,{span:8}),f(Y,{span:8})],64)):v("",!0),f(Y,{span:C(n(c))},{default:g(()=>[f(A,{"label-col":{span:0},"wrapper-col":{span:24}},{default:g(()=>[f(q,null,{default:g(()=>[f(Y,{span:24,style:{"text-align":"right"}},{default:g(()=>[a.showReset?(s(),p(H,{key:0,onClick:t[0]||(t[0]=()=>{r.onCancel?r.onCancel():i()})},{default:g(()=>[J("重置")]),_:1})):v("",!0),a.showSubmit?(s(),p(H,{key:1,style:{margin:"0 0px 0 8px"},type:"primary","html-type":"submit"},{default:g(()=>[J(ye((r==null?void 0:r.submitText)||"查询"),1)]),_:1})):v("",!0),me(a.$slots,"formRender",{style:"margin: 0 0px 0 8px"},void 0,!0)]),_:3})]),_:3})]),_:3})]),_:3},8,["span"])]),_:3},8,["style"])],2)]),_:3},8,["model","label-col","wrapper-col"])])}}});const Qe=Ye(Ve,[["__scopeId","data-v-191fa55a"]]);export{Qe as C,Pe as _,Me as a};
