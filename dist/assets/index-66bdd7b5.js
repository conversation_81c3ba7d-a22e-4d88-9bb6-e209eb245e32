import{r as G,bb as F,ao as D,c,u as d,cK as L,cL as Q,cM as R,cN as Y,cO as w,aa as O,ae as H,am as W,ac as X,ad as j,b2 as ee,cP as te,a3 as oe,a9 as ae,cC as ne,aD as se,af as z,cQ as le,d as B,a as f,b as y,e as h,v as I,F as re,a1 as C,aP as E,Z as N,aB as x,y as P,aI as ie,aj as A,K as ce,b4 as K,aq as ue,ar as de}from"./index-db94d997.js";import{e as ve}from"./index-ec316fb4.js";import{i as T}from"./icon-831229e8.js";const M=Symbol(),_=G();function U(e,r=void 0){const a=F()?O(M,_):_;return e?c(()=>{var o,n;return(n=(o=a.value)==null?void 0:o[e])!=null?n:r}):a}const Ve=(e,r,a=!1)=>{var o;const n=!!F(),s=n?U():void 0,t=(o=r==null?void 0:r.provide)!=null?o:n?D:void 0;if(!t)return;const i=c(()=>{const u=d(e);return s!=null&&s.value?fe(s.value,u):u});return t(M,i),t(L,c(()=>i.value.locale)),t(Q,c(()=>i.value.namespace)),t(R,c(()=>i.value.zIndex)),t(Y,{size:c(()=>i.value.size||"")}),t(ve,c(()=>({emptyValues:i.value.emptyValues,valueOnClear:i.value.valueOnClear}))),(a||!_.value)&&(_.value=i.value),i},fe=(e,r)=>{const a=[...new Set([...w(e),...w(r)])],o={};for(const n of a)o[n]=r[n]!==void 0?r[n]:e[n];return o},Z=Symbol("buttonGroupContextKey"),be=(e,r)=>{H({from:"type.text",replacement:"link",version:"3.0.0",scope:"props",ref:"https://element-plus.org/en-US/component/button.html#button-attributes"},c(()=>e.type==="text"));const a=O(Z,void 0),o=U("button"),{form:n}=W(),s=X(c(()=>a==null?void 0:a.size)),t=j(),i=G(),u=ee(),b=c(()=>e.type||(a==null?void 0:a.type)||""),m=c(()=>{var l,g,p;return(p=(g=e.autoInsertSpace)!=null?g:(l=o.value)==null?void 0:l.autoInsertSpace)!=null?p:!1}),k=c(()=>e.tag==="button"?{ariaDisabled:t.value||e.loading,disabled:t.value||e.loading,autofocus:e.autofocus,type:e.nativeType}:{}),S=c(()=>{var l;const g=(l=u.default)==null?void 0:l.call(u);if(m.value&&(g==null?void 0:g.length)===1){const p=g[0];if((p==null?void 0:p.type)===te){const J=p.children;return/^\p{Unified_Ideograph}{2}$/u.test(J.trim())}}return!1});return{_disabled:t,_size:s,_type:b,_ref:i,_props:k,shouldAddSpace:S,handleClick:l=>{if(t.value||e.loading){l.stopPropagation();return}e.nativeType==="reset"&&(n==null||n.resetFields()),r("click",l)}}},ge=["default","primary","success","warning","info","danger","text",""],me=["button","submit","reset"],V=oe({size:ae,disabled:Boolean,type:{type:String,values:ge,default:""},icon:{type:T},nativeType:{type:String,values:me,default:"button"},loading:Boolean,loadingIcon:{type:T,default:()=>ne},plain:Boolean,text:Boolean,link:Boolean,bg:Boolean,autofocus:Boolean,round:Boolean,circle:Boolean,color:String,dark:Boolean,autoInsertSpace:{type:Boolean,default:void 0},tag:{type:se([String,Object]),default:"button"}}),pe={click:e=>e instanceof MouseEvent};function v(e,r=20){return e.mix("#141414",r).toString()}function ye(e){const r=j(),a=z("button");return c(()=>{let o={},n=e.color;if(n){const s=n.match(/var\((.*?)\)/);s&&(n=window.getComputedStyle(window.document.documentElement).getPropertyValue(s[1]));const t=new le(n),i=e.dark?t.tint(20).toString():v(t,20);if(e.plain)o=a.cssVarBlock({"bg-color":e.dark?v(t,90):t.tint(90).toString(),"text-color":n,"border-color":e.dark?v(t,50):t.tint(50).toString(),"hover-text-color":`var(${a.cssVarName("color-white")})`,"hover-bg-color":n,"hover-border-color":n,"active-bg-color":i,"active-text-color":`var(${a.cssVarName("color-white")})`,"active-border-color":i}),r.value&&(o[a.cssVarBlockName("disabled-bg-color")]=e.dark?v(t,90):t.tint(90).toString(),o[a.cssVarBlockName("disabled-text-color")]=e.dark?v(t,50):t.tint(50).toString(),o[a.cssVarBlockName("disabled-border-color")]=e.dark?v(t,80):t.tint(80).toString());else{const u=e.dark?v(t,30):t.tint(30).toString(),b=t.isDark()?`var(${a.cssVarName("color-white")})`:`var(${a.cssVarName("color-black")})`;if(o=a.cssVarBlock({"bg-color":n,"text-color":b,"border-color":n,"hover-bg-color":u,"hover-text-color":b,"hover-border-color":u,"active-bg-color":i,"active-border-color":i}),r.value){const m=e.dark?v(t,50):t.tint(50).toString();o[a.cssVarBlockName("disabled-bg-color")]=m,o[a.cssVarBlockName("disabled-text-color")]=e.dark?"rgba(255, 255, 255, 0.5)":`var(${a.cssVarName("color-white")})`,o[a.cssVarBlockName("disabled-border-color")]=m}}}return o})}const ke=B({name:"ElButton"}),Ce=B({...ke,props:V,emits:pe,setup(e,{expose:r,emit:a}){const o=e,n=ye(o),s=z("button"),{_ref:t,_size:i,_type:u,_disabled:b,_props:m,shouldAddSpace:k,handleClick:S}=be(o,a),$=c(()=>[s.b(),s.m(u.value),s.m(i.value),s.is("disabled",b.value),s.is("loading",o.loading),s.is("plain",o.plain),s.is("round",o.round),s.is("circle",o.circle),s.is("text",o.text),s.is("link",o.link),s.is("has-bg",o.bg)]);return r({ref:t,size:i,type:u,disabled:b,shouldAddSpace:k}),(l,g)=>(f(),y(x(l.tag),ie({ref_key:"_ref",ref:t},d(m),{class:d($),style:d(n),onClick:d(S)}),{default:h(()=>[l.loading?(f(),I(re,{key:0},[l.$slots.loading?C(l.$slots,"loading",{key:0}):(f(),y(d(E),{key:1,class:N(d(s).is("loading"))},{default:h(()=>[(f(),y(x(l.loadingIcon)))]),_:1},8,["class"]))],64)):l.icon||l.$slots.icon?(f(),y(d(E),{key:1},{default:h(()=>[l.icon?(f(),y(x(l.icon),{key:0})):C(l.$slots,"icon",{key:1})]),_:3})):P("v-if",!0),l.$slots.default?(f(),I("span",{key:2,class:N({[d(s).em("text","expand")]:d(k)})},[C(l.$slots,"default")],2)):P("v-if",!0)]),_:3},16,["class","style","onClick"]))}});var _e=A(Ce,[["__file","button.vue"]]);const Be={size:V.size,type:V.type},Se=B({name:"ElButtonGroup"}),he=B({...Se,props:Be,setup(e){const r=e;D(Z,ce({size:K(r,"size"),type:K(r,"type")}));const a=z("button");return(o,n)=>(f(),I("div",{class:N(d(a).b("group"))},[C(o.$slots,"default")],2))}});var q=A(he,[["__file","button-group.vue"]]);const ze=ue(_e,{ButtonGroup:q});de(q);export{ze as E,Ve as p,U as u};
