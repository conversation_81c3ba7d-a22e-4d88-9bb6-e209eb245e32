import{_ as De}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as L,d as ue,I as re,r as f,K as ae,o as de,D as ce,w as Ce,a as m,b as O,bX as Ke,e as a,f as e,h as r,u as t,v as D,y as P,i as d,U as Te,q as Ne,bc as Y,bg as _e,be as Me,bf as pe,aw as fe,ax as me,bY as be,bh as ve,bi as ge,_ as ye,c as Pe,g as V,F as qe,x as Ve,n as Oe,s as Re,V as Ue,av as Be,bn as Ye}from"./index-db94d997.js";import{g as ze}from"./index-5fcafee1.js";import{g as $e}from"./index-0abb3d3a.js";import{D as Ae}from"./dayjs-a8e42122.js";import{_ as ke}from"./index-39334618.js";import{_ as Ee}from"./index-42d7fb9b.js";import{w as ie,a as ee}from"./dictLocal-9822709a.js";import"./CForm-ffa1b2bc.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";function Fe(h){return L({url:"/web/workflow/v1/todo",method:"POST",isTable:!0,data:h})}function Le(h){return L({url:"/web/workflow/v1/finish",method:"POST",isTable:!0,data:h})}function je(h){return L({url:"/web/workflow/v1/hasStart",method:"POST",isTable:!0,data:h})}function te(h){return L({url:"/web/workflow/v1/approval",method:"POST",data:h})}const He={key:0,class:"examine"},Ge={key:1,class:"sell"},Je=ue({__name:"OrderModal",props:{visible:{type:Boolean,required:!0},recordData:{type:Object,required:!0},currentKey:{type:String,required:!0}},emits:["setVisible"],setup(h,{emit:I}){const l=h,j=re(),C=f({}),g=f(!1),w=f(!1),S=f();let n=ae({});de(()=>{console.log("1111")}),ce(()=>{console.log("222")});const K=()=>{var b,i,x,N;l.recordData&&(((b=l.recordData)==null?void 0:b.businessKey)=="businessKeyYuTaiStationInfoExamine"?ze({pageNum:1,pageSize:10,id:(i=l.recordData)==null?void 0:i.relationId}).then(v=>{for(let o in v.result[0])n[o]=v.result[0][o];console.log("forms---exmaine",n,n.examineDate),n.examineDate="2024-10-23 15:31:38",C.value=v.result[0]}):((x=l.recordData)==null?void 0:x.businessKey)=="businessKeyAssetSell"&&$e({pageNum:1,pageSize:10,id:(N=l.recordData)==null?void 0:N.relationId}).then(v=>{for(let o in v.result[0])n[o]=v.result[0][o];console.log("forms---sell",n),C.value=v.result[0]}))},z=()=>{console.log("form按钮--",C.value),I("setVisible",!1),j.push({path:"/pAssetManage/assetManage/checkAsset/detail",state:{pdata:Ne.cloneDeep(C.value)}})},T=()=>{let b={msg:n.msg,processInstanceId:l.recordData.processInstanceId,activitiId:l.recordData.activitiId,assigneeId:l.recordData.assigneeId,approvalType:"1"};g.value=!0,te(b).then(i=>{console.log(i),g.value=!1,Y.success("审核通过成功"),I("setVisible",!1)}).catch(i=>{g.value=!1})},H=()=>{let b={msg:n.msg,processInstanceId:l.recordData.processInstanceId,activitiId:l.recordData.activitiId,assigneeId:l.recordData.assigneeId,approvalType:"2"};w.value=!0,te(b).then(i=>{console.log(i),w.value=!1,Y.success("审核驳回成功"),I("setVisible",!1)}).catch(i=>{w.value=!1})},G=()=>{I("setVisible",!1)};return Ce(()=>l.visible,b=>{var i;if(b)(i=S.value)==null||i.resetFields(),console.log(l.recordData),K();else for(let x in n)n[x]=""}),(b,i)=>{const x=_e,N=ke,v=Me,o=pe,u=fe,p=me,J=Ae,U=be,Q=Ee,X=ve,W=ge;return m(),O(W,{class:"order_modal",visible:l.visible,maskClosable:!1,title:"审批单",width:"800px",onOk:T,onCancel:G},Ke({default:a(()=>{var M,$;return[e(X,{ref_key:"formRef",ref:S,model:t(n),"label-col":((M=l.recordData)==null?void 0:M.businessKey)=="businessKeyYuTaiStationInfoExamine"?{span:3}:{span:8},"wrapper-col":(($=l.recordData)==null?void 0:$.businessKey)=="businessKeyYuTaiStationInfoExamine"?{span:21}:{span:16}},{default:a(()=>{var A,E;return[((A=l.recordData)==null?void 0:A.businessKey)=="businessKeyYuTaiStationInfoExamine"?(m(),D("div",He,[e(p,{gutter:24},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"检查单号",name:"relationCode"},{default:a(()=>[e(v,{value:t(n).relationCode,"onUpdate:value":i[0]||(i[0]=s=>t(n).relationCode=s),required:"",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"检查站数量",name:"stationNum"},{default:a(()=>[e(v,{value:t(n).stationNum,"onUpdate:value":i[1]||(i[1]=s=>t(n).stationNum=s),required:"",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"检查时间",name:"examineDate"},{default:a(()=>[e(J,{style:{width:"100%"},value:t(n).examineDate,"onUpdate:value":i[2]||(i[2]=s=>t(n).examineDate=s),"value-format":"YYYY-MM-DD",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"存在问题",name:"existingProblem"},{default:a(()=>[e(v,{value:t(n).existingProblem,"onUpdate:value":i[3]||(i[3]=s=>t(n).existingProblem=s),required:"",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"整改建议",name:"rectificationSuggestion"},{default:a(()=>[e(U,{value:t(n).rectificationSuggestion,"onUpdate:value":i[4]||(i[4]=s=>t(n).rectificationSuggestion=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"整改情况",name:"correctSituation"},{default:a(()=>[e(U,{value:t(n).correctSituation,"onUpdate:value":i[5]||(i[5]=s=>t(n).correctSituation=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:3}),e(u,{span:21},{default:a(()=>[e(x,{type:"primary",onClick:z},{default:a(()=>[r("检查清单")]),_:1})]),_:1})]),_:1})])):P("",!0),((E=l.recordData)==null?void 0:E.businessKey)=="businessKeyAssetSell"?(m(),D("div",Ge,[e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"拟出售电站编号",name:"stationCode"},{default:a(()=>[r(d(t(n).stationCode),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"业主名称",name:"stationName"},{default:a(()=>[r(d(t(n).stationName),1)]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"产权公司",name:"companyName"},{default:a(()=>[r(d(t(n).companyName),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"实际组件数量",name:"subassemblyNum"},{default:a(()=>[r(d(t(n).subassemblyNum),1)]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"实际装机容量",name:"capins"},{default:a(()=>[r(d(t(n).capins),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"合同约定每瓦单价",name:"capinsUnit"},{default:a(()=>[r(d(t(n).capinsUnit),1)]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"合同约定合作年限",name:"agreedTermYear"},{default:a(()=>[r(d(t(n).agreedTermYear),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"并网时间",name:"datongrd"},{default:a(()=>[r(d(t(Te)(t(n).datongrd)),1)]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"结束时间",name:"endMonth"},{default:a(()=>[r(d(t(n).endMonth),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"电站原值",name:"originalValue"},{default:a(()=>[r(d(t(n).originalValue),1)]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"合同约定残值率",name:"salvageRate"},{default:a(()=>[r(d(t(n).salvageRate),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"电站净值",name:"netValue"},{default:a(()=>[r(d(t(n).netValue),1)]),_:1})]),_:1})]),_:1}),e(p,{gutter:24},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(o,{label:"产品总价",name:"productPriceSum"},{default:a(()=>[r(d(t(n).productPriceSum),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(o,{label:"产品售价",name:"productPrice"},{default:a(()=>[r(d(t(n).productPrice),1)]),_:1})]),_:1})]),_:1})])):P("",!0),l.currentKey==="1"?(m(),O(Q,{key:2})):P("",!0),l.currentKey==="1"?(m(),O(p,{key:3},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(o,{label:"审核意见",name:"msg","label-col":{span:3},"wrapper-col":{span:21}},{default:a(()=>[e(U,{value:t(n).msg,"onUpdate:value":i[6]||(i[6]=s=>t(n).msg=s),required:"","show-count":"",maxlength:200},null,8,["value"])]),_:1})]),_:1})]),_:1})):P("",!0)]}),_:1},8,["model","label-col","wrapper-col"])]}),_:2},[l.currentKey==="1"?{name:"footer",fn:a(()=>[e(N,null,{default:a(()=>[e(x,{key:"submit",type:"primary",loading:g.value,onClick:T},{default:a(()=>[r("审核通过")]),_:1},8,["loading"]),e(x,{loading:w.value,onClick:H},{default:a(()=>[r("审核驳回")]),_:1},8,["loading"])]),_:1})]),key:"0"}:{name:"footer",fn:a(()=>[]),key:"1"}]),1032,["visible"])}}});const Qe=ye(Je,[["__scopeId","data-v-533b4ab6"]]),Xe={class:"areaPrice"},We={class:"areaPrice_table"},Ze={class:"table_header"},ea={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","align-items":"center"}},aa={style:{display:"inline-block"}},ta={key:0,style:{"border-bottom":"2px solid #29cca0","margin-left":"15px","margin-right":"15px"}},sa={class:"text-hide"},na={key:0,class:"status_tag"},la={class:"tag_three"},oa={key:1,class:"status_tag"},ia={class:"tag_one"},ua={key:2,class:"status_tag"},ra={class:"tag_two"},da=ue({__name:"index",setup(h){const I=f();f(history.state.pdata);const l=f("1"),j=f(!1),C=f(!1),g=f({}),w=ae({msg:""}),S=f(!1),n=f(!1),K=f(""),z=f(),T=ae({businessKey:""}),H=[{title:"审批流类型",dataIndex:"businessKey",valueType:"select",width:120,valueEnum:ie,fixed:"left",search:!1},{title:"发起时间",dataIndex:"startTime",search:!1,resizable:!0,width:120},{title:"发起人",dataIndex:"startUserName",search:!1,resizable:!0,width:120},{title:"状态",key:"flowStatus",dataIndex:"flowStatus",render:!0,search:!1,resizable:!0,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:120}],G=Pe(()=>{if(l.value==="1")return Fe;if(l.value==="2")return Le;if(l.value==="3")return je}),b=f([]),i=()=>{b.value=[{patternCode:"1",patternName:"我的待办"},{patternCode:"2",patternName:"我的已办"},{patternCode:"3",patternName:"我的发起"}],l.value=b.value[0].patternCode},x=s=>{j.value=!0,l.value!==s&&(l.value=s,Ue(()=>{I.value.reload()}))};de(()=>{i()}),ce(()=>{I.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&M()});const N=re(),v=s=>{s.businessKey==="businessKeyIncomeConfirm"?N.push({path:"/financeManage/incomeManage/station/incomeConfirm/list",query:{goBack:1,relationId:s==null?void 0:s.relationId}}):s.businessKey==="businessKeyOtherIncomeConfirm"?N.push({path:"/financeManage/incomeManage/other/otherIncomeConfirm/list",query:{goBack:1,relationId:s==null?void 0:s.relationId}}):s.businessKey==="businessKeyErrorPayment"&&N.push({path:"/financeManage/everydayManage/mistakenTransfer/list",query:{goBack:1,relationId:s==null?void 0:s.relationId}})},o=()=>{w.msg=""},u=(s,y)=>{o(),S.value=!0,K.value=s,g.value=y},p=s=>{z.value.validateFields().then(y=>{let F={msg:y.msg,processInstanceId:g.value.processInstanceId,activitiId:g.value.activitiId,assigneeId:g.value.assigneeId,approvalType:K.value};n.value=!0,te(F).then(q=>{console.log(q),S.value=!1,n.value=!1,K.value==="1"?Y.success("审核通过"):K.value==="2"?Y.success("审核驳回"):Y.success("操作成功"),M()}).catch(q=>{n.value=!1})})},J=()=>{S.value=!1},U=s=>{C.value=!0,g.value=s||{}},Q=s=>{C.value=s,M()},X=f([]),W=s=>{X.value=s||[]},M=()=>{var s;(s=I.value)==null||s.reload()},$=()=>{M()},A=()=>{T.businessKey="",M()},E=(s,y)=>{let q={businessKey:T.businessKey?new Array(1).fill(T.businessKey):""};return new Promise(R=>{const k={...q,...s,noJoin:!0,delStatus:0};console.log(k,s,"===="),R(k)})};return(s,y)=>{const F=Be,q=pe,R=fe,k=_e,Z=ke,se=me,he=Ye,xe=De,Ie=be,we=ve,Se=ge;return m(),D("div",Xe,[V("div",We,[V("div",Ze,[e(se,{span:24},{default:a(()=>[e(R,{span:8},{default:a(()=>[e(q,{label:"审批流类型"},{default:a(()=>[e(F,{options:t(ie),value:t(T).businessKey,"onUpdate:value":y[0]||(y[0]=c=>t(T).businessKey=c)},null,8,["options","value"])]),_:1})]),_:1}),e(R,{span:8}),e(R,{span:8},{default:a(()=>[V("div",ea,[e(Z,null,{default:a(()=>[e(k,{onClick:A},{default:a(()=>[r("重置")]),_:1}),e(k,{type:"primary",onClick:$},{default:a(()=>[r("查询")]),_:1})]),_:1})])]),_:1})]),_:1})]),e(xe,{columns:H,ref_key:"actionRef",ref:I,request:t(G),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:W,"before-query-params":E,search:!1},{tableHeader:a(()=>[e(Z,null,{default:a(()=>[e(k,{style:{display:"none"},type:"primary"})]),_:1})]),tableHeaderLeft:a(()=>[(m(!0),D(qe,null,Ve(t(b),c=>(m(),D("div",aa,[e(k,{type:"link",style:Oe({color:t(l)===c.patternCode?"#29cca0":"#333"}),onClick:_=>x(c.patternCode)},{default:a(()=>[r(d(c.patternName),1)]),_:2},1032,["style","onClick"]),t(l)===c.patternCode?(m(),D("div",ta)):P("",!0)]))),256))]),businessKeyRender:a(({column:c,record:_,index:ne})=>[e(he,null,{title:a(()=>[r(d(_.businessKeyDesc),1)]),default:a(()=>[V("span",sa,d(_.businessKeyDesc),1)]),_:2},1024)]),flowStatusRender:a(({column:c,record:_,index:ne})=>{var B,le,oe;return[_[c.dataIndex]=="1"?(m(),D("span",na,[V("span",la,d((B=t(ee)[_[c.dataIndex]-1])==null?void 0:B.label),1)])):_[c.dataIndex]=="2"?(m(),D("span",oa,[V("span",ia,d((le=t(ee)[_[c.dataIndex]-1])==null?void 0:le.label),1)])):_[c.dataIndex]=="3"?(m(),D("span",ua,[V("span",ra,d((oe=t(ee)[_[c.dataIndex]-1])==null?void 0:oe.label),1)])):P("",!0)]}),actionRender:a(({column:c,record:_,index:ne})=>[_.businessKey==="businessKeyYuTaiStationInfoExamine"||_.businessKey==="businessKeyAssetSell"?(m(),O(k,{key:0,type:"link",size:"small",onClick:B=>U(_)},{default:a(()=>[r("查看明细")]),_:2},1032,["onClick"])):(m(),O(Z,{key:1},{default:a(()=>[e(k,{type:"link",size:"small",onClick:()=>v(_)},{default:a(()=>[r("查看明细")]),_:2},1032,["onClick"]),_.flowStatus===1&&t(l)==="1"?(m(),O(k,{key:0,type:"link",size:"small",onClick:B=>u("2",_)},{default:a(()=>[r("驳回")]),_:2},1032,["onClick"])):P("",!0),_.flowStatus===1&&t(l)==="1"?(m(),O(k,{key:1,type:"link",size:"small",onClick:B=>u("1",_)},{default:a(()=>[r("通过")]),_:2},1032,["onClick"])):P("",!0)]),_:2},1024))]),_:1},8,["request"])]),e(Qe,{visible:t(C),currentKey:t(l),recordData:t(g),onSetVisible:Q},null,8,["visible","currentKey","recordData"]),e(Se,{visible:t(S),"onUpdate:visible":y[2]||(y[2]=c=>Re(S)?S.value=c:null),title:"填写审批意见","confirm-loading":t(n),destroyOnClose:!0,onOk:p,onCancel:J,okText:"确定"},{default:a(()=>[e(we,{model:t(w),ref_key:"formRef",ref:z,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:a(()=>[e(se,{span:24},{default:a(()=>[e(R,{span:24},{default:a(()=>[e(q,{label:t(K)==="2"?"驳回原因":"审核意见",name:"msg",required:t(K)==="2"},{default:a(()=>[e(Ie,{value:t(w).msg,"onUpdate:value":y[1]||(y[1]=c=>t(w).msg=c),placeholder:"请输入",rows:3},null,8,["value"])]),_:1},8,["label","required"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])])}}});const xa=ye(da,[["__scopeId","data-v-49d1c8eb"]]);export{xa as default};
