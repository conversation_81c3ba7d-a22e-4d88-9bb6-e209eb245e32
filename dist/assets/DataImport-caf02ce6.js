import{_ as U}from"./index-e7bdfdf4.js";import{_ as V}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as R,r as _,o as q,c8 as K,a1 as G,a2 as x,bP as j,c9 as H,bL as J,a as i,v as c,f as l,e as n,g as h,h as m,u,y as k,F as g,x as Q,i as I,s as A,bd as W,bc as X,bg as Y,bi as Z,p as ee,j as te,_ as ae}from"./index-db94d997.js";import{e as se}from"./empty1-348ef1fe.js";import{_ as oe}from"./index-83ca18bc.js";import{_ as ne}from"./index-39334618.js";import"./CaretUpOutlined-7e71a64b.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const le=R({__name:"index",props:{permsKey:{}},setup(o){const v=o,f=_(!1);return q(()=>{if(window.self!==window.top)f.value=!0;else{const{permsKey:r}=v,w=K("auth").find(b=>b.action===r);f.value=!!w}}),(r,d)=>G(r.$slots,"default",{key:0})}});function de(o){return x({url:"/web/ptvtTemplateManage/v1/list",method:"POST",data:o})}function re(o){return x({url:"/web/ptvtTemplateRecord/v1/page",method:"POST",isTable:!0,data:o})}function ie(o){return x({url:"/web/ptvtTemplateRecord/v1/delete",method:"GET",data:o})}function ce(o){return j({url:"/web/ptvtTemplateRecord/v1/download",method:"GET",data:o})}const pe=o=>(ee("data-v-dc388059"),o=o(),te(),o),ue={class:"table-button-box"},me={key:0},_e={key:1},fe={key:2},ye={key:0,class:"downLoad_list"},he={class:"item_name"},ve={key:1,class:"empty"},we=["src"],be=pe(()=>h("div",null,"暂无数据",-1)),Se=R({__name:"DataImport",setup(o){const{showModal:v,setFileState:f}=H(),r=_(!1),d=J(),w=(a,s)=>new Promise(t=>{let p={};p={...p,...a,templateType:d.query.templateType},t(p)}),b=_([{title:"导入时间",dataIndex:"importTime",search:!1,width:100},{title:"文件名称",dataIndex:"fileName",search:!1,width:140},{title:"导入状态",dataIndex:"dataStatus",key:"dataStatus",search:!1,render:!0,width:100},{title:"数据行数",dataIndex:"dataCount",search:!1,width:90},{title:"成功行数",dataIndex:"dataSuccessCount",search:!1,width:90},{title:"失败行数",dataIndex:"dataFailCount",search:!1,width:90},{title:"操作人",dataIndex:"createBy",search:!1,width:100},{title:"操作",key:"action",width:120,fixed:"right",render:!0}]),C=_(),S=()=>{var a;(a=C.value)==null||a.reload()},F=()=>{P()},L=a=>{var t;const s={fileId:a.fileId,companyCode:(t=d.query)==null?void 0:t.companyCode};W(s)},P=()=>{const a=t=>{t&&JSON.parse(t.successNum)>0&&S()},s=t=>{console.log("上传成功",t),S()};f({title:"批量上传",importUrl:"/web/ptvtTemplateRecord/v1/upload",accept:d.query.fileType||".xls,.xlsx",upLoadSuccess:s,downloadText:" ",onClickResultOk:a,visible:!0,isTemplate:!1,fileSize:d.query.fileSize||30,data:{templateType:d.query.templateType}}),v()},N=a=>{const s={id:a.id};ce(s)},$=a=>{var t;const s={id:a.id,companyCode:(t=d.query)==null?void 0:t.companyCode};ie(s).then(p=>{X.success("删除成功"),S()})},T=_([]),B=()=>{const a={templateType:d.query.templateType};de(a).then(s=>{T.value=s})};return q(()=>{B()}),(a,s)=>{const t=Y,p=le,D=oe,E=ne,M=V,z=U,O=Z;return i(),c(g,null,[l(z,{title:"批量上传列表",itemKey:"1",defaultShow:!0,isHideSwitch:!0},{btnRender:n(()=>[h("div",ue,[l(t,{onClick:F},{default:n(()=>[m("批量上传")]),_:1}),l(t,{type:"primary",onClick:s[0]||(s[0]=e=>r.value=!0)},{default:n(()=>[m("下载模板")]),_:1})])]),default:n(()=>[l(M,{columns:u(b),ref_key:"actionRef",ref:C,scroll:{x:790},"label-col":{span:6},"wrapper-col":{span:18},"before-query-params":w,"default-query":!0,search:!1,request:u(re)},{dataStatusRender:n(({record:e})=>[e.dataStatus==1||e.dataStatus==2?(i(),c("div",me,"导入中")):k("",!0),e.dataStatus==3?(i(),c("div",_e,"导入完成")):k("",!0),e.dataStatus==4?(i(),c("div",fe,"导入失败")):k("",!0)]),actionRender:n(({record:e})=>[l(E,null,{default:n(()=>[l(p,{"perms-key":"datalmport:list:download"},{default:n(()=>[l(t,{size:"small",type:"link",onClick:y=>N(e),disabled:e.dataStatus==1||e.dataStatus==2},{default:n(()=>[m(" 下载反馈文件 ")]),_:2},1032,["onClick","disabled"])]),_:2},1024),l(p,{"perms-key":"datalmport:list:del"},{default:n(()=>[l(D,{title:"确认删除?",onConfirm:y=>$(e)},{default:n(()=>[l(t,{size:"small",type:"link"},{default:n(()=>[m(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)]),_:2},1024)]),_:1},8,["columns","request"])]),_:1}),l(O,{visible:u(r),"onUpdate:visible":s[1]||(s[1]=e=>A(r)?r.value=e:null),footer:null,width:"35%",title:"下载模版"},{default:n(()=>[u(T).length>0?(i(),c("div",ye,[(i(!0),c(g,null,Q(u(T),(e,y)=>(i(),c("div",{class:"item",key:(e==null?void 0:e.id)||y},[h("div",he,I(y+1)+"."+I(e==null?void 0:e.templateName),1),l(t,{class:"item_btn",type:"primary",onClick:Te=>L(e)},{default:n(()=>[m("下载")]),_:2},1032,["onClick"])]))),128))])):(i(),c("div",ve,[h("img",{src:u(se),alt:""},null,8,we),be]))]),_:1},8,["visible"])],64)}}});const Be=ae(Se,[["__scopeId","data-v-dc388059"]]);export{Be as default};
