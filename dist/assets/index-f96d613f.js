import{_ as ye}from"./index-6d6caf4c.js";import{d as he,r as m,K,I as _e,o as be,D as ve,a as f,v as W,g as k,f as r,e as i,y as v,b as g,h as j,u as c,en as Ce,F as Ie,bi as J,eo as we,bc as E,q as Re,H as De,ep as Ne,eq as ke,er as Ue,es as Be,bM as xe,bN as Oe,bg as Se,be as Ke,bf as Ee,av as Le,bh as Te,p as Me,j as Qe,_ as Ve}from"./index-db94d997.js";import{g as He}from"./index-4185a861.js";import{E as Fe}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as qe}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const We="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADaSURBVHgBnVIBDYNADOwvCHgJzAEOhgQcsClgDsAJEpCwOWAOwAE4+F1DyZpPCbBLLm249p7v15FCCMEjpODsnBspAvSMI7QPWUDBEBZMa7HSmvDD02rOpdGDHdga5iVYcG4ZeDHoJJaR/hKTnmu2rpBKc72h9WLk1++JLuLBQZyRvmMD0UakI/LZNNiCnNiABXjVWrLTmCLcwQrkU3PreeMmHlQldw0Sb3QUahfaU42RQX20/hI18/Z5+gdqB8xFOvIHXvigZQ8yOgNZ5UFOn+QJz4Mnr1d1D1/BufDqDCiKEgAAAABJRU5ErkJggg==",je="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADBSURBVHgBtZOhDsIwFEUvyxKmwM3hcHwB/MTcHDj+CIfkK/iBzaGQKCwK1HC8m7wmj4aONstOctN26T1p2gwYyMRb15IF4mgkbW4+sFhKjhFl7qt8AekkrwgB95w4yZDG1MxnVlBGlOeSnWSp5coKts6orLRgy7zgq+RmrVnPUWst2nLrb8wDgouk0CJC5T4BaXTsVIhUgZUESX1GR/HrBA8d9/gPL/nuC/iMZ3w/p+OtsesnJ+5n2kjWCMPCAWPwAdeeHOw75jEoAAAAAElFTkSuQmCC",Z=C=>(Me("data-v-100fadb8"),C=C(),Qe(),C),Je={class:"areaPrice"},Ze={style:{width:"100%",display:"flex","justify-content":"space-between"}},Pe={key:0,class:"header_title"},Xe=Z(()=>k("img",{src:We},null,-1)),Ge=Z(()=>k("img",{src:je},null,-1)),Ye=he({__name:"index",setup(C){const A=m(),P=["provinceCodeList","cityCodeList","areaCodeList"],X=m([]),U=m([]),L=m([]),y=m([]),T=m([]),h=m(1),u=m(!1),I=m(!1),w=m(!1),s=K({userName:"",mobile:"",identityCard:"",companyIds:[],rolesIds:[]}),B=m(),G=_e(),Y=[{title:"用户名称",key:"userName",dataIndex:"userName",width:120},{title:"手机号",key:"mobile",dataIndex:"mobile",width:120,rightWidth:130},{title:"归属公司",key:"companyName",dataIndex:"companyName",valueType:"select",valueEnum:L,width:120,hideInTable:!0},{title:"归属公司",key:"companyName",dataIndex:"companyName",valueType:"select",valueEnum:U,checkInfo:{checkFieldList:"companyInfoUser",fieldLabel:"companyName",fieldValue:"id",fieldValueReplace:"companyInfoId",goUrl:"/system/company/detail/list"},width:120,search:!1,rightWidth:160},{title:"创建人",key:"createUser",dataIndex:"createUser",width:120,rightWidth:130},{title:"最近修改时间",dataIndex:"updateTime",search:!1,resizable:!0,width:100,rightWidth:130}],z={userName:[{required:!0,message:"用户姓名不能为空",trigger:"change"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9_·]{1,50}$/,message:"请输入正确的用户名（支持中文、·、英文、_）",trigger:"change"}],mobile:[{required:!0,message:"手机号不能为空",trigger:"change"},{pattern:/^1[1-9]\d{9}$/,message:"请输入正确的手机号",trigger:"change"}],identityCard:[{required:!1,message:"",trigger:"change"},{pattern:/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,message:"请输入正确的身份证号",trigger:"change"}],companyIds:[{required:!0,message:"归属公司不能为空",trigger:"change"}],rolesIds:[{required:!0,message:"角色不能为空",trigger:"change"}]},$=e=>{J.confirm({title:"删除提示",icon:r(Fe),content:"确认要删除该用户吗？",onOk(){const t={ids:e.id};we(t).then(a=>{E.success("删除成功"),N()})},onCancel(){console.log("Cancel")}})},ee=e=>{G.push({path:"/system/user/detail",state:{pdata:Re.cloneDeep(e)}})},te=()=>{I.value=!0},ae=()=>{h.value=1,u.value=!1},x=m(),R=K({rolesIds:[]}),oe={rolesIds:[{required:!0,message:"角色不能为空",trigger:"change"}]},se=()=>{x.value.validate().then(async e=>{if(e){let t=[];for(let l of A.value.selectArr)t.push(l.id);const a={userIds:t,rolesIds:De(R.rolesIds)};await Ne(a)=="成功"&&(E.success("分配成功"),A.value.cancel(),M(),u.value=!1,N())}})},M=()=>{x.value.resetFields(),I.value=!1},le=()=>{A.value.selectAll()},ne=e=>{w.value=!0},re=()=>{B.value.validate().then(async e=>{if(e){const t={userName:s.userName,mobile:s.mobile,identityCard:s.identityCard,companyIds:s.companyIds,rolesIds:s.rolesIds};await ke(t)=="成功"&&(E.success("新建成功"),Q())}})},Q=()=>{for(let e of y.value)e.disabled=!1;B.value.resetFields(),w.value=!1,N()},ie=(e,t)=>{let a=0;for(let o of e)o==d.safeRoles?a=1:o==d.systemRoles?a=2:o==d.safeAuditRoles&&(a=3);a==0?D(0):a==1?(D(d.safeRoles),s.rolesIds=[],s.rolesIds=[d.safeRoles]):a==2?(D(d.systemRoles),s.rolesIds=[],s.rolesIds=[d.systemRoles]):a==3&&(D(d.safeAuditRoles),s.rolesIds=[],s.rolesIds=[d.safeAuditRoles])},D=e=>{if(e>0)for(let t of y.value)t.id!=e&&(t.disabled=!0);else for(let t of y.value)t.disabled=!1},d=K({safeRoles:"",systemRoles:"",safeAuditRoles:""}),de=async()=>{const e=await Ue();d.safeRoles=e.safeRoles,d.systemRoles=e.systemRoles,d.safeAuditRoles=e.safeAuditRoles,me()};be(()=>{ce(),de()}),ve(()=>{A.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&N()});const ce=()=>{He().then(e=>{let t=e||[],a=t.map(l=>({...l,label:l.companyName,value:l.id}));U.value=a;let o=t.map(l=>({...l,label:l.companyName,value:l.companyName}));L.value=o})},me=()=>{Be().then(e=>{let t=e||[];y.value=t;const a=JSON.parse(JSON.stringify(t));T.value=a.filter(o=>o.id!==d.safeRoles&&o.id!==d.systemRoles&&o.id!==d.safeAuditRoles)})},ue=(e,t)=>t.companyName.toLowerCase().indexOf(e.toLowerCase())>=0,V=(e,t)=>t.roleName.toLowerCase().indexOf(e.toLowerCase())>=0,pe=m([]),fe=e=>{pe.value=e||[],console.log("dataSource=",e)},N=()=>{var e;(e=A.value)==null||e.reload()},Ae=e=>{const t=new Map;return e.forEach(a=>{const o=P[a.level-1];if(t.has(o))t.get(o).push(a.value);else{let l=[];l.push(a.value),t.set(o,l)}}),Object.fromEntries(t)},H=(e,t,a)=>!e||!t?[]:(e.forEach(o=>{t.find(_=>o.value===_)&&a.push(o),o.children&&o.children.length>0&&H(o.children,t,a)}),a),ge=(e,t)=>new Promise(a=>{const o=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[0]:"",l=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[1]:"";let _={delStatus:0,noJoin:!0,startTime:o,endTime:l};const O=H(X.value,e==null?void 0:e.cityTree,[]);let b=Ae(O);e==null||delete e.yKey;const p={..._,...e,...b};a(p)});return(e,t)=>{const a=xe,o=Oe,l=Se,_=qe,O=ye,b=Ke,p=Ee,S=Le,F=Te,q=J;return f(),W(Ie,null,[k("div",Je,[r(O,{columns:Y,ref_key:"actionRef",ref:A,request:c(Ce),onGetDataSource:fe,"before-query-params":ge,activeKey:h.value,isAdd:!0,isDelete:!0,isSee:!0,addText:"新建用户",batchVisible:u.value,onAdd:ne,onDelete:$,onSee:ee,onCancel:ae,onConfirm:te},{tableHeader:i(()=>[k("div",Ze,[u.value?v("",!0):(f(),W("span",Pe,"用户列表")),u.value?(f(),g(o,{key:1,activeKey:h.value,"onUpdate:activeKey":t[0]||(t[0]=n=>h.value=n)},{default:i(()=>[(f(),g(a,{key:1,tab:"用户列表"})),(f(),g(a,{key:2,tab:`已选列表（${A.value.selectArr.length}）`},null,8,["tab"]))]),_:1},8,["activeKey"])):v("",!0),h.value==1?(f(),g(_,{key:2},{default:i(()=>[u.value?v("",!0):(f(),g(l,{key:0,type:"primary",class:"common-btn",onClick:t[1]||(t[1]=n=>u.value=!0)},{icon:i(()=>[Xe]),default:i(()=>[j(" 批量分配 ")]),_:1})),u.value?(f(),g(l,{key:1,class:"common-btn",onClick:le},{icon:i(()=>[Ge]),default:i(()=>[j(" 全选 ")]),_:1})):v("",!0)]),_:1})):v("",!0)])]),_:1},8,["request","activeKey","batchVisible"])]),r(q,{visible:w.value,"onUpdate:visible":t[7]||(t[7]=n=>w.value=n),title:"新建用户",onOk:re,onCancel:Q,width:"446px"},{default:i(()=>[r(F,{model:c(s),rules:z,name:"formRef",ref_key:"formRef",ref:B,"label-col":{style:{width:"100px"}},autocomplete:"off"},{default:i(()=>[r(p,{name:"userName",label:"用户姓名"},{default:i(()=>[r(b,{value:c(s).userName,"onUpdate:value":t[2]||(t[2]=n=>c(s).userName=n),placeholder:"请输入",maxlength:50},null,8,["value"])]),_:1}),r(p,{name:"mobile",label:"手机号"},{default:i(()=>[r(b,{value:c(s).mobile,"onUpdate:value":t[3]||(t[3]=n=>c(s).mobile=n),placeholder:"请输入"},null,8,["value"])]),_:1}),r(p,{name:"identityCard",label:"身份证号"},{default:i(()=>[r(b,{value:c(s).identityCard,"onUpdate:value":t[4]||(t[4]=n=>c(s).identityCard=n),placeholder:"请输入"},null,8,["value"])]),_:1}),r(p,{name:"companyIds",label:"归属公司"},{default:i(()=>[r(S,{placeholder:"请选择",value:c(s).companyIds,"onUpdate:value":t[5]||(t[5]=n=>c(s).companyIds=n),allowClear:"","show-search":"",mode:"multiple",options:U.value,"filter-option":ue,maxTagCount:5},null,8,["value","options"])]),_:1}),r(p,{name:"rolesIds",label:"角色"},{default:i(()=>[r(S,{placeholder:"请选择",value:c(s).rolesIds,"onUpdate:value":t[6]||(t[6]=n=>c(s).rolesIds=n),allowClear:"","show-search":"",mode:"multiple",options:y.value,fieldNames:{label:"roleName",value:"id"},"filter-option":V,maxTagCount:5,onChange:ie},null,8,["value","options"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"]),r(q,{visible:I.value,"onUpdate:visible":t[9]||(t[9]=n=>I.value=n),title:"分配角色",onOk:se,onCancel:M,width:"446px"},{default:i(()=>[r(F,{model:c(R),rules:oe,name:"batchformRef",ref_key:"batchformRef",ref:x,"label-col":{style:{width:"100px"}},autocomplete:"off"},{default:i(()=>[r(p,{name:"rolesIds",label:"角色"},{default:i(()=>[r(S,{placeholder:"请选择",value:c(R).rolesIds,"onUpdate:value":t[8]||(t[8]=n=>c(R).rolesIds=n),allowClear:"","show-search":"",mode:"multiple",options:T.value,fieldNames:{label:"roleName",value:"id"},"filter-option":V,maxTagCount:5},null,8,["value","options"])]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"])],64)}}});const it=Ve(Ye,[["__scopeId","data-v-100fadb8"]]);export{it as default};
