import{_ as A}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{bP as G,d as H,I as J,r as x,o as Q,D as Y,at as j,a as l,v as _,g as w,f as r,e as s,h as u,b,u as p,i,y as k,bc as W,bg as X,av as Z,bn as F,_ as ee}from"./index-db94d997.js";import{b as te,c as ae}from"./index-7c2ccd19.js";import{p as y,b as se}from"./dictLocal-9822709a.js";import{_ as ne}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";function oe(C){return G({url:"/web/paybillsincash/v1/exportStore",method:"POST",data:C})}const le={class:"areaPrice"},ie={class:"areaPrice_table"},de={key:1,class:"status_tag"},re={key:0,class:"tag_three"},ue={key:1,class:"tag_four"},pe={key:2,class:"tag_one"},ce={key:3,class:"tag_two"},_e={class:"text-hide"},ye={class:"text-hide"},fe=H({__name:"index",setup(C){const T=J(),h=x();x([]);const S=x([]),g=x(!1),E=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:1,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:S,render:!0,resizable:!0,order:3,fixed:"left"},{title:"季度",key:"quarterKey",dataIndex:"quarterKey",valueType:"quarterDate",resizable:!0,render:!0,width:120},{title:"分享金额-正泰",dataIndex:"shareMoney",search:!1,resizable:!0,formatMoney:!0,width:150},{title:"商城金额(抵扣金额-正泰)",dataIndex:"guarantyMoney",search:!1,resizable:!0,formatMoney:!0,width:150},{title:"支付建议",dataIndex:"paySuggest",valueType:"select",valueEnum:se,resizable:!0,order:6,width:120},{title:"支付状态",key:"value",dataIndex:"storePayStatus",valueType:"select",valueEnum:y,resizable:!0,render:!0,order:5,width:120},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:100}];Q(()=>{N()}),Y(()=>{h.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&P()});const N=()=>{j({}).then(e=>{let n=(e||[]).map(d=>({label:d.companyName,value:d.companyCode}));S.value=n})},M=e=>{e.isEdit=!0,e.storePayStatusTem=e.storePayStatus},K=e=>{e.isEdit=!1,e.storePayStatus=e.storePayStatusTem},L=e=>{let o={id:e.id,storePayStatus:e.storePayStatus};g.value=!0,ae(o).then(n=>{g.value=!1,W.info("保存成功"),P(!0)}).catch(n=>{g.value=!1})},R=()=>{T.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/shoppingPaymentsDocument/dataImport",query:{templateType:11,fileType:".csv,.xls,.xlsx",fileSize:30}})},B=()=>{var o;let e=(o=h.value)==null?void 0:o.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),oe(e)},U=x([]),V=e=>{U.value=e||[]},P=e=>{var o;(o=h.value)==null||o.reload(e)},$=(e,o)=>{let n={};return new Promise(d=>{if(e!=null&&e.quarterKey){const f=new Date(e==null?void 0:e.quarterKey),I=f.getMonth(),a=f.getFullYear(),t=Math.floor(I/3)+1;n={yearKey:a,quarter:t}}const c={...n,...e,noJoin:!0,delStatus:0};c==null||delete c.quarterKey,d(c)})};return(e,o)=>{const n=X,d=ne,c=Z,f=F,I=A;return l(),_("div",le,[w("div",ie,[r(I,{columns:E,ref_key:"actionRef",ref:h,request:p(te),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:V,"before-query-params":$},{tableHeader:s(()=>[r(d,null,{default:s(()=>[r(n,{onClick:R},{default:s(()=>[u("导入支付状态")]),_:1}),r(n,{type:"primary",onClick:B},{default:s(()=>[u("导出")]),_:1})]),_:1})]),valueRender:s(({column:a,record:t,index:v})=>{var m,q,D,z;return[t.isEdit?(l(),b(c,{key:0,value:t[a.dataIndex],"onUpdate:value":O=>t[a.dataIndex]=O,options:p(y),placeholder:"请选择"},null,8,["value","onUpdate:value","options"])):(l(),_("span",de,[t[a.dataIndex]=="1"?(l(),_("span",re,i(t[a.dataIndex]&&((m=p(y)[t[a.dataIndex]-1])==null?void 0:m.label)),1)):t[a.dataIndex]=="2"?(l(),_("span",ue,i(t[a.dataIndex]&&((q=p(y)[t[a.dataIndex]-1])==null?void 0:q.label)),1)):t[a.dataIndex]=="3"?(l(),_("span",pe,i(t[a.dataIndex]&&((D=p(y)[t[a.dataIndex]-1])==null?void 0:D.label)),1)):t[a.dataIndex]=="4"?(l(),_("span",ce,i(t[a.dataIndex]&&((z=p(y)[t[a.dataIndex]-1])==null?void 0:z.label)),1)):k("",!0)]))]}),companyCodeListRender:s(({column:a,record:t,index:v})=>[r(f,null,{title:s(()=>[u(i(t.companyName),1)]),default:s(()=>[w("span",_e,i(t.companyName),1)]),_:2},1024)]),quarterKeyRender:s(({column:a,record:t,index:v})=>[r(f,null,{title:s(()=>[u(i(t.yearKey+"-"+t.quarter+"季度"),1)]),default:s(()=>[w("span",ye,i(t.yearKey+"-"+t.quarter+"季度"),1)]),_:2},1024)]),actionRender:s(({column:a,record:t,index:v})=>[r(d,null,{default:s(()=>[t.isEdit?k("",!0):(l(),b(n,{key:0,type:"link",size:"small",onClick:m=>M(t),disabled:t.paySuggest=="1"},{default:s(()=>[u("编辑")]),_:2},1032,["onClick","disabled"])),t.isEdit?(l(),b(n,{key:1,type:"link",size:"small",onClick:m=>K(t),disabled:!1},{default:s(()=>[u("取消")]),_:2},1032,["onClick"])):k("",!0),t.isEdit?(l(),b(n,{key:2,type:"link",size:"small",onClick:m=>L(t),loading:p(g)},{default:s(()=>[u("保存")]),_:2},1032,["onClick","loading"])):k("",!0)]),_:2},1024)]),_:1},8,["request"])])])}}});const Se=ee(fe,[["__scopeId","data-v-45627279"]]);export{Se as default};
