import{d as pe,r as h,K as he,o as me,D as fe,at as _e,w as z,a as _,v as C,g as o,f as n,e as l,u as d,h as m,y as Y,b as F,i as g,z as O,e9 as J,m as q,F as ue,k as ye,au as ke,q as $,bI as ve,bf as ge,bU as xe,av as Ie,bg as we,bh as be,bn as Ce,ay as De,az as Te,p as ze,j as Fe,_ as Ne}from"./index-db94d997.js";import{f as Pe,h as Se,i as je,j as Ue}from"./index-4cf21c7d.js";import{e as Ye}from"./empty1-348ef1fe.js";import{R as Me,D as Le}from"./dayjs-a8e42122.js";import{_ as Ve}from"./index-42d7fb9b.js";import{_ as <PERSON>}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";const N=P=>(ze("data-v-1c5d47bd"),P=P(),Fe(),P),Be={class:"statements_container"},We=N(()=>o("div",{class:"item_title"},"加权方式",-1)),Ae={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ee={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},Oe={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Je={style:{flex:"1",display:"flex","align-items":"center"}},qe={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},$e={style:{flex:"1",display:"flex","align-items":"center","padding-left":"5%"}},Ke={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Qe=N(()=>o("div",{class:"item_title"},"统计维度",-1)),Ge={style:{display:"flex","flex-direction":"row","align-items":"center"}},He={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},Xe={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Ze=N(()=>o("span",{style:{"margin-left":"5px"}},"全选",-1)),et={style:{flex:"1",display:"flex","align-items":"center"}},tt={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},at=N(()=>o("span",{style:{"margin-left":"5px"}},"全选",-1)),dt={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},it={key:0,class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},nt={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},lt={class:"text-hide"},st={class:"text-hide"},ot={class:"text-hide"},ct={class:"text-hide"},rt={key:1,class:"content_container"},pt={key:2,class:"content_container",style:{position:"relative"}},ht=["src"],mt=N(()=>o("span",{class:"empty_text"}," 暂无数据，请选择正确查询信息 ",-1)),ft=pe({__name:"index",setup(P){const x=h(),k=h(!1),I=h([]),p=h({}),D=h([]),T=h([]),S=h(0),j=h(1),M=h(10),w=h([]),e=he({weiType:"1",corpId3:"",time1:[],time2:"",time3:"",companyId:[],companyId_checked:!1,companyId_indeterminate:!1,provinceId:[],provinceId_checked:!1,provinceId_indeterminate:!1,checked1:!0,checked2:!1,checked3:!1,checked4:!0,checked5:!1}),K=h([{title:"产权公司",dataIndex:"corpName3",resizable:!0,width:140},{title:"开始时间",dataIndex:"startTime",width:120,resizable:!0},{title:"结束时间",dataIndex:"endTime",search:!1,width:120,resizable:!0},{title:"加权上网电价(元/kWh)",dataIndex:"weOngrienPri",search:!1,width:120,formatDecimal:4,resizable:!0}]),Q=h([{title:"省",dataIndex:"provinceName",resizable:!0,width:140},{title:"开始时间",dataIndex:"startTime",width:120,resizable:!0},{title:"结束时间",dataIndex:"endTime",search:!1,width:120,resizable:!0},{title:"加权上网电价(元/kWh)",dataIndex:"weOngrienPri",search:!1,width:120,formatDecimal:4,resizable:!0}]),G=a=>{const t=e.time1;if(!t||t.length===0)return!1;const c=t[0]&&a.diff(t[0],"day")>89;return t[1]&&ye(t[1]).diff(a,"day")>89||c},H=a=>{e.time1=a},V=(a,{attrs:t})=>t.vnodes,X=(a,t)=>{e[a]=t.map(c=>c.value)},R=(a,t,c)=>{var v;((v=a==null?void 0:a.target)==null?void 0:v.checked)?X(t,c):e[t]=[],e[t+"_indeterminate"]=!1},B=(a,t,c,s)=>{t.length===0?(e[s+"_indeterminate"]=!1,e[s+"_checked"]=!1):t.length===c.length?(e[s+"_indeterminate"]=!1,e[s+"_checked"]=!0):(e[s+"_indeterminate"]=!0,e[s+"_checked"]=!1)},Z=h({companyId:[{validator:async(a,t)=>{if((!t||t.length===0)&&e.checked4)return Promise.reject("请选择产权公司")},trigger:"change"}],provinceId:[{validator:async(a,t)=>{if((!t||t.length===0)&&e.checked5)return Promise.reject("请选择行政区划")},trigger:"change"}],time1:[{validator:async(a,t)=>{if((!t||t.length===0)&&e.checked1)return Promise.reject("请选择时间周期")},trigger:"change"}],time2:[{validator:async(a,t)=>{if((!t||t.length===0)&&e.checked2)return Promise.reject("请选择日期")},trigger:"change"}],time3:[{validator:async(a,t)=>{if((!t||t.length===0)&&e.checked3)return Promise.reject("请选择日期")},trigger:"change"}]}),ee=a=>{W(a)},te=a=>{console.log(a)},ae=()=>{x.value.resetFields(),p.value={},D.value=[],T.value=[],e.companyId_checked=!1,e.companyId_indeterminate=!1,e.provinceId_checked=!1,e.provinceId_indeterminate=!1},de=a=>{j.value=a.current,M.value=a.pageSize,S.value=a.total;const t={...e,pageNum:a.current,pageSize:a.pageSize};W({...e,...t})},ie=()=>{ke({pid:0}).then(a=>{if(a&&a.length>0){let t=a.map(c=>({label:c.name,value:c.code}));I.value=t}})},W=a=>{let t="",c="",s="";if(a.checked1?(t="1",c=a.time1[0],s=a.time1[1]):a.checked2?(t="2",c=a.time2,s=a.time2):a.checked3&&(t="3",c=a.time3,s=a.time3),e.checked4){let v=$.cloneDeep(a.companyId)||[],b=JSON.stringify(v),u={weiType:t,corpId3:b,startTime:c,endTime:s,pageNum:j.value,pageSize:M.value,checked4:e.checked4};console.log("params=",u),k.value=!0,Pe(u).then(f=>{k.value=!1,p.value=u,console.log("公司维度res=",f),D.value=f||[],A(f,1)}).catch(()=>{k.value=!1})}else if(e.checked5){let v=$.cloneDeep(a.provinceId)||[],b=JSON.stringify(v),u={weiType:t,type:"1",id:b,startTime:c,endTime:s,pageNum:j.value,pageSize:M.value,checked5:e.checked5};console.log("params=",u),k.value=!0,Se(u).then(f=>{k.value=!1,p.value=u,console.log("地区维度res=",f),T.value=f||[],A(f,2)}).catch(f=>{k.value=!1})}},A=(a,t)=>{t===1?(D.value=(a==null?void 0:a.result)||[],S.value=(a==null?void 0:a.total)||0):t===2&&(T.value=(a==null?void 0:a.result)||[],S.value=(a==null?void 0:a.total)||0)};me(()=>{ne(),ie()}),fe(()=>{console.log("onActivated")});const ne=()=>{_e({}).then(a=>{console.log("产权公司res=",a);let c=(a||[]).map(s=>({label:s.companyName,value:s.companyCode}));w.value=c})};z(()=>e.checked1,a=>{a&&(e.checked2=!1,e.checked3=!1,e.weiType="1",e.time2="",e.time3="",x.value.clearValidate(["time2","time3"]))}),z(()=>e.checked2,a=>{a&&(e.checked1=!1,e.checked3=!1,e.weiType="2",e.time1=[],e.time3="",x.value.clearValidate(["time1","time3"]))}),z(()=>e.checked3,a=>{a&&(e.checked1=!1,e.checked2=!1,e.weiType="3",e.time1=[],e.time2="",x.value.clearValidate(["time1","time2"]))}),z(()=>e.checked4,a=>{a&&(e.checked5=!1,e.checked6=!1,e.provinceId=[],x.value.clearValidate(["provinceId"]))}),z(()=>e.checked5,a=>{a&&(e.checked4=!1,e.checked6=!1,e.companyId=[],x.value.clearValidate(["companyId"]))});const le=()=>{console.log("pageParams=",p.value),p.value.checked4?je(p.value).then(a=>{console.log("公司维度导出res:",a)}):p.value.checked5&&Ue(p.value).then(a=>{console.log("公司维度导出res:",a)})};return(a,t)=>{const c=ve,s=ge,v=Me,b=Le,u=xe,f=Ve,E=Ie,L=we,se=Re,oe=be,U=Ce,ce=De,re=Te;return _(),C(ue,null,[o("div",Be,[n(oe,{ref_key:"formRef",ref:x,name:"formRef",model:d(e),rules:d(Z),onFinish:ee,onFinishFailed:te},{default:l(()=>[We,o("div",Ae,[o("div",Ee,[o("div",Oe,[n(s,{name:"checked1"},{default:l(()=>[n(c,{checked:d(e).checked1,"onUpdate:checked":t[0]||(t[0]=i=>d(e).checked1=i)},{default:l(()=>[m("发电量加权：")]),_:1},8,["checked"])]),_:1})]),n(s,{name:"time1",style:{flex:"1",display:"flex"}},{default:l(()=>[n(v,{value:d(e).time1,"onUpdate:value":t[1]||(t[1]=i=>d(e).time1=i),style:{width:"100%"},disabled:!d(e).checked1,"disabled-date":G,onCalendarChange:H,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})]),o("div",Je,[o("div",qe,[n(s,{name:"checked2"},{default:l(()=>[n(c,{checked:d(e).checked2,"onUpdate:checked":t[2]||(t[2]=i=>d(e).checked2=i)},{default:l(()=>[m("装机容量加权：")]),_:1},8,["checked"])]),_:1})]),n(s,{name:"time2",style:{flex:"1",display:"flex"}},{default:l(()=>[n(b,{value:d(e).time2,"onUpdate:value":t[3]||(t[3]=i=>d(e).time2=i),style:{width:"100%"},disabled:!d(e).checked2,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})]),o("div",$e,[o("div",Ke,[n(s,{name:"checked3"},{default:l(()=>[n(c,{checked:d(e).checked3,"onUpdate:checked":t[4]||(t[4]=i=>d(e).checked3=i)},{default:l(()=>[m("净资产加权：")]),_:1},8,["checked"])]),_:1})]),n(s,{name:"time3",style:{flex:"1"}},{default:l(()=>[n(b,{value:d(e).time3,"onUpdate:value":t[5]||(t[5]=i=>d(e).time3=i),style:{width:"100%"},disabled:!d(e).checked3,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})])]),Qe,o("div",Ge,[o("div",He,[o("div",Xe,[n(s,{name:"checked4"},{default:l(()=>[n(c,{checked:d(e).checked4,"onUpdate:checked":t[6]||(t[6]=i=>d(e).checked4=i)},{default:l(()=>[m("产权公司：")]),_:1},8,["checked"])]),_:1})]),n(s,{name:"companyId",style:{flex:"1",display:"flex"}},{default:l(()=>[n(E,{value:d(e).companyId,"onUpdate:value":t[10]||(t[10]=i=>d(e).companyId=i),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:d(w),mode:"multiple",disabled:!d(e).checked4,"filter-option":(i,r)=>((r==null?void 0:r.label)??"").toLowerCase().includes(i.toLowerCase()),onChange:t[11]||(t[11]=(i,r)=>B(i,r,d(w),"companyId"))},{dropdownRender:l(({menuNode:i})=>[d(w)&&d(w).length>0?(_(),C("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[9]||(t[9]=r=>r.preventDefault())},[n(u,{checked:d(e).companyId_checked,"onUpdate:checked":t[7]||(t[7]=r=>d(e).companyId_checked=r),indeterminate:d(e).companyId_indeterminate,onChange:t[8]||(t[8]=r=>R(r,"companyId",d(w)))},null,8,["checked","indeterminate"]),Ze],32)):Y("",!0),n(f,{style:{margin:"4px 0"}}),n(V,{vnodes:i},null,8,["vnodes"])]),_:1},8,["value","options","disabled","filter-option"])]),_:1})]),o("div",et,[o("div",tt,[n(s,{name:"checked5"},{default:l(()=>[n(c,{checked:d(e).checked5,"onUpdate:checked":t[12]||(t[12]=i=>d(e).checked5=i)},{default:l(()=>[m("行政区划：")]),_:1},8,["checked"])]),_:1})]),n(s,{name:"provinceId",style:{flex:"1",display:"flex"}},{default:l(()=>[n(E,{value:d(e).provinceId,"onUpdate:value":t[16]||(t[16]=i=>d(e).provinceId=i),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:d(I),mode:"multiple",disabled:!d(e).checked5,"filter-option":(i,r)=>((r==null?void 0:r.label)??"").toLowerCase().includes(i.toLowerCase()),onChange:t[17]||(t[17]=(i,r)=>B(i,r,d(I),"provinceId"))},{dropdownRender:l(({menuNode:i})=>[d(I)&&d(I).length>0?(_(),C("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[15]||(t[15]=r=>r.preventDefault())},[n(u,{checked:d(e).provinceId_checked,"onUpdate:checked":t[13]||(t[13]=r=>d(e).provinceId_checked=r),indeterminate:d(e).provinceId_indeterminate,onChange:t[14]||(t[14]=r=>R(r,"provinceId",d(I)))},null,8,["checked","indeterminate"]),at],32)):Y("",!0),n(f,{style:{margin:"4px 0"}}),n(V,{vnodes:i},null,8,["vnodes"])]),_:1},8,["value","options","disabled","filter-option"])]),_:1})]),o("div",dt,[n(se,null,{default:l(()=>[n(L,{onClick:ae},{default:l(()=>[m("重置")]),_:1}),n(L,{type:"primary","html-type":"submit"},{default:l(()=>[m("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),d(p).checked4||d(p).checked5?(_(),C("div",it,[o("div",nt,[d(p).checked4&&d(D).length>0||d(p).checked5&&d(T).length>0?(_(),F(L,{key:0,type:"primary",onClick:le},{default:l(()=>[m("导出")]),_:1})):Y("",!0)]),n(ce,{style:{"margin-bottom":"24px"},columns:d(p).checked4?d(K):d(p).id?d(Q):[],"data-source":d(p).checked4?d(D):d(p).checked5?d(T):[],pagination:{current:d(j),total:d(S),showTotal:i=>`共 ${i} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0},scroll:{y:500},loading:d(k),onChange:de},{bodyCell:l(({column:i,text:r,record:y,index:gt})=>[i!=null&&i.formatMoney&&!i.render?(_(),F(U,{key:0},{title:l(()=>[m(g(d(O)(y[i.dataIndex])),1)]),default:l(()=>[o("span",lt,g(d(O)(y[i.dataIndex])),1)]),_:2},1024)):i!=null&&i.formatFixed&&!i.render?(_(),F(U,{key:1},{title:l(()=>[m(g(y[i.dataIndex]&&d(J)(y[i.dataIndex])),1)]),default:l(()=>[o("span",st,g(y[i.dataIndex]&&d(J)(y[i.dataIndex])),1)]),_:2},1024)):(i!=null&&i.formatDecimal||(i==null?void 0:i.formatDecimal)==0)&&!i.render?(_(),F(U,{key:2},{title:l(()=>[m(g(d(q)(y[i.dataIndex],i==null?void 0:i.formatDecimal)),1)]),default:l(()=>[o("span",ot,g(d(q)(y[i.dataIndex],i==null?void 0:i.formatDecimal)),1)]),_:2},1024)):i.render?Y("",!0):(_(),F(U,{key:3},{title:l(()=>[m(g(y[i.dataIndex]),1)]),default:l(()=>[o("span",ct,g(y[i.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","pagination","loading"])])):d(k)?(_(),C("div",rt,[n(re)])):(_(),C("div",pt,[o("img",{src:d(Ye),style:{width:"188px",height:"168px"}},null,8,ht),mt]))],64)}}});const zt=Ne(ft,[["__scopeId","data-v-1c5d47bd"]]);export{zt as default};
