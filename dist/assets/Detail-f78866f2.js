import{_ as k}from"./index-4c4cf647.js";import{c as A}from"./index-3dae1b9e.js";import{s as B}from"./index-9f4dd0ea.js";import{d as F,I as L,r as m,K as V,w as h,a as z,v as E,g as x,f as t,e as l,u as o,h as f,F as K,bc as M,be as O,bf as $,aw as j,ax as G,av as H,bh as J,bg as Q,_ as W}from"./index-db94d997.js";import{_ as X}from"./index-4a280682.js";import{_ as Y}from"./index-39334618.js";import"./index-e7bdfdf4.js";import"./CaretUpOutlined-7e71a64b.js";import"./weiZhi-78534cab.js";const Z={class:"card",style:{"margin-bottom":"24px"}},ee={class:"footer"},ae=F({__name:"Detail",setup(te){const v=L(),y=m(),g=m(),_=m(!1),s=m(history.state.pdata||{}),b=m(history.state.type||""),N=m([{label:"是",value:1},{label:"否",value:0}]),U=m([{label:"运维协商中",value:1},{label:"公司确认中",value:2},{label:"已完成",value:3}]),q=m([{label:"已结案",value:1},{label:"未结案",value:0}]),e=V({id:s.value.id,caseNumber:s.value.caseNumber,stationUniqueId:s.value.stationUniqueId,householdName:s.value.householdName,companyName:s.value.companyName,policyNumber:s.value.policyNumber,insuranceTypeName:s.value.insuranceTypeName,digest:s.value.digest,claimAmount:s.value.claimAmount,claimProgress:s.value.claimProgress||2,premiumStatus:s.value.premiumStatus||0,operationsSettleStatus:s.value.operationsSettleStatus||0,caseStatus:s.value.caseStatus||0});h(()=>e.premiumStatus,r=>{e.premiumStatus===1?e.operationsSettleStatus===1?e.caseStatus=1:e.caseStatus=0:(e.operationsSettleStatus=0,e.caseStatus=0)}),h(()=>e.operationsSettleStatus,r=>{e.premiumStatus===1&&e.operationsSettleStatus===1?e.caseStatus=1:e.caseStatus=0});const w=()=>{if(e.stationUniqueId){let r={pageNum:1,pageSize:10,psCode:e.stationUniqueId};A(r).then(a=>{a=a[0]||{},e.stationUniqueId=a==null?void 0:a.psCode,e.householdName=a==null?void 0:a.psName,e.companyCode=a==null?void 0:a.companyCode,e.companyName=a==null?void 0:a.companyName,e.policyNumber=a==null?void 0:a.insNumber,e.insuranceTypeName=a==null?void 0:a.insCategory})}else e.stationUniqueId="",e.householdName="",e.companyCode="",e.companyName="",e.policyNumber="",e.insuranceTypeName=""},C=()=>{v.go(-1)},I=()=>{y.value.validateFields().then(r=>{var i;const a=((i=g.value)==null?void 0:i.getFileList())||[];console.log("fileList=",a);const p=a.map(d=>d.id);let u={...r,fileIdList:p,id:e.id,companyCode:e.companyCode};console.log("params=",u),_.value=!0,B(u).then(d=>{console.log("新建出险单res=",d),_.value=!1,M.info("保存成功"),v.go(-1)}).catch(d=>{_.value=!1})})};return(r,a)=>{const p=O,u=$,i=j,d=G,T=X,c=H,D=J,P=k,S=Q,R=Y;return z(),E(K,null,[x("div",Z,[t(D,{model:o(e),name:"formRef",ref_key:"formRef",ref:y,autocomplete:"off","label-col":{style:{width:"120px",paddingRight:"8px"}}},{default:l(()=>[t(d,{span:24},{default:l(()=>[t(i,{span:12},{default:l(()=>[t(u,{label:"案件编号：",style:{"padding-right":"12px"},name:"caseNumber",required:""},{default:l(()=>[t(p,{placeholder:"请输入",value:o(e).caseNumber,"onUpdate:value":a[0]||(a[0]=n=>o(e).caseNumber=n),style:{width:"60%"},disabled:o(b)===1},null,8,["value","disabled"])]),_:1})]),_:1}),t(i,{span:12},{default:l(()=>[t(u,{label:"电站编号：",style:{"padding-left":"12px"},name:"stationUniqueId",required:""},{default:l(()=>[t(p,{placeholder:"请输入",value:o(e).stationUniqueId,"onUpdate:value":a[1]||(a[1]=n=>o(e).stationUniqueId=n),style:{width:"60%"},disabled:o(b)===1,onBlur:w},null,8,["value","disabled"])]),_:1})]),_:1})]),_:1}),t(d,{span:24},{default:l(()=>[t(i,{span:12},{default:l(()=>[t(u,{label:"业主名称：",style:{"padding-right":"12px"},name:"householdName",required:""},{default:l(()=>[t(p,{value:o(e).householdName,"onUpdate:value":a[2]||(a[2]=n=>o(e).householdName=n),disabled:!0,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),t(i,{span:12},{default:l(()=>[t(u,{label:"产权公司：",style:{"padding-left":"12px"},name:"companyName",required:""},{default:l(()=>[t(p,{value:o(e).companyName,"onUpdate:value":a[3]||(a[3]=n=>o(e).companyName=n),disabled:!0,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(d,{span:24},{default:l(()=>[t(i,{span:12},{default:l(()=>[t(u,{label:"保单号：",style:{"padding-right":"12px"},name:"policyNumber",required:""},{default:l(()=>[t(p,{value:o(e).policyNumber,"onUpdate:value":a[4]||(a[4]=n=>o(e).policyNumber=n),disabled:!0,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),t(i,{span:12},{default:l(()=>[t(u,{label:"险种：",style:{"padding-left":"12px"},name:"insuranceTypeName",required:""},{default:l(()=>[t(p,{value:o(e).insuranceTypeName,"onUpdate:value":a[5]||(a[5]=n=>o(e).insuranceTypeName=n),disabled:!0,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(d,{span:24},{default:l(()=>[t(i,{span:12},{default:l(()=>[t(u,{label:"摘要：",style:{"padding-right":"12px"},name:"digest",required:""},{default:l(()=>[t(p,{placeholder:"请输入",value:o(e).digest,"onUpdate:value":a[6]||(a[6]=n=>o(e).digest=n),style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),t(i,{span:12},{default:l(()=>[t(u,{label:"理赔金额：",style:{"padding-left":"12px"},name:"claimAmount",required:""},{default:l(()=>[t(T,{placeholder:"请输入",value:o(e).claimAmount,"onUpdate:value":a[7]||(a[7]=n=>o(e).claimAmount=n),controls:!1,style:{width:"60%"}},{addonAfter:l(()=>[f("元")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),t(d,{span:24},{default:l(()=>[t(i,{span:12},{default:l(()=>[t(u,{label:"理赔进度：",style:{"padding-right":"12px"},name:"claimProgress",required:""},{default:l(()=>[t(c,{value:o(e).claimProgress,"onUpdate:value":a[8]||(a[8]=n=>o(e).claimProgress=n),options:o(U),disabled:!1,style:{width:"60%"}},null,8,["value","options"])]),_:1})]),_:1}),t(i,{span:12},{default:l(()=>[t(u,{label:"保费是否到账：",style:{"padding-left":"12px"},name:"premiumStatus",required:""},{default:l(()=>[t(c,{value:o(e).premiumStatus,"onUpdate:value":a[9]||(a[9]=n=>o(e).premiumStatus=n),options:o(N),disabled:!1,style:{width:"60%"}},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),t(d,{span:24},{default:l(()=>[t(i,{span:12},{default:l(()=>[t(u,{label:"是否与运维结算：",style:{"padding-right":"12px"},name:"operationsSettleStatus",required:""},{default:l(()=>[t(c,{value:o(e).operationsSettleStatus,"onUpdate:value":a[10]||(a[10]=n=>o(e).operationsSettleStatus=n),options:o(N),disabled:o(e).premiumStatus!==1,style:{width:"60%"}},null,8,["value","options","disabled"])]),_:1})]),_:1}),t(i,{span:12},{default:l(()=>[t(u,{label:"结案标志：",style:{"padding-left":"12px"},name:"caseStatus",required:""},{default:l(()=>[t(c,{value:o(e).caseStatus,"onUpdate:value":a[11]||(a[11]=n=>o(e).caseStatus=n),options:o(q),disabled:!0,style:{width:"60%"}},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),t(P,{ref_key:"uploadRef",ref:g,businessType:8,relationCode:o(s).id,companyCode:o(s).companyCode},null,8,["relationCode","companyCode"]),x("div",ee,[t(R,null,{default:l(()=>[t(S,{onClick:C},{default:l(()=>[f("取消")]),_:1}),t(S,{type:"primary",onClick:I,loading:o(_)},{default:l(()=>[f("保存")]),_:1},8,["loading"])]),_:1})])],64)}}});const re=W(ae,[["__scopeId","data-v-61281f84"]]);export{re as default};
