import{u as q,g as Oe,a as Pe,B as ye,b as ee,c as Me,d as Ye,e as ze,f as Ue,h as We,i as He,j as Ke,k as fe,l as je,m as Be,_ as Ge,r as Qe,n as qe,H as Xe}from"./init-a1079076.js";import{d as O,t as j,r as i,o as G,w as z,c as he,a as $,b as M,e as Y,f as N,u as e,g as t,h as _e,i as B,p as ne,j as le,_ as U,n as ae,k as Je,l as Ae,m as ie,q as ve,s as oe,v as I,F as Fe,x as Ze,y as De,E as et,z as Q,A as te,B as Ve,C as pe,D as tt,G as ot,H as ke,I as at,J as Ee,K as nt}from"./index-db94d997.js";import{L as ce,i as Ie,r as lt}from"./index-36a0e5b9.js";import{E as st,a as it}from"./index-7c60ebfa.js";import"./index-96df45ba.js";import"./index-834c2e10.js";import"./index-4481a9dc.js";import"./icon-831229e8.js";import"./index-326d414f.js";import"./index-ec316fb4.js";const ct="/assets/1-d97da5e6.svg",rt="/assets/2-28c7a63e.svg",dt="/assets/3-7b07968b.svg",ut="/assets/4-a1228765.svg",pt="/assets/5-b051b981.svg",vt="/assets/6-f2ad643d.svg",me="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACDSURBVHgBxZJtDYAgEIbvNgMYwQhWsIE2wAZGoIEVbKINtIFEoAG+DvjhP/D8eLZnjK934ziiL3DOlbCiu+Cyhrs0ZHolhMNGiaFOzBnheb5hZlOERQUHykNBTb8Ta9CSf1sOG2rQxRos0JIvZA9NQoC9zEK3raK/fjpkJgkISG0sGQcpZl7+zJ5pwQAAAABJRU5ErkJggg==",_t={style:{display:"flex","align-items":"center"}},mt=["src"],yt={style:{display:"flex","align-items":"center"}},ft=["src"],ht=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c}=j(k),v=i([{name:"省份",key:"zoneName",width:"30%",isCustom:!0,isSort:!1},{name:"装机容量(万kW)",width:"30%",key:"capins",isSort:!1,formatMoney:1},{name:"电站数量",width:"20%",key:"stationCount",isSort:!1},{name:"占比",width:"20%",key:"ratio",isSort:!1,formatPercentage:1}]),S=i([{name:"市",key:"zoneName",width:"30%",isCustom:!0,isSort:!1},{name:"装机容量(万kW)",width:"30%",key:"capins",formatMoney:1,isSort:!1},{name:"电站数量",width:"20%",key:"stationCount",isSort:!1},{name:"占比",width:"20%",key:"ratio",isSort:!1,formatPercentage:1}]),f=i([{name:"公司",key:"name",width:"30%",isCustom:!0,isSort:!1},{name:"装机容量(万kW)",width:"30%",key:"capins",formatMoney:1,isSort:!1},{name:"电站数量",width:"20%",key:"stationCount",isSort:!1},{name:"占比",width:"20%",key:"ratio",isSort:!1,formatPercentage:1}]),C=i(),m=i(!0),w=()=>{console.log("省份-公司切换"),m.value=!m.value,b()};G(()=>{b()}),z(()=>s.companyCode,p=>{b()}),z([r,c],p=>{b()});const y=he(()=>{let p="";return m.value?p=r.value===1?v.value:r.value===2?S.value:"":p=f.value,p}),b=()=>{m.value?g():u()},g=()=>{var h;let p={cockpitType:r.value,zoneCode:(h=c.value)==null?void 0:h.zoneCode,assetCompanyCodes:s.companyCode};Oe(p).then(A=>{console.log("res=",A),C.value=A||[]})},u=()=>{var h;let p={cockpitType:r.value,zoneCode:(h=c.value)==null?void 0:h.zoneCode,assetCompanyCodes:s.companyCode};Pe(p).then(A=>{console.log("res=",A),C.value=A||[]})};return(p,h)=>($(),M(ee,{title:"装机容量对比",isHidePadding:!0},{content:Y(()=>[N(ye,{columns:e(y),dataList:e(C)},{zoneNameTitle:Y(()=>[t("div",_t,[_e(B(e(r)===1?"省份":e(r)===2?"市":"")+" ",1),t("img",{src:e(me),style:{"margin-left":"4px",cursor:"pointer"},onClick:w},null,8,mt)])]),nameTitle:Y(()=>[t("div",yt,[_e(" 公司 "),t("img",{src:e(me),style:{"margin-left":"4px",cursor:"pointer"},onClick:w},null,8,ft)])]),_:1},8,["columns","dataList"])]),_:1}))}}),gt=n=>(ne("data-v-9e2756eb"),n=n(),le(),n),wt={class:"content_box"},Ct={class:"formList"},bt=gt(()=>t("span",{style:{color:"#fff","margin-left":"20px"}},"万kWh",-1)),St={class:"BTable_wrap"},xt={style:{display:"flex","align-items":"center"}},kt=["src"],Dt={style:{display:"flex","align-items":"center"}},zt=["src"],At=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c}=j(k),v=i(1),S=i([{name:"排行",key:"zoneName",width:"14%",isSort:!0},{name:"省份",key:"zoneName",width:"20%",isCustom:!0,isSort:!1},{name:"计划发电量",width:"25%",key:"planPower",formatMoney:1,isSort:!1},{name:"已完成发电量",width:"25%",key:"completedPower",formatMoney:1,isSort:!1},{name:"完成比例",width:"16%",key:"completedRatio",isSort:!1,formatPercentage:1}]),f=i([{name:"排行",key:"zoneName",width:"14%",isSort:!0},{name:"市",key:"zoneName",width:"20%",isCustom:!0,isSort:!1},{name:"计划发电量",width:"25%",key:"planPower",formatMoney:1,isSort:!1},{name:"已完成发电量",width:"25%",key:"completedPower",formatMoney:1,isSort:!1},{name:"完成比例",width:"16%",key:"completedRatio",isSort:!1,formatPercentage:1}]),C=i([{name:"排行",key:"name",width:"14%",isSort:!0},{name:"公司",key:"name",width:"20%",isCustom:!0,isSort:!1},{name:"计划发电量",width:"25%",key:"planPower",formatMoney:1,isSort:!1},{name:"已完成发电量",width:"25%",key:"completedPower",formatMoney:1,isSort:!1},{name:"完成比例",width:"16%",key:"completedRatio",isSort:!1,formatPercentage:1}]),m=i([]),w=[{label:"本月",value:1},{label:"本年",value:2}],y=i(!0),b=()=>{console.log("省份-公司切换"),y.value=!y.value,u()},g=he(()=>{let x="";return y.value?x=r.value===1?S.value:r.value===2?f.value:"":x=C.value,x});G(()=>{u()}),z(()=>s.companyCode,x=>{u()}),z([r,c],x=>{u()});const u=()=>{y.value?p():h()},p=()=>{var L;let x={cockpitType:r.value,timeType:v.value,zoneCode:(L=c.value)==null?void 0:L.zoneCode,assetCompanyCodes:s.companyCode};Me(x).then(F=>{console.log("res=1111",F),m.value=F||[]})},h=()=>{var L;let x={cockpitType:r.value,timeType:v.value,zoneCode:(L=c.value)==null?void 0:L.zoneCode,assetCompanyCodes:s.companyCode};Ye(x).then(F=>{console.log("res=1111",F),m.value=F||[]})},A=x=>{v.value=x};return z(v,()=>{u()}),(x,L)=>($(),M(ee,{title:"发电计划完成排行",isHidePadding:!0},{content:Y(()=>[t("div",wt,[t("div",Ct,[N(ze,{tabList:w,onTabChange:A,tabValue:e(v)},null,8,["tabValue"]),bt]),t("div",St,[N(ye,{columns:e(g),dataList:e(m),isHideRowSty:!0,minHeight:"28px"},{zoneNameTitle:Y(()=>[t("div",xt,[_e(B(e(r)===1?"省份":e(r)===2?"市":"")+" ",1),t("img",{src:e(me),style:{"margin-left":"4px",cursor:"pointer"},onClick:b},null,8,kt)])]),nameTitle:Y(()=>[t("div",Dt,[_e(" 公司 "),t("img",{src:e(me),style:{"margin-left":"4px",cursor:"pointer"},onClick:b},null,8,zt)])]),_:1},8,["columns","dataList"])])])]),_:1}))}});const Tt=U(At,[["__scopeId","data-v-9e2756eb"]]),Te=n=>(ne("data-v-35b21dec"),n=n(),le(),n),$t={class:"main_wrap"},Nt={class:"item_data"},Bt=Te(()=>t("div",{class:"item_data_name"},"待审批",-1)),Vt={class:"item_progress"},Lt={class:"item_data_speed"},Ft={class:"item_data_num"},Et={class:"item_data"},It=Te(()=>t("div",{class:"item_data_name"},"审批完成",-1)),Rt={class:"item_progress"},Ot={class:"item_data_speed"},Pt={class:"item_data_num"},Mt={class:"item_data"},Yt=Te(()=>t("div",{class:"item_data_name"},"已完结",-1)),Ut={class:"item_progress"},Wt={class:"item_data_speed"},Ht={class:"item_data_num"},Kt=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c}=j(k),v=i(1);i([]);const S=i(0),f=i(0),C=i(0),m=i(0),w=i(0),y=i(0),b=[{label:"本月",value:1},{label:"本年",value:2}],g=p=>{v.value=p};z(v,()=>{console.log("请求逻辑"),u()}),G(()=>{u()}),z([r,c],p=>{u()}),z(()=>s.companyCode,p=>{u()});const u=()=>{var h;let p={cockpitType:r.value,timeType:v.value,zoneCode:(h=c.value)==null?void 0:h.zoneCode,assetCompanyCodes:s.companyCode};Ue(p).then(A=>{console.log("res=",A);const x=A||{};let L=(x==null?void 0:x.ticketTodo)||0,F=(x==null?void 0:x.ticketComplete)||0,P=(x==null?void 0:x.ticketFinish)||0,H=x==null?void 0:x.totall;m.value=L/H*100,w.value=F/H*100,y.value=P/H*100,S.value=L,f.value=F,C.value=P})};return(p,h)=>($(),M(ee,{title:"工作票完成进度"},{content:Y(()=>[N(ze,{tabList:b,onTabChange:g,tabValue:e(v)},null,8,["tabValue"]),t("div",$t,[t("div",Nt,[Bt,t("div",Vt,[t("div",Lt,[t("div",{class:"progress",style:ae({width:e(m)?`${e(m)}%`:"0px"})},null,4)]),t("div",Ft,B(e(S))+"条",1)])]),t("div",Et,[It,t("div",Rt,[t("div",Ot,[t("div",{class:"progress",style:ae({width:e(w)?`${e(w)}%`:"0px"})},null,4)]),t("div",Pt,B(e(f))+"条",1)])]),t("div",Mt,[Yt,t("div",Ut,[t("div",Wt,[t("div",{class:"progress",style:ae({width:e(y)?`${e(y)}%`:"0px"})},null,4)]),t("div",Ht,B(e(C))+"条",1)])])])]),_:1}))}});const jt=U(Kt,[["__scopeId","data-v-35b21dec"]]),Gt={class:"project_right_top"},Qt={class:"fromWrap"},qt=t("div",{id:"powerAnalysis_Chart",style:{width:"100%",height:"100%"}},null,-1),Xt=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:""}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c}=j(k),v=i(1),S=i(!1),f=i([]),C=Je().format("YYYY"),m=[];for(let D=2;D<=C-2020;D++)m.unshift({value:String(Number(C)-D),label:`${Number(C)-D}年`});const w=[{label:"本月",value:1},{label:"本年",value:2}],y={2020:"#FF9090",2021:"#FFD900",2022:"#0E70E9",2023:"#D998FF"},b=D=>{v.value=D},g=i("本月发电量"),u=i("同期发电量"),p=i("本月均值"),h=i("同期均值"),A=i([{name:g,itemStyle:{color:"rgba(0, 234, 255, 1)"}},{name:u,itemStyle:{color:"#B4FF7A"}},{name:p,itemStyle:{color:"rgba(255, 174, 80, 1)"}},{name:h,itemStyle:{color:"#B346F2"}}]),x=i([]),L=i([]),F=i([]),P=i([]),H=i([]);let X=null;const we={tooltip:{trigger:"axis",backgroundColor:"rgba(17, 34, 60, 1) ",borderColor:"rgba(17, 34, 60, 1) ",axisPointer:{type:"line"},textStyle:{color:"#fff"}},grid:{left:"2%",right:"4%",bottom:"12%",top:"20%",containLabel:!0},legend:{left:"24%",top:"2%",textStyle:{color:"rgba(189, 254, 255, 1)"},itemWidth:12,itemHeight:4,data:A},xAxis:{type:"category",data:x,axisLine:{lineStyle:{color:"rgba(255, 255, 255, 0.20)"}},axisLabel:{textStyle:{color:"#BDFEFF"}}},yAxis:{type:"value",name:"万kWh",nameTextStyle:{padding:[0,0,0,20],color:"#BDFEFF"},axisLine:{show:!1,lineStyle:{}},splitLine:{show:!0,lineStyle:{type:"dashed",color:"rgba(255, 255, 255, 0.10)"}},axisLabel:{textStyle:{color:"#BDFEFF"}}},series:[{name:g,type:"line",z:0,symbol:"none",showAllSymbol:!0,showSymbol:!0,symbolSize:6,lineStyle:{normal:{color:"#00EAFF"},borderColor:"#f0f"},label:{show:!1,position:"top",textStyle:{color:"#fff"}},itemStyle:{normal:{color:"#00EAFF"}},tooltip:{show:!0},areaStyle:{normal:{color:new ce(0,0,0,1,[{offset:0,color:"rgba(0, 234, 255, 0.30)"},{offset:1,color:"rgba(0, 234, 255, 0.00)"}],!1),shadowColor:"rgba(53,142,215, 0.9)",shadowBlur:20}},data:L},{name:u,type:"line",z:0,symbol:"none",showAllSymbol:!0,showSymbol:!0,symbolSize:6,lineStyle:{normal:{color:"#B4FF7A"},borderColor:"#f0f"},label:{show:!1,position:"top",textStyle:{color:"#fff"}},itemStyle:{normal:{color:"#B4FF7A"}},tooltip:{show:!0},areaStyle:{normal:{color:new ce(0,0,0,1,[{offset:0,color:"rgba(255, 223, 39, 0.30)"},{offset:1,color:"rgba(255, 223, 39, 0.00)"}],!1),shadowColor:"rgba(53,142,215, 0.9)",shadowBlur:20}},data:F},{name:p,type:"line",z:0,symbol:"none",showAllSymbol:!0,showSymbol:!0,symbolSize:6,lineStyle:{normal:{color:"#FFAE50"},borderColor:"#f0f"},label:{show:!1,position:"top",textStyle:{color:"#fff"}},itemStyle:{normal:{color:"#FFAE50"}},tooltip:{show:!0},areaStyle:{normal:{color:new ce(0,0,0,1,[{offset:0,color:"rgba(255, 223, 39, 0.30)"},{offset:1,color:"rgba(255, 223, 39, 0.00)"}],!1),shadowColor:"rgba(53,142,215, 0.9)",shadowBlur:20}},data:P},{name:h,type:"line",z:0,symbol:"none",showAllSymbol:!0,showSymbol:!0,symbolSize:6,lineStyle:{normal:{color:"#B346F2"},borderColor:"#f0f"},label:{show:!1,position:"top",textStyle:{color:"#fff"}},itemStyle:{normal:{color:"#B346F2"}},tooltip:{show:!0},areaStyle:{normal:{color:new ce(0,0,0,1,[{offset:0,color:"rgba(255, 223, 39, 0.30)"},{offset:1,color:"rgba(255, 223, 39, 0.00)"}],!1),shadowColor:"rgba(53,142,215, 0.9)",shadowBlur:20}},data:H}]},re=i(we),de=D=>{let o=document.getElementById("powerAnalysis_Chart");X=X||Ie(o),X.setOption(D,!0)};z(v,D=>{console.log("请求逻辑"),D===1?(g.value="本月发电量",p.value="本月均值"):D===2&&(g.value="本年发电量",p.value="本年均值"),J()}),z(S,D=>{D||(f.value=[],J())}),G(()=>{J()}),z(()=>s.companyCode,D=>{J()}),Ae(()=>{console.log("发电同比分析_onBeforeUnmount"),X==null||X.dispose()}),z(r,D=>{J()});const Ce=D=>{J()},se=D=>{let o={};return D.forEach(a=>{a.yearKey&&o[a.yearKey]||(o[a.yearKey]=[]),o[a.yearKey].push(a)}),o},be=D=>{let o={};return D.forEach(a=>{a.monthKey&&o[a.monthKey]||(o[a.monthKey]=[]),o[a.monthKey].push(a)}),o},Se=D=>{let o={};return v.value===1?o=be(D):v.value===2&&(o=se(D)),o},J=()=>{var a;let D=f.value&&f.value.length>0?f.value.join(","):"",o={cockpitType:r.value,timeType:v.value,zoneCode:(a=c.value)==null?void 0:a.zoneCode,assetCompanyCodes:s.companyCode};D&&(o.year=D),We(o).then(d=>{if(console.log("发电同比分析res=",d),d=d||[],(f==null?void 0:f.value.length)<=0){x.value=d.map(l=>l.timeName),L.value=d.map(l=>ie(l.power,1)),F.value=d.map(l=>ie(l.periodPower,1)),P.value=d.map(l=>ie(l.averagePower,1)),H.value=d.map(l=>ie(l.preAveragePower,1));let _=ve.cloneDeep(re.value);console.log("echarts渲染optionData.value=",_),de(_)}else{let _=Se(d);console.log("处理后的yearsData=",_);const l=ve.cloneDeep(re.value);let T=[],V=[];for(let R in _)if(_.hasOwnProperty(R)){console.log(R,_[R]);const E=xe(R,_[R]);l.series.push(E);let Z={name:R&&R+"发电量",itemStyle:{color:y[R]}};V.push(Z),T=_[R].map(ue=>ue.timeName)}let K=ve.cloneDeep(A.value);l.legend.data=[...K,...V],console.log("更多年份optionsClone=",l),l.xAxis.data=T,de(l)}})},xe=(D,o)=>{let a=o.map(l=>ie(l.power,1)),d=y[D]||y[0];return{name:D&&D+"发电量",type:"line",z:0,symbol:"none",showAllSymbol:!0,showSymbol:!0,symbolSize:6,lineStyle:{normal:{color:d},borderColor:"#f0f"},label:{show:!1,position:"top",textStyle:{color:"#fff"}},itemStyle:{normal:{color:d}},tooltip:{show:!0},areaStyle:{normal:{color:new ce(0,0,0,1,[{offset:0,color:"rgba(0, 234, 255, 0.30)"},{offset:1,color:"rgba(0, 234, 255, 0.00)"}],!1),shadowColor:"rgba(53,142,215, 0.9)",shadowBlur:20}},data:a}};return(D,o)=>{const a=et,d=st,_=it;return $(),M(ee,{title:"发电同比分析"},{content:Y(()=>[t("div",Gt,[N(ze,{tabList:w,onTabChange:b,tabValue:e(v)},null,8,["tabValue"]),t("div",Qt,[N(a,{modelValue:e(S),"onUpdate:modelValue":o[0]||(o[0]=l=>oe(S)?S.value=l:null),label:"多年份"},null,8,["modelValue"]),e(S)?($(),M(_,{key:0,modelValue:e(f),"onUpdate:modelValue":o[1]||(o[1]=l=>oe(f)?f.value=l:null),multiple:!0,"collapse-tags":"",placeholder:"请选择",placement:"bottom-start","popper-class":"custom-select2341",style:{width:"150px","margin-left":"8px"},size:"small",teleported:!1,onChange:Ce},{default:Y(()=>[($(),I(Fe,null,Ze(m,l=>N(d,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])):De("",!0)])]),qt]),_:1})}}});const Jt=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c}=j(k),v=i([{name:"名称",key:"targetName",width:"35%",isSort:!1},{name:"本年累计(万吨)",width:"33%",key:"yearSum",formatMoney:1,isSort:!1},{name:"历史累计(万吨)",width:"32%",key:"historySum",formatMoney:1,isSort:!1}]),S=i([]);G(()=>{f()}),z(()=>s.companyCode,C=>{f()}),z([r,c],C=>{f()});const f=()=>{var m;let C={cockpitType:r.value,zoneCode:(m=c.value)==null?void 0:m.zoneCode,assetCompanyCodes:s.companyCode};He(C).then(w=>{console.log("res=",w),S.value=w||[]})};return(C,m)=>($(),M(ee,{title:"节能减排分析",isHidePadding:!0},{content:Y(()=>[N(ye,{columns:e(v),dataList:e(S)},null,8,["columns","dataList"])]),_:1}))}}),$e="/assets/nhsy-bd989c26.svg",Ne=n=>(ne("data-v-bd65eee3"),n=n(),le(),n),Zt={class:"icon_wrap"},eo={class:"item_icon"},to={class:"item_icon_value"},oo=Ne(()=>t("img",{src:$e,alt:""},null,-1)),ao={class:"item_icon_name"},no={class:"item_icon"},lo={class:"item_icon_value"},so=Ne(()=>t("img",{src:$e,alt:""},null,-1)),io={class:"item_icon_name"},co={class:"item_icon"},ro={class:"item_icon_value"},uo=Ne(()=>t("img",{src:$e,alt:""},null,-1)),po={class:"item_icon_name"},vo=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:""}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c}=j(k),v=i({});G(()=>{S()}),z(()=>s.companyCode,f=>{S()}),z([r,c],f=>{S()});const S=()=>{var C;let f={targetType:r.value,provinceCode:(C=c.value)==null?void 0:C.zoneCode,assetCompanyCodes:s.companyCode};Ke(f).then(m=>{if(console.log("电站经营效益res=",m),m&&m.length>0){let w=m&&m.length>0?m[0]:{};v.value={targetNameOne:w.targetNameOne,targetValueOne:w.targetValueOne,targetNameTwo:w.targetNameTwo,targetValueTwo:w.targetValueTwo,targetNameThree:w.targetNameThree,targetValueThree:w.targetValueThree}}else v.value={}})};return(f,C)=>($(),M(ee,{title:"电站经营效益",isHidePadding:!0},{content:Y(()=>{var m,w,y,b,g,u,p,h,A;return[t("div",Zt,[t("div",eo,[t("div",to,[oo,t("div",null,B((m=e(v))!=null&&m.targetValueOne?e(Q)((w=e(v))==null?void 0:w.targetValueOne,1):""),1)]),t("div",ao,B((y=e(v))==null?void 0:y.targetNameOne),1)]),t("div",no,[t("div",lo,[so,t("div",null,B((b=e(v))!=null&&b.targetValueTwo?e(Q)((g=e(v))==null?void 0:g.targetValueTwo,1):""),1)]),t("div",io,B((u=e(v))==null?void 0:u.targetNameTwo),1)]),t("div",co,[t("div",ro,[uo,t("div",null,B((p=e(v))!=null&&p.targetValueThree?e(Q)((h=e(v))==null?void 0:h.targetValueThree,1):""),1)]),t("div",po,B((A=e(v))==null?void 0:A.targetNameThree),1)])])]}),_:1}))}});const _o=U(vo,[["__scopeId","data-v-bd65eee3"]]),mo=Object.prototype.toString;function yo(n,k){return mo.call(n)===`[object ${k}]`}function fo(n){return yo(n,"Number")}const ho={startVal:{type:Number,default:0},endVal:{type:Number,default:2021},duration:{type:Number,default:1500},autoplay:{type:Boolean,default:!0},decimals:{type:Number,default:0,validator(n){return n>=0}},prefix:{type:String,default:""},suffix:{type:String,default:""},separator:{type:String,default:","},decimal:{type:String,default:"."},color:{type:String,default:"#161840"},fontWeight:{type:String,default:"bold"},fontSize:{type:String,default:"18px"},style:{type:Object,default:{}},useEasing:{type:Boolean,default:!0},transition:{type:String,default:"linear"}},go=O({name:"CountTo",props:ho,emits:["onStarted","onFinished"],setup(n,{emit:k}){function s(y,b={}){const{disabled:g=i(!1),duration:u=1500,onFinished:p,onStarted:h}=b,A=i(y.value);return z(y,x=>{if(g.value)A.value=x;else{h&&h();let L=Date.now(),F=A.value;const P=()=>{let H=Date.now()-L;H<u?(A.value=F+(x-F)*(H/u),requestAnimationFrame(P)):(A.value=x,p&&p())};P()}}),A}const r=i(n.startVal),c=i(!1);let v=s(r);const S=he(()=>w(e(v)));z([()=>n.startVal,()=>n.endVal],()=>{r.value=n.startVal,n.autoplay&&f()}),G(()=>{n.autoplay&&f()});function f(){m(),r.value=n.endVal}function C(){r.value=n.startVal,m()}function m(){v=s(r,{disabled:c,duration:n.duration,onFinished:()=>k("onFinished"),onStarted:()=>k("onStarted")})}function w(y){if(!y||y===0||y==="0")return 0;const{decimals:b,decimal:g,separator:u,suffix:p,prefix:h}=n;if(y=Number(y).toFixed(b),Number(y)===0)return 0;y+="";const A=y.split(".");let x=A[0];const L=A.length>1?g+A[1]:"",F=/(\d+)(\d{3})/;if(u&&!fo(u))for(;F.test(x);)x=x.replace(F,"$1"+u+"$2");return h+x+L+p}return{value:S,start:f,reset:C}}});function wo(n,k,s,r,c,v){return $(),I("span",{style:ae({color:n.color,fontWeight:n.fontWeight,fontSize:n.fontSize,...n.style})},B(n.value),5)}const Re=U(go,[["render",wo]]),ge=n=>(ne("data-v-5c433ec3"),n=n(),le(),n),Co={class:"contentTop"},bo={class:"contentTop_item"},So={class:"item_name"},xo=ge(()=>t("img",{src:fe,alt:""},null,-1)),ko={class:"item_value"},Do={class:"contentTop_item"},zo=ge(()=>t("div",{class:"item_name"},[t("img",{src:fe,alt:""}),t("div",null,"总装机规模(万kW)")],-1)),Ao={class:"item_value"},To={class:"contentTop_item",style:{width:"24%"}},$o=ge(()=>t("div",{class:"item_name"},[t("img",{src:fe,alt:""}),t("div",null,"总负荷(万kW)")],-1)),No={key:0,class:"item_value"},Bo={class:"contentTop_item"},Vo=ge(()=>t("div",{class:"item_name"},[t("img",{src:fe,alt:""}),t("div",null,"安全运行天数(天)")],-1)),Lo={class:"item_value"},Fo=O({__name:"index",props:{pdata:{default:{}},zoneData:{default:{}},cockpitType:{default:""}},setup(n){const k=n,s=i(0),{pdata:r,zoneData:c,cockpitType:v}=j(k),S=i(r.value.totalLoad),f=i(r.value.totalLoad);return z(v,()=>{console.log("全国zoneData.value=",c.value),s.value=0}),z(r,(C,m)=>{s.value=s.value+1,console.log("总负荷runCount.value=",s.value),s.value>1&&(S.value=m!=null&&m.totalLoad?Number(m==null?void 0:m.totalLoad):0,f.value=C!=null&&C.totalLoad?Number(C==null?void 0:C.totalLoad):0)}),(C,m)=>{var y,b,g,u,p,h;const w=Re;return $(),I("div",Co,[t("div",bo,[t("div",So,[xo,t("div",null,B(e(v)===1?"全国":e(c).zoneName)+"电站总数(户)",1)]),t("div",ko,B(((y=e(r))==null?void 0:y.stationCount)||0),1)]),t("div",Do,[zo,t("div",Ao,B((b=e(r))!=null&&b.capinsCount?e(Q)((g=e(r))==null?void 0:g.capinsCount,1):0),1)]),t("div",To,[$o,e(s)<=1?($(),I("div",No,B((u=e(r))!=null&&u.totalLoad?e(Q)((p=e(r))==null?void 0:p.totalLoad,1):0),1)):($(),M(w,{key:1,startVal:e(S),endVal:e(f),decimals:1,style:ae({fontFamily:"YouSheBiaoTiHei",fontSize:"32px",fontWeight:400,textAlign:"left",color:"#fff",marginLeft:"15.12px",textShadow:"0 4px 2px rgba(0, 0, 0, 0.5)"})},null,8,["startVal","endVal","style"]))]),t("div",Bo,[Vo,t("div",Lo,B(((h=e(r))==null?void 0:h.safeOperatingDays)||0),1)])])}}});const Eo=U(Fo,[["__scopeId","data-v-5c433ec3"]]),Io="/assets/dayIcon-d0c7cee8.svg",Ro="/assets/monthIcon-4489a16a.svg",Oo="/assets/yearIcon-fcdfe0eb.svg",Po="/assets/summaryIcon-830fb227.svg",W=n=>(ne("data-v-09b42d0a"),n=n(),le(),n),Mo={class:"contentBottom"},Yo={class:"contentBottom_item",style:{width:"22%"}},Uo=W(()=>t("img",{src:Io,alt:""},null,-1)),Wo={class:"item_info"},Ho=W(()=>t("div",{class:"item_info_name"},"日发电量",-1)),Ko={class:"item_info_value"},jo={key:0},Go=W(()=>t("span",null,"万kWh",-1)),Qo={class:"contentBottom_item",style:{width:"24%"}},qo=W(()=>t("img",{src:Ro,alt:""},null,-1)),Xo={class:"item_info"},Jo=W(()=>t("div",{class:"item_info_name"},"月累计发电量",-1)),Zo={class:"item_info_value"},ea={key:0},ta=W(()=>t("span",null,"万kWh",-1)),oa={class:"contentBottom_item",style:{width:"26%"}},aa=W(()=>t("img",{src:Oo,alt:""},null,-1)),na={class:"item_info"},la=W(()=>t("div",{class:"item_info_name"},"年累计发电量",-1)),sa={class:"item_info_value"},ia={key:0},ca=W(()=>t("span",null,"万kWh",-1)),ra={class:"contentBottom_item",style:{width:"28%"}},da=W(()=>t("img",{src:Po,alt:""},null,-1)),ua={class:"item_info"},pa=W(()=>t("div",{class:"item_info_name"},"总累计发电量",-1)),va={class:"item_info_value"},_a={key:0},ma=W(()=>t("span",null,"万kWh",-1)),ya=O({__name:"index",props:{pdata:{default:{}},cockpitType:{default:""}},setup(n){const k=n,{pdata:s}=j(k),r=i(0),c=i(0),v=i(0),S=i(0),f=i(0),C=i(0),m=i(0),w=i(0),y=i(0);return z(()=>k.cockpitType,()=>{y.value=0}),z(s,(b,g)=>{y.value=y.value+1,console.log("日发电量runCount.value=",y.value),r.value=g!=null&&g.dayPower?Number(g==null?void 0:g.dayPower):0,c.value=b!=null&&b.dayPower?Number(b==null?void 0:b.dayPower):0;let u=g.monthPower?Number(g.monthPower):0,p=b.monthPower?Number(b.monthPower):0;v.value=te(r.value,u),S.value=te(c.value,p);let h=g.yearPower?Number(g.yearPower):0,A=b.yearPower?Number(b.yearPower):0;f.value=te(r.value,h),C.value=te(c.value,A);let x=g.totalPower?Number(g.totalPower):0,L=b.totalPower?Number(b.totalPower):0;m.value=te(r.value,x),w.value=te(c.value,L)}),(b,g)=>{var p,h;const u=Re;return $(),I("div",Mo,[t("div",Yo,[Uo,t("div",Wo,[Ho,t("div",Ko,[e(y)<=1?($(),I("div",jo,B((p=e(s))!=null&&p.dayPower?e(Q)((h=e(s))==null?void 0:h.dayPower,1):0)+" ",1)):($(),M(u,{key:1,startVal:e(r),endVal:e(c),decimals:1,style:{fontSize:"20px",fontFamily:"YouSheBiaoTiHei",color:"#fff",paddingBottom:"0px"}},null,8,["startVal","endVal"])),Go])])]),t("div",Qo,[qo,t("div",Xo,[Jo,t("div",Zo,[e(y)<=1?($(),I("div",ea,B(e(S)?e(Q)(e(S),1):"")+" ",1)):($(),M(u,{key:1,startVal:e(v),endVal:e(S),decimals:1,style:{fontSize:"20px",fontFamily:"YouSheBiaoTiHei",color:"#fff",paddingBottom:"0px"}},null,8,["startVal","endVal"])),ta])])]),t("div",oa,[aa,t("div",na,[la,t("div",sa,[e(y)<=1?($(),I("div",ia,B(e(C)?e(Q)(e(C),1):"")+" ",1)):($(),M(u,{key:1,startVal:e(f),endVal:e(C),decimals:1,style:{fontSize:"20px",fontFamily:"YouSheBiaoTiHei",color:"#fff",paddingBottom:"0px"}},null,8,["startVal","endVal"])),ca])])]),t("div",ra,[da,t("div",ua,[pa,t("div",va,[e(y)<=1?($(),I("div",_a,B(e(w)?e(Q)(e(w),1):"")+" ",1)):($(),M(u,{key:1,startVal:e(m),endVal:e(w),decimals:1,style:{fontSize:"20px",fontFamily:"YouSheBiaoTiHei",color:"#fff",paddingBottom:"0px"}},null,8,["startVal","endVal"])),ma])])])])}}});const fa=U(ya,[["__scopeId","data-v-09b42d0a"]]),Le="/assets/map_bg3-27d3230f.png",ha="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAK4SURBVHgBnVNLaBNRFH0zb/Jp0qbRmqQVCkEttC5qSsEKUgnVLC0BjdiFqOsKVReKWBHRhaLLgLooUnCjCTYLFUywAaFYDJYobjSpDopJk2m1bTpj/uN904w+81Hpgcu77+Xccz+5w6A6kO+5zciExhCDTsDVXnmOIhkFUB5NMiMBvjqGqRHxuZ0Ioylwzag+eFRGZ5jDgUBDobx/uE/DsnPof1BEfcyRQFS9stRPmGPYKZpbKJbFL4vSLLEaIU6pmq2pSHpw8FCTBvt/3XOl1Env3PmHM18XyP3oYGfHxKjjukGLbSpnJVNwmY8/eQ6urCpiGOQQnXD6nTAJInFw08T8L5Mf7gY/36Q5ej12ezwedr3ASqEcZnpp0tPo4oxer09ks9lVUjnHcc2xxGqM5siyvC0SiWjALbGV9jRStpSkScMOi7GlpUUAdxmyfgfBb8ecnR0050ehvMbzPCYaamtMOpN7T5NcDuuN2XEHCSz5fL7SvHd/++7tmy7SHD4tvlbGUqmGsVgsxj123PPo3MA0tNhMc2F2UfhvluF0o6rduuyPDd0OpSOCIIiKmtVqZd/MC9yB3jZd55amAYprhlTdcDrIbGmRt/zKxMitV48lSSIzLCqt2e32Yi6Xk/ZeeHH/U0p8hv6Bj0kxuOts+A64Yn9/fxFOWd0jImjU6XTtINge87ou7dhqdNUTiSfEUNep0BXgLphMphS0JcFzmf5EyCqYwMjCWUBsvFosnhRDPWPhqxjjNCRMwdMaaUutRAV5yKD1BRR6Toevkey0SNdoSBVJw0oQbgk1gLJTYG1gO2EJ90FlQWJwHwTrNgPQ70X+I7Dem9KmVqu15vP5zeDLYEuVajNqOzQwqg8SWDAYDEUQKpDg1tbWJWjpr+00AuN0OkllBpvNZqwkZdBGAB+lIkhZQ/wEYkUJnKsnBD0AAAAASUVORK5CYII=",ga=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}},rootProvinceCode:{default:""}},emits:["update:cockpitType","update:zoneData","update:countyData","update:rootProvinceCode"],setup(n,{emit:k}){const s=n,r=q();console.log("baseURLDRS=",Ve);const{cockpitType:c,zoneData:v,rootProvinceCode:S}=j(s);console.log("map-cockpitType.value=",c.value);const f=i("china"),C=i(!1),m=i(!1),w=i(),y=i([]),b=i(),g=i(0),u=i([]),p=[{coords:[[127.4998,50.2323],[98.4917,25.0077]],lineStyle:{color:"rgba(255, 217, 45, 0.80)"}}],h=i([]),A=()=>{let o=ke(u.value);return{type:"effectScatter",coordinateSystem:"geo",showEffectOn:"render",zlevel:1,silent:!0,rippleEffect:{period:10,scale:4,brushType:"fill"},hoverAnimation:!0,label:{normal:{position:"right",offset:[15,0],color:"#1DE9B6",show:!1}},itemStyle:{normal:{color:"#1DE9B6",shadowBlur:10,shadowColor:"#333"}},symbolSize:5,data:o}},x=()=>({type:"lines",zlevel:2,silent:!0,effect:{show:!0,period:5,trailLength:.1,symbol:"arrow",symbolSize:7},lineStyle:{normal:{label:{show:!1},color:"rgba(255, 255, 255, 0.70)",width:1,opacity:.5,curveness:.1}},data:ke(h.value)});i();const L=()=>({type:"lines",zlevel:2,silent:!0,lineStyle:{normal:{label:{show:!0},color:"rgba(255, 200, 0, 1)",width:2,type:"dashed",opacity:1}},data:p}),F=()=>{const o=[{province:"北京",longitude:"116.405285",latitude:"39.904989",count:125},{province:"上海",longitude:"121.472644",latitude:"31.231706",count:200},{province:"广东",longitude:"113.264365",latitude:"23.129094",count:300}],a=c.value===1?{type:"scatter",silent:!0,roam:!0,symbol:`image://${ha}`,symbolSize:16,coordinateSystem:"geo",data:[[107.7539,29.8904]],label:{show:!1}}:{};let d=[],_=[];for(var l=0;l<o.length;l++)d.push({name:o[l].province,value:o[l].count}),_.push({name:o[l].province,value:[o[l].longitude,o[l].latitude,0],times:o[l].count});let T=c.value===1?A():{},V=c.value===1?x():{},K=c.value===1?L():{},R=c.value===1?{name:"城市标注",type:"scatter",silent:!0,coordinateSystem:"geo",data:[{name:"黑河",value:[127.4998,50.2323]},{name:"腾冲",value:[98.4917,25.0077]}],label:{show:!0,formatter:E=>E.name,position:[12,2],color:"rgba(255, 200, 0, 1)",borderWidth:0},symbolSize:6}:{};return{geo:{map:f.value,aspectScale:.75,zoom:1,roam:!1,itemStyle:{normal:{areaColor:{type:"pattern",image:Le,repeat:"repeat"},shadowColor:"#001930",shadowOffsetX:7,shadowOffsetY:7},emphasis:{areaColor:"rgba(0, 234, 255, 0.80)",borderWidth:.5,borderColor:"rgba(0, 234, 255, 0.60)",label:{show:!0}}}},tooltip:{show:!0,formatter:E=>{var Z;if(E.name==="腾冲"||E.name==="黑河")return!1;if(E.name)return`${E.name}：${((Z=E==null?void 0:E.data)==null?void 0:Z.value)||0}户`}},series:[{type:"map",roam:!1,label:{show:!0,normal:{show:!0,textStyle:{color:"rgba(255, 255, 255, 0.70)"},formatter:E=>{var Z,ue;if((Z=E==null?void 0:E.data)!=null&&Z.value)return`${(ue=E==null?void 0:E.data)==null?void 0:ue.value}户
${E.name}`},align:"left",lineHeight:16},emphasis:{textStyle:{color:"rgba(255, 255, 255)"}}},itemStyle:{normal:{opacity:1,borderColor:"#214288",borderWidth:1,areaColor:{type:"pattern",image:Le,repeat:"repeat"}},emphasis:{color:"#fff",areaColor:"rgba(0, 234, 255, 0.80)",borderWidth:0,borderColor:"#00FFFB",shadowColor:"#001930",shadowOffsetX:5,shadowOffsetY:5}},select:{disabled:c.value===1||c.value===2,label:{color:"#fff"},itemStyle:{color:"#fff",areaColor:"rgba(0, 234, 255, 0.80)",borderWidth:0,borderColor:"#00FFFB",shadowColor:"#001930",shadowOffsetX:5,shadowOffsetY:5}},zoom:1,map:f.value,data:ke(y.value)},a,T,V,K,R]}};let P=null;console.log("config.provinceData=",pe.provinceData),G(()=>{se(),H()}),z(()=>r.companyCode,o=>{se()}),tt(()=>{console.log("onActivated")}),Ae(()=>{var o;console.log("地图_onBeforeUnmount"),(o=w.value)==null||o.dispose()}),z(g,(o,a)=>{console.log(`Div width changed from ${a} to ${o}`),o>0&&D()});const H=()=>{new ResizeObserver(o=>{for(const a of o)g.value=a.contentRect.width}).observe(b.value)},X=(o=[])=>o.map(a=>{let d="",_=a.value||0;_>=1&&_<=100?d="#4ECC82":_>=101&&_<=5e3?d="#12ADA6":_>=5001&&_<=3e4?d="#A4A067":_>3e4&&(d="#8C686B");let l={};return d?l.normal={areaColor:d}:l=a.itemStyle,{...a,name:a.name,code:a.code,value:a.value,itemStyle:l}}),we=(o=[])=>o.map(a=>{let d="",_=a.value||0;_>=1&&_<=100?d="#4ECC82":_>=101&&_<=2e3?d="#12ADA6":_>=2001&&_<=8e3?d="#A4A067":_>8e3&&(d="#8C686B");let l={};return d?l.normal={areaColor:d}:l=a.itemStyle,{...a,name:a.name,code:a.code,value:a.value,itemStyle:l}}),re=o=>{const a=pe.provinceData.map(l=>{let T=o.filter(K=>K.provinceName===l.name),V=T&&T.length>0?T[0]:{};return V?{...l,value:(V==null?void 0:V.stationCount)||0}:{...l,value:0}});y.value=X(a),console.log("mapData.value=",y.value);const d=o.map(l=>{let T=pe.provinceData.filter(K=>l.provinceName===K.name),V=T&&T.length>0?T[0]:{};return{...l,value:V.pos||"",itemStyle:{color:"#B4F9FF"}}});u.value=d.filter(l=>l.stationCount||l.provinceCode==="530000");const _=o.map(l=>{let T=pe.provinceData.filter(R=>l.provinceName===R.name),V=T&&T.length>0?T[0]:{};return{...l,coords:[[107.7539,29.8904],V==null?void 0:V.pos],lineStyle:{color:"#00EAFF"}}});h.value=_.filter(l=>l.stationCount||l.provinceCode==="530000")},de=(o=[])=>{let a=o.map(d=>C.value&&c.value===2?{...d,name:d.areaName,code:d.areaCode,value:d.stationCount}:{...d,name:d.cityName,code:d.cityCode,value:d.stationCount});y.value=we(a),console.log("mapData.value=",y.value),u.value=[],h.value=[]},Ce=(o=[])=>{y.value=o.map(a=>{let d="",_=a.stationCount||0;_>=1&&_<=20?d="#12ADA6":_>=21&&_<=50?d="#A29E62":_>=51&&_<=100?d="#B86E74":_>=101&&_<=500?d="#6257B9":_>=501&&_<=1e3?d="#118CC7":_>1e3&&(d="#11B2D0");let l={};return d&&(l.normal={areaColor:d}),C.value&&c.value===2?{...a,name:a.areaName,code:a.areaCode,value:a.stationCount,itemStyle:l}:{...a,name:a.areaName,code:a.areaCode,value:a.stationCount,itemStyle:l}}),u.value=[],h.value=[]},se=()=>{let o={provinceCode:S.value,assetCompanyCodes:r.companyCode,cockpitType:C.value&&c.value===2?c.value+2:c.value+1,upCode:c.value===2||c.value===3?v.value[c.value].zoneCode:""};je(o).then(a=>{console.log("地图数据res=",a),m.value=!1,a=a||[],c.value===1?re(a):c.value===2?de(a):c.value===3&&(console.log("县级地图数据res=",a),Ce(a)),Se()}).catch(()=>{m.value=!1})},be=o=>{const a=(o==null?void 0:o.features)||[];y.value.forEach(d=>{var l;let _=a.find(T=>{var V,K;return d.code&&((V=T==null?void 0:T.properties)==null?void 0:V.adcode)&&Number(d.code)===Number((K=T==null?void 0:T.properties)==null?void 0:K.adcode)});_&&(d.name=(l=_==null?void 0:_.properties)==null?void 0:l.name)})},Se=()=>{let o=document.getElementById("main");w.value=w.value||Ie(o),ot.get(`${Ve}map-json/${f.value}.json`).then(a=>{var _;console.log("res=",a);let d=a==null?void 0:a.data;be(d),lt(f.value,d),P=F(),console.log("optionData===",P),(_=w.value)==null||_.setOption(P,!0)}).catch(()=>{}),w.value.on("click",xe)},J=o=>{var a,d,_,l,T;(((a=o==null?void 0:o.data)==null?void 0:a.code)===11e4||((d=o==null?void 0:o.data)==null?void 0:d.code)===12e4||((_=o==null?void 0:o.data)==null?void 0:_.code)===31e4||((l=o==null?void 0:o.data)==null?void 0:l.code)===5e5)&&(k("update:rootProvinceCode",(T=o==null?void 0:o.data)==null?void 0:T.code),C.value=!0)},xe=o=>{var a,d,_,l,T;console.log(o);try{if(J(o),o.name!=="重庆市"){if(m.value||c.value>=3){let V={zoneName:(a=o==null?void 0:o.data)==null?void 0:a.name,zoneCode:(d=o==null?void 0:o.data)==null?void 0:d.code};k("update:countyData",V);return}if(o.name==="南海诸岛"||!((_=o==null?void 0:o.data)!=null&&_.value)||((l=o==null?void 0:o.data)==null?void 0:l.value)===0)return}m.value=!0,c.value<=3&&(f.value=o.name,v.value[c.value+1]={zoneName:o==null?void 0:o.name,zoneCode:(T=o==null?void 0:o.data)==null?void 0:T.code,cockpitType:c.value+1},k("update:cockpitType",c.value+1),k("update:zoneData",v.value))}catch{m.value=!1}},D=()=>{var o;console.log("地图resize=",w.value),(o=w.value)==null||o.resize()};return z([c,v],()=>{c.value===1&&(k("update:rootProvinceCode",""),C.value=!1),v.value[c.value]&&(f.value=v.value[c.value].zoneName,P=F(),se())}),(o,a)=>($(),I("div",{class:"map-container",style:ae({padding:e(c)===1||e(c)===2?"0px 0px 60px 0px":"0px 600px 200px 0px"})},[t("div",{id:"main",class:"main-container",ref_key:"mapRef",ref:b},null,512)],4))}});const wa=U(ga,[["__scopeId","data-v-6d37f646"]]),Ca={class:"content_box"},ba={class:"BTable_wrap"},Sa=["onClick"],xa=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}},countyData:{default:{}},rootProvinceCode:{default:""}},setup(n){const k=n,s=q(),{cockpitType:r,zoneData:c,countyData:v,rootProvinceCode:S}=j(k),f=at(),C=i([{name:"电站名称",key:"stationName",width:"25%",isSort:!1,render:!0},{name:"装机容量(kW)",width:"25%",key:"capacity",formatMoney:1,isSort:!1},{name:"今日发电量(kWh)",width:"25%",key:"cdayEnergy",formatMoney:1,isSort:!1},{name:"累计发电量(kWh)",width:"25%",key:"accuEnergy",formatMoney:1,isSort:!1}]),m=i([]),w=g=>{f.push({path:"/cockpit/station",state:{pdata:ve.cloneDeep(g)}})};G(()=>{y()}),z([r,S],g=>{y()}),z(()=>s.companyCode,g=>{y()}),z(v,(g,u)=>{g.zoneCode!==u.zoneCode&&b()});const y=()=>{let g={cityCode:c.value.zoneCode,type:"1",pageNum:1,pageSize:9999,assetCompanyCodes:s.companyCode,provinceCode:S.value};Be(g).then(u=>{console.log("res=",u),m.value=u||[]})},b=()=>{let g={cityCode:v.value.zoneCode,type:"3",pageNum:1,pageSize:9999,assetCompanyCodes:s.companyCode,provinceCode:S.value};Be(g).then(u=>{console.log("res=",u),m.value=u||[]})};return(g,u)=>($(),M(ee,{title:"电站详情",isHidePadding:!0,activeBg:"active1"},{content:Y(()=>[t("div",Ca,[t("div",ba,[N(ye,{columns:e(C),dataList:e(m)},{stationNameRender:Y(({record:p,column:h,index:A})=>[t("div",{style:{cursor:"pointer"},onClick:x=>w(p)},B(p.stationName),9,Sa)]),_:1},8,["columns","dataList"])])])]),_:1}))}});const ka=U(xa,[["__scopeId","data-v-e3671cc3"]]),Da=n=>(ne("data-v-b295b2bb"),n=n(),le(),n),za={class:"LeftTop_county"},Aa=Da(()=>t("div",{class:"bottom_mask"},[t("div",{class:"mask_black"},[t("div",{class:"mask_blue"})])],-1)),Ta=O({__name:"index",props:{cockpitType:{default:""},zoneData:{default:{}}},setup(n){const k=n,{cockpitType:s,zoneData:r}=j(k);return console.log("市级大屏zoneData=",r),(c,v)=>($(),I("div",za,[t("span",null,B(e(r).zoneName)+"电站分布图",1),Aa]))}});const $a=U(Ta,[["__scopeId","data-v-b295b2bb"]]);const Na={},Ba={class:"leftBottom_county"},Va=Ee('<div class="left_county_title" data-v-220d6ee9><img src="'+Ge+'" alt="" data-v-220d6ee9><div data-v-220d6ee9>电站数量</div></div><div class="left_legend" data-v-220d6ee9><div class="item_legend" data-v-220d6ee9><div class="num" data-v-220d6ee9>1-20</div><div class="speed" data-v-220d6ee9><div style="background-color:#12ada6;width:10%;" data-v-220d6ee9></div></div></div><div class="item_legend" data-v-220d6ee9><div class="num" data-v-220d6ee9>21-50</div><div class="speed" data-v-220d6ee9><div style="background-color:#a29e62;width:15%;" data-v-220d6ee9></div></div></div><div class="item_legend" data-v-220d6ee9><div class="num" data-v-220d6ee9>51-100</div><div class="speed" data-v-220d6ee9><div style="background-color:#b86e74;width:20%;" data-v-220d6ee9></div></div></div><div class="item_legend" data-v-220d6ee9><div class="num" data-v-220d6ee9>101-500</div><div class="speed" data-v-220d6ee9><div style="background-color:#6257b9;width:30%;" data-v-220d6ee9></div></div></div><div class="item_legend" data-v-220d6ee9><div class="num" data-v-220d6ee9>501-1000</div><div class="speed" data-v-220d6ee9><div style="background-color:#118cc7;width:50%;" data-v-220d6ee9></div></div></div><div class="item_legend" data-v-220d6ee9><div class="num" data-v-220d6ee9>1000以上</div><div class="speed" data-v-220d6ee9><div style="background-color:#11b2d0;width:70%;" data-v-220d6ee9></div></div></div></div>',2),La=[Va];function Fa(n,k){return $(),I("div",Ba,La)}const Ea=U(Na,[["render",Fa],["__scopeId","data-v-220d6ee9"]]),Ia={class:"county_wrapper"},Ra={class:"card_wrap",style:{top:"132px",left:"40px"}},Oa={class:"card_wrap",style:{width:"966px",top:"854px",left:"40px"}},Pa={class:"card_wrap",style:{width:"574px",height:"940px",top:"110px",right:"40px"}},Ma=O({__name:"index",props:{zoneData:{default:{}},cockpitType:{default:3},countyData:{default:{}},rootProvinceCode:{default:""}},setup(n){return(k,s)=>($(),I("div",Ia,[t("div",Ra,[N($a,{zoneData:k.zoneData},null,8,["zoneData"])]),t("div",Oa,[N(Ea)]),t("div",Pa,[N(ka,{zoneData:k.zoneData,countyData:k.countyData,rootProvinceCode:k.rootProvinceCode},null,8,["zoneData","countyData","rootProvinceCode"])])]))}});const Ya=U(Ma,[["__scopeId","data-v-20cee64e"]]),Ua={class:"home-wrapper",id:"bigscreen-container"},Wa={class:"card_wrap",style:{width:"100%",height:"90px",top:"0",left:"0","z-index":"1"}},Ha={class:"card_wrap",style:{width:"450px",height:"292px",top:"110px",left:"40px","z-index":"1"}},Ka={class:"card_wrap",style:{width:"450px",height:"310px",top:"422px",left:"40px","z-index":"1"}},ja={class:"card_wrap",style:{width:"450px",height:"308px",top:"752px",left:"40px","z-index":"1"}},Ga={class:"card_wrap",style:{width:"450px",height:"354px",top:"110px",right:"40px","z-index":"1"}},Qa={class:"card_wrap",style:{width:"450px",height:"288px",top:"484px",right:"40px","z-index":"1"}},qa={class:"card_wrap",style:{width:"450px",height:"268px",top:"792px",right:"40px","z-index":"1"}},Xa={class:"card_wrap",style:{width:"828px",height:"68px",top:"130px",left:"546px","z-index":"1"}},Ja={class:"card_wrap",style:{width:"912px",height:"94px",bottom:"71px",left:"504px","z-index":"1"}},Za=Ee('<div class="card_wrap nether-menu" style="width:912px;height:36px;bottom:20px;left:504px;z-index:1;" data-v-9251e759><a href="#" data-v-9251e759><img src="'+ct+'" data-v-9251e759></a><a href="#" data-v-9251e759><img src="'+rt+'" data-v-9251e759></a><a href="#" data-v-9251e759><img src="'+dt+'" data-v-9251e759></a><a href="#" data-v-9251e759><img src="'+ut+'" data-v-9251e759></a><a href="#" data-v-9251e759><img src="'+pt+'" data-v-9251e759></a><a href="#" data-v-9251e759><img src="'+vt+'" data-v-9251e759></a></div>',1),en={key:1,style:{width:"100%",height:"90px",top:"0",left:"0","z-index":"1"}},tn=O({__name:"index",setup(n){const k=q(),s=i(1),r=nt({1:{zoneName:"china",zoneCode:"",cockpitType:1}}),c=i(),v=i(),S=he(()=>r[s.value]),f=i(""),C=i(),m=i(0);let w=null;G(()=>{b(),y(),w=setInterval(()=>{b()},6e4)}),Ae(()=>{w&&clearInterval(w)}),z(()=>k.companyCode,u=>{b()}),z(f,()=>{console.log("rootProvinceCode.value=",f.value)}),z(s,u=>{(u===1||u===2)&&(b(),w&&clearInterval(w),w=setInterval(()=>{b()},6e4)),console.log("cockpitType=",s.value)}),z(m,(u,p)=>{if(console.log(`大屏width changed from ${p} to ${u}`),u>0){const h=document.querySelector("#bigscreen-container");Qe(h)()}});const y=()=>{new ResizeObserver(u=>{for(const p of u)m.value=p.contentRect.width}).observe(C.value)},b=()=>{var p;let u={cockpitType:s.value,code:(p=r[s.value])==null?void 0:p.zoneCode,assetCompanyCodes:k.companyCode};qe(u).then(h=>{console.log("res=",h),h&&h.length>0?v.value=h[0]:v.value={}})},g=()=>{s.value>1&&(r[s.value]&&delete r[s.value],s.value=s.value-1)};return(u,p)=>($(),I("div",{class:"page_black",id:"bigscreen-page-container",ref_key:"bigScreenRef",ref:C},[t("div",Ua,[t("div",Wa,[N(Xe,{isGoBack:e(s)&&e(s)!=1,title:"渝泰零碳乡村能源数智管控平台",goBack:g},null,8,["isGoBack"])]),e(s)===1||e(s)===2?($(),I(Fe,{key:0},[t("div",Ha,[N(ht,{cockpitType:e(s),zoneData:e(S)},null,8,["cockpitType","zoneData"])]),t("div",Ka,[N(Tt,{cockpitType:e(s),zoneData:e(S)},null,8,["cockpitType","zoneData"])]),t("div",ja,[N(jt,{cockpitType:e(s),zoneData:e(S)},null,8,["cockpitType","zoneData"])]),t("div",Ga,[N(Xt,{cockpitType:e(s),zoneData:e(S)},null,8,["cockpitType","zoneData"])]),t("div",Qa,[N(Jt,{cockpitType:e(s),zoneData:e(S)},null,8,["cockpitType","zoneData"])]),t("div",qa,[N(_o,{cockpitType:e(s),zoneData:e(S)},null,8,["cockpitType","zoneData"])]),t("div",Xa,[N(Eo,{pdata:e(v),zoneData:e(S),cockpitType:e(s)},null,8,["pdata","zoneData","cockpitType"])]),t("div",Ja,[N(fa,{pdata:e(v),cockpitType:e(s)},null,8,["pdata","cockpitType"])]),Za],64)):De("",!0),e(s)===3?($(),I("div",en,[N(Ya,{zoneData:e(S),cockpitType:e(s),countyData:e(c),rootProvinceCode:e(f)},null,8,["zoneData","cockpitType","countyData","rootProvinceCode"])])):De("",!0),N(wa,{style:{width:"100%",height:"100%"},cockpitType:e(s),"onUpdate:cockpitType":p[0]||(p[0]=h=>oe(s)?s.value=h:null),zoneData:e(r),"onUpdate:zoneData":p[1]||(p[1]=h=>oe(r)?r.value=h:null),countyData:e(c),"onUpdate:countyData":p[2]||(p[2]=h=>oe(c)?c.value=h:null),rootProvinceCode:e(f),"onUpdate:rootProvinceCode":p[3]||(p[3]=h=>oe(f)?f.value=h:null)},null,8,["cockpitType","zoneData","countyData","rootProvinceCode"])])],512))}});const vn=U(tn,[["__scopeId","data-v-9251e759"]]);export{vn as default};
