import{_ as P}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as v,r as f,I as F,o as T,D as L,at as R,a as B,v as E,f as r,e as a,h as i,i as u,g as b,u as V,q as j,bn as Y,bg as $,bR as A,bS as G,bT as J,_ as O}from"./index-db94d997.js";import{g as Q,e as H,a as U}from"./index-7c2ccd19.js";import{_ as W}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const X={class:"areaPrice"},Z={class:"text-hide"},ee={class:"text-hide"},te=v({__name:"index",setup(ae){const c=f(),m=f([]),w=F(),S=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:m,render:!0,resizable:!0,order:1,fixed:"left"},{title:"季度",key:"quarterKey",dataIndex:"quarterKey",valueType:"quarterDate",width:120,render:!0,resizable:!0,fixed:"left"},{title:"分享金额-正泰",dataIndex:"shareMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"抵扣金额-正泰",dataIndex:"guarantyMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"应付确认金额-正泰",dataIndex:"oughtPayConfirmMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"调整金额-正泰",dataIndex:"adjustMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"实付确认金额-正泰",dataIndex:"practicalPayConfirmMoney",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"操作",key:"action",dataIndex:"action",search:!1,width:160,render:!0,align:"center",fixed:"right"}];T(()=>{C()}),L(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&K()});const C=()=>{R({}).then(t=>{console.log("产权公司res=",t);let e=(t||[]).map(n=>({label:n.companyName,value:n.companyCode}));m.value=e})},M=t=>{console.log("item--",t),w.push({path:"/financeManage/farmerIncomeManage/quarterPayableManage/cashPaymentsDocument/detail",state:{pdata:j.cloneDeep(t)}})},q=(t,l)=>{const{key:e}=t;e=="1"?k(l):e=="2"&&I(l)},k=t=>{var n;const e={...(n=c.value)==null?void 0:n.getInitialFormStateNew(),id:t==null?void 0:t.id};e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatus,e==null||delete e.yearKey,e==null||delete e.quarter,H(e)},I=t=>{var n;const e={...(n=c.value)==null?void 0:n.getInitialFormStateNew(),id:t==null?void 0:t.id};e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatus,e==null||delete e.yearKey,e==null||delete e.quarter,U(e)},D=f([]),z=t=>{D.value=t||[],console.log("dataSource=",t)},K=()=>{var t;(t=c.value)==null||t.reload()},N=(t,l)=>{let e={};return new Promise(n=>{if(t!=null&&t.quarterKey){const d=new Date(t==null?void 0:t.quarterKey),_=d.getMonth(),y=d.getFullYear(),p=Math.floor(_/3)+1;e={yearKey:y,quarter:p}}const s={...e,...t,noJoin:!0,delStatus:0};s==null||delete s.quarterKey,n(s)})};return(t,l)=>{const e=Y,n=$,s=A,d=G,_=J,y=W,p=P;return B(),E("div",X,[r(p,{columns:S,ref_key:"actionRef",ref:c,request:V(Q),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:z,"before-query-params":N},{companyCodeListRender:a(({column:h,record:o,index:x})=>[r(e,null,{title:a(()=>[i(u(o.companyName),1)]),default:a(()=>[b("span",Z,u(o.companyName),1)]),_:2},1024)]),quarterKeyRender:a(({column:h,record:o,index:x})=>[r(e,null,{title:a(()=>[i(u(o.yearKey+"-"+o.quarter+"季度"),1)]),default:a(()=>[b("span",ee,u(o.yearKey+"-"+o.quarter+"季度"),1)]),_:2},1024)]),actionRender:a(({column:h,record:o,index:x})=>[r(y,null,{default:a(()=>[r(n,{type:"link",size:"small",onClick:g=>M(o),disabled:!1},{default:a(()=>[i("查看明细")]),_:2},1032,["onClick"]),r(_,null,{overlay:a(()=>[r(d,{onClick:g=>q(g,o)},{default:a(()=>[r(s,{key:"1"},{default:a(()=>[i(" 泰极云支付单 ")]),_:1}),r(s,{key:"2"},{default:a(()=>[i(" 银行支付单 ")]),_:1})]),_:2},1032,["onClick"])]),default:a(()=>[r(n,{type:"link",size:"small"},{default:a(()=>[i(" 导出支付单 ")]),_:1})]),_:2},1024)]),_:2},1024)]),_:1},8,["request"])])}}});const _e=O(te,[["__scopeId","data-v-208e2637"]]);export{_e as default};
