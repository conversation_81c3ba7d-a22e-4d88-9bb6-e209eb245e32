import{_ as le}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{a2 as P,d as oe,r as i,K as E,o as se,D as ne,a as re,v as ue,g as B,f as l,e as t,h as x,i as G,u as n,s as V,F as ie,bc as T,bg as de,bn as ce,be as fe,bf as me,aw as _e,ax as pe,bh as ve,bi as he,_ as be}from"./index-db94d997.js";import{_ as ge}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";function ye(c){return P({url:"/web/workflowRole/v1/list",method:"GET",isTable:!0,data:c})}function we(c){return P({url:"/web/workflowRole/v1/create",method:"POST",data:c})}function ke(c){return P({url:"/web/workflowRole/v1/getUserByPhone",method:"GET",data:c})}function A(c){return P({url:"/web/workflowRole/v1/createUserRole",method:"POST",data:c})}const xe={class:"areaPrice"},Ce={class:"areaPrice_table"},Re={class:"text-hide"},Ie=oe({__name:"index",setup(c){const U=i();let C=i({}),O=i({});const p=i(!1),g=i(),R=i(!1),y=E({}),f=i(!1),I=i(!1),N=i(!1),v=i(),h=E({}),m=i(!1),_=i(!1),M={companyCodeList:[{validator:async(e,a)=>{if(!y.companyCodeList||y.companyCodeList.length===0)return Promise.reject("请选择产权公司")},trigger:"change"}]},$=[{title:"角色名称",dataIndex:"roleName",search:!0,resizable:!0,width:100,fixed:"left"},{title:"绑定相关人员",key:"userList",dataIndex:"userList",search:!1,resizable:!0,render:!0,width:100},{title:"角色创建人",dataIndex:"roleCreateBy",search:!1,resizable:!0,width:100},{title:"角色创建时间",dataIndex:"roleCreateTime",search:!1,resizable:!0,width:100},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:120}],q=e=>{if(!e)return;let a=[];return e.forEach(o=>a.push(Object.values(o)[0])),a.join("; ")};se(()=>{}),ne(()=>{U.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&S()});const H=()=>{g.value.validateFields().then(e=>{let a={...e};R.value=!0,we(a).then(o=>{var r;p.value=!1,R.value=!1,T.info("创建成功"),(r=g.value)==null||r.resetFields(),S()}).catch(o=>{R.value=!1})})},J=()=>{var e;p.value=!1,(e=g.value)==null||e.resetFields()},j=()=>{v.value.validateFields().then(e=>{var o,r;let a={...e,roleId:(o=C.value)==null?void 0:o.roleId,userId:(r=O.value)==null?void 0:r.id,delStatus:0};I.value=!0,A(a).then(s=>{f.value=!1,I.value=!1,T.info("绑定成功"),S()}).catch(s=>{I.value=!1})})},K=()=>{var e;f.value=!1,m.value=!1,_.value=!1,(e=v.value)==null||e.resetFields()},Q=()=>{v.value.validateFields().then(e=>{var o,r;let a={...e,roleId:(o=C.value)==null?void 0:o.roleId,userId:(r=O.value)==null?void 0:r.id,delStatus:1};N.value=!0,A(a).then(s=>{var d;f.value=!1,N.value=!1,(d=v.value)==null||d.resetFields(),T.info("解绑成功"),S()}).catch(s=>{N.value=!1})})},W=e=>{var o;let a=(o=e.target)==null?void 0:o.value;a.length==11&&ke({phone:a}).then(s=>{var b;O.value=s,h.userName=s.userName;let d=C.value.userList;if(d){let w=(d==null?void 0:d.map(k=>Object.keys(k)[0])).includes((b=s==null?void 0:s.id)==null?void 0:b.toString());s!=null&&s.userName?(w?(m.value=!1,_.value=!0):(m.value=!0,_.value=!1),console.log(m.value,_.value)):(m.value=!1,_.value=!1)}else m.value=!0,_.value=!1}).catch(s=>{})},X=()=>{var e;p.value=!0,(e=g.value)==null||e.resetFields()},Y=e=>{var a;f.value=!0,C.value=e,(a=v.value)==null||a.resetFields()},Z=i([]),ee=e=>{Z.value=e||[]},S=()=>{var e;(e=U.value)==null||e.reload()},ae=(e,a)=>{let o={};return new Promise(r=>{const s={...o,...e,noJoin:!0,delStatus:0};r(s)})};return(e,a)=>{const o=de,r=ge,s=ce,d=le,b=fe,F=me,w=_e,k=pe,z=ve,D=he;return re(),ue(ie,null,[B("div",xe,[B("div",Ce,[l(d,{columns:$,ref_key:"actionRef",ref:U,request:n(ye),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:ee,"before-query-params":ae},{tableHeader:t(()=>[l(r,null,{default:t(()=>[l(o,{type:"primary",onClick:X},{default:t(()=>[x("创建角色")]),_:1})]),_:1})]),userListRender:t(({column:u,record:L,index:te})=>[l(s,null,{title:t(()=>[x(G(q(L[u.dataIndex])),1)]),default:t(()=>[B("span",Re,G(q(L[u.dataIndex])),1)]),_:2},1024)]),actionRender:t(({column:u,record:L,index:te})=>[l(o,{type:"link",size:"small",onClick:Se=>Y(L)},{default:t(()=>[x("绑定人员")]),_:2},1032,["onClick"])]),_:1},8,["request"])])]),l(D,{visible:n(p),"onUpdate:visible":a[1]||(a[1]=u=>V(p)?p.value=u:null),title:"创建角色","confirm-loading":n(R),destroyOnClose:!0,"ok-text":"立即创建",onOk:H,onCancel:J},{default:t(()=>[l(z,{model:n(y),ref_key:"formRef1",ref:g,name:"basic",rules:M,"label-col":{style:{width:"80px"}},autocomplete:"off"},{default:t(()=>[l(k,{span:24},{default:t(()=>[l(w,{span:24},{default:t(()=>[l(F,{label:"角色名称",name:"roleName",required:""},{default:t(()=>[l(b,{placeholder:"请输入",style:{width:"100%"},value:n(y).roleName,"onUpdate:value":a[0]||(a[0]=u=>n(y).roleName=u)},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"]),l(D,{visible:n(f),"onUpdate:visible":a[4]||(a[4]=u=>V(f)?f.value=u:null),title:"绑定人员",onOk:j,onCancel:K},{footer:t(()=>[l(r,null,{default:t(()=>[l(o,{key:"submit",type:"primary",disabled:!n(m),loading:n(I),onClick:j},{default:t(()=>[x(" 立即绑定 ")]),_:1},8,["disabled","loading"]),l(o,{disabled:!n(_),key:"back",loading:n(N),onClick:Q},{default:t(()=>[x(" 解除绑定 ")]),_:1},8,["disabled","loading"])]),_:1})]),default:t(()=>[l(z,{model:n(h),ref_key:"formRef2",ref:v,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:t(()=>[l(k,{span:24},{default:t(()=>[l(w,{span:24},{default:t(()=>[l(F,{label:"手机号",name:"phone",required:""},{default:t(()=>[l(b,{value:n(h).phone,"onUpdate:value":a[2]||(a[2]=u=>n(h).phone=u),placeholder:"请输入",style:{width:"100%"},onChange:W},null,8,["value"])]),_:1})]),_:1})]),_:1}),l(k,{span:24},{default:t(()=>[l(w,{span:24},{default:t(()=>[l(F,{label:"用户名称",name:"userName"},{default:t(()=>[l(b,{value:n(h).userName,"onUpdate:value":a[3]||(a[3]=u=>n(h).userName=u),placeholder:"请输入",disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible"])],64)}}});const je=be(Ie,[["__scopeId","data-v-14a24bd9"]]);export{je as default};
