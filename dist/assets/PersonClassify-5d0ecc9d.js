import{a as Ze,u as we,b as $e,r as Ge}from"./index-4b995c57.js";import{g as <PERSON>}from"./index-a08ebcd6.js";import{d as Je,I as Oe,r as N,K as Ve,w as q,bZ as Re,b_ as ke,o as Qe,a as h,v as g,u as n,f as a,e as l,g as y,F as Ke,x as We,h as Le,bc as w,av as Xe,bf as xe,aw as ea,ax as aa,be as ta,bh as la,bg as na,p as sa,j as oa,_ as ua}from"./index-db94d997.js";import{D as ia}from"./dayjs-a8e42122.js";import{_ as ra}from"./index-4a280682.js";import{_ as ma}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";const D=A=>(sa("data-v-492fee91"),A=A(),oa(),A),da={key:0,class:"card"},pa=D(()=>y("span",{class:"title"}," 基本信息 ",-1)),ca={style:{padding:"0 24px"}},va={key:0},fa={key:1},_a=D(()=>y("div",{class:"solid"},null,-1)),ya={style:{padding:"0 24px"}},ba={key:1,class:"card"},Ca=D(()=>y("span",{class:"title"}," 基本信息 ",-1)),Na={style:{padding:"0 24px"}},ha=D(()=>y("div",{class:"solid"},null,-1)),ga={style:{padding:"0 24px"}},Ta={class:"card1"},Ma=Je({__name:"PersonClassify",setup(A){var F,I,B,P,Y,E,j,z,Z,$,G,H,J,O,Q,W,X,x,ee,ae,te,le,ne,se,oe,ue,ie,re,me,de,pe,ce,ve,fe,_e,ye,be,Ce,Ne,he,ge,Te,Me,Ae,De,Se;const S=Oe(),r=N((F=history.state)==null?void 0:F.pdata),b=N((I=history.state)==null?void 0:I.type),t=Ve({id:((B=r.value)==null?void 0:B.id)||"",variationType:(P=r.value)!=null&&P.variationType||((Y=r.value)==null?void 0:Y.variationType)===0?(E=r.value)==null?void 0:E.variationType:1,replacementStationNum:((j=r.value)==null?void 0:j.replacementStationNum)||1,stationCode:((z=r.value)==null?void 0:z.stationCode)||"",stationName:((Z=r.value)==null?void 0:Z.stationName)||"",companyCode:(($=r.value)==null?void 0:$.companyCode)||"",companyName:((G=r.value)==null?void 0:G.companyName)||"",pushTime:((H=r.value)==null?void 0:H.pushTime)||"",list:[{stationCode:((J=r.value)==null?void 0:J.stationCode)||"",stationName:((O=r.value)==null?void 0:O.stationName)||"",companyCode:((Q=r.value)==null?void 0:Q.companyCode)||"",companyName:((W=r.value)==null?void 0:W.companyName)||"",pushTime:((X=r.value)==null?void 0:X.pushTime)||""}],replacementStationCode:((x=r.value)==null?void 0:x.replacementStationCode)||"",replacementStationName:((ee=r.value)==null?void 0:ee.replacementStationName)||"",replacementCompanyCode:((ae=r.value)==null?void 0:ae.replacementCompanyCode)||"",replacementCompanyName:((te=r.value)==null?void 0:te.replacementCompanyName)||"",assetValue:((le=r.value)==null?void 0:le.assetValue)||"",usrMonth:((ne=r.value)==null?void 0:ne.usrMonth)||"",monthKey:((se=r.value)==null?void 0:se.monthKey)||"",beforeAccountDepreciationCost:((oe=r.value)==null?void 0:oe.beforeAccountDepreciationCost)||"",beforeAccountDepreciationMonth:((ue=r.value)==null?void 0:ue.beforeAccountDepreciationMonth)||"",accountAssetValue:((ie=r.value)==null?void 0:ie.accountAssetValue)||"",residueDepreciationMonth:((re=r.value)==null?void 0:re.residueDepreciationMonth)||"",salvageRate:((me=r.value)==null?void 0:me.salvageRate)||""}),i=Ve({id:((de=r.value)==null?void 0:de.id)||"",variationType:(pe=r.value)!=null&&pe.variationType||((ce=r.value)==null?void 0:ce.variationType)===0?(ve=r.value)==null?void 0:ve.variationType:1,stationCode:((fe=r.value)==null?void 0:fe.stationCode)||"",stationName:((_e=r.value)==null?void 0:_e.stationName)||"",companyCode:((ye=r.value)==null?void 0:ye.companyCode)||"",companyName:((be=r.value)==null?void 0:be.companyName)||"",pushTime:((Ce=r.value)==null?void 0:Ce.pushTime)||"",assetValue:((Ne=r.value)==null?void 0:Ne.assetValue)||"",usrMonth:((he=r.value)==null?void 0:he.usrMonth)||"",monthKey:((ge=r.value)==null?void 0:ge.monthKey)||"",beforeAccountDepreciationCost:((Te=r.value)==null?void 0:Te.beforeAccountDepreciationCost)||"",beforeAccountDepreciationMonth:((Me=r.value)==null?void 0:Me.beforeAccountDepreciationMonth)||"",accountAssetValue:((Ae=r.value)==null?void 0:Ae.accountAssetValue)||"",residueDepreciationMonth:((De=r.value)==null?void 0:De.residueDepreciationMonth)||"",salvageRate:((Se=r.value)==null?void 0:Se.salvageRate)||""}),V=N(),R=N(),C=N(!1),T=N([]),k=N([{label:"新增",value:0},{label:"置换",value:1}]);q(()=>t.replacementStationNum,u=>{if(t.list.length>=u)t.list=t.list.slice(0,u);else{const e=t.list.slice(0,t.list.length);let d=e.length;for(let o=0;o<u-d;o++)e.push({stationCode:"",stationName:"",companyCode:"",companyName:"",pushTime:""});t.list=e}}),q(()=>t.variationType,u=>{i.variationType=t.variationType}),q(()=>i.variationType,u=>{t.variationType=i.variationType}),Re(()=>{i.assetValue&&i.beforeAccountDepreciationCost&&(i.accountAssetValue=ke(Number(i.assetValue),Number(i.beforeAccountDepreciationCost)))}),Re(()=>{i.usrMonth&&i.beforeAccountDepreciationMonth&&(i.residueDepreciationMonth=ke(Number(i.usrMonth),Number(i.beforeAccountDepreciationMonth)))}),Qe(()=>{Fe()});const Fe=()=>{Ze({variationType:-1}).then(e=>{console.log("新增-电站编码查询电站信息res=",e);const d=e||[];T.value=d.map(o=>({...o,label:o.stationCode,value:o.stationCode}))})},U=(u,e,d,o)=>{console.log("option=",e),d?(d.id=e==null?void 0:e.id,d.stationCode=e==null?void 0:e.stationCode,d.stationName=e==null?void 0:e.stationName,d.companyCode=e==null?void 0:e.companyCode,d.companyName=e==null?void 0:e.companyName,d.pushTime=e==null?void 0:e.pushTime,t["stationCode"+(o+1)]=e==null?void 0:e.stationCode,t["stationName"+(o+1)]=e==null?void 0:e.stationName,t["companyCode"+(o+1)]=e==null?void 0:e.companyCode,t["companyName"+(o+1)]=e==null?void 0:e.companyName,t["pushTime"+(o+1)]=e==null?void 0:e.pushTime):(i.id=e==null?void 0:e.id,i.stationName=e==null?void 0:e.stationName,i.companyCode=e==null?void 0:e.companyCode,i.companyName=e==null?void 0:e.companyName,i.pushTime=e==null?void 0:e.pushTime)},K=u=>{t.replacementStationName=u.stationName,t.replacementCompanyName=u.companyName,t.replacementCompanyCode=u.companyCode,t.assetValue=u==null?void 0:u.assetValue,t.usrMonth=u==null?void 0:u.usrMonth,t.monthKey=u==null?void 0:u.monthKey,t.beforeAccountDepreciationCost=u==null?void 0:u.beforeAccountDepreciationCost,t.beforeAccountDepreciationMonth=u==null?void 0:u.beforeAccountDepreciationMonth,t.accountAssetValue=u==null?void 0:u.accountAssetValue,t.residueDepreciationMonth=u==null?void 0:u.residueDepreciationMonth,t.salvageRate=u==null?void 0:u.salvageRate},Ie=()=>{if(!t.replacementStationCode){K({});return}let u={stationCode:t.replacementStationCode,pageNum:1,pageSize:10};He(u).then(e=>{console.log("被置换电站编码查询信息res=",e);let d={};e!=null&&e.data&&(e==null?void 0:e.data.length)>0&&(d=e==null?void 0:e.data[0]),K(d)})},L=u=>{console.log("提交values=",u)},Be=()=>{S.go(-1)},Pe=()=>{if(t.variationType===0)Ye();else if(t.variationType===1){const u=t.list.map(d=>d.stationCode),e=Array.from(new Set(u));if(u.length!==e.length){w.error("置换电站中存在重复电站");return}Ee()}},Ye=()=>{R.value.validateFields().then(u=>{console.log("新增表单values=",u);let e={...u,id:i.id,companyCode:i.companyCode};console.log("新增表单params=",e);const d=b.value===1?we:$e;C.value=!0,d(e).then(o=>{console.log("保存成功"),C.value=!1,w.info("保存成功"),S.go(-1)}).catch(o=>{C.value=!1})})},Ee=()=>{V.value.validateFields().then(u=>{console.log("置换表单values=",u),t.list=t.list.map(o=>({...T.value.find(_=>_.id===o.id),...o,variationType:t.variationType,replacementStationNum:t.replacementStationNum}));let e={...u,id:t.id,companyCode:t.companyCode,list:t.list,replaceStationNum:t.replacementStationNum,replaceStationCode:t.replacementStationCode,replaceStationName:t.replacementStationName,replaceCompanyCode:t.replacementCompanyCode,replaceCompanyName:t.replacementCompanyName,replacementStationNum:t.replacementStationNum,replacementStationCode:t.replacementStationCode,replacementStationName:t.replacementStationName,replacementCompanyCode:t.replacementCompanyCode,replacementCompanyName:t.replacementCompanyName};console.log("置换表单params=",e);const d=b.value===1?we:Ge;C.value=!0,d(e).then(o=>{console.log("保存成功"),C.value=!1,w.info("保存成功"),S.go(-1)}).catch(o=>{C.value=!1})})};return(u,e)=>{const d=Xe,o=xe,m=ea,_=ra,v=aa,c=ta,Ue=la,je=ia,qe=na,ze=ma;return h(),g(Ke,null,[n(t).variationType===1?(h(),g("div",da,[pa,a(Ue,{model:n(t),ref_key:"formRef1",ref:V,name:"basic",onFinish:L,"label-col":{style:{width:"165px"}}},{default:l(()=>[y("div",ca,[a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"变动类型",name:"variationType",class:"f_item",required:""},{default:l(()=>[a(d,{value:n(t).variationType,"onUpdate:value":e[0]||(e[0]=s=>n(t).variationType=s),options:n(k),disabled:n(b)===1},null,8,["value","options","disabled"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"置换电站数量",name:"replacementStationNum",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(t).replacementStationNum,"onUpdate:value":e[1]||(e[1]=s=>n(t).replacementStationNum=s),style:{width:"100%"},precision:0,controls:!1,min:1,disabled:n(b)===1},null,8,["value","disabled"])]),_:1})]),_:1})]),_:1}),n(b)===0?(h(),g("div",va,[(h(!0),g(Ke,null,We(n(t).list,(s,p)=>(h(),g("div",null,[a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"电站编码"+(p+1),name:"stationCode"+(p+1),class:"f_item",required:""},{default:l(()=>[a(d,{value:n(t)["stationCode"+(p+1)],"onUpdate:value":f=>n(t)["stationCode"+(p+1)]=f,options:n(T),"allow-clear":"","show-search":"","filter-option":(f,M)=>((M==null?void 0:M.label)??"").toLowerCase().includes(f.toLowerCase()),onChange:(f,M)=>U(f,M,s,p)},null,8,["value","onUpdate:value","options","filter-option","onChange"])]),_:2},1032,["label","name"])]),_:2},1024),a(m,{span:12},{default:l(()=>[a(o,{label:"业主名称",name:"stationName"+(p+1),class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t)["stationName"+(p+1)],"onUpdate:value":f=>n(t)["stationName"+(p+1)]=f,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),_:2},1024)]),_:2},1024),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"产权公司",name:"companyName"+(p+1),class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t)["companyName"+(p+1)],"onUpdate:value":f=>n(t)["companyName"+(p+1)]=f,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),_:2},1024),a(m,{span:12},{default:l(()=>[a(o,{label:"推送时间",name:"pushTime"+(p+1),class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t)["pushTime"+(p+1)],"onUpdate:value":f=>n(t)["pushTime"+(p+1)]=f,disabled:""},null,8,["value","onUpdate:value"])]),_:2},1032,["name"])]),_:2},1024)]),_:2},1024)]))),256))])):(h(),g("div",fa,[a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"电站编码",name:"stationCode",class:"f_item",required:""},{default:l(()=>[a(d,{value:n(t).stationCode,"onUpdate:value":e[2]||(e[2]=s=>n(t).stationCode=s),options:n(T),"allow-clear":"","show-search":"",disabled:"","filter-option":(s,p)=>((p==null?void 0:p.label)??"").toLowerCase().includes(s.toLowerCase()),onChange:e[3]||(e[3]=(s,p)=>U(s,p))},null,8,["value","options","filter-option"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"业主名称",name:"stationName",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).stationName,"onUpdate:value":e[4]||(e[4]=s=>n(t).stationName=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"产权公司",name:"companyName",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).companyName,"onUpdate:value":e[5]||(e[5]=s=>n(t).companyName=s),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"推送时间",name:"pushTime",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).pushTime,"onUpdate:value":e[6]||(e[6]=s=>n(t).pushTime=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})]))]),_a,y("div",ya,[a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"被置换电站编码",name:"replacementStationCode",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).replacementStationCode,"onUpdate:value":e[7]||(e[7]=s=>n(t).replacementStationCode=s),"allow-clear":"",onBlur:Ie},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"被置换电站业主姓名",name:"replacementStationName",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).replacementStationName,"onUpdate:value":e[8]||(e[8]=s=>n(t).replacementStationName=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"被置换电站所属产权公司",name:"replacementCompanyName",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).replacementCompanyName,"onUpdate:value":e[9]||(e[9]=s=>n(t).replacementCompanyName=s),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"资产原值",name:"assetValue",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).assetValue,"onUpdate:value":e[10]||(e[10]=s=>n(t).assetValue=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"总折旧月份",name:"usrMonth",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).usrMonth,"onUpdate:value":e[11]||(e[11]=s=>n(t).usrMonth=s),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"入账月份",name:"monthKey",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).monthKey,"onUpdate:value":e[12]||(e[12]=s=>n(t).monthKey=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"入账前已计提折旧额",name:"beforeAccountDepreciationCost",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).beforeAccountDepreciationCost,"onUpdate:value":e[13]||(e[13]=s=>n(t).beforeAccountDepreciationCost=s),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"入账前已计提折旧月份",name:"beforeAccountDepreciationMonth",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).beforeAccountDepreciationMonth,"onUpdate:value":e[14]||(e[14]=s=>n(t).beforeAccountDepreciationMonth=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"入账时资产价值",name:"accountAssetValue",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).accountAssetValue,"onUpdate:value":e[15]||(e[15]=s=>n(t).accountAssetValue=s),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"剩余折旧月份",name:"residueDepreciationMonth",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).residueDepreciationMonth,"onUpdate:value":e[16]||(e[16]=s=>n(t).residueDepreciationMonth=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"净残值率",name:"salvageRate",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(t).salvageRate,"onUpdate:value":e[17]||(e[17]=s=>n(t).salvageRate=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model"])])):(h(),g("div",ba,[Ca,a(Ue,{model:n(i),ref_key:"formRef2",ref:R,name:"basic",onFinish:L,"label-col":{style:{width:"165px"}}},{default:l(()=>[y("div",Na,[a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"变动类型",name:"variationType",class:"f_item",required:""},{default:l(()=>[a(d,{value:n(i).variationType,"onUpdate:value":e[18]||(e[18]=s=>n(i).variationType=s),options:n(k),disabled:n(b)===1},null,8,["value","options","disabled"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"电站编码",name:"stationCode",class:"f_item",required:""},{default:l(()=>[a(d,{value:n(i).stationCode,"onUpdate:value":e[19]||(e[19]=s=>n(i).stationCode=s),options:n(T),"allow-clear":"","show-search":"","filter-option":(s,p)=>((p==null?void 0:p.label)??"").toLowerCase().includes(s.toLowerCase()),onChange:e[20]||(e[20]=(s,p)=>U(s,p)),disabled:n(b)===1},null,8,["value","options","filter-option","disabled"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"业主名称",name:"stationName",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(i).stationName,"onUpdate:value":e[21]||(e[21]=s=>n(i).stationName=s),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"产权公司",name:"companyName",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(i).companyName,"onUpdate:value":e[22]||(e[22]=s=>n(i).companyName=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"推送时间",name:"pushTime",class:"f_item",required:""},{default:l(()=>[a(c,{value:n(i).pushTime,"onUpdate:value":e[23]||(e[23]=s=>n(i).pushTime=s),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})]),ha,y("div",ga,[a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"资产原值(元)",name:"assetValue",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).assetValue,"onUpdate:value":e[24]||(e[24]=s=>n(i).assetValue=s),style:{width:"100%"},controls:!1},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"总折旧月份",name:"usrMonth",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).usrMonth,"onUpdate:value":e[25]||(e[25]=s=>n(i).usrMonth=s),style:{width:"100%"},controls:!1,precision:0,min:1},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"入账月份",name:"monthKey",class:"f_item",required:""},{default:l(()=>[a(je,{value:n(i).monthKey,"onUpdate:value":e[26]||(e[26]=s=>n(i).monthKey=s),"value-format":"YYYY-MM",style:{width:"100%"},picker:"month"},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"入账前已计提折旧额(元)",name:"beforeAccountDepreciationCost",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).beforeAccountDepreciationCost,"onUpdate:value":e[27]||(e[27]=s=>n(i).beforeAccountDepreciationCost=s),style:{width:"100%"},controls:!1},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"入账前已计提折旧月份",name:"beforeAccountDepreciationMonth",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).beforeAccountDepreciationMonth,"onUpdate:value":e[28]||(e[28]=s=>n(i).beforeAccountDepreciationMonth=s),style:{width:"100%"},controls:!1,precision:0,min:1},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"入账时资产价值",name:"accountAssetValue",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).accountAssetValue,"onUpdate:value":e[29]||(e[29]=s=>n(i).accountAssetValue=s),style:{width:"100%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(v,{span:24},{default:l(()=>[a(m,{span:12},{default:l(()=>[a(o,{label:"剩余折旧月份",name:"residueDepreciationMonth",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).residueDepreciationMonth,"onUpdate:value":e[30]||(e[30]=s=>n(i).residueDepreciationMonth=s),disabled:"",style:{width:"100%"},controls:!1},null,8,["value"])]),_:1})]),_:1}),a(m,{span:12},{default:l(()=>[a(o,{label:"残值率",name:"salvageRate",class:"f_item",required:""},{default:l(()=>[a(_,{value:n(i).salvageRate,"onUpdate:value":e[31]||(e[31]=s=>n(i).salvageRate=s),style:{width:"100%"},controls:!1,min:0,max:1},null,8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model"])])),y("div",Ta,[a(ze,null,{default:l(()=>[a(qe,{onClick:Be},{default:l(()=>[Le("取消")]),_:1}),a(qe,{type:"primary",onClick:Pe,loading:n(C)},{default:l(()=>[Le("保存")]),_:1},8,["loading"])]),_:1})])],64)}}});const Ra=ua(Ma,[["__scopeId","data-v-492fee91"]]);export{Ra as default};
