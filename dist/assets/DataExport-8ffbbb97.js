import{_ as R}from"./index-e7bdfdf4.js";import{_ as A}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as J,c9 as F,bL as O,r as v,o as V,a as n,b as m,e as a,g as w,f as p,h as i,i as T,v as f,y,u as I,ca as $,bc as N,bd as z,cb as L,bg as K,bn as H,_ as M}from"./index-db94d997.js";import{_ as Q}from"./index-83ca18bc.js";import{_ as U}from"./index-39334618.js";import"./CaretUpOutlined-7e71a64b.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const j={class:"table-button-box"},G={class:"text-hide"},W={key:0},X={key:1,style:{color:"#29cca0"}},Y={key:2,style:{color:"#f44"}},Z=J({__name:"DataExport",setup(ee){var C;F();const h=O(),q=(e,s)=>new Promise(r=>{let o={};Array.isArray(u)?o.reportTypeList=u:o.reportType=u,o={...o,...e,templateType:h.query.templateType,delStatus:0,noJoin:!0},r(o)}),S=(C=h.query)==null?void 0:C.reportType,u=S?JSON.parse(S):"",B=v([{title:"导出文件名称",dataIndex:"reportName",search:!1,width:140},{title:"操作时间",dataIndex:"createTime",search:!1,width:100},{title:"操作人",dataIndex:"createByName",search:!1,width:100},{title:"导出状态",dataIndex:"reportStatus",key:"reportStatus",search:!1,render:!0,width:100},{title:"操作",key:"action",width:100,fixed:"right",render:!0}]),b=v(),x=(e,s)=>{var o;if(!e)return"";let r=(o=JSON.parse(s))==null?void 0:o.queryParameterDesc;if(Array.isArray(r)){const l=r.find(d=>{let c=!1;try{c=JSON.parse(d.label)===JSON.parse(e)}catch{c=d.label===e}return c});return(l==null?void 0:l.value)||""}},D=()=>{k()},k=()=>{var e;(e=b.value)==null||e.reload()},E=e=>{if(e.reportStatus===0||e.reportStatus===1||e.reportStatus===3){N.warning("文件未生成");return}const s={fileId:e.fileId,companyCode:e.companyCode};z(s)},P=e=>{const s={id:e.id,companyCode:e.companyCode};L(s).then(r=>{N.success("删除成功"),k()})};return V(()=>{}),(e,s)=>{const r=K,o=H,l=Q,d=U,c=A,g=R;return n(),m(g,{title:"导出文件",itemKey:"1",defaultShow:!0,isHideSwitch:!0},{btnRender:a(()=>[w("div",j,[p(r,{type:"primary",onClick:D},{default:a(()=>[i("刷新")]),_:1})])]),default:a(()=>[p(c,{columns:I(B),ref_key:"actionRef",ref:b,"label-col":{span:6},"wrapper-col":{span:18},"before-query-params":q,"default-query":!0,search:!1,request:I($)},{dynamicRender:a(({record:t,column:_})=>[p(o,null,{title:a(()=>[i(T(x(_.dataIndex,t.reportParam)),1)]),default:a(()=>[w("span",G,T(x(_.dataIndex,t.reportParam)),1)]),_:2},1024)]),reportStatusRender:a(({record:t})=>[t.reportStatus==0||t.reportStatus==1?(n(),f("div",W,"导出中")):y("",!0),t.reportStatus==2?(n(),f("div",X,"导出成功")):y("",!0),t.reportStatus==3||t.reportStatus==4?(n(),f("div",Y," 导出失败 ")):y("",!0)]),actionRender:a(({record:t})=>[p(d,null,{default:a(()=>[p(r,{size:"small",type:"link",disabled:t.reportStatus!=2,onClick:_=>E(t)},{default:a(()=>[i(" 下载 ")]),_:2},1032,["disabled","onClick"]),t.reportStatus===2||t.reportStatus===3?(n(),m(l,{key:0,title:"确认删除?",onConfirm:_=>P(t)},{default:a(()=>[p(r,{size:"small",type:"link"},{default:a(()=>[i(" 删除 ")]),_:1})]),_:2},1032,["onConfirm"])):(n(),m(r,{key:1,size:"small",type:"link",disabled:""},{default:a(()=>[i(" 删除 ")]),_:1}))]),_:2},1024)]),_:1},8,["columns","request"])]),_:1})}}});const _e=M(Z,[["__scopeId","data-v-1ed6cf0d"]]);export{_e as default};
