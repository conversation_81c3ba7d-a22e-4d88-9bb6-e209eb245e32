import{a2 as ze,d as Se,I as Fe,r as p,K as Ue,o as je,D as Le,at as Pe,w as H,a as b,v as G,g as s,f as i,e as c,u as n,y as L,h as f,b as P,i as C,z as X,e9 as Z,F as Re,au as ee,k as te,q as Me,c7 as Ee,bc as Ve,bU as Ye,av as Be,bf as qe,be as Oe,bI as Ae,bV as We,bg as Ke,bh as Qe,bn as $e,ay as Je,p as He,j as Ge,_ as Xe}from"./index-db94d997.js";import{m as v}from"./dealTable-c35ad2da.js";import{_ as Ze}from"./index-42d7fb9b.js";import{_ as et}from"./index-07f7e8bf.js";import{R as tt}from"./dayjs-a8e42122.js";import{_ as at}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";function lt(x){return ze({url:"/web/station/adjustElectric/v1/page",method:"POST",isTable:!0,data:x})}const w=x=>(He("data-v-f213a39c"),x=x(),Ge(),x),nt={class:"statements_container"},ot={style:{display:"flex","flex-direction":"row","align-items":"center"}},it={style:{flex:"1","padding-right":"5%"}},st=w(()=>s("span",{style:{"margin-left":"5px"}},"全选",-1)),dt={style:{flex:"1","padding-right":"5%"}},ct={style:{flex:"1","padding-right":"5%"}},rt={style:{display:"flex","flex-direction":"row","align-items":"center"}},ut={style:{flex:"1","padding-right":"5%"}},pt=w(()=>s("div",{style:{flex:"1","padding-right":"5%"}},null,-1)),mt=w(()=>s("div",{style:{flex:"1","padding-right":"5%"}},null,-1)),_t=w(()=>s("div",{class:"item_title"},"时间维度",-1)),ft={style:{display:"flex","flex-direction":"row","align-items":"center"}},ht={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},yt={style:{display:"flex","justify-content":"flex-start","flex-direction":"row"}},gt={style:{flex:"1",display:"flex","align-items":"center"}},vt={style:{display:"flex","justify-content":"flex-start","flex-direction":"row"}},xt=w(()=>s("div",{style:{flex:"1",display:"flex","align-items":"center"}},null,-1)),kt=w(()=>s("div",{class:"item_title"},"指标维度",-1)),bt={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ct={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},wt={style:{display:"flex","justify-content":"flex-end","flex-direction":"row"}},Tt={style:{display:"flex","flex-direction":"row","align-items":"center"}},It={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},Dt={class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},Nt={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},zt={class:"text-hide"},St={class:"text-hide"},Ft={class:"text-hide"},Ut=Se({__name:"index",setup(x){const ae=(t,e)=>{e.width=t},le=Fe(),N=p(),z=p(!1),ne=p([]),R=p({}),S=p([]),O=p([]),T=p(0),M=p(1),E=p(12),k=p([]),a=Ue({companyCodeList:"",time1:[],time2:[],targetTypes:["0"],companyId:[],companyId_checked:!1,companyId_indeterminate:!1,checked1:!0,checked2:!1}),A=[{label:"逆变器电量(kWh)",value:"0"},{label:"人工调整发电量(kWh)",value:"1"},{label:"调整后发电量(kWh)",value:"2"}],oe=[{label:"逆变器电量",value:"0"},{label:"人工调整发电量",value:"1"},{label:"调整后发电量",value:"2"}],h=p(1),W=p([{title:"电站编号",dataIndex:"stationCode",resizable:!0,width:120,fixed:"left",customCell:(t,e,o)=>v(h.value,t,e)},{title:"业主名称",dataIndex:"stationName",resizable:!0,width:120,fixed:"left",customCell:(t,e,o)=>v(h.value,t,e)},{title:"产权公司",dataIndex:"companyName",resizable:!0,width:120,fixed:"left",customCell:(t,e,o)=>v(h.value,t,e)},{title:"省",dataIndex:"provinceName",resizable:!0,width:120,customCell:(t,e,o)=>v(h.value,t,e)},{title:"市",dataIndex:"cityName",resizable:!0,width:120,customCell:(t,e,o)=>v(h.value,t,e)},{title:"区",dataIndex:"areaName",resizable:!0,width:120,customCell:(t,e,o)=>v(h.value,t,e)},{title:"装机容量(kW)",dataIndex:"capins",resizable:!0,width:120,customCell:(t,e,o)=>v(h.value,t,e)},{title:"指标",key:"targetType",dataIndex:"targetType",resizable:!0,width:200}]),F=p([]),V=p([]),Y=p([]);let B="",K="";const ie=(t,e,o)=>{var u,m;const d=((m=(u=o==null?void 0:o.triggerNode)==null?void 0:u.props)==null?void 0:m.level)||1;B=["provinceCode","cityCode","areaCode","town","vil"][d-1],K=t},se=t=>new Promise((e,o)=>{ee({pid:t||"0"}).then(d=>{console.log("行政区res=",d);let _=Q(d);Y.value=_,console.log(Y.value),e(!0)}).catch(()=>{o()})}),Q=t=>(t.forEach(e=>{e.label=e.name,e.value=e.code,e.isLeaf=e.level>=3,e.subDistrict&&e.subDistrict&&(e.children=e.subDistrict,Q(e.subDistrict))}),t),de=t=>{const e=a.time1;if(!e||e.length===0)return!1;const o=e[0]&&t.diff(e[0],"day")>89;return e[1]&&te(e[1]).diff(t,"day")>89||o},ce=t=>{a.time1=t},re=t=>{const e=a.time2;if(!e||e.length===0)return!1;const o=e[0]&&t.diff(e[0],"month")>11;return e[1]&&te(e[1]).diff(t,"month")>10||o},ue=t=>{a.time2=t},pe=(t,{attrs:e})=>e.vnodes,me=(t,e)=>{a[t]=e.map(o=>o.value)},_e=(t,e,o)=>{var _;((_=t==null?void 0:t.target)==null?void 0:_.checked)?me(e,o):a[e]=[],a[e+"_indeterminate"]=!1},fe=(t,e,o,d)=>{e.length===0?(a[d+"_indeterminate"]=!1,a[d+"_checked"]=!1):e.length===o.length?(a[d+"_indeterminate"]=!1,a[d+"_checked"]=!0):(a[d+"_indeterminate"]=!0,a[d+"_checked"]=!1)},he=p({time1:[{validator:async(t,e)=>{if((!e||e.length===0)&&a.checked1)return Promise.reject("请选择时间周期")},trigger:"change"}],time2:[{validator:async(t,e)=>{if((!e||e.length===0)&&a.checked2)return Promise.reject("请选择日期")},trigger:"change"}]}),ye=t=>{$(t)},ge=t=>{console.log(t)},ve=()=>{N.value.resetFields(),R.value={},S.value=[],O.value=[],F.value=[],V.value=[...W.value,...F.value],T.value=0},xe=t=>{M.value=t.current,E.value=t.pageSize,T.value=t.total;const e={...a,pageNum:t.current};console.log(e,"分页==--"),$(e)},ke=()=>{ee({pid:0}).then(t=>{if(t&&t.length>0){let e=t.map(o=>({label:o.name,value:o.code}));ne.value=e}})},$=t=>{var y,g;let e="",o="",d="",_={};t.checked1?(e="1",o=t.time1[0],d=t.time1[1]):t.checked2&&(e="2",o=t.time2[0],d=t.time2[1]),B&&(_={[B]:K});let u=Me.cloneDeep(a.companyId)||[],m={..._,timeType:e,companyCodeList:u,startTime:o,endTime:d,stationCode:t.stationCode,stationName:t.stationName,pageNum:M.value,pageSize:E.value/((y=a.targetTypes)==null?void 0:y.length),eqTarget:(g=t.targetTypes)==null?void 0:g.join(",")};m.stationCode||delete m.stationCode,m.stationName||delete m.stationName,z.value=!0,lt(m).then(I=>{z.value=!1,R.value=m,console.log("----------公司维度res=",I),be(I,1)}).catch(()=>{z.value=!1})},be=(t,e)=>{var o,d,_,u,m;if(e===1){h.value=(o=a.targetTypes)==null?void 0:o.length,F.value=((d=t==null?void 0:t.result)==null?void 0:d.timeList.map(g=>({title:g,dataIndex:g,formatMoney:!0,resizable:!0,width:120})))||[],V.value=[...W.value,...F.value],S.value=((_=t==null?void 0:t.result)==null?void 0:_.dataList)||[];let y=((u=a.targetTypes)==null?void 0:u.length)||1;T.value=t!=null&&t.total?(t==null?void 0:t.total)*y:0}else if(e===2){O.value=(t==null?void 0:t.result)||[];let y=((m=a.targetTypes)==null?void 0:m.length)||1;T.value=t!=null&&t.total?(t==null?void 0:t.total)*y:0}};je(()=>{Ce(),se(),ke()}),Le(()=>{});const Ce=()=>{Pe({}).then(t=>{let o=(t||[]).map(d=>({label:d.companyName,value:d.companyCode}));k.value=o})};H(()=>a.checked1,t=>{t&&(a.checked2=!1,a.weiType="1",a.time2="",N.value.clearValidate(["time2"]))}),H(()=>a.checked2,t=>{t&&(a.checked1=!1,a.weiType="2",a.time1=[],N.value.clearValidate(["time1"]))});const we=()=>{le.push({path:"/financeManage/electricQuantity/realElectricQuantity/station/dataImport",query:{templateType:13,fileType:".csv,.xls,.xlsx",fileSize:30}})},U=p(!1),Te=()=>{let t={...R.value,queryParameterDesc:[]},e={reportType:8,reportParam:JSON.stringify(t)};U.value=!0,Ee(e).then(o=>{Ve.info("数据导出中，稍后请去导出中心查看"),U.value=!1}).catch(o=>{U.value=!1})};return(t,e)=>{const o=Ye,d=Ze,_=Be,u=qe,m=Oe,y=et,g=Ae,I=tt,Ie=We,j=Ke,J=at,De=Qe,q=$e,Ne=Je;return b(),G(Re,null,[s("div",nt,[i(De,{ref_key:"formRef",ref:N,name:"formRef",model:n(a),rules:n(he),onFinish:ye,onFinishFailed:ge},{default:c(()=>[s("div",ot,[s("div",it,[i(u,{label:"产权公司",name:"companyId"},{default:c(()=>[i(_,{value:n(a).companyId,"onUpdate:value":e[3]||(e[3]=l=>n(a).companyId=l),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:n(k),mode:"multiple","filter-option":(l,r)=>((r==null?void 0:r.label)??"").toLowerCase().includes(l.toLowerCase()),onChange:e[4]||(e[4]=(l,r)=>fe(l,r,n(k),"companyId"))},{dropdownRender:c(({menuNode:l})=>[n(k)&&n(k).length>0?(b(),G("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:e[2]||(e[2]=r=>r.preventDefault())},[i(o,{checked:n(a).companyId_checked,"onUpdate:checked":e[0]||(e[0]=r=>n(a).companyId_checked=r),indeterminate:n(a).companyId_indeterminate,onChange:e[1]||(e[1]=r=>_e(r,"companyId",n(k)))},null,8,["checked","indeterminate"]),st],32)):L("",!0),i(d,{style:{margin:"4px 0"}}),i(pe,{vnodes:l},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1})]),s("div",dt,[i(u,{label:"电站编号",name:"stationCode"},{default:c(()=>[i(m,{value:n(a).stationCode,"onUpdate:value":e[5]||(e[5]=l=>n(a).stationCode=l),style:{width:"100%"},placeholder:"请输入"},null,8,["value"])]),_:1})]),s("div",ct,[i(u,{label:"业主名称",name:"stationName"},{default:c(()=>[i(m,{value:n(a).stationName,"onUpdate:value":e[6]||(e[6]=l=>n(a).stationName=l),style:{width:"100%"},placeholder:"请输入"},null,8,["value"])]),_:1})])]),s("div",rt,[s("div",ut,[i(u,{label:"行政区划",name:"cityTree"},{default:c(()=>[i(y,{value:n(a).cityTree,"onUpdate:value":e[7]||(e[7]=l=>n(a).cityTree=l),"show-search":"",style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},placeholder:"请选择","allow-clear":"","tree-data":n(Y),"tree-node-filter-prop":"label",onChange:ie},null,8,["value","tree-data"])]),_:1})]),pt,mt]),_t,s("div",ft,[s("div",ht,[s("div",yt,[i(u,{name:"checked1"},{default:c(()=>[i(g,{checked:n(a).checked1,"onUpdate:checked":e[8]||(e[8]=l=>n(a).checked1=l)},{default:c(()=>[f("日维度：")]),_:1},8,["checked"])]),_:1})]),i(u,{name:"time1",style:{flex:"1",display:"flex"}},{default:c(()=>[i(I,{value:n(a).time1,"onUpdate:value":e[9]||(e[9]=l=>n(a).time1=l),style:{width:"100%"},disabled:!n(a).checked1,"disabled-date":de,onCalendarChange:ce,valueFormat:"YYYY-MM-DD"},null,8,["value","disabled"])]),_:1})]),s("div",gt,[s("div",vt,[i(u,{name:"checked2"},{default:c(()=>[i(g,{checked:n(a).checked2,"onUpdate:checked":e[10]||(e[10]=l=>n(a).checked2=l)},{default:c(()=>[f("月维度：")]),_:1},8,["checked"])]),_:1})]),i(u,{name:"time2",style:{flex:"1",display:"flex"}},{default:c(()=>[i(I,{value:n(a).time2,"onUpdate:value":e[11]||(e[11]=l=>n(a).time2=l),style:{width:"100%"},disabled:!n(a).checked2,"disabled-date":re,onCalendarChange:ue,valueFormat:"YYYY-MM",picker:"month"},null,8,["value","disabled"])]),_:1})]),xt]),kt,s("div",bt,[s("div",Ct,[s("div",wt,[i(u,{name:"targetTypes"},{default:c(()=>[i(Ie,{value:n(a).targetTypes,"onUpdate:value":e[12]||(e[12]=l=>n(a).targetTypes=l),name:"checkboxgroup",options:oe},null,8,["value"])]),_:1})])])]),s("div",Tt,[s("div",It,[i(J,null,{default:c(()=>[i(j,{onClick:ve},{default:c(()=>[f("重置")]),_:1}),i(j,{type:"primary","html-type":"submit"},{default:c(()=>[f("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),s("div",Dt,[s("div",Nt,[i(J,null,{default:c(()=>[i(j,{type:"default",onClick:we},{default:c(()=>[f("导入调整电量")]),_:1}),n(S).length>0?(b(),P(j,{key:0,type:"primary",loading:n(U),onClick:Te},{default:c(()=>[f("导出")]),_:1},8,["loading"])):L("",!0)]),_:1})]),i(Ne,{style:{"margin-bottom":"24px"},columns:n(V),"data-source":n(S),pagination:{current:n(M),total:n(T),showTotal:l=>`共 ${l} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0,pageSizeOptions:["6","12","24","60","120"],pageSize:n(E)},scroll:{y:500},loading:n(z),onResizeColumn:ae,onChange:xe},{bodyCell:c(({column:l,record:r})=>[l!=null&&l.formatMoney&&!l.render?(b(),P(q,{key:0},{title:c(()=>[f(C(n(X)(r[l.dataIndex])),1)]),default:c(()=>[s("span",zt,C(n(X)(r[l.dataIndex])),1)]),_:2},1024)):l!=null&&l.formatFixed&&!l.render?(b(),P(q,{key:1},{title:c(()=>[f(C(r[l.dataIndex]&&n(Z)(r[l.dataIndex])),1)]),default:c(()=>[s("span",St,C(r[l.dataIndex]&&n(Z)(r[l.dataIndex])),1)]),_:2},1024)):L("",!0),l.key=="targetType"?(b(),P(q,{key:2},{title:c(()=>{var D;return[f(C((D=A[r[l.dataIndex]])==null?void 0:D.label),1)]}),default:c(()=>{var D;return[s("span",Ft,C((D=A[r[l.dataIndex]])==null?void 0:D.label),1)]}),_:2},1024)):L("",!0)]),_:1},8,["columns","data-source","pagination","loading"])])],64)}}});const qt=Xe(Ut,[["__scopeId","data-v-f213a39c"]]);export{qt as default};
