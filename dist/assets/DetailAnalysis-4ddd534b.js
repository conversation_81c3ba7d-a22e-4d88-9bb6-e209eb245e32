import{_ as T}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as k,r as i,I as v,o as E,D as A,at as q,a as y,v as M,f as d,e as n,u as f,b as B,h,y as R,i as b,g as F,bg as V,bn as Y,_ as j}from"./index-db94d997.js";import{g,e as O}from"./index-62e1f9f0.js";import{_ as G}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const H={class:"areaPrice"},J={class:"text-hide"},K=k({__name:"DetailAnalysis",setup(Q){const s=i(),x=["provinceCodeList","cityCodeList","areaCodeList"],w=i([]),u=i([]);v();const C=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0,search:!0,order:3},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:4},{title:"账期选择",key:"status",dataIndex:"paymentMonths",valueType:"select",valueEnum:[{label:"1个月",value:1},{label:"2个月",value:2},{label:"3个月",value:3}],resizable:!0,hideInTable:!0,width:100,order:1,rules:[{required:!0,message:"请选择"}]},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:120,valueEnum:u,render:!0,resizable:!0,order:5},{title:"省",dataIndex:"provinceName",width:120,resizable:!0,search:!1},{title:"市",dataIndex:"cityName",width:120,resizable:!0,search:!1},{title:"区",dataIndex:"areaName",width:120,resizable:!0,search:!1},{title:"收款账期",dataIndex:"collectionPeriod",valueType:"date",dateFormat:"YYYY-MM",width:120,resizable:!0,search:!0,order:2,rules:[{required:!0,message:"请选择"}]},{title:"应收电费",dataIndex:"electricityReceivableSum",width:120,resizable:!0,search:!1},{title:"实收电费",dataIndex:"actualElectricitySum",width:120,search:!1,resizable:!0},{title:"待回款金额",dataIndex:"returnMoneySum",width:120,search:!1,resizable:!0}],D=()=>{s.value.reset()};E(()=>{I(),console.log("getAnalyseList=",g)}),A(()=>{s.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&z()});const I=()=>{q({}).then(e=>{console.log("产权公司res=",e);let o=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));u.value=o})},N=()=>{var a;let e=(a=s.value)==null?void 0:a.getInitialFormStateNew();O(e)},p=i([]),S=e=>{p.value=e||[],console.log("dataSource=",e)},z=()=>{var e;(e=s.value)==null||e.reload()},L=e=>{const a=new Map;return e.forEach(o=>{const t=x[o.level-1];if(a.has(t))a.get(t).push(o.value);else{let r=[];r.push(o.value),a.set(t,r)}}),Object.fromEntries(a)},m=(e,a,o)=>!e||!a?[]:(e.forEach(t=>{a.find(l=>t.value===l)&&o.push(t),t.children&&t.children.length>0&&m(t.children,a,o)}),o),P=(e,a)=>new Promise(o=>{let r={delStatus:0,noJoin:!0,actualProceedsData:e!=null&&e.collectionPeriod?[e==null?void 0:e.collectionPeriod]:[""]};const l=m(w.value,e==null?void 0:e.cityTree,[]);let _=L(l);e==null||delete e.collectionPeriod;const c={...r,...e,..._};o(c)});return(e,a)=>{const o=V,t=G,r=Y,l=T;return y(),M("div",H,[d(l,{columns:C,ref_key:"actionRef",ref:s,request:f(g),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:S,"before-query-params":P,"default-query":!1,onCancel:D},{tableHeader:n(()=>[d(t,null,{default:n(()=>[f(p).length>0?(y(),B(o,{key:0,type:"primary",onClick:N},{default:n(()=>[h("导出")]),_:1})):R("",!0)]),_:1})]),companyCodeListRender:n(({column:_,record:c,index:U})=>[d(r,null,{title:n(()=>[h(b(c.companyName),1)]),default:n(()=>[F("span",J,b(c.companyName),1)]),_:2},1024)]),_:1},8,["request"])])}}});const se=j(K,[["__scopeId","data-v-92ff9f8b"]]);export{se as default};
