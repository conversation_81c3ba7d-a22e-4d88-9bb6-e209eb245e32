import{_ as E}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as F,r as p,I as R,o as q,D as z,at as B,a as y,v as f,f as s,e as o,h as _,b as V,y as Y,i as h,g as C,u as j,q as O,bg as A,bn as G,_ as H}from"./index-db94d997.js";import{g as J,e as Q}from"./index-bad8f65c.js";import{_ as U}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const $={class:"areaPrice"},W={class:"text-hide"},X={class:"text-hide"},Z={key:0},v={key:1},ee=F({__name:"Match",setup(te){const m=p(),w=["provinceCodeList","cityCodeList","areaCodeList"],I=p([]),u=p([]);p(!1);const x=R(),k=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:120,valueEnum:u,order:1,render:!0,fixed:"left"},{title:"账单周期",dataIndex:"yKey",valueType:"dateRange",width:120,dateFormat:"YYYY-MM",hideInTable:!0,order:2},{title:"实际电费总金额(元)",dataIndex:"feeSum",search:!1,resizable:!0,formatMoney:!0,width:120},{title:"收款月份",dataIndex:"monthKey",search:!1,resizable:!0,width:120},{title:"操作时间",dataIndex:"updateTime",search:!1,resizable:!0,width:120},{title:"收款方式",dataIndex:"proceedsType",search:!1,resizable:!0,width:80},{title:"操作",key:"action",dataIndex:"action",search:!1,width:80,render:!0,align:"center",fixed:"right"}];q(()=>{M()}),z(()=>{m.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&L()});const M=()=>{B({}).then(e=>{console.log("产权公司res=",e);let n=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));u.value=n})},D=(e,a)=>{x.push({path:"/financeManage/feeManage/realFee/match/detail",state:{pdata:O.cloneDeep(e)}})},K=()=>{var a;let e=(a=m.value)==null?void 0:a.getInitialFormStateNew();Q(e)},S=()=>{x.push({path:"/financeManage/feeManage/realFee/match/dataImport",query:{templateType:4,fileType:".csv,.xls,.xlsx",fileSize:30}})},g=p([]),T=e=>{g.value=e||[],console.log("dataSource=",e)},L=()=>{var e;(e=m.value)==null||e.reload()},N=e=>{const a=new Map;return e.forEach(n=>{const t=w[n.level-1];if(a.has(t))a.get(t).push(n.value);else{let r=[];r.push(n.value),a.set(t,r)}}),Object.fromEntries(a)},b=(e,a,n)=>!e||!a?[]:(e.forEach(t=>{a.find(l=>t.value===l)&&n.push(t),t.children&&t.children.length>0&&b(t.children,a,n)}),n),P=(e,a)=>new Promise(n=>{const t=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[0]:"",r=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[1]:"";let l={delStatus:0,noJoin:!0,startMonthKey:t,endMonthKey:r};const i=b(I.value,e==null?void 0:e.cityTree,[]);let c=N(i);e==null||delete e.yKey;const d={...l,...e,...c};n(d)});return(e,a)=>{const n=A,t=U,r=G,l=E;return y(),f("div",$,[s(l,{columns:k,ref_key:"actionRef",ref:m,request:j(J),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:T,"before-query-params":P},{tableHeader:o(()=>[s(t,null,{default:o(()=>[s(n,{onClick:S},{default:o(()=>[_("批量上传")]),_:1}),g.value.length>0?(y(),V(n,{key:0,type:"primary",onClick:K},{default:o(()=>[_("导出")]),_:1})):Y("",!0)]),_:1})]),companyCodeListRender:o(({column:i,record:c,index:d})=>[s(r,null,{title:o(()=>[_(h(c.companyName),1)]),default:o(()=>[C("span",W,h(c.companyName),1)]),_:2},1024)]),yKeyRender:o(({column:i,record:c,index:d})=>[s(r,null,{title:o(()=>[_(h(c[i.dataIndex]),1)]),default:o(()=>[C("span",X,h(c[i.dataIndex]),1)]),_:2},1024)]),statusRender:o(({column:i,record:c,index:d})=>[c[i.dataIndex]===1?(y(),f("span",Z,"导入完成")):(y(),f("span",v,"导入失败"))]),actionRender:o(({column:i,record:c,index:d})=>[s(t,null,{default:o(()=>[s(n,{size:"small",type:"link",onClick:()=>D(c,d)},{default:o(()=>[_(" 查看明细 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const pe=H(ee,[["__scopeId","data-v-07c734af"]]);export{pe as default};
