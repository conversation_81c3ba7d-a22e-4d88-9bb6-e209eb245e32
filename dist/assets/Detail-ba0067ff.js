import{_ as I}from"./index-4c4cf647.js";import{d as V,I as E,r,K as S,o as j,at as K,a as W,v as $,g as T,f as e,e as a,u as n,h as m,F as z,k as v,bc as G,av as H,bf as J,aw as O,be as Q,ax as X,bh as Z,bg as ee,_ as ae}from"./index-db94d997.js";import{s as te}from"./index-3dae1b9e.js";import{D as le}from"./dayjs-a8e42122.js";import{_ as ne}from"./index-4a280682.js";import{_ as oe}from"./index-39334618.js";import"./index-e7bdfdf4.js";import"./CaretUpOutlined-7e71a64b.js";import"./weiZhi-78534cab.js";import"./customParseFormat-ed0c33ac.js";const se={class:"card",style:{"margin-bottom":"24px"}},de={class:"footer"},ue=V({__name:"Detail",setup(ie){var D,Y,U;const b=E(),g=r(),C=r(),c=r(!1),w=r(((D=history.state)==null?void 0:D.pdata)||{});r(((Y=history.state)==null?void 0:Y.type)||"");const h=r([]);r([{label:"是",value:1},{label:"否",value:0}]),r([{label:"运维协商中",value:1},{label:"公司确认中",value:2},{label:"已完成",value:3}]),r([{label:"已结案",value:1},{label:"未结案",value:0}]);const t=S(((U=history.state)==null?void 0:U.pdata)||{});j(()=>{M()});const A=u=>u&&t.endTime&&v(u.format("YYYY-MM-DD")).isAfter(v(t.endTime)),k=u=>u&&t.startTime&&v(t.startTime).isAfter(v(u.format("YYYY-MM-DD"))),L=(u,l)=>{t.companyName=l.label},M=()=>{K({}).then(u=>{console.log("产权公司res=",u);let _=(u||[]).map(s=>({label:s.companyName,value:s.companyCode}));h.value=_})},N=()=>{b.go(-1)},B=()=>{g.value.validateFields().then(u=>{var d;const l=((d=C.value)==null?void 0:d.getFileList())||[];console.log("fileList=",l);const _=l.map(i=>i.id);let s={...u,fileIdList:_,companyName:t.companyName,id:t.id};console.log("params=",s),c.value=!0,te(s).then(i=>{console.log("新建保单res=",i),c.value=!1,G.info("保存成功"),b.go(-1)}).catch(i=>{c.value=!1})})};return(u,l)=>{const _=H,s=J,d=O,i=Q,p=X,q=le,f=ne,R=Z,F=I,x=ee,P=oe;return W(),$(z,null,[T("div",se,[e(R,{model:n(t),name:"formRef",ref_key:"formRef",ref:g,autocomplete:"off","label-col":{style:{width:"120px",paddingRight:"8px"}}},{default:a(()=>[e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"产权公司",name:"companyCode",required:""},{default:a(()=>[e(_,{placeholder:"请选择",value:n(t).companyCode,"onUpdate:value":l[0]||(l[0]=o=>n(t).companyCode=o),options:n(h),style:{width:"60%"},allowClear:"",showArrow:"","show-search":"","filter-option":(o,y)=>((y==null?void 0:y.label)??"").toLowerCase().includes(o.toLowerCase()),onChange:L},null,8,["value","options","filter-option"])]),_:1})]),_:1}),e(d,{span:12},{default:a(()=>[e(s,{label:"险种",name:"insCategory",required:""},{default:a(()=>[e(i,{placeholder:"请输入",value:n(t).insCategory,"onUpdate:value":l[1]||(l[1]=o=>n(t).insCategory=o),style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"保险公司",name:"insCompany",required:""},{default:a(()=>[e(i,{placeholder:"请输入",value:n(t).insCompany,"onUpdate:value":l[2]||(l[2]=o=>n(t).insCompany=o),style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),e(d,{span:12},{default:a(()=>[e(s,{label:"保单号",name:"insNumber",required:""},{default:a(()=>[e(i,{placeholder:"请输入",value:n(t).insNumber,"onUpdate:value":l[3]||(l[3]=o=>n(t).insNumber=o),style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"投保方",name:"insProvider",required:""},{default:a(()=>[e(i,{placeholder:"请输入",value:n(t).insProvider,"onUpdate:value":l[4]||(l[4]=o=>n(t).insProvider=o),style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),e(d,{span:12},{default:a(()=>[e(s,{label:"第一受益人",name:"insBeneficiaries",required:""},{default:a(()=>[e(i,{placeholder:"请输入",value:n(t).insBeneficiaries,"onUpdate:value":l[5]||(l[5]=o=>n(t).insBeneficiaries=o),style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"保险开始时间",name:"startTime",required:""},{default:a(()=>[e(q,{placeholder:"请选择",value:n(t).startTime,"onUpdate:value":l[6]||(l[6]=o=>n(t).startTime=o),"value-format":"YYYY-MM-DD",style:{width:"60%"},"disabled-date":A},null,8,["value"])]),_:1})]),_:1}),e(d,{span:12},{default:a(()=>[e(s,{label:"保险结束时间",name:"endTime",required:""},{default:a(()=>[e(q,{placeholder:"请选择",value:n(t).endTime,"onUpdate:value":l[7]||(l[7]=o=>n(t).endTime=o),controls:!1,"value-format":"YYYY-MM-DD",style:{width:"60%"},"disabled-date":k},null,8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"总保额",name:"insLimit",required:""},{default:a(()=>[e(f,{placeholder:"请输入",value:n(t).insLimit,"onUpdate:value":l[8]||(l[8]=o=>n(t).insLimit=o),controls:!1,style:{width:"60%"}},{addonAfter:a(()=>[m("元")]),_:1},8,["value"])]),_:1})]),_:1}),e(d,{span:12},{default:a(()=>[e(s,{label:"总保费",name:"insCosts",required:""},{default:a(()=>[e(f,{placeholder:"请输入",value:n(t).insCosts,"onUpdate:value":l[9]||(l[9]=o=>n(t).insCosts=o),controls:!1,style:{width:"60%"}},{addonAfter:a(()=>[m("元")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"装机容量",name:"capins",required:""},{default:a(()=>[e(f,{placeholder:"请输入",value:n(t).capins,"onUpdate:value":l[10]||(l[10]=o=>n(t).capins=o),controls:!1,style:{width:"60%"}},{addonAfter:a(()=>[m("万kW")]),_:1},8,["value"])]),_:1})]),_:1}),e(d,{span:12},{default:a(()=>[e(s,{label:"电站数量",name:"stationCount",required:""},{default:a(()=>[e(f,{placeholder:"请输入",value:n(t).stationCount,"onUpdate:value":l[11]||(l[11]=o=>n(t).stationCount=o),controls:!1,style:{width:"60%"}},{addonAfter:a(()=>[m("个")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1}),e(p,{span:24},{default:a(()=>[e(d,{span:12},{default:a(()=>[e(s,{label:"固定资产原值",name:"fixedAssets",required:""},{default:a(()=>[e(f,{placeholder:"请输入",value:n(t).fixedAssets,"onUpdate:value":l[12]||(l[12]=o=>n(t).fixedAssets=o),controls:!1,style:{width:"60%"}},{addonAfter:a(()=>[m("元")]),_:1},8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),e(F,{ref_key:"uploadRef",ref:C,businessType:9,relationCode:n(w).id,companyCode:n(w).companyCode},null,8,["relationCode","companyCode"]),T("div",de,[e(P,null,{default:a(()=>[e(x,{onClick:N},{default:a(()=>[m("取消")]),_:1}),e(x,{type:"primary",onClick:B,loading:n(c)},{default:a(()=>[m("保存")]),_:1},8,["loading"])]),_:1})])],64)}}});const Ce=ae(ue,[["__scopeId","data-v-d740f070"]]);export{Ce as default};
