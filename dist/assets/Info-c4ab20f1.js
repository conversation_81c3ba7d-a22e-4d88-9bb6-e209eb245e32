import{_ as H}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as W,r as _,a as P,v as j,g as e,i as s,u as t,z as A,f as h,e as i,b as G,h as R,y as O,bO as T,F as Q,bg as $,bn as J,p as K,j as U,_ as X}from"./index-db94d997.js";import{a as Z,b as ee}from"./index-3dae1b9e.js";import{_ as te}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const a=l=>(K("data-v-d901c2a9"),l=l(),U(),l),se={class:"card top"},ae=a(()=>e("div",{class:"title"},"保单信息",-1)),oe={class:"content"},ie={class:"item"},ne=a(()=>e("span",{class:"label"},"产权公司：",-1)),le={class:"value"},ce={class:"item"},de=a(()=>e("span",{class:"label"},"装机容量(万kW)：",-1)),_e={class:"value"},re={class:"item"},pe=a(()=>e("span",{class:"label"},"电站个数：",-1)),me={class:"value"},he={class:"item"},ue=a(()=>e("span",{class:"label"},"险种：",-1)),be={class:"value"},ve={class:"item"},fe=a(()=>e("span",{class:"label"},"保单号：",-1)),xe={class:"value"},ye={class:"item"},Ie=a(()=>e("span",{class:"label"},"保险公司：",-1)),we={class:"value"},ze={class:"item"},ge=a(()=>e("span",{class:"label"},"保险开始时间：",-1)),Ce={class:"value"},Ne={class:"item"},De=a(()=>e("span",{class:"label"},"保险结束时间：",-1)),Me={class:"value"},Se={class:"item"},ke=a(()=>e("span",{class:"label"},"保险周期(月)：",-1)),Ye={class:"value"},Be={class:"item"},Fe=a(()=>e("span",{class:"label"},"投保方：",-1)),Pe={class:"value"},Re={class:"item"},Te=a(()=>e("span",{class:"label"},"第一受益人：",-1)),Le={class:"value"},Ve={class:"item"},qe=a(()=>e("span",{class:"label"},"固定资产净值(元)：",-1)),Ee={class:"value"},He={class:"item"},We=a(()=>e("span",{class:"label"},"总保额(元)：",-1)),je={class:"value"},Ae={class:"item"},Ge=a(()=>e("span",{class:"label"},"总保费(元)：",-1)),Oe={class:"value"},Qe=a(()=>e("span",{class:"table_header_left"},"投保电站明细",-1)),$e={class:"text-hide"},Je=W({__name:"Info",setup(l){var v;const o=_(((v=history.state)==null?void 0:v.pdata)||{});_([]);const u=_([]),b=_(),L=[{title:"电站编号",dataIndex:"psCode",width:120,resizable:!0},{title:"业主名称",dataIndex:"psName",width:120,resizable:!0},{title:"产权公司",dataIndex:"companyName",width:120,resizable:!0},{title:"装机容量(kW)",dataIndex:"capins",resizable:!0,width:120},{title:"发电客户编号",dataIndex:"customerNumber",resizable:!0,width:120},{title:"省",dataIndex:"prvName",resizable:!0,width:120},{title:"市",dataIndex:"cityName",resizable:!0,width:120},{title:"区",dataIndex:"distName",resizable:!0,width:120},{title:"详细地址",dataIndex:"lco",resizable:!0,width:120},{title:"保额(元)",dataIndex:"insLimit",resizable:!0,formatMoney:!0,width:120},{title:"费率",key:"insRate",dataIndex:"insRate",resizable:!0,width:120,render:!0},{title:"保费(元)",dataIndex:"insCosts",resizable:!0,formatMoney:!0,width:120},{title:"保险开始时间",dataIndex:"startTime",dateFormat:"YYYY-MM-DD",resizable:!0,width:120},{title:"保险结束时间",dataIndex:"endTime",dateFormat:"YYYY-MM-DD",resizable:!0,width:120},{title:"保险周期(月)",dataIndex:"insCycle",resizable:!0,width:120}],V=()=>{var c;let n=(c=b.value)==null?void 0:c.getInitialFormStateNew();ee(n)},q=n=>{u.value=n||[]},E=(n,c)=>{let r={};return new Promise(p=>{var d;const m={...r,...n,delStatus:0,policyManagementId:(d=o.value)==null?void 0:d.id};p(m)})};return(n,c)=>{var f,x,y,I,w,z,g,C,N,D,M,S,k,Y;const r=$,p=te,m=J,d=H;return P(),j(Q,null,[e("div",se,[ae,e("div",oe,[e("div",ie,[ne,e("span",le,s((f=t(o))==null?void 0:f.companyName),1)]),e("div",ce,[de,e("span",_e,s(t(A)((x=t(o))==null?void 0:x.capins)),1)]),e("div",re,[pe,e("span",me,s((y=t(o))==null?void 0:y.stationCount),1)]),e("div",he,[ue,e("span",be,s((I=t(o))==null?void 0:I.insCategory),1)]),e("div",ve,[fe,e("span",xe,s((w=t(o))==null?void 0:w.insNumber),1)]),e("div",ye,[Ie,e("span",we,s((z=t(o))==null?void 0:z.insCompany),1)]),e("div",ze,[ge,e("span",Ce,s((g=t(o))==null?void 0:g.startTime),1)]),e("div",Ne,[De,e("span",Me,s((C=t(o))==null?void 0:C.endTime),1)]),e("div",Se,[ke,e("span",Ye,s((N=t(o))==null?void 0:N.insCycle),1)]),e("div",Be,[Fe,e("span",Pe,s((D=t(o))==null?void 0:D.insProvider),1)]),e("div",Re,[Te,e("span",Le,s((M=t(o))==null?void 0:M.insBeneficiaries),1)]),e("div",Ve,[qe,e("span",Ee,s((S=t(o))==null?void 0:S.fixedAssets),1)]),e("div",He,[We,e("span",je,s((k=t(o))==null?void 0:k.insLimit),1)]),e("div",Ae,[Ge,e("span",Oe,s((Y=t(o))==null?void 0:Y.insCosts),1)])])]),e("div",null,[h(d,{columns:L,ref_key:"actionRef",ref:b,request:t(Z),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:q,"before-query-params":E,search:!1},{tableHeader:i(()=>[h(p,null,{default:i(()=>[t(u).length>0?(P(),G(r,{key:0,onClick:V,type:"primary"},{default:i(()=>[R("导出")]),_:1})):O("",!0)]),_:1})]),tableHeaderLeft:i(()=>[Qe]),insRateRender:i(({record:B,column:F,index:Ke})=>[h(m,null,{title:i(()=>[R(s(t(T)(B[F.dataIndex],100)+"%"),1)]),default:i(()=>[e("span",$e,s(t(T)(B[F.dataIndex],100)+"%"),1)]),_:2},1024)]),_:1},8,["request"])])],64)}}});const nt=X(Je,[["__scopeId","data-v-d901c2a9"]]);export{nt as default};
