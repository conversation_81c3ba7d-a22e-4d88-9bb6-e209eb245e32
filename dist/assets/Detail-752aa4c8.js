import{_ as oe}from"./index-4c4cf647.js";import{d as ne,I as le,r as g,K as B,o as se,b$ as ue,q as F,w as W,a as de,v as ie,g as h,i as re,u as l,f as a,e as o,h as S,s as pe,F as ce,bi as L,c0 as me,bc as I,bW as fe,be as _e,bf as ge,aw as be,ax as ye,bh as he,bg as Ce,av as we,p as Ne,j as Ue,_ as Pe}from"./index-db94d997.js";import{a as Re,b as ke,c as Se,d as Ie}from"./index-0abb3d3a.js";import{b as Ve,c as Ye}from"./index-5fcafee1.js";import{w as qe,m as Te}from"./dictLocal-9822709a.js";import{D as ve}from"./dayjs-a8e42122.js";import{_ as xe}from"./index-4a280682.js";import{_ as Me}from"./index-39334618.js";import"./index-e7bdfdf4.js";import"./CaretUpOutlined-7e71a64b.js";import"./weiZhi-78534cab.js";import"./customParseFormat-ed0c33ac.js";const A=w=>(Ne("data-v-61e11b7c"),w=w(),Ue(),w),De={class:"page_title"},Be={class:"card",style:{"margin-bottom":"24px"}},Fe=A(()=>h("div",{class:"card_title"},"回购测算",-1)),We={class:"card",style:{"margin-bottom":"24px"}},Le=A(()=>h("div",{class:"card_title"},"测算结果",-1)),Ae={class:"footer"},je=ne({__name:"Detail",setup(w){var x;const V=le(),N=g(),Y=g(),y=g(!1),c=g(((x=history.state)==null?void 0:x.pdata)||{});let n=B({stationCode:"",stationName:"",companyCode:"",companyName:"",subassemblyNum:"",capinsWatt:"",capinsUnit:"",agreedTermYear:null,datongrd:"",endMonth:"",originalValue:"",salvageRate:"",netValue:null,productPriceSum:null,productPrice:null});const b=g(!1),U=g(!1),C=B({}),R=g(),q=g([]),T=g([]),k=g(""),j={stationCode:[{required:!0}],stationName:[{required:!0}],companyName:[{required:!0}],subassemblyNum:[{required:!0}],capinsWatt:[{required:!0}],capinsUnit:[{required:!0}],agreedTermYear:[{required:!0,validator:async(e,t)=>!t&&t!==0?Promise.reject("请输入合同约定合作年限"):(console.log(typeof t),!Number.isInteger(t)||t<0?Promise.reject("请输入正整数"):Promise.resolve()),trigger:"change"}],datongrd:[{required:!0}],endMonth:[{required:!0}],originalValue:[{required:!0}],salvageRate:[{required:!0,validator:async(e,t)=>!t&&t!==0?Promise.reject("请输入合同约定残值率"):t<0||t>1?Promise.reject("请输入大于等于0且小于1的数"):Promise.resolve(),trigger:"change"}],netValue:[{required:!0}],productPriceSum:[{required:!0}],productPrice:[{required:!0}]};se(()=>{var e,t,u;if(G(),(e=c.value)!=null&&e.id){for(let i in n)n[i]=c.value[i];n.agreedTermYear=(t=c.value)!=null&&t.agreedTermYear?(u=c.value)==null?void 0:u.agreedTermYear:null}}),ue(()=>{var e;(e=N.value)==null||e.resetFields()});const K=async e=>{var u;const t=(u=e.target)==null?void 0:u.value;t&&await E(t)},$=()=>{L.warning({title:"未匹配到该电站",icon:a(me),content:"",okText:"",closable:!0,class:"test"})},O=e=>{},E=F.debounce(e=>{console.log(e),Re({stationUniqueId:e}).then(u=>{var i,d,r,p,_;if(console.log("回购测算--",u),u&&(u!=null&&u.length)){for(let m in n)m!="stationCode"&&m!="netValue"&&m!="productPriceSum"&&m!="productPrice"&&(n[m]=u[0][m]);n.companyName=(i=u[0])==null?void 0:i.ownerCompanyName,n.companyCode=(d=u[0])==null?void 0:d.ownerCompanyCode,n.subassemblyNum=(r=u[0])==null?void 0:r.componentQuantity,n.originalValue=(p=u[0])==null?void 0:p.assetValue}else(_=N.value)==null||_.resetFields(["stationName","companyName","subassemblyNum","capinsWatt","datongrd"]),$()}).catch(u=>{console.log(u)})},400),v=F.debounce(()=>{const e={...n};ke(e).then(t=>{console.log("测算结果--",t),n.netValue=t==null?void 0:t.netValue,n.productPriceSum=t==null?void 0:t.productPriceSum,n.productPrice=t==null?void 0:t.productPrice})},600),Q=()=>{V.go(-1)},z=()=>{N.value.validateFields().then(e=>{var r,p,_,m,P;const t=((r=Y.value)==null?void 0:r.getFileList())||[];console.log("fileList=",t);const u=t.map(f=>f.id),i=(p=c.value)!=null&&p.relationCode?(_=c.value)==null?void 0:_.relationCode:"";let d={...e,companyCode:n.companyCode,fileIdList:u,relationCode:i,instanceId:"1"};console.log("params===",d),y.value=!0,(m=c.value)!=null&&m.id?(d={...d,id:(P=c.value)==null?void 0:P.id},Ie(d).then(f=>{console.log("编辑出售表单res=",f),I.info("编辑成功"),y.value=!1,b.value=!0,k.value=f}).catch(f=>{y.value=!1})):Se(d).then(f=>{console.log("新建出售表单res=",f),I.info("保存成功"),y.value=!1,b.value=!0,k.value=f}).catch(f=>{y.value=!1})})},G=()=>{Ve().then(e=>{q.value=e==null?void 0:e.map(t=>({label:t.roleName,value:t.id}))})},H=e=>{Ye({roleId:e}).then(t=>{T.value=t==null?void 0:t.map(u=>({label:u.userName,value:u.userId}))})},J=e=>{console.log(`selected ${e}`),H(e)},X=()=>{var e;b.value=!1,(e=R.value)==null||e.resetFields()},Z=()=>{R.value.validateFields().then(e=>{var r,p;console.log("审批流---",e);const t=(r=c.value)!=null&&r.id?(p=c.value)==null?void 0:p.id:k.value,u=qe[1].value,i=Te[0].value;let d={assigneeId:e.assigneeId,relationId:t,businessKey:u,processKey:i};U.value=!0,fe(d).then(_=>{b.value=!1,U.value=!1,I.info("保存成功"),V.go(-1)}).catch(_=>{U.value=!1})})};return W(()=>n,e=>{e!=null&&e.stationCode&&(e!=null&&e.stationName)&&(e!=null&&e.companyName)&&(e!=null&&e.capinsWatt)&&(e!=null&&e.capinsUnit)&&(e!=null&&e.agreedTermYear)&&(e!=null&&e.datongrd)&&(e!=null&&e.endMonth)&&(e!=null&&e.originalValue)&&(e!=null&&e.salvageRate)&&v()},{deep:!0}),W(()=>n,e=>{e!=null&&e.stationCode&&(e!=null&&e.stationName)&&(e!=null&&e.companyName)&&(e!=null&&e.capinsWatt)&&(e!=null&&e.capinsUnit)&&(e!=null&&e.agreedTermYear)&&(e!=null&&e.datongrd)&&(e!=null&&e.endMonth)&&(e!=null&&e.originalValue)&&(e!=null&&e.salvageRate)&&v()},{deep:!0}),(e,t)=>{var D;const u=_e,i=ge,d=be,r=ye,p=xe,_=ve,m=he,P=oe,f=Ce,ee=Me,M=we,te=L;return de(),ie(ce,null,[h("div",De,re((D=l(c))!=null&&D.id?"编辑出售表单":"新建出售表单"),1),a(m,{model:l(n),name:"formRef",ref_key:"formRef",ref:N,rules:j,autocomplete:"off","label-col":{style:{width:"140px",paddingRight:"8px"}}},{default:o(()=>[h("div",Be,[Fe,a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"拟出售电站编号",name:"stationCode"},{default:o(()=>{var s;return[a(u,{placeholder:"请输入",value:l(n).stationCode,"onUpdate:value":t[0]||(t[0]=ae=>l(n).stationCode=ae),onBlur:K,style:{width:"60%"},disabled:!!((s=l(c))!=null&&s.id)},null,8,["value","disabled"])]}),_:1})]),_:1}),a(d,{span:12},{default:o(()=>[a(i,{label:"业主名称",name:"stationName"},{default:o(()=>[a(u,{placeholder:"系统带出",value:l(n).stationName,"onUpdate:value":t[1]||(t[1]=s=>l(n).stationName=s),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"产权公司",name:"companyName"},{default:o(()=>[a(u,{placeholder:"系统带出",value:l(n).companyName,"onUpdate:value":t[2]||(t[2]=s=>l(n).companyName=s),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:o(()=>[a(i,{label:"实际组件数量",name:"subassemblyNum"},{default:o(()=>[a(p,{placeholder:"系统带出,可修改",value:l(n).subassemblyNum,"onUpdate:value":t[3]||(t[3]=s=>l(n).subassemblyNum=s),controls:!1,onBlur:O,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"实际装机容量",name:"capinsWatt"},{default:o(()=>[a(p,{placeholder:"系统带出,可修改",value:l(n).capinsWatt,"onUpdate:value":t[4]||(t[4]=s=>l(n).capinsWatt=s),controls:!1,style:{width:"60%"}},{addonAfter:o(()=>[S("W")]),_:1},8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:o(()=>[a(i,{label:"合同约定每瓦电价",name:"capinsUnit"},{default:o(()=>[a(p,{placeholder:"请输入",value:l(n).capinsUnit,"onUpdate:value":t[5]||(t[5]=s=>l(n).capinsUnit=s),controls:!1,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"合同约定合作年限",name:"agreedTermYear"},{default:o(()=>[a(p,{placeholder:"请输入",value:l(n).agreedTermYear,"onUpdate:value":t[6]||(t[6]=s=>l(n).agreedTermYear=s),controls:!1,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:o(()=>[a(i,{label:"并网日期",name:"datongrd"},{default:o(()=>[a(_,{placeholder:"系统带出",value:l(n).datongrd,"onUpdate:value":t[7]||(t[7]=s=>l(n).datongrd=s),"value-format":"YYYY-MM-DD",style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"结束日期",name:"endMonth"},{default:o(()=>[a(_,{placeholder:"请选择",value:l(n).endMonth,"onUpdate:value":t[8]||(t[8]=s=>l(n).endMonth=s),"value-format":"YYYY-MM",style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:o(()=>[a(i,{label:"电站原值",name:"originalValue"},{default:o(()=>[a(p,{placeholder:"系统带出",value:l(n).originalValue,"onUpdate:value":t[9]||(t[9]=s=>l(n).originalValue=s),controls:!1,style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"合同约定残值率",name:"salvageRate"},{default:o(()=>[a(p,{placeholder:"请输入",value:l(n).salvageRate,"onUpdate:value":t[10]||(t[10]=s=>l(n).salvageRate=s),controls:!1,style:{width:"60%"}},null,8,["value"])]),_:1})]),_:1})]),_:1})]),h("div",We,[Le,a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"电站净值",name:"netValue"},{default:o(()=>[a(u,{placeholder:"系统带出",value:l(n).netValue,"onUpdate:value":t[11]||(t[11]=s=>l(n).netValue=s),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1}),a(d,{span:12},{default:o(()=>[a(i,{label:"产品总价",name:"productPriceSum"},{default:o(()=>[a(u,{placeholder:"系统带出",value:l(n).productPriceSum,"onUpdate:value":t[12]||(t[12]=s=>l(n).productPriceSum=s),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:12},{default:o(()=>[a(i,{label:"产品售价",name:"productPrice"},{default:o(()=>[a(u,{placeholder:"系统带出",value:l(n).productPrice,"onUpdate:value":t[13]||(t[13]=s=>l(n).productPrice=s),style:{width:"60%"},disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model"]),a(P,{ref_key:"uploadRef",ref:Y,businessType:9,relationCode:l(c).id,companyCode:l(c).companyCode},null,8,["relationCode","companyCode"]),h("div",Ae,[a(ee,null,{default:o(()=>[a(f,{onClick:Q},{default:o(()=>[S("取消")]),_:1}),a(f,{type:"primary",onClick:z,loading:l(y)},{default:o(()=>[S("发起审批流")]),_:1},8,["loading"])]),_:1})]),a(te,{visible:l(b),"onUpdate:visible":t[16]||(t[16]=s=>pe(b)?b.value=s:null),title:"提审","confirm-loading":l(U),okText:"提交审批",onOk:Z,onCancel:X},{default:o(()=>[a(m,{model:l(C),ref_key:"formRef2",ref:R,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:o(()=>[a(r,{span:24},{default:o(()=>[a(d,{span:24},{default:o(()=>[a(i,{label:"审批角色",name:"aaa"},{default:o(()=>[a(M,{value:l(C).aaa,"onUpdate:value":t[14]||(t[14]=s=>l(C).aaa=s),options:l(q),placeholder:"请选择",style:{width:"100%"},onChange:J},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),a(r,{span:24},{default:o(()=>[a(d,{span:24},{default:o(()=>[a(i,{label:"审批人员",name:"assigneeId"},{default:o(()=>[a(M,{value:l(C).assigneeId,"onUpdate:value":t[15]||(t[15]=s=>l(C).assigneeId=s),options:l(T),placeholder:"请选择"},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const ot=Pe(je,[["__scopeId","data-v-61e11b7c"]]);export{ot as default};
