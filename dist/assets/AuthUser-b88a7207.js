import{d as ie,r as f,K as ce,I as pe,c as de,o as _e,a as c,v as b,g as v,f as l,e,u as s,h as I,b as k,F as S,x as L,i as _,s as me,bi as re,bc as fe,H as ve,ey as ye,q as W,be as he,bf as ge,aw as be,ax as ke,bg as Ce,bh as xe,bn as Ne,ay as Ie,bM as we,bN as Ue,_ as Se}from"./index-db94d997.js";import{s as ze}from"./index-96df45ba.js";import{E as Pe}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as $e}from"./index-39334618.js";const De={class:"card top"},Re={style:{width:"100%",display:"flex","flex-direction":"row","align-items":"center","justify-content":"flex-end"}},Ae={class:"table_container"},Le=["onClick"],Be=["onClick"],Ke=["onClick"],Fe=["onClick"],Te={class:"text-hide"},Ee={class:"table_container"},Me=["onClick"],Ve=["onClick"],je=["onClick"],Oe=["onClick"],qe={class:"text-hide"},He={class:"card footer"},Je=ie({__name:"AuthUser",setup(Qe){const B=f(1),E=f(),w=f(),M=f([{title:"用户编码",dataIndex:"id",resizable:!0,ellipsis:!0,width:60},{title:"用户姓名",dataIndex:"userName",resizable:!0,ellipsis:!0,width:150},{title:"手机号",dataIndex:"mobile",resizable:!0,ellipsis:!0,width:120},{title:"归属公司",dataIndex:"companyName",resizable:!0,ellipsis:!0,width:120},{title:"创建人",dataIndex:"createUser",resizable:!0,ellipsis:!0,width:120},{title:"最近修改时间",dataIndex:"updateTime",resizable:!0,ellipsis:!0,width:120}]),m=ce({userName:"",mobile:"",companyName:"",createUser:"",roleName:""}),V=f([]),C=f(1),U=f(10),K=f(0),z=f(!1),P=f(!1),j=f(history.state.pdata||{}),F=pe(),h=f([]),y=f([]),X=(t,n,i,p)=>{n?(y.value.push(t.id),h.value.push(t)):(y.value=y.value.filter(o=>o!==t.id),h.value=h.value.filter(o=>o.id!==t.id))},Y=(t,n,i)=>{const p=i.map(o=>o.id);t?(y.value=y.value.concat(p),h.value=h.value.concat(i)):(y.value=y.value.filter(o=>!p.includes(o)),h.value=h.value.filter(o=>!p.includes(o.id)))},O=de(()=>({selectedRowKeys:y,onSelect:X,onSelectAll:Y})),q=()=>{F.go(-1)},Z=()=>{y.value&&y.value.length>0?re.confirm({title:"确认提示",icon:l(Pe),content:"确认要授权吗？",onOk:ee,onCancel(){console.log("Cancel")}}):fe.error("请选择授权用户！")},ee=()=>{const t=y.value;let n={companyIds:j.value.authCompanyIds,userIds:ve(t)};P.value=!0,ze(n).then(i=>{P.value=!1,q()}).catch(i=>{P.value=!1})},te=(t,n,i,{currentDataSource:p})=>{console.log("tableChange"),C.value=t==null?void 0:t.current,U.value=t==null?void 0:t.pageSize,K.value=t==null?void 0:t.size;let o={pageNum:C.value,pageSize:U.value,...m};$(o)},ae=()=>{E.value.resetFields();let t={pageNum:C.value,pageSize:U.value,...m};$(t)},le=t=>{console.log("values=",t);let n={pageNum:C.value,pageSize:U.value,...t};$(n)},$=t=>{z.value=!0,t.authCompanyIds=j.value.authCompanyIds,ye(t).then(n=>{if(z.value=!1,n){const{total:i,records:p}=n;K.value=i||0,V.value=p||[]}}).catch(n=>{z.value=!1})},ne=(t={})=>{let n={pageNum:C.value,pageSize:U.value,...t};$(n)},x=t=>{const n=t||[];let i=[];for(let p of n){const o={companyName:p.companyName,id:p.companyInfoId};i.push(o)}return i},D=t=>{F.push({path:"/system/company/detail/list",state:{pdata:W.cloneDeep(t)}})},R=t=>{F.push({path:"/system/user/detail",state:{pdata:W.cloneDeep(t)}})};return _e(()=>{ne()}),(t,n)=>{const i=he,p=ge,o=be,H=ke,A=Ce,J=$e,se=xe,N=Ne,Q=Ie,G=we,oe=Ue;return c(),b(S,null,[v("div",De,[l(se,{model:s(m),ref_key:"formRef",ref:E,name:"basic","label-col":{style:{width:"100px"}},autocomplete:"off",onFinish:le},{default:e(()=>[l(H,{span:24},{default:e(()=>[l(o,{span:8},{default:e(()=>[l(p,{label:"用户姓名",name:"userName"},{default:e(()=>[l(i,{value:s(m).userName,"onUpdate:value":n[0]||(n[0]=d=>s(m).userName=d),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[l(p,{label:"手机号",name:"mobile"},{default:e(()=>[l(i,{value:s(m).mobile,"onUpdate:value":n[1]||(n[1]=d=>s(m).mobile=d),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[l(p,{label:"归属公司",name:"companyName"},{default:e(()=>[l(i,{value:s(m).companyName,"onUpdate:value":n[2]||(n[2]=d=>s(m).companyName=d),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1})]),_:1}),l(H,{span:24},{default:e(()=>[l(o,{span:8},{default:e(()=>[l(p,{label:"创建人",name:"createUser"},{default:e(()=>[l(i,{value:s(m).createUser,"onUpdate:value":n[3]||(n[3]=d=>s(m).createUser=d),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[l(p,{label:"角色名称",name:"roleName"},{default:e(()=>[l(i,{value:s(m).roleName,"onUpdate:value":n[4]||(n[4]=d=>s(m).roleName=d),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),l(o,{span:8},{default:e(()=>[v("div",Re,[l(J,null,{default:e(()=>[l(A,{onClick:ae},{default:e(()=>[I("重置")]),_:1}),l(A,{type:"primary","html-type":"submit"},{default:e(()=>[I("查询")]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1},8,["model"])]),v("div",{class:"card content",ref_key:"permissionDetailRef",ref:w},[l(oe,{activeKey:s(B),"onUpdate:activeKey":n[5]||(n[5]=d=>me(B)?B.value=d:null),tabBarStyle:{height:"60px",paddingLeft:"24px"}},{default:e(()=>[(c(),k(G,{key:1,tab:"用户列表"},{default:e(()=>[v("div",Ae,[l(Q,{columns:s(M),"data-source":s(V),loading:s(z),"row-key":"id",onChange:te,rowSelection:s(O),scroll:{x:"100%"},pagination:{current:s(C),total:s(K),showTotal:d=>`共 ${d} 条`,size:"small",showQuickJumper:!0}},{bodyCell:e(({text:d,record:a,index:ue,column:r})=>[r.dataIndex==="companyName"?(c(),k(N,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[(c(!0),b(S,null,L(x(a==null?void 0:a.companyInfoUser),(u,g)=>(c(),b("span",{class:"light-name",key:g,onClick:T=>D(u)},_(u.companyName),9,Le))),128))]),default:e(()=>[(c(!0),b(S,null,L(x(a==null?void 0:a.companyInfoUser),(u,g)=>(c(),b("span",{class:"light-name",key:g,onClick:T=>D(u)},_(u.companyName)+" "+_(x(a==null?void 0:a.companyInfoUser).length-1==g?"":"，"),9,Be))),128))]),_:2},1032,["getPopupContainer"])):r.dataIndex==="userName"?(c(),k(N,{key:1,placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},_(a[r.dataIndex]),9,Ke)]),default:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},_(a[r.dataIndex]),9,Fe)]),_:2},1032,["getPopupContainer"])):(c(),k(N,{key:2},{title:e(()=>[I(_(a[r.dataIndex]),1)]),default:e(()=>[v("span",Te,_(a[r.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","loading","rowSelection","pagination"])])]),_:1})),(c(),k(G,{key:2,tab:`已选列表(${s(h).length})`},{default:e(()=>[v("div",Ee,[l(Q,{"row-selection":s(O),columns:s(M),"row-key":"id","data-source":s(h)},{bodyCell:e(({text:d,record:a,index:ue,column:r})=>[r.dataIndex==="companyName"?(c(),k(N,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[(c(!0),b(S,null,L(x(a==null?void 0:a.companyInfoUser),(u,g)=>(c(),b("span",{class:"light-name",key:g,onClick:T=>D(u)},_(u.companyName),9,Me))),128))]),default:e(()=>[(c(!0),b(S,null,L(x(a==null?void 0:a.companyInfoUser),(u,g)=>(c(),b("span",{class:"light-name",key:g,onClick:T=>D(u)},_(u.companyName)+" "+_(x(a==null?void 0:a.companyInfoUser).length-1==g?"":"，"),9,Ve))),128))]),_:2},1032,["getPopupContainer"])):r.dataIndex==="userName"?(c(),k(N,{key:1,placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},_(a[r.dataIndex]),9,je)]),default:e(()=>[v("span",{class:"light-name",onClick:u=>R(a)},_(a[r.dataIndex]),9,Oe)]),_:2},1032,["getPopupContainer"])):(c(),k(N,{key:2},{title:e(()=>[I(_(a[r.dataIndex]),1)]),default:e(()=>[v("span",qe,_(a[r.dataIndex]),1)]),_:2},1024))]),_:1},8,["row-selection","columns","data-source"])])]),_:1},8,["tab"]))]),_:1},8,["activeKey"])],512),v("div",He,[l(J,null,{default:e(()=>[l(A,{onClick:q},{default:e(()=>[I("取消")]),_:1}),l(A,{type:"primary",onClick:Z,loading:s(P)},{default:e(()=>[I("确认关联")]),_:1},8,["loading"])]),_:1})])],64)}}});const Ze=Se(Je,[["__scopeId","data-v-454b7c3e"]]);export{Ze as default};
