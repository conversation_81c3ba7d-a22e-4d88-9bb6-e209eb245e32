import{d as ie,r as f,K as ce,I as pe,c as _e,o as de,a as c,v as b,g as v,f as n,e,u as s,h as I,b as k,F as S,x as B,i as d,s as me,bi as re,eG as fe,ey as ve,q as H,be as ge,bf as ye,aw as he,ax as be,bg as ke,bh as xe,bn as Ce,ay as Ne,bM as Ie,bN as we,_ as Ue}from"./index-db94d997.js";import{E as Se}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as ze}from"./index-39334618.js";const Pe={class:"card top"},$e={style:{width:"100%",display:"flex","flex-direction":"row","align-items":"center","justify-content":"flex-end"}},De={class:"table_container"},Le=["onClick"],Re=["onClick"],Be=["onClick"],Ae=["onClick"],Ke={class:"text-hide"},Fe={class:"table_container"},Te=["onClick"],Ee=["onClick"],Me=["onClick"],Ve=["onClick"],je={class:"text-hide"},Oe={class:"card footer"},qe=ie({__name:"AuthUser",setup(Ge){const A=f(1),E=f(),w=f(),M=f([{title:"用户编码",dataIndex:"id",resizable:!0,ellipsis:!0,width:60},{title:"用户姓名",dataIndex:"userName",resizable:!0,ellipsis:!0,width:150},{title:"手机号",dataIndex:"mobile",resizable:!0,ellipsis:!0,width:120},{title:"归属公司",dataIndex:"companyName",resizable:!0,ellipsis:!0,width:120},{title:"创建人",dataIndex:"createUser",resizable:!0,ellipsis:!0,width:120},{title:"最近修改时间",dataIndex:"updateTime",resizable:!0,ellipsis:!0,width:120}]),m=ce({userName:"",mobile:"",companyName:"",createUser:"",roleName:""}),V=f([]),x=f(1),U=f(10),K=f(0),z=f(!1),P=f(!1),W=f(history.state.pdata||{}),F=pe(),g=f([]),y=f([]),X=(t,l,i,p)=>{l?(y.value.push(t.id),g.value.push(t)):(y.value=y.value.filter(o=>o!==t.id),g.value=g.value.filter(o=>o.id!==t.id))},Y=(t,l,i)=>{const p=i.map(o=>o.id);t?(y.value=y.value.concat(p),g.value=g.value.concat(i)):(y.value=y.value.filter(o=>!p.includes(o)),g.value=g.value.filter(o=>!p.includes(o.id)))},j=_e(()=>({selectedRowKeys:y,onSelect:X,onSelectAll:Y})),O=()=>{F.go(-1)},Z=()=>{re.confirm({title:"确认提示",icon:n(Se),content:"确认要授权吗？",onOk:ee,onCancel(){console.log("Cancel")}})},ee=()=>{console.log("授权用户");const t=y.value;let l={rolesId:W.value.id,userIds:t};console.log("授权params=",l),P.value=!0,fe(l).then(i=>{console.log("授权用户res=",i),P.value=!1,O()}).catch(i=>{P.value=!1})},te=(t,l,i,{currentDataSource:p})=>{console.log("tableChange"),x.value=t==null?void 0:t.current,U.value=t==null?void 0:t.pageSize,K.value=t==null?void 0:t.size;let o={pageNum:x.value,pageSize:U.value,...m};$(o)},ae=()=>{E.value.resetFields();let t={pageNum:x.value,pageSize:U.value,...m};$(t)},le=t=>{console.log("values=",t);let l={pageNum:x.value,pageSize:U.value,...t};$(l)},$=t=>{z.value=!0,ve(t).then(l=>{if(console.log("用户列表res=",l),z.value=!1,l){const{total:i,records:p}=l;K.value=i||0,V.value=p||[]}}).catch(l=>{z.value=!1})},ne=(t={})=>{let l={pageNum:x.value,pageSize:U.value,...t};$(l)},C=t=>{const l=t||[];let i=[];for(let p of l){const o={companyName:p.companyName,id:p.companyInfoId};i.push(o)}return i},D=t=>{F.push({path:"/system/company/detail/list",state:{pdata:H.cloneDeep(t)}})},L=t=>{F.push({path:"/system/user/detail",state:{pdata:H.cloneDeep(t)}})};return de(()=>{ne()}),(t,l)=>{const i=ge,p=ye,o=he,q=be,R=ke,G=ze,se=xe,N=Ce,J=Ne,Q=Ie,oe=we;return c(),b(S,null,[v("div",Pe,[n(se,{model:s(m),ref_key:"formRef",ref:E,name:"basic","label-col":{style:{width:"100px"}},autocomplete:"off",onFinish:le},{default:e(()=>[n(q,{span:24},{default:e(()=>[n(o,{span:8},{default:e(()=>[n(p,{label:"用户姓名",name:"userName"},{default:e(()=>[n(i,{value:s(m).userName,"onUpdate:value":l[0]||(l[0]=_=>s(m).userName=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),n(o,{span:8},{default:e(()=>[n(p,{label:"手机号",name:"mobile"},{default:e(()=>[n(i,{value:s(m).mobile,"onUpdate:value":l[1]||(l[1]=_=>s(m).mobile=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),n(o,{span:8},{default:e(()=>[n(p,{label:"归属公司",name:"companyName"},{default:e(()=>[n(i,{value:s(m).companyName,"onUpdate:value":l[2]||(l[2]=_=>s(m).companyName=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1})]),_:1}),n(q,{span:24},{default:e(()=>[n(o,{span:8},{default:e(()=>[n(p,{label:"创建人",name:"createUser"},{default:e(()=>[n(i,{value:s(m).createUser,"onUpdate:value":l[3]||(l[3]=_=>s(m).createUser=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),n(o,{span:8},{default:e(()=>[n(p,{label:"角色名称",name:"roleName"},{default:e(()=>[n(i,{value:s(m).roleName,"onUpdate:value":l[4]||(l[4]=_=>s(m).roleName=_),placeholder:"请输入"},null,8,["value"])]),_:1})]),_:1}),n(o,{span:8},{default:e(()=>[v("div",$e,[n(G,null,{default:e(()=>[n(R,{onClick:ae},{default:e(()=>[I("重置")]),_:1}),n(R,{type:"primary","html-type":"submit"},{default:e(()=>[I("查询")]),_:1})]),_:1})])]),_:1})]),_:1})]),_:1},8,["model"])]),v("div",{class:"card content",ref_key:"roleDetailRef",ref:w},[n(oe,{activeKey:s(A),"onUpdate:activeKey":l[5]||(l[5]=_=>me(A)?A.value=_:null),tabBarStyle:{height:"60px",paddingLeft:"24px"}},{default:e(()=>[(c(),k(Q,{key:1,tab:"用户列表"},{default:e(()=>[v("div",De,[n(J,{columns:s(M),"data-source":s(V),loading:s(z),"row-key":"id",onChange:te,rowSelection:s(j),scroll:{x:"100%"},pagination:{current:s(x),total:s(K),showTotal:_=>`共 ${_} 条`,size:"small",showQuickJumper:!0}},{bodyCell:e(({text:_,record:a,index:ue,column:r})=>[r.dataIndex==="companyName"?(c(),k(N,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[(c(!0),b(S,null,B(C(a==null?void 0:a.companyInfoUser),(u,h)=>(c(),b("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName),9,Le))),128))]),default:e(()=>[(c(!0),b(S,null,B(C(a==null?void 0:a.companyInfoUser),(u,h)=>(c(),b("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName)+" "+d(C(a==null?void 0:a.companyInfoUser).length-1==h?"":"，"),9,Re))),128))]),_:2},1032,["getPopupContainer"])):r.dataIndex==="userName"?(c(),k(N,{key:1,placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[v("span",{class:"light-name",onClick:u=>L(a)},d(a[r.dataIndex]),9,Be)]),default:e(()=>[v("span",{class:"light-name",onClick:u=>L(a)},d(a[r.dataIndex]),9,Ae)]),_:2},1032,["getPopupContainer"])):(c(),k(N,{key:2},{title:e(()=>[I(d(a[r.dataIndex]),1)]),default:e(()=>[v("span",Ke,d(a[r.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","loading","rowSelection","pagination"])])]),_:1})),(c(),k(Q,{key:2,tab:`已选列表(${s(g).length})`},{default:e(()=>[v("div",Fe,[n(J,{"row-selection":s(j),columns:s(M),"row-key":"id",scroll:{x:"100%"},"data-source":s(g)},{bodyCell:e(({text:_,record:a,index:ue,column:r})=>[r.dataIndex==="companyName"?(c(),k(N,{key:0,class:"tooltip-box",placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[(c(!0),b(S,null,B(C(a==null?void 0:a.companyInfoUser),(u,h)=>(c(),b("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName),9,Te))),128))]),default:e(()=>[(c(!0),b(S,null,B(C(a==null?void 0:a.companyInfoUser),(u,h)=>(c(),b("span",{class:"light-name",key:h,onClick:T=>D(u)},d(u.companyName)+" "+d(C(a==null?void 0:a.companyInfoUser).length-1==h?"":"，"),9,Ee))),128))]),_:2},1032,["getPopupContainer"])):r.dataIndex==="userName"?(c(),k(N,{key:1,placement:"topLeft",getPopupContainer:u=>s(w)},{title:e(()=>[v("span",{class:"light-name",onClick:u=>L(a)},d(a[r.dataIndex]),9,Me)]),default:e(()=>[v("span",{class:"light-name",onClick:u=>L(a)},d(a[r.dataIndex]),9,Ve)]),_:2},1032,["getPopupContainer"])):(c(),k(N,{key:2},{title:e(()=>[I(d(a[r.dataIndex]),1)]),default:e(()=>[v("span",je,d(a[r.dataIndex]),1)]),_:2},1024))]),_:1},8,["row-selection","columns","data-source"])])]),_:1},8,["tab"]))]),_:1},8,["activeKey"])],512),v("div",Oe,[n(G,null,{default:e(()=>[n(R,{onClick:O},{default:e(()=>[I("取消")]),_:1}),n(R,{type:"primary",onClick:Z,loading:s(P)},{default:e(()=>[I("确认授权")]),_:1},8,["loading"])]),_:1})])],64)}}});const We=Ue(qe,[["__scopeId","data-v-003a57c9"]]);export{We as default};
