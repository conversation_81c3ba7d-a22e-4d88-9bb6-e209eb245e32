import{C as Z}from"./CForm-ffa1b2bc.js";import{d as c,r as S,b2 as m,w as ee,o as ae,a as g,v as R,b as _,e as r,a1 as F,y as D,g as y,f as I,h as p,i as l,u as i,d0 as te,S as W,d1 as A,d2 as E,z as $,d3 as q,U as H,m as O,W as V,n as se,F as re,aw as ie,ax as de,bn as oe,ay as le}from"./index-db94d997.js";const ne={key:0},fe={class:"text-hide"},ge={class:"text-hide"},he={class:"text-hide"},_e={class:"text-hide"},pe={class:"text-hide"},ye={class:"text-hide"},be={class:"text-hide"},xe={class:"text-hide"},we={class:"text-hide"},ve={class:"text-hide"},ue=c({__name:"index",props:{columns:{},dataSource:{},request:{},beforeQueryParams:{},pagination:{},size:{},scroll:{},labelCol:{},wrapperCol:{},search:{},rowSelection:{},defaultQuery:{type:Boolean,default:!0},allDataSource:{type:Boolean,default:!1},paginationAllData:{type:Boolean,default:!1},getDataSource:{},bordered:{type:Boolean},showReset:{type:Boolean,default:!0},showSubmit:{type:Boolean,default:!0},submitText:{default:"查询"},onCancel:{},hidenBoxShadow:{type:Boolean},noPadding:{type:Boolean}},emits:["getDataSource","tableChange"],setup(j,{expose:J,emit:Q}){var N;const t=j,L=(N=t.columns)==null?void 0:N.filter(a=>!a.hideInTable).map(a=>({...a,align:(a==null?void 0:a.align)||"left",ellipsis:{showTitle:!1},minWidth:(a==null?void 0:a.minWidth)||(a==null?void 0:a.width),maxWidth:(a==null?void 0:a.maxWidth)||600,resizable:a.resizable!==!1})),M=S(L),x=S([]),k=S(!1),u=S(0),v=S(1),w=S(),z=S({});let P={};const K=!!m().tableHeader,C={pageNum:1,pageSize:10};J({getInitialFormState:()=>{var a;return(a=w.value)==null?void 0:a.getInitialFormState()},getInitialFormStateNew:()=>z.value,setLoading:a=>{k.value=a},reload:(a=!0)=>{t!=null&&t.beforeQueryParams?t==null||t.beforeQueryParams().then(f=>{console.log("beforeQueryParams拼接后的参数",f);let h={...P,...f};b(h,a)}):b(P,a)},reloadBeforeQueryParamsAndFormParams:()=>{var a;(a=w.value)==null||a.getFormParams().then(f=>{console.log("表单参数formParams=",f),t!=null&&t.beforeQueryParams?t==null||t.beforeQueryParams().then(h=>{console.log("beforeQueryParams拼接后的参数",h);let s={...f,...h};b(s)}):b(f)})},getDataSource:()=>x,setDataSource:a=>{x.value=a},getFormParams:async()=>{var a;return await((a=w.value)==null?void 0:a.getFormParams())},validateFormParams:async()=>{var a;return await((a=w.value)==null?void 0:a.validateFormParams())},setFormParams:(a,f)=>new Promise(async h=>{var s,d;if(await((s=w.value)==null?void 0:s.setFormParams(a)),f){const o=await((d=w.value)==null?void 0:d.getFormParams());b({...P,...o}).then(e=>{h(e)})}}),reset:()=>{w.value.reset(),x.value=[]},getTableList:()=>B()});const b=(a,f)=>new Promise(h=>{if(t.request){k.value=!0;const s={...C,...a};if(f&&(s.pageNum=v.value),!(a!=null&&a.noJoin))for(const o in s){const e=s[o];Array.isArray(e)&&(s[o]=e.join(","))}delete s.current,v.value=s.pageNum;let d={};a!=null&&a.notFilterNull?d={...s}:Object.keys(s).forEach(o=>{s[o]!==null&&s[o]!==""&&(d[o]=s[o])}),console.log("filterParams",d),P=d,t.request(d).then(o=>{console.log("props.allDataSource",t.allDataSource),console.log("res",o);let{records:e,total:n}=o;t.allDataSource&&(e=o),e=Array.isArray(e)?e:[],x.value=e,u.value=n,k.value=!1,Q("getDataSource",e,n),h(e),z.value=P}).catch(()=>{x.value=[],u.value=0,k.value=!1,Q("getDataSource",[],0),h([])})}else if(t.paginationAllData){const s={...C,...a};for(const o in s){const e=s[o];Array.isArray(e)&&(s[o]=e.join(","))}delete s.current,v.value=s.pageNum;const d={};Object.keys(s).forEach(o=>{s[o]!==null&&s[o]!==""&&(d[o]=s[o])}),P=d,x.value=t.dataSource,u.value=t.dataSource.length}});ee(()=>t.dataSource,()=>{t.paginationAllData&&(x.value=t.dataSource,u.value=t.dataSource.length)});const U=(a,f,h)=>{Q("tableChange",a);const s={...P,...a,...f,...h};delete s.total,delete s.size,delete s.showQuickJumper,s.pageNum=s.current,b(s)},G=(a,f)=>{f.width=a},B=()=>{t!=null&&t.beforeQueryParams?t==null||t.beforeQueryParams().then(a=>{console.log("beforeQueryParams拼接后的参数",a),b(a)}):b()};return ae(()=>{t.defaultQuery&&B()}),(a,f)=>{const h=ie,s=de,d=oe,o=le;return g(),R(re,null,[t.search||t.search===void 0?(g(),_(Z,{key:0,ref_key:"cFormRef",ref:w,"data-source":a.columns,"get-page-data":b,"label-col":t.labelCol,"wrapper-col":t.wrapperCol,"before-query-params":t.beforeQueryParams,showReset:t.showReset,showSubmit:t.showSubmit,submitText:t.submitText,onCancel:t.onCancel},{formRender:r(()=>[F(a.$slots,"formRender")]),_:3},8,["data-source","label-col","wrapper-col","before-query-params","showReset","showSubmit","submitText","onCancel"])):D("",!0),y("div",{class:"ctable-main bg-white",style:se({boxShadow:t.hidenBoxShadow?"0px 0px 0px 0px rgba(21, 102, 80, 0.1)":"0px 0px 16px 0px rgba(21, 102, 80, 0.1)",padding:t.noPadding?"0px 0px":"20px 24px"})},[K?(g(),_(s,{key:0,gutter:24,span:24,class:"table-but-box"},{default:r(()=>[I(h,{flex:1},{default:r(()=>[F(a.$slots,"tableHeaderLeft")]),_:3}),I(h,null,{default:r(()=>[F(a.$slots,"tableHeader")]),_:3})]),_:3})):D("",!0),I(o,{columns:i(M),"row-key":"id","data-source":i(x),size:t.size||"middle",pagination:t.hasOwnProperty("pagination")&&t.pagination!==void 0?t.pagination:{current:i(v),total:i(u),showTotal:e=>`共 ${e} 条`,size:"small",showQuickJumper:!0},loading:i(k),onChange:U,scroll:{x:"100%"},onResizeColumn:G,"row-selection":t==null?void 0:t.rowSelection,bordered:t.bordered},{headerCell:r(({column:e})=>[e.showIconHelp?(g(),R("span",ne,[p(l(e.title)+" ",1),I(i(te))])):D("",!0),e.tooltip?(g(),_(d,{key:1},{title:r(()=>[p(l(e.tooltip),1)]),default:r(()=>[p(" "+l(e.title),1)]),_:2},1024)):D("",!0)]),bodyCell:r(({column:e,record:n,index:T,text:X})=>[(e==null?void 0:e.valueType)==="select"&&!e.render?(g(),_(d,{key:0},{title:r(()=>[p(l(i(W)(n[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),default:r(()=>[y("span",fe,l(i(W)(n[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),_:2},1024)):(e==null?void 0:e.valueType)==="select2"&&!e.render?(g(),_(d,{key:1},{title:r(()=>[p(l(i(A)(n[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),default:r(()=>[y("span",ge,l(i(A)(n[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),_:2},1024)):(e==null?void 0:e.valueType)==="select3"&&!e.render?(g(),_(d,{key:2},{title:r(()=>[p(l(i(E)(n[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),default:r(()=>[y("span",he,l(i(E)(n[e.dataIndex],e==null?void 0:e.valueEnum)),1)]),_:2},1024)):(e==null?void 0:e.valueType)==="index"&&!e.render?(g(),_(d,{key:3},{title:r(()=>[p(l(T+(1+C.pageSize*(i(v)-1))),1)]),default:r(()=>[y("span",_e,l(T+(1+C.pageSize*(i(v)-1))),1)]),_:2},1024)):e!=null&&e.formatMoney&&!e.render?(g(),_(d,{key:4},{title:r(()=>[p(l(i($)(n[e.dataIndex])),1)]),default:r(()=>[y("span",pe,l(i($)(n[e.dataIndex])),1)]),_:2},1024)):e!=null&&e.kWtoWanKW&&!e.render?(g(),_(d,{key:5},{title:r(()=>[p(l(i(q)(n[e.dataIndex])),1)]),default:r(()=>[y("span",ye,l(i(q)(n[e.dataIndex])),1)]),_:2},1024)):e!=null&&e.dateFormat&&!e.render?(g(),_(d,{key:6},{title:r(()=>[p(l(i(H)(n[e.dataIndex],e==null?void 0:e.dateFormat)),1)]),default:r(()=>[y("span",be,l(i(H)(n[e.dataIndex],e==null?void 0:e.dateFormat)),1)]),_:2},1024)):(e!=null&&e.formatDecimal||(e==null?void 0:e.formatDecimal)==0)&&!e.render?(g(),_(d,{key:7},{title:r(()=>[p(l(i(O)(n[e.dataIndex],e==null?void 0:e.formatDecimal)),1)]),default:r(()=>[y("span",xe,l(i(O)(n[e.dataIndex],e==null?void 0:e.formatDecimal)),1)]),_:2},1024)):(e!=null&&e.formatPercentage||(e==null?void 0:e.formatPercentage)==0)&&!e.render?(g(),_(d,{key:8},{title:r(()=>[p(l(i(V)(n[e.dataIndex],e==null?void 0:e.formatPercentage)),1)]),default:r(()=>[y("span",we,l(i(V)(n[e.dataIndex],e==null?void 0:e.formatPercentage)),1)]),_:2},1024)):e.render?D("",!0):(g(),_(d,{key:9},{title:r(()=>[p(l(n[e.dataIndex]),1)]),default:r(()=>[y("span",ve,l(n[e.dataIndex]),1)]),_:2},1024)),e.render?F(a.$slots,`${e.key}Render`,{key:10,record:n,index:T,column:e,text:X,pagination:t.hasOwnProperty("pagination")&&t.pagination!==void 0?t.pagination:{current:i(v),total:i(u),showTotal:Y=>`共 ${Y} 条`,size:"small",showQuickJumper:!0}}):D("",!0)]),_:3},8,["columns","data-source","size","pagination","loading","row-selection","bordered"])],4)],64)}}});export{ue as _};
