import{_ as K}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as L,r as c,o as B,D as M,au as V,at as q,a as v,v as E,f as y,e as r,u as x,b as O,h as w,y as R,i as C,g as A,bg as G,bn as H,_ as J}from"./index-db94d997.js";import{a as Q,b as $}from"./index-8efc2159.js";import{_ as j}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const P={class:"areaPrice"},U={class:"text-hide"},W=L({__name:"Info",setup(X){var g;const i=c(),u=c((g=history.state)==null?void 0:g.pdata),p=c([]),I=c([]);let d="",f="";const D=[{title:"电站编码",dataIndex:"stationCode",width:120,resizable:!0,search:!0,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,fixed:"left"},{title:"产权公司",dataIndex:"companyName",width:120,resizable:!0,search:!1,fixed:"left"},{title:"省",dataIndex:"prvName",width:120,resizable:!0,search:!1},{title:"市",dataIndex:"cityName",width:120,resizable:!0,search:!1},{title:"区",dataIndex:"distName",width:120,resizable:!0,search:!1},{title:"装机容量",dataIndex:"capins",width:130,resizable:!0,formatMoney:!0,search:!1},{title:"资产原值",dataIndex:"fixedAssets",width:130,resizable:!0,formatMoney:!0,search:!1},{title:"当月收入",dataIndex:"monthIncome",width:130,resizable:!0,formatMoney:!0,search:!1},{title:"其他业务收入",dataIndex:"otherIncome",width:130,resizable:!0,formatMoney:!0,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:p,valueTreeLoad:({id:e})=>m(e),onSelect:(e,t,a)=>{var s,l;const o=((l=(s=a==null?void 0:a.triggerNode)==null?void 0:s.props)==null?void 0:l.level)||1;d=["prvCode","cityCode","distCode","town","vil"][o-1],f=e},render:!0,width:150,resizable:!0,hideInTable:!0}];B(async()=>{console.log(u.value),N(),m()}),M(()=>{i.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&S()});const m=e=>new Promise((t,a)=>{V({pid:e||"0"}).then(o=>{console.log("行政区res=",o);let n=h(o);p.value=n,console.log(p.value),t(!0)}).catch(()=>{a()})}),h=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,h(t.subDistrict))}),e),N=()=>{q({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(o=>({label:o.companyName,value:o.companyCode}));I.value=a})},T=()=>{var t;let e=(t=i.value)==null?void 0:t.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),$(e)},b=c([]),z=e=>{b.value=e||[],console.log("dataSource=",e)},S=()=>{var e;(e=i.value)==null||e.reload()},k=(e,t)=>{let a={};return new Promise(o=>{const n=u.value.companyCode,s=u.value.monthKey;t&&t.hasOwnProperty("cityTree")&&!(t!=null&&t.cityTree)&&(d="",f=""),d&&(a={[d]:f});const l={...a,...e,companyCode:n,monthKey:s,noJoin:!0,delStatus:0};o(l)})};return(e,t)=>{const a=G,o=j,n=H,s=K;return v(),E("div",P,[y(s,{columns:D,ref_key:"actionRef",ref:i,request:x(Q),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:z,"before-query-params":k},{tableHeader:r(()=>[y(o,null,{default:r(()=>[x(b).length>0?(v(),O(a,{key:0,type:"primary",onClick:T},{default:r(()=>[w("导出")]),_:1})):R("",!0)]),_:1})]),ownerCompanyCodeListRender:r(({column:l,record:_,index:F})=>[y(n,null,{title:r(()=>[w(C(_.ownerCompanyName),1)]),default:r(()=>[A("span",U,C(_.ownerCompanyName),1)]),_:2},1024)]),_:1},8,["request"])])}}});const ie=J(W,[["__scopeId","data-v-fae7833d"]]);export{ie as default};
