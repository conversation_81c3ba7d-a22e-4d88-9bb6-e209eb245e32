import{_ as Z}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as aa,r as u,k as r,w as ea,o as ta,a as T,v as j,g as L,f as n,e as o,ai as na,h as b,y as q,b as A,z as O,c7 as oa,bc as la,at as sa,bI as ia,ee as ua,av as ra,bJ as pa,bf as da,bU as ca,aw as ma,bg as _a,ax as va,bh as fa,p as ya,j as ga,_ as ha}from"./index-db94d997.js";import{g as Ya}from"./index-0f6c6517.js";import{R as ba}from"./dayjs-a8e42122.js";import{_ as wa}from"./index-39334618.js";import{_ as Ca}from"./index-42d7fb9b.js";import"./CForm-ffa1b2bc.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const E=w=>(ya("data-v-b795fb26"),w=w(),ga(),w),xa={class:"wrapper"},Ma={class:"cform-main bg-white"},ka=E(()=>L("span",{class:"weight4566"},"日维度",-1)),Da=E(()=>L("span",{class:"weight4566"},"月维度",-1)),Ta=E(()=>L("span",{style:{"margin-left":"5px"}},"全选",-1)),La=aa({__name:"station",setup(w){const e=u({dateType:1,dateDay:[r().subtract(10,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],dateMon:[r().subtract(10,"month").format("YYYY-MM"),r().format("YYYY-MM")],monType:1,companyList:[],pageNum:1,pageSize:10}),F=(l,{attrs:t})=>t.vnodes,C=u(!1),B=u(!1),H=()=>{C.value?e.value.companyList=p.value.map(l=>l.value):e.value.companyList=[]};ea(()=>e.value.companyList.length,l=>{C.value=l===p.value.length,B.value=l>0&&l<p.value.length});const K=()=>{e.value={dateType:1,dateDay:[r().subtract(10,"day").format("YYYY-MM-DD"),r().format("YYYY-MM-DD")],dateMon:[r().subtract(10,"month").format("YYYY-MM"),r().format("YYYY-MM")],monType:1,companyList:[],pageNum:1,pageSize:10},P()},P=()=>{S()},V=u(0),I=u([]),$=u(0),N=u(),_=[{title:"电站编号",dataIndex:"stationUniqueId",width:120,search:!1},{title:"业主名称",dataIndex:"stationName",width:120,search:!1},{title:"省",dataIndex:"provinceName",width:120,search:!1},{title:"市",dataIndex:"cityName",width:120,search:!1},{title:"区",dataIndex:"areaName",width:120,search:!1},{title:"产权公司",dataIndex:"ownerCompanyName",width:120,search:!1},{title:"装机容量(kW)",dataIndex:"capins",formatMoney:!0,width:120,search:!1},{title:"实际电量(kWh)",key:"actualCapinList",width:120,search:!1,align:"center",formatMoney:!0,children:[]},{title:"上年同期电量(kWh)",key:"lastYearCapinList",width:120,search:!1,align:"center",formatMoney:!0,children:[]}],J=u({}),S=async()=>{var l,t,d,v,f,y,c,M,g,k,D,h;(l=N.value)==null||l.setLoading(!0);try{const i={pageNum:e.value.pageNum,pageSize:e.value.pageSize,dateType:e.value.dateType,companyList:e.value.companyList};e.value.dateType===1&&(i.dayStart=((t=e.value)==null?void 0:t.dateDay[0])||"",i.dayEnd=((d=e.value)==null?void 0:d.dateDay[1])||""),e.value.dateType===2&&(i.monStart=((v=e.value)==null?void 0:v.dateMon[0])||"",i.monEnd=((f=e.value)==null?void 0:f.dateMon[1])||"",i.monType=e.value.monType);const m=await Ya(i);J.value=i||{};const z=_.findIndex(a=>a.key==="actualCapinList");_[z].children=((M=(c=(y=m.result)==null?void 0:y[0])==null?void 0:c.actualCapinList)==null?void 0:M.map((a,s)=>({title:a.date,dataIndex:"actualCapinList_"+(s+1),align:"left",width:100})))||[];const R=_.findIndex(a=>a.key==="lastYearCapinList");_[R].children=((D=(k=(g=m.result)==null?void 0:g[0])==null?void 0:k.lastYearCapinList)==null?void 0:D.map((a,s)=>({title:a.date,dataIndex:"lastYearCapinList_"+(s+1),align:"left",width:100})))||[],V.value=new Date().getTime(),I.value=m.result.map(a=>{var s,W;return(s=a==null?void 0:a.actualCapinList)==null||s.forEach((Y,U)=>{a["actualCapinList_"+(U+1)]=Y.capins?O(Y.capins):""}),(W=a==null?void 0:a.lastYearCapinList)==null||W.forEach((Y,U)=>{a["lastYearCapinList_"+(U+1)]=Y.capins?O(Y.capins):""}),a}),$.value=m.total||0}finally{(h=N.value)==null||h.setLoading(!1)}},x=u(!1),Q=()=>{let l={...J.value,queryParameterDesc:[]},t={reportType:3,reportParam:JSON.stringify(l)};x.value=!0,oa(t).then(d=>{la.info("数据导出中，稍后请去导出中心查看"),x.value=!1}).catch(d=>{x.value=!1})},p=u([]),G=()=>{sa({}).then(l=>{p.value=(l==null?void 0:l.map(t=>({label:t.companyName,value:t.companyCode})))||[]})},X=({current:l,pageSize:t})=>{e.value.pageNum=l||1,e.value.pageSize=t||10,S()};return ta(async()=>{G(),await S()}),(l,t)=>{const d=ba,v=ia,f=ua,y=ra,c=wa,M=pa,g=da,k=ca,D=Ca,h=ma,i=_a,m=va,z=fa,R=Z;return T(),j("div",xa,[L("div",Ma,[n(z,{ref:"formRef",name:"basic",autocomplete:"off"},{default:o(()=>[n(g,{label:"时间选择",name:"stationCode",style:{"margin-bottom":"14px"}},{default:o(()=>[n(M,{value:e.value.dateType,"onUpdate:value":t[4]||(t[4]=a=>e.value.dateType=a)},{default:o(()=>[n(v,{value:1,style:{"margin-bottom":"10px"}},{default:o(()=>[ka,n(d,{value:e.value.dateDay,"onUpdate:value":t[0]||(t[0]=a=>e.value.dateDay=a),"value-format":"YYYY-MM-DD",disabled:e.value.dateType===2,"allow-clear":!1},null,8,["value","disabled"])]),_:1}),n(v,{value:2,style:{"margin-bottom":"10px"}},{default:o(()=>[Da,n(c,null,{default:o(()=>[n(d,{value:e.value.dateMon,"onUpdate:value":t[1]||(t[1]=a=>e.value.dateMon=a),picker:"month","value-format":"YYYY-MM",disabled:e.value.dateType===1,"allow-clear":!1},null,8,["value","disabled"]),n(y,{value:e.value.monType,"onUpdate:value":t[2]||(t[2]=a=>e.value.monType=a),disabled:e.value.dateType===1,onClick:t[3]||(t[3]=na(()=>{},["prevent"]))},{default:o(()=>[n(f,{value:1},{default:o(()=>[b("自然月份")]),_:1}),n(f,{value:2},{default:o(()=>[b("结账月份")]),_:1})]),_:1},8,["value","disabled"])]),_:1})]),_:1})]),_:1},8,["value"])]),_:1}),n(g,{label:"产权公司",name:"stationCode"},{default:o(()=>[n(m,null,{default:o(()=>[n(h,{span:16},{default:o(()=>[n(y,{value:e.value.companyList,"onUpdate:value":t[7]||(t[7]=a=>e.value.companyList=a),style:{width:"368px"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:p.value,mode:"multiple","allow-clear":!0,"filter-option":(a,s)=>((s==null?void 0:s.label)??"").toLowerCase().includes(a.toLowerCase())},{dropdownRender:o(({menuNode:a})=>[p.value&&p.value.length>0?(T(),j("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[6]||(t[6]=s=>s.preventDefault())},[n(k,{checked:C.value,"onUpdate:checked":t[5]||(t[5]=s=>C.value=s),indeterminate:B.value,onChange:H},null,8,["checked","indeterminate"]),Ta],32)):q("",!0),n(D,{style:{margin:"4px 0"}}),n(F,{vnodes:a},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1}),n(h,{span:8},{default:o(()=>[n(c,{style:{float:"right"}},{default:o(()=>[n(i,{onClick:K},{default:o(()=>[b("重置")]),_:1}),n(i,{type:"primary",onClick:P},{default:o(()=>[b("查询")]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},512)]),(T(),A(R,{columns:_,ref_key:"actionRef",ref:N,key:V.value,paginationAllData:!0,pagination:{current:e.value.pageNum,pageSize:e.value.pageSize,total:$.value,showTotal:a=>`共 ${a} 条`,size:"small",showQuickJumper:!0},"data-source":I.value,"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},bordered:!1,search:!1,onTableChange:X},{tableHeader:o(()=>[n(c,null,{default:o(()=>[I.value.length>0?(T(),A(i,{key:0,type:"primary",onClick:Q,loading:x.value},{default:o(()=>[b("导出")]),_:1},8,["loading"])):q("",!0)]),_:1})]),_:1},8,["pagination","data-source"]))])}}});const Va=ha(La,[["__scopeId","data-v-b795fb26"]]);export{Va as default};
