import{_ as ie}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as pe,r as _,I as re,K as de,o as ce,a as i,v as g,g as o,u as a,b as y,e as t,h,y as x,f as s,i as c,S as ue,s as me,bi as _e,q as Z,bg as ye,be as fe,bf as ge,aw as he,ax as ve,bI as Ce,bJ as be,bU as ke,bV as Ie,av as xe,bh as Te,bn as Ne,p as Oe,j as we,bc as De,_ as Ae}from"./index-db94d997.js";import{s as qe}from"./search-5ccd1e6d.js";import{c as Ue,d as Re,g as Pe,b as Le}from"./index-4185a861.js";import{E as Se}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as ze}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const m=b=>(Oe("data-v-27de322a"),b=b(),we(),b),$e=m(()=>o("span",{class:"page-title"},"公司详情",-1)),Be={class:"basic_info"},Ee={class:"title"},Ve=m(()=>o("span",null,"基础信息",-1)),Fe={key:1,class:"f_item"},je=m(()=>o("span",{class:"label"},"公司名称：",-1)),He={class:"f_item"},Me=m(()=>o("span",{class:"label"},"公司编码：",-1)),Ze={key:1,class:"f_item"},Je=m(()=>o("span",{class:"label"},"统一社会信用代码：",-1)),Ke={key:1,class:"f_item"},Qe=m(()=>o("span",{class:"label"},"状态：",-1)),Ge={key:1,class:"f_item"},We=m(()=>o("span",{class:"label"},"公司类型：",-1)),Xe={key:1,class:"f_item"},Ye=m(()=>o("span",{class:"label"},"上级产权公司：",-1)),ea={key:1,class:"f_item"},aa=m(()=>o("span",{class:"label"},"上级运维公司：",-1)),ta={key:1,class:"f_item"},sa=m(()=>o("span",{class:"label"},"公司资产类型：",-1)),na={key:0,class:"footer"},oa=m(()=>o("div",{class:"table_title"},"关联用户",-1)),la={style:{display:"flex","flex-direction":"row","align-items":"center","justify-content":"space-between"}},ia=["src"],pa=["onClick"],ra=["onClick"],da=pe({__name:"Detail",setup(b){var V;const w=_(),D=re(),T=_(),e=de({companyType:[],companyAssetType:void 0}),A=_([{label:"表内",value:1},{label:"表外",value:2},{label:"受托",value:3}]),J=n=>{n.includes(1)?e.isTitleCompany=1:e.isTitleCompany=0,n.includes(2)?e.isOperationCompany=1:e.isOperationCompany=0},q=_(((V=history.state)==null?void 0:V.pdata)||{}),u=_(!1),K=()=>{u.value=!0},N=_(!1),Q={companyName:[{required:!0,message:"公司名称不能为空",trigger:"change"},{pattern:/^[\u4e00-\u9fa5a-zA-Z\s\p{P}\p{S}]+$/u,message:"请输入正确的公司名称（支持中文、英文、特殊符号）",trigger:"change"}],creditCode:[{required:!0,message:"统一社会信用代码不能为空",trigger:"change"},{pattern:/^[\u4e00-\u9fa5a-zA-Z0-9\s\p{P}\p{S}]+$/u,message:"请输入正确的统一社会信用代码",trigger:"change"}],status:[{required:!0,message:"",trigger:"change"}],companyType:[{required:!0,message:"请选择公司类型",trigger:"change"}],parentId:[{required:!0,message:"请选择上级权限公司",trigger:"change"}],parentOperationId:[{required:!0,message:"请选择上级运维公司",trigger:"change"}]},U=()=>{u.value=!1,T.value.resetFields()},G=async()=>{N.value=!0;const n={id:e.id,companyName:e.companyName,creditCode:e.creditCode,parentId:e.parentId,parentOperationId:e.parentOperationId,status:e.status,isTitleCompany:e.isTitleCompany,isOperationCompany:e.isOperationCompany,companyAssetType:e.companyAssetType};await Le(n)&&(De.success("编辑成功"),N.value=!1,U(),R())},W=()=>{T.value.validateFields().then(n=>{_e.confirm({title:"确认提示",icon:s(Se),content:"确认保存修改后的信息吗？",onOk:G,onCancel(){console.log("Cancel")}})})},R=async()=>{const n=await Re({id:q.value.id});e.id=n.id,e.companyName=n.companyName,e.ucCompanyCode=n.ucCompanyCode,e.parentComanyName=n.parentComanyName,e.parentId=n.parentId||null,e.parentOperationComanyName=n.parentOperationComanyName,e.parentOperationId=n.parentOperationId||null,e.status=n.status||0,e.creditCode=n.creditCode,e.isTitleCompany=n.isTitleCompany,e.isOperationCompany=n.isOperationCompany,e.companyType=[],e.companyAssetType=n.companyAssetType,e.isTitleCompany==1&&e.companyType.push(1),e.isOperationCompany==1&&e.companyType.push(2),B(1),B(2)},k=_(""),P=()=>{X()},L=_(),X=()=>{var n;(n=L.value)==null||n.reload()},Y=()=>{D.push({path:"/system/company/detail/authUser",state:{pdata:Z.cloneDeep(e)}})},S=n=>{D.push({path:"/system/user/detail",state:{pdata:Z.cloneDeep(n)}})},ee=[{title:"用户编码",dataIndex:"id",search:!1,resizable:!0,ellipsis:!0,width:60},{title:"用户姓名",key:"userName",dataIndex:"userName",render:!0,search:!1,resizable:!0,ellipsis:!0,width:120},{title:"手机号",dataIndex:"mobile",search:!1,resizable:!0,ellipsis:!0,width:120},{title:"授权人",dataIndex:"authUser",search:!1,resizable:!0,ellipsis:!0,width:120},{title:"授权时间",dataIndex:"authTime",search:!1,resizable:!0,ellipsis:!0,width:120}],z=_([]),$=_([]),B=n=>{Pe({ownOrOperation:n}).then(l=>{let r=l||[];n==1&&(r=r.filter(d=>d.id!=e.id),z.value=r),n==2&&(r=r.filter(d=>d.id!=e.id),$.value=r)})},E=(n,l)=>l.companyName.toLowerCase().indexOf(n.toLowerCase())>=0,ae=(n,l)=>new Promise(r=>{let d={companyInfoId:q.value.id,userName:k.value};d={...d,...n},l&&(l.hasOwnProperty("provinceCodeList"),delete d.provinceCodeList),r(d)});return ce(()=>{R()}),(n,l)=>{const r=ye,d=fe,v=ge,f=he,C=ve,F=Ce,te=be,j=ke,se=Ie,O=xe,H=ze,ne=Te,oe=Ne,le=ie;return i(),g("div",{class:"areaPrice",ref_key:"companyDetailRef",ref:w},[$e,o("div",Be,[o("div",Ee,[Ve,a(u)?x("",!0):(i(),y(r,{key:0,type:"primary",onClick:K},{default:t(()=>[h("编辑")]),_:1}))]),s(ne,{model:a(e),name:"formRef",ref_key:"formRef",ref:T,rules:Q,"label-col":{style:{width:"140px",color:"red"}},autocomplete:"off"},{default:t(()=>[s(C,{span:24},{default:t(()=>[s(f,{span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"companyName",label:"公司名称",style:{"padding-right":"100px"}},{default:t(()=>[s(d,{value:a(e).companyName,"onUpdate:value":l[0]||(l[0]=p=>a(e).companyName=p)},null,8,["value"])]),_:1})):(i(),g("div",Fe,[je,o("span",null,c(a(e).companyName),1)]))]),_:1}),s(f,{span:12},{default:t(()=>[o("div",He,[Me,o("span",null,c(a(e).ucCompanyCode),1)])]),_:1})]),_:1}),s(C,{span:24},{default:t(()=>[s(f,{span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"creditCode",label:"统一社会信用代码",style:{"padding-right":"100px"}},{default:t(()=>[s(d,{value:a(e).creditCode,"onUpdate:value":l[1]||(l[1]=p=>a(e).creditCode=p)},null,8,["value"])]),_:1})):(i(),g("div",Ze,[Je,o("span",null,c(a(e).creditCode),1)]))]),_:1}),s(f,{span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"status",label:"状态：",required:"",style:{"padding-right":"100px"}},{default:t(()=>[s(te,{value:a(e).status,"onUpdate:value":l[2]||(l[2]=p=>a(e).status=p)},{default:t(()=>[s(F,{value:0},{default:t(()=>[h("启用")]),_:1}),s(F,{value:1},{default:t(()=>[h("禁用")]),_:1})]),_:1},8,["value"])]),_:1})):(i(),g("div",Ke,[Qe,o("span",null,c(a(e).status===0?"启用":a(e).status===1?"禁用":""),1)]))]),_:1})]),_:1}),s(C,{span:24},{default:t(()=>[s(f,{span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"companyType",label:"公司类型：",required:"",style:{"padding-right":"100px"}},{default:t(()=>[s(se,{value:a(e).companyType,"onUpdate:value":l[3]||(l[3]=p=>a(e).companyType=p),onChange:J},{default:t(()=>[s(j,{value:1},{default:t(()=>[h("产权公司")]),_:1}),s(j,{value:2},{default:t(()=>[h("运维公司")]),_:1})]),_:1},8,["value"])]),_:1})):(i(),g("div",Ge,[We,o("span",null,c(a(e).isTitleCompany?"产权公司":"")+c(a(e).isTitleCompany&&a(e).isOperationCompany?",":"")+c(a(e).isOperationCompany?"运维公司":""),1)]))]),_:1}),a(e).companyType.includes(1)?(i(),y(f,{key:0,span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"parentId",label:"上级产权公司",style:{"padding-right":"100px"}},{default:t(()=>[s(O,{placeholder:"请选择",value:a(e).parentId,"onUpdate:value":l[4]||(l[4]=p=>a(e).parentId=p),allowClear:"","show-search":"",options:a(z),fieldNames:{label:"companyName",value:"id"},"filter-option":E},null,8,["value","options"])]),_:1})):(i(),g("div",Xe,[Ye,o("span",null,c(a(e).parentComanyName),1)]))]),_:1})):x("",!0)]),_:1}),s(C,null,{default:t(()=>[a(e).companyType.includes(2)?(i(),y(f,{key:0,span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"parentOperationId",label:"上级运维公司",style:{"padding-right":"100px"}},{default:t(()=>[s(O,{placeholder:"请选择",value:a(e).parentOperationId,"onUpdate:value":l[5]||(l[5]=p=>a(e).parentOperationId=p),allowClear:"","show-search":"",options:a($),fieldNames:{label:"companyName",value:"id"},"filter-option":E},null,8,["value","options"])]),_:1})):(i(),g("div",ea,[aa,o("span",null,c(a(e).parentOperationComanyName),1)]))]),_:1})):x("",!0)]),_:1}),s(C,{span:24},{default:t(()=>[s(f,{span:12},{default:t(()=>[a(u)?(i(),y(v,{key:0,name:"companyAssetType",label:"公司资产类型",required:!1},{default:t(()=>[s(O,{placeholder:"请选择",value:a(e).companyAssetType,"onUpdate:value":l[6]||(l[6]=p=>a(e).companyAssetType=p),options:a(A)},null,8,["value","options"])]),_:1})):(i(),g("div",ta,[sa,o("span",null,c(a(ue)(a(e).companyAssetType,a(A))),1)]))]),_:1})]),_:1}),s(C,{span:24},{default:t(()=>[s(f,{span:24},{default:t(()=>[a(u)?(i(),g("div",na,[s(H,null,{default:t(()=>[s(r,{onClick:U},{default:t(()=>[h("取消")]),_:1}),s(r,{onClick:W,type:"primary",loading:a(N)},{default:t(()=>[h("保存")]),_:1},8,["loading"])]),_:1})])):x("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),s(le,{columns:ee,ref_key:"actionRef",ref:L,request:a(Ue),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},search:!1,"before-query-params":ae},{tableHeaderLeft:t(()=>[oa,o("div",la,[s(d,{style:{width:"282px"},value:a(k),"onUpdate:value":l[7]||(l[7]=p=>me(k)?k.value=p:null),placeholder:"请输入",onPressEnter:P},{suffix:t(()=>[o("img",{src:a(qe),style:{width:"16px",height:"16px",cursor:"pointer"},onClick:P},null,8,ia)]),_:1},8,["value"]),s(H,null,{default:t(()=>[s(r,{type:"primary",onClick:Y},{default:t(()=>[h("关联用户")]),_:1})]),_:1})])]),tableHeader:t(()=>[]),userNameRender:t(({column:p,record:I,index:ca})=>[s(oe,{placement:"topLeft",getPopupContainer:M=>a(w)},{title:t(()=>[o("span",{class:"light-name",onClick:M=>S(I)},c(I[p.dataIndex]),9,pa)]),default:t(()=>[o("span",{class:"light-name",onClick:M=>S(I)},c(I[p.dataIndex]),9,ra)]),_:2},1032,["getPopupContainer"])]),_:1},8,["request"])],512)}}});const Ia=Ae(da,[["__scopeId","data-v-27de322a"]]);export{Ia as default};
