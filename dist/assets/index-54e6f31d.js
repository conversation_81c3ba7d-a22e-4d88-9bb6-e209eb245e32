import{_ as N}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as P,I as Y,r as u,T as B,o as q,D as F,at as R,a as y,v as E,f as o,e as a,h as r,u as h,b as L,y as V,i as x,g as $,q as g,bg as A,bn as U,_ as G}from"./index-db94d997.js";import{g as H,e as O}from"./index-3dae1b9e.js";import{_ as Q}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const W={class:"areaPrice"},j={class:"text-hide"},J=P({__name:"index",setup(K){const d=Y(),l=u(),p=u([]),b=[{title:"产权公司",key:"companyCode",dataIndex:"companyCode",valueType:"select",width:160,valueEnum:p,fixed:"left",order:1,resizable:!0,render:!0},{title:"装机容量(万kW)",dataIndex:"capins",resizable:!0,search:!1,formatMoney:!0,width:150},{title:"电站数量(个)",dataIndex:"stationCount",resizable:!0,search:!1,width:120},{title:"保单号",dataIndex:"insNumber",search:!0,resizable:!0,width:120,order:3},{title:"保险公司",dataIndex:"insCompany",search:!0,resizable:!0,width:120,order:2},{title:"险种",dataIndex:"insCategory",search:!1,resizable:!0,width:120},{title:"第一受益人",dataIndex:"insBeneficiaries",search:!1,resizable:!0,width:120},{title:"保险开始时间",dataIndex:"startTime",valueType:"date",dateFormat:"YYYY-MM-DD",search:!0,resizable:!0,width:120,order:5},{title:"保险结束时间",dataIndex:"endTime",valueType:"date",dateFormat:"YYYY-MM-DD",search:!0,resizable:!0,width:120,order:6},{title:"保险周期(月)",dataIndex:"insCycle",resizable:!0,search:!1,width:120},{title:"总保额(元)",dataIndex:"insLimit",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"总保费(元)",dataIndex:"insCosts",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"保单状态",dataIndex:"insStatus",valueType:"select",valueEnum:B("INS_STATUS"),resizable:!0,search:!0,width:120,order:4},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:140}];q(()=>{w()}),F(()=>{l.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&k()});const v=()=>{d.push({path:"/financeManage/dailyManage/insurancePolicy/dataImport",query:{templateType:5,fileType:".csv,.xls,.xlsx",fileSize:30}})},w=()=>{R({}).then(e=>{console.log("产权公司res=",e);let t=(e||[]).map(s=>({label:s.companyName,value:s.companyCode}));p.value=t})},C=()=>{var n;let e=(n=l.value)==null?void 0:n.getInitialFormStateNew();O(e)},m=(e,n)=>{let t="";e===0?t="/financeManage/dailyManage/insurancePolicy/detail":e===1&&(t="/financeManage/dailyManage/insurancePolicy/edit"),d.push({path:t,state:{pdata:g.cloneDeep(n),type:e}})},I=e=>{console.log("跳转到明细页"),d.push({path:"/financeManage/dailyManage/insurancePolicy/info",state:{pdata:g.cloneDeep(e)}})},_=u([]),M=e=>{_.value=e||[]},k=()=>{var e;(e=l.value)==null||e.reload()},z=(e,n)=>{let t={};return new Promise(s=>{const c={...t,...e,delStatus:0};s(c)})};return(e,n)=>{const t=A,s=Q,c=U,D=N;return y(),E("div",W,[o(D,{columns:b,ref_key:"actionRef",ref:l,request:h(H),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:M,"before-query-params":z},{tableHeader:a(()=>[o(s,null,{default:a(()=>[o(t,{onClick:v},{default:a(()=>[r("批量导入")]),_:1}),h(_).length>0?(y(),L(t,{key:0,onClick:C},{default:a(()=>[r("导出")]),_:1})):V("",!0),o(t,{type:"primary",onClick:n[0]||(n[0]=f=>m(0))},{default:a(()=>[r("新建保单")]),_:1})]),_:1})]),companyCodeRender:a(({column:f,record:i,index:S})=>[o(c,null,{title:a(()=>[r(x(i.companyName),1)]),default:a(()=>[$("span",j,x(i.companyName),1)]),_:2},1024)]),actionRender:a(({column:f,record:i,index:S})=>[o(s,null,{default:a(()=>[o(t,{type:"link",size:"small",onClick:T=>I(i)},{default:a(()=>[r("电站明细")]),_:2},1032,["onClick"]),o(t,{type:"link",size:"small",onClick:T=>m(1,i)},{default:a(()=>[r("编辑")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const ie=G(J,[["__scopeId","data-v-fdd086ee"]]);export{ie as default};
