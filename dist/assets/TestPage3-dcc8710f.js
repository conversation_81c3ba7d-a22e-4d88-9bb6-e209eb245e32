import{d as h,r as t,o as _,G as g,B as w,w as B,l as E,a as z,v as C,p as D,j as x,g as F,_ as I}from"./index-db94d997.js";import{i as O,r as T}from"./index-36a0e5b9.js";const k="/assets/china_map_bg-bde4f98d.png",L=r=>(D("data-v-6c8cfbce"),r=r(),x(),r),j={class:"map-container"},P=L(()=>F("div",{id:"main",class:"main-container"},null,-1)),W=[P],M=h({__name:"TestPage3",props:{},emits:["update"],setup(r,{emit:m}){const l=t("china"),e=t(1),n=t(!1),a=t();_(()=>{c(),window.addEventListener("resize",d)});const p=[{value:[118.8062,31.9208],itemStyle:{color:"#4ab2e5"}},{value:[127.9688,45.368],itemStyle:{color:"#4fb6d2"}},{value:[110.3467,41.4899],itemStyle:{color:"#52b9c7"}},{value:[125.8154,44.2584],itemStyle:{color:"#5abead"}},{value:[116.4551,40.2539],itemStyle:{color:"#f34e2b"}},{value:[123.1238,42.1216],itemStyle:{color:"#f56321"}},{value:[114.4995,38.1006],itemStyle:{color:"#f56f1c"}},{value:[117.4219,39.4189],itemStyle:{color:"#f58414"}},{value:[112.3352,37.9413],itemStyle:{color:"#f58f0e"}},{value:[109.1162,34.2004],itemStyle:{color:"#f5a305"}},{value:[103.5901,36.3043],itemStyle:{color:"#e7ab0b"}},{value:[106.3586,38.1775],itemStyle:{color:"#dfae10"}},{value:[101.4038,36.8207],itemStyle:{color:"#d5b314"}},{value:[103.9526,30.7617],itemStyle:{color:"#c1bb1f"}},{value:[108.384366,30.439702],itemStyle:{color:"#b9be23"}},{value:[113.0823,28.2568],itemStyle:{color:"#a6c62c"}},{value:[102.9199,25.46639],itemStyle:{color:"#96cc34"}},{value:[119.4543,25.9222]}],v=t(e.value===1?{type:"effectScatter",coordinateSystem:"geo",showEffectOn:"render",zlevel:1,rippleEffect:{period:15,scale:4,brushType:"fill"},hoverAnimation:!0,label:{normal:{formatter:"{b}",position:"right",offset:[15,0],color:"#1DE9B6",show:!0}},itemStyle:{normal:{color:"#1DE9B6",shadowBlur:10,shadowColor:"#333"}},symbolSize:3,data:p}:{}),y=t(e.value===1?{type:"lines",zlevel:2,effect:{show:!0,period:5,trailLength:.1,symbol:"arrow",symbolSize:7},lineStyle:{normal:{color:"#1DE9B6",width:2,opacity:.1,curveness:.2}},data:[{coords:[[119.4543,25.9222],[118.8062,31.9208]],lineStyle:{color:"#4ab2e5"}},{coords:[[119.4543,25.9222],[127.9688,45.368]],lineStyle:{color:"#4fb6d2"}},{coords:[[119.4543,25.9222],[110.3467,41.4899]],lineStyle:{color:"#52b9c7"}},{coords:[[119.4543,25.9222],[125.8154,44.2584]],lineStyle:{color:"#5abead"}},{coords:[[119.4543,25.9222],[116.4551,40.2539]],lineStyle:{color:"#f34e2b"}},{coords:[[119.4543,25.9222],[123.1238,42.1216]],lineStyle:{color:"#f56321"}},{coords:[[119.4543,25.9222],[114.4995,38.1006]],lineStyle:{color:"#f56f1c"}},{coords:[[119.4543,25.9222],[117.4219,39.4189]],lineStyle:{color:"#f58414"}},{coords:[[119.4543,25.9222],[112.3352,37.9413]],lineStyle:{color:"#f58f0e"}},{coords:[[119.4543,25.9222],[109.1162,34.2004]],lineStyle:{color:"#f5a305"}},{coords:[[119.4543,25.9222],[103.5901,36.3043]],lineStyle:{color:"#e7ab0b"}},{coords:[[119.4543,25.9222],[106.3586,38.1775]],lineStyle:{color:"#dfae10"}},{coords:[[119.4543,25.9222],[101.4038,36.8207]],lineStyle:{color:"#d5b314"}},{coords:[[119.4543,25.9222],[103.9526,30.7617]],lineStyle:{color:"#c1bb1f"}},{coords:[[119.4543,25.9222],[108.384366,30.439702]],lineStyle:{color:"#b9be23"}},{coords:[[119.4543,25.9222],[113.0823,28.2568]],lineStyle:{color:"#a6c62c"}},{coords:[[119.4543,25.9222],[102.9199,25.46639]],lineStyle:{color:"#96cc34"}}]}:{}),i=()=>(console.log("province.value=",l.value),{geo:[{map:"china",aspectScale:.75,zoom:1,layoutCenter:["50%","50%"],layoutSize:"100%",show:!0,silent:!0,roam:!1,itemStyle:{normal:{opacity:1,borderColor:"#BDEFFF",borderWidth:2,shadowColor:"#BDEFFF",areaColor:{image:k,repeat:"repeat"}}},regions:[]}],series:[{type:"map",roam:!1,label:{normal:{show:!0,textStyle:{color:"#1DE9B6"}},emphasis:{textStyle:{color:"rgb(183,185,14)"}}},itemStyle:{normal:{borderColor:"transparent",borderWidth:1,areaColor:{type:"radial",x:.5,y:.5,r:.8,colorStops:[{offset:0,color:"transparent"},{offset:1,color:"transparent"}],globalCoord:!0}},emphasis:{areaColor:"red",borderWidth:.1}},zoom:1,map:l.value},v.value,y.value]}),c=()=>{let o=document.getElementById("main");a.value=a.value||O(o),n.value=!0,g.get(`${w}map-json/${l.value}.json`).then(s=>{var u;console.log("res=",s);let S=s.data;n.value=!1,T(l.value,S);const b=(l.value==="china",i());(u=a.value)==null||u.setOption(b,!0)}),a.value.on("click",f)},f=o=>{console.log(o),e.value<3&&(e.value=e.value+1,l.value=e.value===1?"china":e.value===2?"河北省":e.value===3?"石家庄市":"",c())},d=()=>{var o;(o=a.value)==null||o.resize()};return B(e,o=>{l.value=e.value===1?"china":e.value===2?"河北省":e.value===3?"石家庄市":"",c(),m("update",o)}),E(()=>{console.log("地图组件销毁且销毁resize方法"),window.removeEventListener("resize",d)}),(o,s)=>(z(),C("div",j,W))}});const $=I(M,[["__scopeId","data-v-6c8cfbce"]]);export{$ as default};
