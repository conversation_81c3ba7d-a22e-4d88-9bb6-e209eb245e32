import{_ as X}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as Z,I as ee,r as g,K as te,o as ae,D as ne,a as c,v as U,g as f,f as i,e as o,u as d,y as oe,h as _,b as u,n as Y,i as m,b_ as se,q as le,at as ie,bc as de,bU as re,av as ce,bf as pe,aw as _e,ax as ue,bg as me,bh as fe,az as he,bn as ye,p as ge,j as ve,_ as xe}from"./index-db94d997.js";import{g as Ce,e as be,u as ke}from"./index-705af6a9.js";import{D as we}from"./dayjs-a8e42122.js";import{_ as De}from"./index-42d7fb9b.js";import{_ as Ie}from"./index-4a280682.js";import{_ as <PERSON>}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const Ye=v=>(ge("data-v-2016b466"),v=v(),ve(),v),Me={class:"areaPrice"},Se={class:"areaPrice_table"},Ne={class:"forecast_mode"},Te=Ye(()=>f("span",{style:{"margin-left":"5px"}},"全选",-1)),Ue={class:"text-hide"},qe={class:"text-hide"},Re={class:"text-hide"},ze=Z({__name:"index",setup(v){const q=ee(),C=g(),h=g(!1),b=g(!1),p=g([]),s=te({companyCodeList:[],weight:"",sameWeight:""}),R=[{title:"序号",valueType:"index",width:60,resizable:!0},{title:"文档名称",key:"docName",dataIndex:"docName",search:!1,width:120,resizable:!0,render:!0},{title:"预测时间",key:"createTime",dataIndex:"createTime",search:!1,resizable:!0,width:130,render:!0},{title:"操作",key:"action",search:!1,width:50,render:!0,align:"center"}],z=e=>{s.sameWeight=e||e===0?se(1,e):""},P=(e,{attrs:n})=>n.vnodes,F=(e,n)=>{s[e]=n.map(r=>r.value)},B=(e,n,r)=>{var x;((x=e==null?void 0:e.target)==null?void 0:x.checked)?F(n,r):s[n]=[],s[n+"_indeterminate"]=!1},E=(e,n,r,l)=>{n.length===0?(s[l+"_indeterminate"]=!1,s[l+"_checked"]=!1):n.length===r.length?(s[l+"_indeterminate"]=!1,s[l+"_checked"]=!0):(s[l+"_indeterminate"]=!0,s[l+"_checked"]=!1)},M=()=>{var e;(e=C.value)==null||e.reload()},V=async e=>{q.push({path:"/financeManage/electricQuantity/monthForecast/detail",query:{id:e==null?void 0:e.id},state:{pdata:le.cloneDeep(e)}})},W=e=>{let n={pid:e==null?void 0:e.id};be(n).then(r=>{console.log("导出成功")})},$=(e,n)=>new Promise(r=>{let l={delStatus:0,noJoin:!0};l={...l,...e},r(l)}),j=g([]),A=e=>{j.value=e||[]},Q=()=>{ie({}).then(e=>{console.log("产权公司res=",e);let r=(e||[]).map(l=>({...l,label:l.companyName,value:l.companyCode}));p.value=r})};ae(()=>{Q()}),ne(()=>{C.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&M()});const G=e=>{console.log("立即预测values=",e);const n=p.value.filter(l=>s.companyCodeList.includes(l.companyCode));let r={...e,detailList:n};h.value=!0,ke(r).then(l=>{h.value=!1,de.info("操作成功"),M()}).catch(l=>{h.value=!1})};return(e,n)=>{const r=re,l=De,x=ce,k=pe,w=_e,D=Ie,S=ue,I=me,J=fe,K=he,L=ye,N=we,O=Le,H=X;return c(),U("div",Me,[f("div",Se,[f("div",Ne,[i(K,{spinning:d(h)},{default:o(()=>[i(J,{ref:"formRef",model:d(s),name:"basic",autocomplete:"off",onFinish:G},{default:o(()=>[i(S,{span:24},{default:o(()=>[i(w,{span:8},{default:o(()=>[i(k,{label:"产权公司",style:{"padding-right":"20px"},name:"companyCodeList",required:""},{default:o(()=>[i(x,{value:d(s).companyCodeList,"onUpdate:value":n[3]||(n[3]=a=>d(s).companyCodeList=a),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"","allow-clear":"",placeholder:"请选择",options:d(p),mode:"multiple","filter-option":(a,t)=>((t==null?void 0:t.label)??"").toLowerCase().includes(a.toLowerCase()),onChange:n[4]||(n[4]=(a,t)=>E(a,t,d(p),"companyCodeList"))},{dropdownRender:o(({menuNode:a})=>[d(p)&&d(p).length>0?(c(),U("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:n[2]||(n[2]=t=>t.preventDefault())},[i(r,{checked:d(s).companyCodeList_checked,"onUpdate:checked":n[0]||(n[0]=t=>d(s).companyCodeList_checked=t),indeterminate:d(s).companyCodeList_indeterminate,onChange:n[1]||(n[1]=t=>B(t,"companyCodeList",d(p)))},null,8,["checked","indeterminate"]),Te],32)):oe("",!0),i(l,{style:{margin:"4px 0"}}),i(P,{vnodes:a},null,8,["vnodes"])]),_:1},8,["value","options","filter-option"])]),_:1})]),_:1}),i(w,{span:8},{default:o(()=>[i(k,{label:"最近10天权重",style:{padding:"0 10px"},name:"weight",required:""},{default:o(()=>[i(D,{value:d(s).weight,"onUpdate:value":n[5]||(n[5]=a=>d(s).weight=a),controls:!1,style:{width:"100%"},min:0,max:1,onChange:z},null,8,["value"])]),_:1})]),_:1}),i(w,{span:8},{default:o(()=>[i(k,{label:"去年同期权重",style:{"padding-left":"20px"},name:"sameWeight"},{default:o(()=>[i(D,{disabled:!0,value:d(s).sameWeight,"onUpdate:value":n[6]||(n[6]=a=>d(s).sameWeight=a),controls:!1,style:{width:"100%"},min:0,max:1},null,8,["value"])]),_:1})]),_:1})]),_:1}),i(S,{justify:"end"},{default:o(()=>[i(I,{type:"primary","html-type":"submit",disabled:d(h)},{default:o(()=>[_("立即预测")]),_:1},8,["disabled"])]),_:1})]),_:1},8,["model"])]),_:1},8,["spinning"])]),i(H,{columns:R,ref_key:"actionRef",ref:C,search:!1,request:d(Ce),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:A,"before-query-params":$},{docNameRender:o(({column:a,record:t,index:T})=>[t.isEdit?(c(),u(D,{key:0,value:t[a.dataIndex],"onUpdate:value":y=>t[a.dataIndex]=y,placeholder:"请输入",allowClear:"",style:Y({width:"80%",borderColor:d(b)&&!t[a.dataIndex]?"red":""}),controls:!1,stringMode:!0},null,8,["value","onUpdate:value","style"])):(c(),u(L,{key:1},{title:o(()=>[_(m(t[a.dataIndex]),1)]),default:o(()=>[f("span",Ue,m(t[a.dataIndex]),1)]),_:2},1024))]),createTimeRender:o(({column:a,record:t,index:T})=>[t.isEdit?(c(),u(N,{key:0,value:t[a.dataIndex],"onUpdate:value":y=>t[a.dataIndex]=y,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",allowClear:"",style:Y({width:"80%",borderColor:d(b)&&!t[a.dataIndex]?"red":""}),";":""},null,8,["value","onUpdate:value","style"])):(c(),u(L,{key:1},{title:o(()=>[_(m(t[a.dataIndex]),1)]),default:o(()=>[f("span",qe,m(t[a.dataIndex]),1)]),_:2},1024))]),endTimeRender:o(({column:a,record:t,index:T})=>[t.isEdit?(c(),u(N,{key:0,value:t[a.dataIndex],"onUpdate:value":y=>t[a.dataIndex]=y,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",allowClear:"",style:Y({width:"80%",borderColor:d(b)&&!t[a.dataIndex]?"red":""})},null,8,["value","onUpdate:value","style"])):(c(),u(L,{key:1},{title:o(()=>[_(m(t[a.dataIndex]),1)]),default:o(()=>[f("span",Re,m(t[a.dataIndex]),1)]),_:2},1024))]),actionRender:o(({record:a,index:t})=>[i(O,null,{default:o(()=>[i(I,{size:"small",type:"link",onClick:()=>V(a)},{default:o(()=>[_(" 预览 ")]),_:2},1032,["onClick"]),i(I,{size:"small",type:"link",onClick:()=>W(a)},{default:o(()=>[_(" 下载 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])])}}});const Ge=xe(ze,[["__scopeId","data-v-2016b466"]]);export{Ge as default};
