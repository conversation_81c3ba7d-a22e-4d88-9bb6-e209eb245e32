import{_ as O}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as R,r as l,I as M,T as v,o as q,D as B,au as V,at as Y,a as w,v as K,f as i,e as n,u as T,b as P,h as f,y as W,i as I,g as Q,q as j,bg as G,bn as H,_ as J}from"./index-db94d997.js";import{g as U,e as $}from"./index-78c2ffe1.js";import{_ as X}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Z={class:"areaPrice"},F={class:"text-hide"},ee=R({__name:"index",setup(te){const c=l(),_=l([]),m=l([]),C=M();l([]),l([]);const D=({id:e})=>h(e);let d="",y="";const N=[{title:"电站编号",dataIndex:"stationUniqueId",width:120,resizable:!0,search:!0,order:2,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,order:3,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"ownerCompanyCodeList",valueType:"multipleSelect",maxTagCount:1,width:130,valueEnum:m,render:!0,resizable:!0,order:1,fixed:"left"},{title:"省",dataIndex:"provinceName",width:120,resizable:!0,search:!1},{title:"市",dataIndex:"cityName",width:120,resizable:!0,search:!1},{title:"区",dataIndex:"areaName",width:120,resizable:!0,search:!1},{title:"详细地址",dataIndex:"projectLocation",width:160,resizable:!0,search:!1},{title:"行政区划",key:"cityTree",dataIndex:"cityTree",valueType:"treeSelect",valueEnum:_,valueTreeLoad:D,onSelect:(e,t,a)=>{var r,u;const o=((u=(r=a==null?void 0:a.triggerNode)==null?void 0:r.props)==null?void 0:u.level)||1;d=["provinceCode","cityCode","areaCode","town","vil"][o-1],y=e},render:!0,width:150,resizable:!0,hideInTable:!0},{title:"电站类型",dataIndex:"businessStationType",valueType:"select",valueEnum:v("EAM_POWER_STATION_TYPE_CONDITION"),resizable:!0,width:120},{title:"电站状态",dataIndex:"stationStatus",valueType:"select",valueEnum:v("POWER_STATION_STATE"),resizable:!0,width:120},{title:"并网时间",dataIndex:"datongrd",dateFormat:"YYYY-MM-DD",width:120,resizable:!0,search:!1},{title:"装机容量(kW)",dataIndex:"capins",width:120,resizable:!0,formatMoney:!0,search:!1},{title:"组件数量(块)",dataIndex:"componentQuantity",width:120,resizable:!0,search:!1},{title:"操作",key:"action",dataIndex:"action",search:!1,width:100,render:!0,align:"center",fixed:"right"}];q(()=>{S(),h()}),B(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&L()});const h=e=>new Promise((t,a)=>{V({pid:e||"0"}).then(o=>{console.log("行政区res=",o);let s=x(o);_.value=s,console.log(_.value),t(!0)}).catch(()=>{a()})}),x=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=3,t.subDistrict&&t.subDistrict&&(t.children=t.subDistrict,x(t.subDistrict))}),e),S=()=>{Y({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(o=>({label:o.companyName,value:o.companyCode}));m.value=a})},k=(e,t)=>{C.push({path:"/pAssetManage/archivesManage/operateInfo/detail",state:{pdata:j.cloneDeep(e)}})},z=()=>{var t;let e=(t=c.value)==null?void 0:t.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),$(e)},g=l([]),E=e=>{g.value=e||[],console.log("dataSource=",e)},L=()=>{var e;(e=c.value)==null||e.reload()},A=(e,t)=>{let a={};return new Promise(o=>{t&&t.hasOwnProperty("cityTree")&&!(t!=null&&t.cityTree)&&(d="",y=""),d&&(a={[d]:y});const s={...a,...e,noJoin:!0,delStatus:0};o(s)})};return(e,t)=>{const a=G,o=X,s=H,r=O;return w(),K("div",Z,[i(r,{columns:N,ref_key:"actionRef",ref:c,request:T(U),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:E,"before-query-params":A},{tableHeader:n(()=>[i(o,null,{default:n(()=>[T(g).length>0?(w(),P(a,{key:0,type:"primary",onClick:z},{default:n(()=>[f("导出")]),_:1})):W("",!0)]),_:1})]),companyCodeListRender:n(({column:u,record:p,index:b})=>[i(s,null,{title:n(()=>[f(I(p.ownerCompanyName),1)]),default:n(()=>[Q("span",F,I(p.ownerCompanyName),1)]),_:2},1024)]),actionRender:n(({column:u,record:p,index:b})=>[i(o,null,{default:n(()=>[i(a,{size:"small",type:"link",onClick:()=>k(p,b)},{default:n(()=>[f(" 查看详情 ")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const pe=J(ee,[["__scopeId","data-v-98125f55"]]);export{pe as default};
