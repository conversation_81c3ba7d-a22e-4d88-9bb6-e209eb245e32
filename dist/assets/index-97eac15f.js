import{_ as R}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as A,r as n,I as V,o as B,D as E,at as K,a as g,v as q,f as i,e as l,h as r,u as m,b as L,y as O,i as w,g as H,q as G,bc as J,bg as Q,bn as $,_ as j}from"./index-db94d997.js";import{g as U,c as W,e as X}from"./index-4b995c57.js";import{_ as Y}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Z={class:"areaPrice"},P={class:"text-hide"},F=A({__name:"index",setup(ee){const c=n();n([]);const f=n([]),v=V(),d=n(!1);n([]),n([]);let u="",h="";const I=n([{label:"未判定",value:-1},{label:"新增",value:0},{label:"置换",value:1}]),z=[{title:"电站编号",dataIndex:"stationCode",width:120,resizable:!0,search:!0,fixed:"left"},{title:"业主名称",dataIndex:"stationName",width:120,resizable:!0,search:!0,fixed:"left"},{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:1,width:150,valueEnum:f,render:!0,resizable:!0},{title:"推送时间",dataIndex:"pushTime",width:120,resizable:!0,search:!1},{title:"变动类型",dataIndex:"variationType",valueType:"select",valueEnum:I,resizable:!0,width:120},{title:"置换电站数量",dataIndex:"replacementStationNum",width:120,resizable:!0,search:!1},{title:"被置换电站编号",dataIndex:"replacementStationCode",width:120,resizable:!0,search:!1},{title:"被置换电站业主姓名",dataIndex:"replacementStationName",width:150,resizable:!0,search:!1},{title:"被置换电站所属产权公司",dataIndex:"replacementCompanyName",width:180,resizable:!0,search:!1},{title:"总折旧月份",dataIndex:"usrMonth",width:120,resizable:!0,search:!1},{title:"资产原值",dataIndex:"assetValue",width:120,resizable:!0,search:!1},{title:"入账月份",dataIndex:"monthKey",width:120,resizable:!0,search:!1},{title:"入账前已计提折旧额",dataIndex:"beforeAccountDepreciationCost",width:150,resizable:!0,search:!1},{title:"入账前已计提折旧月份",dataIndex:"beforeAccountDepreciationMonth",width:160,resizable:!0,search:!1},{title:"入账时资产价值",dataIndex:"accountAssetValue",width:120,resizable:!0,search:!1},{title:"剩余折旧月份",dataIndex:"residueDepreciationMonth",width:120,resizable:!0,search:!1},{title:"净残值率",dataIndex:"salvageRate",width:120,resizable:!0,search:!1},{title:"净残值",dataIndex:"salvageValue",width:120,resizable:!0,search:!1},{title:"月折旧额",dataIndex:"monthDepreciationCost",width:120,resizable:!0,search:!1},{title:"标杆电价",dataIndex:"sightElectrovalences",width:120,resizable:!0,formatDecimal:4,search:!1},{title:"补贴电价",dataIndex:"electrovalences",width:120,resizable:!0,formatDecimal:4,search:!1},{title:"上网电价",dataIndex:"feedInTariff",width:120,resizable:!0,formatDecimal:4,search:!1},{title:"操作",key:"action",dataIndex:"action",search:!1,width:150,render:!0,align:"center",fixed:"right"}];B(()=>{C()}),E(()=>{c.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&b()});const C=()=>{K({}).then(e=>{console.log("产权公司res=",e);let t=(e||[]).map(o=>({label:o.companyName,value:o.companyCode}));f.value=t})},_=e=>{console.log("item=",e);let a=e?"/pAssetManage/propertyManage/addOrReplacement/edit":"/pAssetManage/propertyManage/addOrReplacement/personClassify";v.push({path:a,state:{pdata:e?G.cloneDeep(e):{},type:e?1:0}})},D=e=>{console.log("确认入库");let a={...e};d.value=!0,W(a).then(t=>{console.log("入库成功"),d.value=!1,J.info("入库成功"),b()}).catch(t=>{d.value=!1})},k=()=>{var a;let e=(a=c.value)==null?void 0:a.getInitialFormStateNew();e==null||delete e.pageNum,e==null||delete e.pageSize,e==null||delete e.delStatsus,console.log(e,"导出---"),X(e)},y=n([]),N=e=>{y.value=e||[],console.log("dataSource=",e)},b=()=>{var e;(e=c.value)==null||e.reload()},T=(e,a)=>{let t={};return new Promise(o=>{a&&a.hasOwnProperty("cityTree")&&!(a!=null&&a.cityTree)&&(u="",h=""),u&&(t={[u]:h});const p={...t,...e,noJoin:!0,delStatus:0};o(p)})};return(e,a)=>{const t=Q,o=Y,p=$,x=R;return g(),q("div",Z,[i(x,{columns:z,ref_key:"actionRef",ref:c,request:m(U),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:N,"before-query-params":T},{tableHeader:l(()=>[i(o,null,{default:l(()=>[i(t,{onClick:a[0]||(a[0]=()=>_())},{default:l(()=>[r("人工分类")]),_:1}),m(y).length>0?(g(),L(t,{key:0,type:"primary",onClick:k},{default:l(()=>[r("导出")]),_:1})):O("",!0)]),_:1})]),companyCodeListRender:l(({column:S,record:s,index:M})=>[i(p,null,{title:l(()=>[r(w(s.companyName),1)]),default:l(()=>[H("span",P,w(s.companyName),1)]),_:2},1024)]),actionRender:l(({column:S,record:s,index:M})=>[i(o,null,{default:l(()=>[i(t,{size:"small",type:"link",onClick:()=>_(s),disabled:s.confirmRepertoryStatus===1||s.variationType===-1},{default:l(()=>[r(" 编辑 ")]),_:2},1032,["onClick","disabled"]),i(t,{size:"small",type:"link",onClick:()=>D(s),loading:m(d),disabled:s.confirmRepertoryStatus===1||s.variationType===-1},{default:l(()=>[r(" 确认入库 ")]),_:2},1032,["onClick","loading","disabled"])]),_:2},1024)]),_:1},8,["request"])])}}});const de=j(F,[["__scopeId","data-v-29ac30cf"]]);export{de as default};
