import{_ as fe}from"./index-914bbc8b.js";import{_ as he}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as ye,r as d,I as ge,bL as be,K as xe,T as Ie,o as ve,D as ke,at as Ce,a as r,v as m,g as u,f as c,e as n,h as y,u as l,b as N,y as E,i as f,S as W,s as A,F as we,q as Se,V as De,bW as Le,bc as Te,bg as Re,bn as Ke,av as Me,bf as Fe,aw as Ne,ax as Ee,bh as ze,bi as Pe,p as Be,j as Oe,_ as qe}from"./index-db94d997.js";import{g as Ue,e as $e}from"./index-8efc2159.js";import{b as Ve,c as Ye}from"./index-5fcafee1.js";import{m as je}from"./dictLocal-9822709a.js";import{_ as We}from"./index-39334618.js";import"./index-326d414f.js";import"./icon-831229e8.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const Ae=I=>(Be("data-v-16d3901b"),I=I(),Oe(),I),Ge={class:"areaPrice"},He={class:"text-hide"},Je={key:0,class:"status_tag"},Qe={class:"tag_one"},Xe={key:1,class:"status_tag"},Ze={class:"tag_two"},et={key:0,class:"status_tag"},tt={class:"tag_two"},at={key:1,class:"status_tag"},nt={class:"tag_one"},ot={key:2,class:"status_tag"},st={class:"tag_three"},lt={key:3,class:"status_tag"},it=Ae(()=>u("span",{class:"tag_two"},"待提交",-1)),ct=[it],dt=ye({__name:"index",setup(I){const G=["provinceCodeList","cityCodeList","areaCodeList"],H=d([]),z=ge(),J=be(),v=d(),P=d([]),h=d(!1),k=d(!1),g=xe({}),C=d(),B=d([]),R=d([]),O=d({}),w=d(!1),q=d([]),K=d([{label:"已确认",value:1},{label:"未确认",value:0}]),Q=d([{label:"待审核",value:0},{label:"审核中",value:1},{label:"审核驳回",value:3},{label:"审核通过",value:2}]),M=d([{label:"审批中",value:1},{label:"审批通过",value:2},{label:"审批驳回",value:3}]),X=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:3,width:120,valueEnum:P,render:!0,fixed:"left"},{title:"账单周期",dataIndex:"monthKey",valueType:"dateRange",width:120,dateFormat:"YYYY-MM",order:2},{title:"其他业务收入",dataIndex:"otherIncome",search:!1,resizable:!0,formatMoney:!0,width:100},{title:"装机容量",dataIndex:"capins",search:!1,resizable:!0,formatMoney:!0,width:100},{title:"电站数量",dataIndex:"stationCount",search:!1,resizable:!0,width:100},{title:"拆分模式",dataIndex:"splitMode",valueType:"select",valueEnum:Ie("SPLIT_MODE_TYPE"),resizable:!0,search:!1,width:120},{title:"确认状态",key:"confirmFlag",dataIndex:"confirmFlag",valueType:"select",valueEnum:K,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"审核状态",key:"approveStatus",dataIndex:"approveStatus",valueType:"select",valueEnum:Q,resizable:!0,search:!1,render:!0,align:"center",width:120},{title:"操作",key:"action",dataIndex:"action",search:!1,width:100,render:!0,align:"center",fixed:"right"}];ve(()=>{Z(),te()}),ke(()=>{v.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&$()});const Z=()=>{Ce({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(i=>({label:i.companyName,value:i.companyCode}));P.value=a})},ee=e=>{z.push({path:"/financeManage/incomeManage/other/otherIncomeConfirm/info",state:{pdata:Se.cloneDeep(e)}})},te=()=>{Ve().then(e=>{B.value=e==null?void 0:e.map(t=>({label:t.roleName,value:t.id}))})},ae=e=>{Ye({roleId:e}).then(t=>{R.value=t==null?void 0:t.map(a=>({label:a.userName,value:a.userId}))})},ne=e=>{console.log(`selected ${e}`),ae(e)},oe=e=>{q.value=e||{},De(()=>{w.value=!0})},se=e=>{var t;h.value=!0,O.value=e,R.value=[],(t=C.value)==null||t.resetFields()},le=()=>{var e;h.value=!1,(e=C.value)==null||e.resetFields()},ie=()=>{console.log("其他业务收入确认-提交审核"),C.value.validateFields().then(e=>{let t=O.value.id;const a="businessKeyOtherIncomeConfirm",i=je[0].value;let p={assigneeId:e.assigneeId,relationId:t,businessKey:a,processKey:i};k.value=!0,Le(p).then(_=>{h.value=!1,k.value=!1,Te.info("保存成功"),$()}).catch(_=>{k.value=!1})})},ce=()=>{var t;let e=(t=v.value)==null?void 0:t.getInitialFormStateNew();$e(e)},de=()=>{z.push({path:"/financeManage/incomeManage/other/otherIncomeConfirm/dataImport",query:{templateType:6,fileType:".csv,.xls,.xlsx",fileSize:30}})},U=d([]),re=e=>{U.value=e||[],console.log("dataSource=",e)},$=()=>{var e;(e=v.value)==null||e.reload()},pe=e=>{const t=new Map;return e.forEach(a=>{const i=G[a.level-1];if(t.has(i))t.get(i).push(a.value);else{let p=[];p.push(a.value),t.set(i,p)}}),Object.fromEntries(t)},V=(e,t,a)=>!e||!t?[]:(e.forEach(i=>{t.find(_=>i.value===_)&&a.push(i),i.children&&i.children.length>0&&V(i.children,t,a)}),a),_e=(e,t)=>new Promise(a=>{var T;const i=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[0]:"",p=e!=null&&e.monthKey&&(e==null?void 0:e.monthKey.length)>0?e==null?void 0:e.monthKey[1]:"";let _={delStatus:0,noJoin:!0,startMonthKey:i,endMonthKey:p};const b=(T=J.query)==null?void 0:T.relationId;b&&(_.id=b);const S=V(H.value,e==null?void 0:e.cityTree,[]);let D=pe(S);e==null||delete e.monthKey;const L={..._,...e,...D};a(L)});return(e,t)=>{const a=Re,i=We,p=Ke,_=he,b=Me,S=Fe,D=Ne,L=Ee,T=ze,ue=Pe,me=fe;return r(),m(we,null,[u("div",Ge,[c(_,{columns:X,ref_key:"actionRef",ref:v,request:l(Ue),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:re,"before-query-params":_e},{tableHeader:n(()=>[c(i,null,{default:n(()=>[c(a,{onClick:de},{default:n(()=>[y("批量导入")]),_:1}),l(U).length>0?(r(),N(a,{key:0,type:"primary",onClick:ce},{default:n(()=>[y("导出")]),_:1})):E("",!0)]),_:1})]),companyCodeListRender:n(({column:o,record:s,index:F})=>[c(p,null,{title:n(()=>[y(f(s.companyName),1)]),default:n(()=>[u("span",He,f(s.companyName),1)]),_:2},1024)]),confirmFlagRender:n(({column:o,record:s,index:F})=>[s[o.dataIndex]===1?(r(),m("span",Je,[u("span",Qe,f(l(W)(s[o.dataIndex],l(K))),1)])):E("",!0),s[o.dataIndex]===0?(r(),m("span",Xe,[u("span",Ze,f(l(W)(s[o.dataIndex],l(K))),1)])):E("",!0)]),approveStatusRender:n(({column:o,record:s,index:F})=>{var x,Y,j;return[s[o.dataIndex]=="1"?(r(),m("span",et,[u("span",tt,f((x=l(M)[s[o.dataIndex]-1])==null?void 0:x.label),1)])):s[o.dataIndex]=="2"?(r(),m("span",at,[u("span",nt,f((Y=l(M)[s[o.dataIndex]-1])==null?void 0:Y.label),1)])):s[o.dataIndex]=="3"?(r(),m("span",ot,[u("span",st,f((j=l(M)[s[o.dataIndex]-1])==null?void 0:j.label),1)])):(r(),m("span",lt,ct))]}),actionRender:n(({column:o,record:s,index:F})=>[c(i,null,{default:n(()=>[s.approveStatus===1?(r(),N(a,{key:0,type:"link",size:"small",onClick:x=>oe(s)},{default:n(()=>[y("查看审批单 ")]),_:2},1032,["onClick"])):(r(),N(a,{key:1,type:"link",size:"small",onClick:x=>se(s),disabled:s.approveStatus===2},{default:n(()=>[y("提交审核 ")]),_:2},1032,["onClick","disabled"])),c(a,{type:"link",size:"small",onClick:x=>ee(s)},{default:n(()=>[y("查看明细")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])]),c(ue,{visible:l(h),"onUpdate:visible":t[2]||(t[2]=o=>A(h)?h.value=o:null),title:"提交审批","confirm-loading":l(k),okText:"提交审批",onOk:ie,onCancel:le},{default:n(()=>[c(T,{model:l(g),ref_key:"formRef2",ref:C,name:"basic","label-col":{style:{width:"80px"}},autocomplete:"off"},{default:n(()=>[c(L,{span:24},{default:n(()=>[c(D,{span:24},{default:n(()=>[c(S,{label:"审批角色",name:"aaa"},{default:n(()=>[c(b,{value:l(g).aaa,"onUpdate:value":t[0]||(t[0]=o=>l(g).aaa=o),options:l(B),placeholder:"请选择",style:{width:"100%"},onChange:ne},null,8,["value","options"])]),_:1})]),_:1})]),_:1}),c(L,{span:24},{default:n(()=>[c(D,{span:24},{default:n(()=>[c(S,{label:"审批人员",name:"assigneeId"},{default:n(()=>[c(b,{value:l(g).assigneeId,"onUpdate:value":t[1]||(t[1]=o=>l(g).assigneeId=o),options:l(R),placeholder:"请选择"},null,8,["value","options"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"]),c(me,{visible:l(w),"onUpdate:visible":t[3]||(t[3]=o=>A(w)?w.value=o:null),rows:l(q),title:"其他收入确认审批"},null,8,["visible","rows"])],64)}}});const Ct=qe(dt,[["__scopeId","data-v-16d3901b"]]);export{Ct as default};
