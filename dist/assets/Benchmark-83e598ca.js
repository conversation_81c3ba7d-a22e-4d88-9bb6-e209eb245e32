import{_ as $}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as A,I as G,r as y,o as H,D as J,a as d,v as K,g as T,f as h,e as r,h as u,u as p,b as c,y as Q,n as b,i as v,m as Y,au as W,bc as E,bg as X,bn as Z,_ as ee}from"./index-db94d997.js";import{g as te,u as ae,e as se}from"./index-4cf21c7d.js";import{D as ne}from"./dayjs-a8e42122.js";import{_ as le}from"./index-39334618.js";import{_ as re}from"./index-4a280682.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const ie={class:"areaPrice"},oe={class:"areaPrice_table"},de={class:"text-hide"},ce={class:"text-hide"},ue={class:"text-hide"},pe=A({__name:"Benchmark",setup(_e){const P=G(),_=y(),f=y(!1),x=y(!1),z=["provinceCodeList","cityCodeList","areaCodeList"],g=y([]),M=e=>new Promise((t,l)=>{W({pid:e||"0"}).then(n=>{let o=D(n);console.log("cityData.value=",o),g.value=o,t(!0)}).catch(()=>{l()})}),D=e=>(e.forEach(t=>{t.label=t.name,t.value=t.code,t.isLeaf=t.level>=2,t.subDistrict&&t.subDistrict.length>0&&t.level<2&&(t.children=t.subDistrict,D(t.subDistrict))}),e),L=e=>{const t=new Map;return e.forEach(l=>{const n=z[l.level-1];if(t.has(n))t.get(n).push(l.value);else{let o=[];o.push(l.value),t.set(n,o)}}),Object.fromEntries(t)},B=[{title:"行政区划",dataIndex:"cityTree",valueType:"multipleTreeSelect",valueEnum:g,maxTagCount:3,hideInTable:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"标杆电价(元/kWh)",key:"electrovalences",dataIndex:"electrovalences",search:!1,width:120,formatDecimal:4,resizable:!0,render:!0},{title:"执行时间",key:"startTime",dataIndex:"startTime",search:!1,resizable:!0,width:130,render:!0},{title:"结束时间",key:"endTime",dataIndex:"endTime",search:!1,resizable:!0,width:130,render:!0},{title:"编辑时间",dataIndex:"updateTime",search:!1,resizable:!0,width:130},{title:"操作",key:"action",search:!1,resizable:!0,width:90,render:!0}],I=()=>{var e;(e=_.value)==null||e.reload()},N=async(e,t)=>{e.isEdit=!0,e.electrovalencesTem=e.electrovalences,e.startTimeTem=e.startTime,e.endTimeTem=e.endTime},R=e=>{e.electrovalences=e.electrovalencesTem,e.startTime=e.startTimeTem,e.endTime=e.endTimeTem,e.isEdit=!1,f.value=!1},U=async(e,t)=>{var n;f.value=!0;let l={id:e.id,electrovalences:e.electrovalences,startTime:e.startTime,endTime:e.endTime,delStatus:0};if(console.log("params=",l),!!e.electrovalences&&e.startTime&&e.endTime){if(new Date(e.endTime)<new Date(e.startTime)){E.warning("执行时间不应晚于结束时间");return}x.value=!0,(n=_.value)==null||n.setLoading(!0),ae(l).then(o=>{var i;x.value=!1,f.value=!0,(i=_.value)==null||i.setLoading(!1),e.isEdit=!1,E.success({content:"操作成功"}),I()}).catch(o=>{var i;x.value=!1,f.value=!0,(i=_.value)==null||i.setLoading(!1)})}},q=()=>{P.push({path:"/financeManage/price/gridPurchasePrice/benchmark/dataImport",query:{templateType:2,fileType:".csv,.xls,.xlsx",fileSize:30}})},C=(e,t,l)=>!e||!t?[]:(e.forEach(n=>{t.find(i=>n.value===i)&&l.push(n),n.children&&n.children.length>0&&C(n.children,t,l)}),l),F=(e,t)=>new Promise(l=>{let n={delStatus:0,noJoin:!0};const o=C(g.value,e==null?void 0:e.cityTree,[]);let i=L(o);n={...n,...e,...i},l(n)}),V=()=>{var t;let e=(t=_.value)==null?void 0:t.getInitialFormStateNew();se(e)},w=y([]),j=e=>{w.value=e||[]};return H(()=>{M()}),J(()=>{_.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&I()}),(e,t)=>{const l=X,n=le,o=re,i=Z,S=ne,O=$;return d(),K("div",ie,[T("div",oe,[h(O,{columns:B,ref_key:"actionRef",ref:_,request:p(te),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:j,"before-query-params":F},{tableHeader:r(()=>[h(n,null,{default:r(()=>[h(l,{onClick:q},{default:r(()=>[u("批量上传")]),_:1}),p(w).length>0?(d(),c(l,{key:0,type:"primary",onClick:V},{default:r(()=>[u("导出")]),_:1})):Q("",!0)]),_:1})]),electrovalencesRender:r(({column:s,record:a,index:k})=>[a.isEdit?(d(),c(o,{key:0,value:a[s.dataIndex],"onUpdate:value":m=>a[s.dataIndex]=m,placeholder:"请输入",allowClear:"",style:b({width:"80%",borderColor:p(f)&&!a[s.dataIndex]?"red":""}),controls:!1,stringMode:!0},null,8,["value","onUpdate:value","style"])):(d(),c(i,{key:1},{title:r(()=>[u(v(p(Y)(a[s.dataIndex],4)),1)]),default:r(()=>[T("span",de,v(a[s.dataIndex]?p(Y)(a[s.dataIndex],4):""),1)]),_:2},1024))]),startTimeRender:r(({column:s,record:a,index:k})=>[a.isEdit?(d(),c(S,{key:0,value:a[s.dataIndex],"onUpdate:value":m=>a[s.dataIndex]=m,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",allowClear:"",style:b({width:"80%",borderColor:p(f)&&!a[s.dataIndex]?"red":""})},null,8,["value","onUpdate:value","style"])):(d(),c(i,{key:1},{title:r(()=>[u(v(a[s.dataIndex]),1)]),default:r(()=>[T("span",ce,v(a[s.dataIndex]),1)]),_:2},1024))]),endTimeRender:r(({column:s,record:a,index:k})=>[a.isEdit?(d(),c(S,{key:0,value:a[s.dataIndex],"onUpdate:value":m=>a[s.dataIndex]=m,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",allowClear:"",style:b({width:"80%",borderColor:p(f)&&!a[s.dataIndex]?"red":""})},null,8,["value","onUpdate:value","style"])):(d(),c(i,{key:1},{title:r(()=>[u(v(a[s.dataIndex]),1)]),default:r(()=>[T("span",ue,v(a[s.dataIndex]),1)]),_:2},1024))]),actionRender:r(({record:s,index:a})=>[h(n,null,{default:r(()=>[s.isEdit?(d(),c(n,{key:1},{default:r(()=>[h(l,{size:"small",type:"link",onClick:()=>R(s)},{default:r(()=>[u(" 取消 ")]),_:2},1032,["onClick"]),h(l,{size:"small",type:"link",onClick:k=>U(s,a),disabled:p(x)},{default:r(()=>[u(" 保存 ")]),_:2},1032,["onClick","disabled"])]),_:2},1024)):(d(),c(l,{key:0,size:"small",type:"link",onClick:()=>N(s,a)},{default:r(()=>[u(" 编辑 ")]),_:2},1032,["onClick"]))]),_:2},1024)]),_:1},8,["request"])])])}}});const De=ee(pe,[["__scopeId","data-v-02e2c973"]]);export{De as default};
