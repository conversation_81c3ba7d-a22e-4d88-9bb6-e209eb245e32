import{d as x,a as t,v as n,g as l,i,a1 as E,y as A,_ as g,r as h,q as C,k as O,aA as F,b as $,e as v,F as b,x as y,u as s,f as u,n as k,Z as T,s as Y,c as M,p as z,j as N,aB as P}from"./index-db94d997.js";import{z as J,a as Z,E as q}from"./zh-cn-bc081ab6.js";import{E as H,a as U}from"./index-7c60ebfa.js";import{E as K,a as L}from"./index-f79018b8.js";import"./index-66bdd7b5.js";import"./index-ec316fb4.js";import"./icon-831229e8.js";import"./customParseFormat-ed0c33ac.js";import"./index-105a9be0.js";import"./index-326d414f.js";const G={class:"card-box"},Q={key:0,class:"header"},X=x({__name:"Card",props:{title:{type:String,default:""}},setup(m){return(o,c)=>(t(),n("div",G,[m.title?(t(),n("div",Q,[l("p",null,i(m.title),1),E(o.$slots,"right",{},void 0,!0)])):A("",!0),E(o.$slots,"default",{},void 0,!0)]))}});const V=g(X,[["__scopeId","data-v-8d934a48"]]),ll="/assets/icon-capacity-1-46f0b454.svg",el="/assets/icon-reliability-1-ae9a7b82.svg",al="/assets/icon-reliability-2-2ab67272.svg",sl="/assets/icon-reliability-3-86a372aa.svg",tl="/assets/icon-other-1-66aeae25.svg",ol="/assets/icon-other-2-5ebd4eb7.svg",nl="/assets/icon-other-3-9b733ce1.svg",cl={item1:[{label:"设计平均发电量",value:null,unit:"万kWh",decimals:2},{label:"计划发电量",value:null,unit:"万kWh",decimals:2},{label:"应发电量",value:null,unit:"万kWh",decimals:2},{label:"发电量",value:null,unit:"万kWh",decimals:2}],item2:[{label:"直流侧利用小时",value:null,unit:"h",decimals:2},{label:"计划发电量完成率(基准)",value:null,unit:"%",decimals:2},{label:"利用小时",value:null,unit:"h",decimals:2},{label:"计划发电量完成率(JYKJ)",value:null,unit:"%",decimals:2}],item3:[{label:"电网限电弃光电量",value:null,unit:"万kWh",decimals:2},{label:"故障电量",value:null,unit:"万kWh",decimals:2},{label:"组串低效运行损失电量",value:null,unit:"万kWh",decimals:2},{label:"逆变器平均转换效率",value:null,unit:"%",decimals:2}],item4:[{label:"平均停机时长",value:null,unit:"h",decimals:2,color:"#C261F2"},{label:"无故障光伏电站个数",value:null,unit:"座",decimals:0,color:"#3091F2"},{label:"无故障电站占比",value:null,unit:"%",decimals:2,color:"#29CCA0"},{label:"长停逆变器台次",value:null,unit:"台",decimals:0,color:"#F2BF24"},{label:"长停逆变器容量占比",value:null,unit:"%",decimals:2,color:"#7961F2"}]},ul={item1:[{label:"场站数量",value:null},{label:"逆变器台数",value:null}],item2:{label:"期末发电设备容量",value:null,unit:"万kW",decimals:2,icon:ll},item3:[{label:"新增装机容量",value:null,unit:"万kW",decimals:2},{label:"新增场站数量",value:null,unit:"座",decimals:0},{label:"新增逆变器台数",value:null,unit:"台",decimals:0}]},il=[{label:"光伏电站利用率",value:null,unit:"%",decimals:2,icon:el,color:"#29CCA0"},{label:"逆变器台均故障时长",value:null,unit:"h",decimals:2,icon:al,color:"#F2BF24"},{label:"逆变器台均故障次数",value:null,unit:"次",decimals:0,icon:sl,color:"#C261F2"}],dl=[{label:"单位千瓦运行维护费(全口径)",value:null,unit:"元",decimals:2},{label:"单位千瓦运行维护费(基准口径)",value:null,unit:"元",decimals:2},{label:"度电运行维护费(全口径)",value:null,unit:"元",decimals:2},{label:"度电运行维护费(基准口径)",value:null,unit:"元",decimals:2},{label:"度电成本",value:null,unit:"元kWh",decimals:2},{label:"度电价格",value:null,unit:"元kWh",decimals:2},{label:"度电利润",value:null,unit:"元kWh",decimals:2},{label:"单位千瓦利润",value:null,unit:"元kWh",decimals:2},{label:"营业收入利润率",value:null,unit:"%",decimals:2}],_l={item1:[{label:"逆变器功率峰值功率",value:null,unit:"kW",decimals:2},{label:"逆变器功率峰值功率比",value:null,unit:"%",decimals:2}],item2:[{label:"电能质量",value:null,unit:"HZ",decimals:2,icon:tl,color:"#29CCA0"},{label:"电站健康度",value:null,unit:"%",decimals:2,icon:ol,color:"#F2BF24"},{label:"标杆电站平均利用小时数",value:null,unit:"h",decimals:2,icon:nl,color:"#3091F2"}]},rl={class:"container"},pl={class:"item-1"},vl={class:"label"},ml={class:"item-2"},bl=["src"],yl={class:"block"},hl={class:"label"},fl={class:"box"},xl={class:"unit"},gl={class:"select-box"},$l={class:"item-3"},kl={class:"label"},Dl={class:"block"},Vl={class:"unit"},Cl=x({__name:"capacity",props:{formData:{type:Object,default:()=>{}}},setup(m){const o=h(C.cloneDeep(ul)),c={2:{type:"month",valueFormat:"YYYY-MM",baseValue:O().format("YYYY-MM"),disabledDate:function(a){return a.getTime()>Date.now()}},3:{type:"year",valueFormat:"YYYY",baseValue:O().format("YYYY"),disabledDate:function(a){return a.getTime()>Date.now()}}},d=h({type:B[0].value,date:c[B[0].value].baseValue}),_=()=>{d.value.date=c[d.value.type].baseValue};return(r,a)=>{const e=F("vis-number"),f=H,j=U,R=Z,w=q;return t(),$(V,{title:"产能指标"},{default:v(()=>[l("div",rl,[l("div",pl,[(t(!0),n(b,null,y(s(o).item1,(p,I)=>(t(),n("div",{class:"flex-box",key:I},[l("p",vl,i(p.label),1),u(e,{class:"value",value:p.value,decimals:p.decimals},null,8,["value","decimals"])]))),128))]),l("div",ml,[l("img",{src:s(o).item2.icon,alt:""},null,8,bl),l("div",yl,[l("p",hl,i(s(o).item2.label),1),l("div",fl,[u(e,{class:"value",style:k({color:s(o).item2.color}),value:s(o).item2.value,decimals:s(o).item2.decimals},null,8,["style","value","decimals"]),l("p",xl,i(s(o).item2.unit),1)])])]),u(w,{locale:s(J)},{default:v(()=>[l("div",gl,[u(j,{modelValue:s(d).type,"onUpdate:modelValue":a[0]||(a[0]=p=>s(d).type=p),"suffix-icon":"CaretBottom",onChange:_},{default:v(()=>[(t(!0),n(b,null,y(s(B),p=>(t(),$(f,{key:p.value,label:p.label,value:p.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),u(R,{modelValue:s(d).date,"onUpdate:modelValue":a[1]||(a[1]=p=>s(d).date=p),type:c[s(d).type].type,"value-format":c[s(d).type].valueFormat,"disabled-date":c[s(d).type].disabledDate,"prefix-icon":"CaretBottom",clearable:!1},null,8,["modelValue","type","value-format","disabled-date"])])]),_:1},8,["locale"]),l("div",$l,[(t(!0),n(b,null,y(s(o).item3,(p,I)=>(t(),n("div",{class:"flex-box",key:I},[l("p",kl,i(p.label),1),l("div",Dl,[u(e,{class:"value",value:p.value,decimals:p.decimals},null,8,["value","decimals"]),l("p",Vl,i(p.unit),1)])]))),128))])])]),_:1})}}});const Fl=g(Cl,[["__scopeId","data-v-8b2ae04f"]]),Yl={class:"vis-radio-wrapper"},Wl=["onClick"],Il=x({__name:"RadioToData",props:{options:{type:Array,default:()=>[{label:"日",value:1},{label:"月",value:2},{label:"年",value:3}]},modelValue:{type:String||Number,default:1}},emits:["update:modelValue","change"],setup(m,{emit:o}){const c=m,d=_=>{o("update:modelValue",_),o("change",_)};return(_,r)=>(t(),n("div",Yl,[(t(!0),n(b,null,y(c.options,a=>(t(),n("span",{class:T({"vis-radio-item":!0,active:a.value===c.modelValue}),key:a.value,onClick:e=>d(a.value)},i(a.label),11,Wl))),128))]))}});const W=g(Il,[["__scopeId","data-v-b753091a"]]),Bl={class:"container"},El={class:"label"},Ol={class:"block"},Sl={class:"unit"},Tl=x({__name:"manage",props:{formData:{type:Object,default:()=>{}}},setup(m){const o=h(C.cloneDeep(dl)),c=h(D[0].value);return(d,_)=>{const r=F("vis-number");return t(),$(V,{title:"经营指标"},{right:v(()=>[u(W,{modelValue:s(c),"onUpdate:modelValue":_[0]||(_[0]=a=>Y(c)?c.value=a:null),options:s(D)},null,8,["modelValue","options"])]),default:v(()=>[l("div",Bl,[(t(!0),n(b,null,y(s(o),(a,e)=>(t(),n("div",{class:"flex-box",key:e},[l("p",El,i(a.label),1),l("div",Ol,[u(r,{class:"value",value:a.value,decimals:a.decimals},null,8,["value","decimals"]),l("p",Sl,i(a.unit),1)])]))),128))])]),_:1})}}});const Ul=g(Tl,[["__scopeId","data-v-e6f08947"]]),jl=m=>(z("data-v-5ada4165"),m=m(),N(),m),Rl={class:"container"},wl={class:"item-1"},Al={class:"label"},Ml={class:"unit"},zl={class:"item-2"},Nl={class:"label"},Pl={class:"block"},Jl={class:"unit"},Zl=jl(()=>l("i",null,null,-1)),ql={class:"item-3"},Hl={class:"label"},Kl={class:"block"},Ll={class:"unit"},Gl={class:"item-4"},Ql={class:"label"},Xl={class:"line"},le={class:"block"},ee={class:"unit"},ae=x({__name:"output",props:{formData:{type:Object,default:()=>{}}},setup(m){const o=h(C.cloneDeep(cl)),c=h(D[0].value),d=M(()=>"5px");return(_,r)=>{const a=F("vis-number");return t(),$(V,{title:"产量指标"},{right:v(()=>[u(W,{modelValue:s(c),"onUpdate:modelValue":r[0]||(r[0]=e=>Y(c)?c.value=e:null),options:s(D)},null,8,["modelValue","options"])]),default:v(()=>[l("div",Rl,[l("div",wl,[(t(!0),n(b,null,y(s(o).item1,(e,f)=>(t(),n("div",{class:"flex-box",key:f},[l("p",Al,i(e.label),1),u(a,{class:"value",value:e.value,decimals:e.decimals},null,8,["value","decimals"]),l("p",Ml,i(e.unit),1)]))),128))]),l("div",zl,[(t(!0),n(b,null,y(s(o).item2,(e,f)=>(t(),n("div",{class:"flex-box",key:f},[l("p",Nl,i(e.label),1),l("div",Pl,[u(a,{class:"value",value:e.value,decimals:e.decimals},null,8,["value","decimals"]),l("p",Jl,i(e.unit),1)])]))),128)),Zl]),l("div",ql,[(t(!0),n(b,null,y(s(o).item3,(e,f)=>(t(),n("div",{class:"flex-box",key:f},[l("p",Hl,i(e.label),1),l("div",Kl,[u(a,{class:"value",value:e.value,decimals:e.decimals},null,8,["value","decimals"]),l("p",Ll,i(e.unit),1)])]))),128))]),l("div",Gl,[(t(!0),n(b,null,y(s(o).item4,(e,f)=>(t(),n("div",{class:"flex-box",key:f},[l("p",Ql,i(e.label),1),l("div",Xl,[l("i",{style:k({background:e.color,width:s(d)})},null,4),l("b",{style:k({background:e.color,opacity:.1})},null,4)]),l("div",le,[u(a,{class:"value",style:k({color:e.color}),value:e.value,decimals:e.decimals},null,8,["style","value","decimals"]),l("p",ee,i(e.unit),1)])]))),128))])])]),_:1})}}});const se=g(ae,[["__scopeId","data-v-5ada4165"]]),te={class:"container"},oe=["src"],ne={class:"block"},ce={class:"label"},ue={class:"box"},ie={class:"unit"},de=x({__name:"reliability",props:{formData:{type:Object,default:()=>{}}},setup(m){const o=h(C.cloneDeep(il)),c=h(D[0].value);return(d,_)=>{const r=F("vis-number");return t(),$(V,{title:"可靠性指标"},{right:v(()=>[u(W,{modelValue:s(c),"onUpdate:modelValue":_[0]||(_[0]=a=>Y(c)?c.value=a:null),options:s(D)},null,8,["modelValue","options"])]),default:v(()=>[l("div",te,[(t(!0),n(b,null,y(s(o),(a,e)=>(t(),n("div",{class:"flex-box",key:e},[l("img",{src:a.icon,alt:""},null,8,oe),l("div",ne,[l("p",ce,i(a.label),1),l("div",ue,[u(r,{class:"value",style:k({color:a.color}),value:a.value,decimals:a.decimals},null,8,["style","value","decimals"]),l("p",ie,i(a.unit),1)])])]))),128))])]),_:1})}}});const _e=g(de,[["__scopeId","data-v-353833d5"]]),re={class:"container"},pe={class:"item-1"},ve={class:"label"},me={class:"unit"},be={class:"item-2"},ye=["src"],he={class:"block"},fe={class:"label"},xe={class:"box"},ge={class:"unit"},$e=x({__name:"other",props:{formData:{type:Object,default:()=>{}}},setup(m){const o=h(C.cloneDeep(_l)),c=h(S[0].value);return(d,_)=>{const r=F("vis-number");return t(),$(V,{title:"其他指标"},{right:v(()=>[u(W,{modelValue:s(c),"onUpdate:modelValue":_[0]||(_[0]=a=>Y(c)?c.value=a:null),options:s(S)},null,8,["modelValue","options"])]),default:v(()=>[l("div",re,[l("div",pe,[(t(!0),n(b,null,y(s(o).item1,(a,e)=>(t(),n("div",{class:"flex-box",key:e},[l("p",ve,i(a.label),1),u(r,{class:"value",value:a.value,decimals:a.decimals},null,8,["value","decimals"]),l("p",me,i(a.unit),1)]))),128))]),l("div",be,[(t(!0),n(b,null,y(s(o).item2,(a,e)=>(t(),n("div",{class:"flex-box",key:e},[l("img",{src:a.icon,alt:""},null,8,ye),l("div",he,[l("p",fe,i(a.label),1),l("div",xe,[u(r,{class:"value",style:k({color:a.color}),value:a.value,decimals:a.decimals},null,8,["style","value","decimals"]),l("p",ge,i(a.unit),1)])])]))),128))])])]),_:1})}}});const ke=g($e,[["__scopeId","data-v-091f5463"]]),De=[{name:"output",component:se,class:"span2"},{name:"capacity",component:Fl},{name:"reliability",component:_e},{name:"manage",component:Ul},{name:"other",component:ke}],D=[{label:"月",value:2},{label:"年",value:3}],S=[{label:"日",value:1},{label:"月",value:2},{label:"年",value:3}],B=[{label:"月度",value:2},{label:"年度",value:3}],Ve={class:"grid-container"},Ce=x({__name:"index",setup(m){const o=h({provincial:"",company:""});return(c,d)=>{const _=U,r=K,a=L;return t(),n(b,null,[u(V,{class:"search-form"},{default:v(()=>[u(a,{inline:!0,model:s(o)},{default:v(()=>[u(r,null,{default:v(()=>[u(_,{modelValue:s(o).provincial,"onUpdate:modelValue":d[0]||(d[0]=e=>s(o).provincial=e),filterable:"",placeholder:"省级","no-data-text":"无数据",class:"select-box"},null,8,["modelValue"])]),_:1}),u(r,null,{default:v(()=>[u(_,{modelValue:s(o).company,"onUpdate:modelValue":d[1]||(d[1]=e=>s(o).company=e),filterable:"",placeholder:"全部资产公司","no-data-text":"无数据",class:"select-box"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),l("div",Ve,[(t(!0),n(b,null,y(s(De),e=>(t(),$(P(e==null?void 0:e.component),{key:e==null?void 0:e.name,formData:s(o),class:T(e==null?void 0:e.class)},null,8,["formData","class"]))),128))])],64)}}});const je=g(Ce,[["__scopeId","data-v-4cd1a48f"]]);export{je as default};
