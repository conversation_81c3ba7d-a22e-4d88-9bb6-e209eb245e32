import{_ as A}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as G,I as H,r as h,o as J,D as K,au as Q,a as d,v as W,ag as X,b6 as Z,u,g as x,f as v,e as r,h as f,b as c,y as ee,n as g,i as m,m as S,bc as Y,bg as ae,bn as te,_ as se}from"./index-db94d997.js";import{a as ne,b as le,c as re}from"./index-4cf21c7d.js";import{D as ie}from"./dayjs-a8e42122.js";import{_ as oe}from"./index-39334618.js";import{_ as de}from"./index-4a280682.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const ce={class:"areaPrice"},ue={class:"areaPrice_table"},fe={class:"text-hide"},pe={class:"text-hide"},_e={class:"text-hide"},he=G({__name:"FeedInTariff",setup(ve){const z=H(),M=h(!1),p=h(),_=h(!1),b=h(!1),P=["provinceCodeList","cityCodeList","areaCodeList"],I=h([]),L=[{title:"行政区划",dataIndex:"cityTree",valueType:"multipleTreeSelect",valueEnum:I,maxTagCount:3,hideInTable:!0},{title:"省",dataIndex:"provinceName",search:!1,resizable:!0,width:100},{title:"市",dataIndex:"cityName",search:!1,resizable:!0,width:100},{title:"区",dataIndex:"districtName",search:!1,resizable:!0,width:100},{title:"补贴电价(元/kWh)",key:"electrovalences",dataIndex:"electrovalences",search:!1,width:120,formatDecimal:4,resizable:!0,render:!0},{title:"执行时间",key:"startTime",dataIndex:"startTime",search:!1,resizable:!0,width:120,render:!0},{title:"结束时间",key:"endTime",dataIndex:"endTime",search:!1,resizable:!0,width:120,render:!0},{title:"编辑时间",dataIndex:"updateTime",search:!1,resizable:!0,width:120},{title:"操作",key:"action",search:!1,width:90,render:!0}];J(()=>{N()}),K(()=>{p.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&C()});const N=e=>new Promise((a,n)=>{Q({pid:e||"0"}).then(l=>{let o=D(l);console.log("cityData.value=",o),I.value=o,a(!0)}).catch(()=>{n()})}),D=e=>(e.forEach(a=>{a.label=a.name,a.value=a.code,a.isLeaf=a.level>=3,a.subDistrict&&a.subDistrict&&(a.children=a.subDistrict,D(a.subDistrict))}),e),F=async(e,a)=>{e.isEdit=!0,e.electrovalencesTem=e.electrovalences,e.startTimeTem=e.startTime,e.endTimeTem=e.endTime},R=e=>{e.electrovalences=e.electrovalencesTem,e.startTime=e.startTimeTem,e.endTime=e.endTimeTem,e.isEdit=!1,_.value=!1},U=()=>{z.push({path:"/financeManage/price/gridPurchasePrice/feedInTariff/dataImport",query:{templateType:1,fileType:".csv,.xls,.xlsx",fileSize:30}})},B=()=>{var a;let e=(a=p.value)==null?void 0:a.getInitialFormStateNew();le(e)},w=h([]),q=e=>{w.value=e||[]},C=()=>{var e;(e=p.value)==null||e.reload()},V=e=>{const a=new Map;return e.forEach(n=>{const l=P[n.level-1];if(a.has(l))a.get(l).push(n.value);else{let o=[];o.push(n.value),a.set(l,o)}}),Object.fromEntries(a)},E=(e,a,n)=>!e||!a?[]:(e.forEach(l=>{a.find(i=>l.value===i)&&n.push(l),l.children&&l.children.length>0&&E(l.children,a,n)}),n),j=(e,a)=>new Promise(n=>{let l={delStatus:0,noJoin:!0};const o=E(I.value,e==null?void 0:e.cityTree,[]);let i=V(o);const T={...l,...e,...i};n(T)}),O=async(e,a)=>{var l;_.value=!0;let n={id:e.id,electrovalences:e.electrovalences,startTime:e.startTime,endTime:e.endTime};if(console.log("params=",n),!!e.electrovalences&&e.startTime&&e.endTime){if(new Date(e.endTime)<new Date(e.startTime)){Y.warning("执行时间不应晚于结束时间");return}b.value=!0,(l=p.value)==null||l.setLoading(!0),re(n).then(o=>{var i;b.value=!1,_.value=!1,(i=p.value)==null||i.setLoading(!1),e.isEdit=!1,Y.success({content:"操作成功"}),C()}).catch(o=>{var i;b.value=!1,_.value=!1,(i=p.value)==null||i.setLoading(!1)})}};return(e,a)=>{const n=ae,l=oe,o=de,i=te,T=ie,$=A;return d(),W("div",ce,[X(x("div",ue,[v($,{columns:L,ref_key:"actionRef",ref:p,request:u(ne),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},onGetDataSource:q,"before-query-params":j},{tableHeader:r(()=>[v(l,null,{default:r(()=>[v(n,{onClick:U},{default:r(()=>[f("批量上传")]),_:1}),u(w).length>0?(d(),c(n,{key:0,type:"primary",onClick:B},{default:r(()=>[f("导出")]),_:1})):ee("",!0)]),_:1})]),electrovalencesRender:r(({column:s,record:t,index:k})=>[t.isEdit?(d(),c(o,{key:0,value:t[s.dataIndex],"onUpdate:value":y=>t[s.dataIndex]=y,placeholder:"请输入",allowClear:"",style:g({width:"80%",borderColor:u(_)&&!t[s.dataIndex]?"red":""}),controls:!1,stringMode:!0},null,8,["value","onUpdate:value","style"])):(d(),c(i,{key:1},{title:r(()=>[f(m(u(S)(t[s.dataIndex],4)),1)]),default:r(()=>[x("span",fe,m(t[s.dataIndex]?u(S)(t[s.dataIndex],4):""),1)]),_:2},1024))]),startTimeRender:r(({column:s,record:t,index:k})=>[t.isEdit?(d(),c(T,{key:0,value:t[s.dataIndex],"onUpdate:value":y=>t[s.dataIndex]=y,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",allowClear:"",style:g({width:"80%",borderColor:u(_)&&!t[s.dataIndex]?"red":""})},null,8,["value","onUpdate:value","style"])):(d(),c(i,{key:1},{title:r(()=>[f(m(t[s.dataIndex]),1)]),default:r(()=>[x("span",pe,m(t[s.dataIndex]),1)]),_:2},1024))]),endTimeRender:r(({column:s,record:t,index:k})=>[t.isEdit?(d(),c(T,{key:0,value:t[s.dataIndex],"onUpdate:value":y=>t[s.dataIndex]=y,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",allowClear:"",style:g({width:"80%",borderColor:u(_)&&!t[s.dataIndex]?"red":""})},null,8,["value","onUpdate:value","style"])):(d(),c(i,{key:1},{title:r(()=>[f(m(t[s.dataIndex]),1)]),default:r(()=>[x("span",_e,m(t[s.dataIndex]),1)]),_:2},1024))]),actionRender:r(({record:s,index:t})=>[v(l,null,{default:r(()=>[s.isEdit?(d(),c(l,{key:1},{default:r(()=>[v(n,{size:"small",type:"link",onClick:()=>R(s)},{default:r(()=>[f(" 取消 ")]),_:2},1032,["onClick"]),v(n,{size:"small",type:"link",onClick:k=>O(s,t)},{default:r(()=>[f(" 保存 ")]),_:2},1032,["onClick"])]),_:2},1024)):(d(),c(n,{key:0,size:"small",type:"link",onClick:()=>F(s,t)},{default:r(()=>[f(" 编辑 ")]),_:2},1032,["onClick"]))]),_:2},1024)]),_:1},8,["request"])],512),[[Z,!u(M)]])])}}});const Ce=se(he,[["__scopeId","data-v-f34ed83f"]]);export{Ce as default};
