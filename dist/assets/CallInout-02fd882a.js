import{_ as Q}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as $,r as c,K as W,I as X,o as Z,D as ee,at as te,a as C,v as M,g as I,f as t,e as o,h as D,b as ae,y as w,u as r,F as oe,bc as f,k as T,bg as ne,be as le,bf as re,aw as ie,ax as se,bY as de,bh as ue,bi as pe,p as ce,j as me,_ as _e}from"./index-db94d997.js";import{c as fe,d as ye,f as he}from"./index-bad8f65c.js";import{D as be}from"./dayjs-a8e42122.js";import{_ as ge}from"./index-39334618.js";import{_ as xe}from"./index-4a280682.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const U=y=>(ce("data-v-722e355b"),y=y(),me(),y),Ce={class:"areaPrice"},Me={key:0,class:"status_tag"},we=U(()=>I("span",{class:"tag_one"},"成功",-1)),Ie=[we],Se={key:1,class:"status_tag"},Ke=U(()=>I("span",{class:"tag_two"},"失败",-1)),ke=[Ke],Ne=$({__name:"CallInout",setup(y){const h=c(),F=["provinceCodeList","cityCodeList","areaCodeList"],Y=c([]),L=c([]);c(!1);const b=c(!1),g=c(!1),x=c(),n=W({outStationCode:"",outMoney:"",outMonthKey:"",outCompanyName:"",intoStationCode:"",intoMoney:"",intoMonthKey:"",intoCompanyName:"",remark:""});X();const R=[{title:"电站编码",dataIndex:"stationCode",search:!0,resizable:!0,hideInTable:!0,width:120},{title:"业主名称",dataIndex:"ownerName",search:!0,resizable:!0,hideInTable:!0,width:120},{title:"调动单据号",dataIndex:"relationCode",search:!1,resizable:!0,width:100},{title:"调出电站编号",dataIndex:"outStationCode",search:!1,resizable:!0,width:120},{title:"调出电站名称",dataIndex:"outStationName",search:!1,resizable:!0,width:120},{title:"调出账期",dataIndex:"outMonthKey",search:!1,resizable:!0,width:120},{title:"调出金额",dataIndex:"outMoney",search:!1,resizable:!0,width:120},{title:"调出公司",dataIndex:"outCompanyName",search:!1,resizable:!0,width:120},{title:"调入电站编号",dataIndex:"intoStationCode",search:!1,resizable:!0,width:120},{title:"调入电站名称",dataIndex:"intoStationName",search:!1,resizable:!0,width:120},{title:"调入账期",dataIndex:"intoMonthKey",search:!1,resizable:!0,width:120},{title:"调入金额",dataIndex:"intoMoney",search:!1,resizable:!0,width:120},{title:"调入公司",dataIndex:"intoCompanyName",search:!1,resizable:!0,width:120},{title:"摘要",dataIndex:"remark",search:!1,resizable:!0,width:150},{title:"操作时间",dataIndex:"updateTime",search:!1,resizable:!0,width:120},{title:"状态",key:"operateStatus",dataIndex:"operateStatus",valueType:"select",valueEnum:[{lable:"成功",value:0},{label:"失败",value:1}],search:!1,resizable:!0,render:!0,align:"center",width:120},{title:"操作人",dataIndex:"updateBy",search:!1,resizable:!0,width:100}];Z(()=>{E()}),ee(()=>{h.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&K()});const B=()=>{if(!n.outStationCode&&!n.intoStationCode){f.error("请填写调入信息或调出信息");return}if(n.outStationCode){if(!n.outMoney){f.error("请填写调出金额");return}if(!n.outMonthKey){f.error("请选择调出账期");return}}if(n.intoStationCode){if(!n.intoMoney){f.error("请填写调入金额");return}if(!n.intoMonthKey){f.error("请选择调入账期");return}}x.value.validateFields().then(e=>{console.log("values=",e),g.value=!0;let a={...e,outMonthKey:e!=null&&e.outMonthKey?T(e.outMonthKey).format("YYYY-MM"):"",intoMonthKey:e!=null&&e.intoMonthKey?T(e.intoMonthKey).format("YYYY-MM"):""};ye(a).then(i=>{b.value=!1,g.value=!1,f.info("保存成功"),K()}).catch(i=>{g.value=!1})})},O=()=>{x.value.resetFields()},E=()=>{te({}).then(e=>{console.log("产权公司res=",e);let i=(e||[]).map(s=>({label:s.companyName,value:s.companyCode}));L.value=i})},P=()=>{var a;let e=(a=h.value)==null?void 0:a.getInitialFormStateNew();he(e)},j=()=>{b.value=!0,x.value.resetFields()},S=c([]),q=e=>{S.value=e||[],console.log("dataSource=",e)},K=()=>{var e;(e=h.value)==null||e.reload()},V=e=>{const a=new Map;return e.forEach(i=>{const s=F[i.level-1];if(a.has(s))a.get(s).push(i.value);else{let m=[];m.push(i.value),a.set(s,m)}}),Object.fromEntries(a)},k=(e,a,i)=>!e||!a?[]:(e.forEach(s=>{a.find(p=>s.value===p)&&i.push(s),s.children&&s.children.length>0&&k(s.children,a,i)}),i),A=(e,a)=>new Promise(i=>{const s=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[0]:"",m=e!=null&&e.yKey&&(e==null?void 0:e.yKey.length)>0?e==null?void 0:e.yKey[1]:"";let p={delStatus:0,noJoin:!0,startTime:s,endTime:m};const d=k(Y.value,e==null?void 0:e.cityTree,[]);let u=V(d);e==null||delete e.yKey;const _={...p,...e,...u};i(_)});return(e,a)=>{const i=ne,s=ge,m=Q,p=le,d=re,u=ie,_=se,N=xe,v=be,G=de,H=ue,J=pe;return C(),M(oe,null,[I("div",Ce,[t(m,{columns:R,ref_key:"actionRef",ref:h,request:r(fe),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:q,"before-query-params":A},{tableHeader:o(()=>[t(s,null,{default:o(()=>[t(i,{onClick:j},{default:o(()=>[D("新建单据")]),_:1}),S.value.length>0?(C(),ae(i,{key:0,type:"primary",onClick:P},{default:o(()=>[D("导出")]),_:1})):w("",!0)]),_:1})]),operateStatusRender:o(({record:l,column:z,index:ze})=>[l[z.dataIndex]===0?(C(),M("span",Me,Ie)):w("",!0),l[z.dataIndex]===1?(C(),M("span",Se,ke)):w("",!0)]),_:1},8,["request"])]),t(J,{visible:b.value,"onUpdate:visible":a[9]||(a[9]=l=>b.value=l),labelAlign:"right",title:"新建单据","confirm-loading":g.value,onOk:B,onCancel:O,okText:"提交",width:"824px"},{default:o(()=>[t(H,{model:r(n),name:"formRef",ref_key:"formRef",ref:x,autocomplete:"off","label-col":{style:{width:"120px",paddingRight:"8px"}}},{default:o(()=>[t(_,{span:24},{default:o(()=>[t(u,{span:12},{default:o(()=>[t(d,{label:"调出电站编号：",style:{"padding-right":"12px"},name:"outStationCode"},{default:o(()=>[t(p,{placeholder:"请输入",value:r(n).outStationCode,"onUpdate:value":a[0]||(a[0]=l=>r(n).outStationCode=l)},null,8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:o(()=>[t(d,{label:"调入电站编号：",style:{"padding-left":"12px"},name:"intoStationCode"},{default:o(()=>[t(p,{placeholder:"请输入",value:r(n).intoStationCode,"onUpdate:value":a[1]||(a[1]=l=>r(n).intoStationCode=l)},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(_,{span:24},{default:o(()=>[t(u,{span:12},{default:o(()=>[t(d,{label:"调出金额：",style:{"padding-right":"12px"},name:"outMoney"},{default:o(()=>[t(N,{placeholder:"请输入",value:r(n).outMoney,"onUpdate:value":a[2]||(a[2]=l=>r(n).outMoney=l),controls:!1,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:o(()=>[t(d,{label:"调入金额：",style:{"padding-left":"12px"},name:"intoMoney"},{default:o(()=>[t(N,{placeholder:"请输入",value:r(n).intoMoney,"onUpdate:value":a[3]||(a[3]=l=>r(n).intoMoney=l),controls:!1,style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(_,{span:24},{default:o(()=>[t(u,{span:12},{default:o(()=>[t(d,{label:"调出账期：",style:{"padding-right":"12px"},name:"outMonthKey"},{default:o(()=>[t(v,{placeholder:"请选择",value:r(n).outMonthKey,"onUpdate:value":a[4]||(a[4]=l=>r(n).outMonthKey=l),picker:"month",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:o(()=>[t(d,{label:"调入账期：",style:{"padding-left":"12px"},name:"intoMonthKey"},{default:o(()=>[t(v,{placeholder:"请选择",value:r(n).intoMonthKey,"onUpdate:value":a[5]||(a[5]=l=>r(n).intoMonthKey=l),picker:"month",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(_,{span:24},{default:o(()=>[t(u,{span:12},{default:o(()=>[t(d,{label:"调出公司名称",style:{"padding-right":"12px"},name:"outCompanyName"},{default:o(()=>[t(p,{placeholder:"请输入",value:r(n).outCompanyName,"onUpdate:value":a[6]||(a[6]=l=>r(n).outCompanyName=l)},null,8,["value"])]),_:1})]),_:1}),t(u,{span:12},{default:o(()=>[t(d,{label:"调入公司名称：",style:{"padding-left":"12px"},name:"intoCompanyName"},{default:o(()=>[t(p,{placeholder:"请输入",value:r(n).intoCompanyName,"onUpdate:value":a[7]||(a[7]=l=>r(n).intoCompanyName=l)},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(_,{span:24},{default:o(()=>[t(u,{span:24},{default:o(()=>[t(d,{label:"摘要：",required:"",name:"remark"},{default:o(()=>[t(G,{value:r(n).remark,"onUpdate:value":a[8]||(a[8]=l=>r(n).remark=l),"show-count":"",maxlength:25},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["visible","confirm-loading"])],64)}}});const Pe=_e(Ne,[["__scopeId","data-v-722e355b"]]);export{Pe as default};
