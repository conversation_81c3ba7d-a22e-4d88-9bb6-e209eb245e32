import{f as c,L as f,e2 as O,e3 as p}from"./index-db94d997.js";function l(r){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?Object(arguments[t]):{},n=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(e).filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),n.forEach(function(a){s(r,a,e[a])})}return r}function s(r,t,e){return t in r?Object.defineProperty(r,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):r[t]=e,r}var u=function(t,e){var n=l({},t,e.attrs);return c(f,l({},n,{icon:O}),null)};u.displayName="CaretDownOutlined";u.inheritAttrs=!1;const m=u;function o(r){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?Object(arguments[t]):{},n=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(e).filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable}))),n.forEach(function(a){b(r,a,e[a])})}return r}function b(r,t,e){return t in r?Object.defineProperty(r,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):r[t]=e,r}var i=function(t,e){var n=o({},t,e.attrs);return c(f,o({},n,{icon:p}),null)};i.displayName="CaretUpOutlined";i.inheritAttrs=!1;const g=i;export{m as C,g as a};
