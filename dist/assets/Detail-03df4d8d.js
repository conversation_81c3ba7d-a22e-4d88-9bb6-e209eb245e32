import{_ as y}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as b,I as g,r as t,o as w,D as C,a as D,v as k,g as I,f as c,e as d,h as N,u as P,bg as S,_ as q}from"./index-db94d997.js";import{d as z}from"./index-5fcafee1.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const B={class:"areaPrice"},R={class:"areaPrice_table"},T=b({__name:"Detail",setup(M){const u=g(),a=t();t([]),t([]),t(!1),t(!1);const n=t(history.state.pdata),_=[{title:"序号",valueType:"index",resizable:!0,width:80},{title:"电站编码",dataIndex:"stationCode",search:!1,resizable:!0,width:100},{title:"电站名称",dataIndex:"stationName",search:!1,resizable:!0,width:100},{title:"所属公司",dataIndex:"companyName",search:!1,resizable:!0,width:100},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"left",width:100}];w(()=>{}),C(()=>{a.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&h()});const p=e=>{u.push({path:"/pAssetManage/assetManage/device/list",query:{goBack:1,stationUniqueId:e.stationCode}})},m=t([]),f=e=>{m.value=e||[]},h=()=>{var e;(e=a.value)==null||e.reload()},v=(e,x)=>new Promise(s=>{var r,l;const o={...{relationCode:(r=n.value)==null?void 0:r.relationCode,examineDateFlag:(l=n.value)!=null&&l.examineDate?1:0},...e};s(o)});return(e,x)=>{const s=S,i=y;return D(),k("div",B,[I("div",R,[c(i,{columns:_,ref_key:"actionRef",ref:a,request:P(z),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},onGetDataSource:f,search:!1,"before-query-params":v},{actionRender:d(({record:o})=>[c(s,{type:"link",size:"small",onClick:r=>p(o)},{default:d(()=>[N("立即检查")]),_:2},1032,["onClick"])]),_:1},8,["request"])])])}}});const Q=q(T,[["__scopeId","data-v-38de6460"]]);export{Q as default};
