import{_ as j}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as G,I as H,r as _,o as J,D as O,at as Q,a as s,v as $,f as v,e as t,u as o,b as d,h as r,i,S as E,g,k as y,q as Y,bc as W,bg as X,av as Z,bn as ee,_ as ae}from"./index-db94d997.js";import{c as te,s as ne}from"./index-ccc1a7be.js";import{D as le}from"./dayjs-a8e42122.js";import{_ as oe}from"./index-39334618.js";import{_ as se}from"./index-4a280682.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const de={class:"areaPrice"},ie={class:"text-hide"},ue={class:"text-hide"},re={class:"text-hide"},ce={class:"text-hide"},pe={class:"text-hide"},_e=G({__name:"Config",setup(me){const z=H(),I=_(),C=_([]),c=_(!1),m=_([]),k=_([]),h=_(!1),T=_([{label:"是",value:1},{label:"否",value:0}]),N=[{title:"产权公司",valueType:"select",valueEnum:C,dataIndex:"companyCode",width:150,fixed:"left",resizable:!0},{title:"月份",key:"month",valueType:"date",dataIndex:"monthKey",width:120,fixed:"left",resizable:!0,render:!0},{title:"开始时间",key:"day",dataIndex:"startTime",width:120,resizable:!0,render:!0},{title:"截止时间",key:"day",dataIndex:"endTime",width:120,resizable:!0,render:!0},{title:"周期天数",dataIndex:"cycleDayNum",width:120,resizable:!0},{title:"是否确认运维收入",key:"select",dataIndex:"operationIncomeFlag",valueType:"select",valueEnum:T,width:120,resizable:!0,render:!0},{title:"是否扣除分享收益",key:"select",dataIndex:"shareDeductIncomeFlag",valueType:"select",valueEnum:T,width:120,resizable:!0,render:!0},{title:"配置时间",key:"createTime",dataIndex:"createTime",width:120,resizable:!0,render:!0}];J(()=>{R()}),O(()=>{I.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&L()});const P=(e,f)=>{let l;e!=null&&e.startTime&&(e!=null&&e.endTime)&&(l=y(y(e.endTime).format("YYYY-MM-DD")).diff(y(y(e.startTime).format("YYYY-MM-DD")),"day")+1),e.cycleDayNum=l},R=()=>{Q({}).then(e=>{console.log("产权公司res=",e);let l=(e||[]).map(p=>({label:p.companyName,value:p.companyCode}));C.value=l})},U=()=>{console.log("编辑"),c.value=!0,k.value=Y.cloneDeep(m.value)},B=()=>{var e;console.log("取消"),c.value=!1,(e=I.value)==null||e.setDataSource(k.value)},F=()=>{console.log("保存"),c.value=!1,k.value=Y.cloneDeep(m.value)},M=()=>{console.log("立即生成数据");let f=(m.value&&m.value.length>0?Y.cloneDeep(m.value):[]).filter(l=>l.monthKey&&l.startTime&&l.endTime&&(l.operationIncomeFlag||l.operationIncomeFlag===0));console.log("params=",f),h.value=!0,ne(f).then(l=>{h.value=!1,W.info("操作成功"),F(),z.go(-1)}).catch(l=>{console.log("err=",l),h.value=!1})},q=e=>{m.value=e||[]},L=()=>{var e;(e=I.value)==null||e.reload()},V=(e,f)=>{let l={};return new Promise(p=>{const w={...l,...e,noJoin:!0,delStatus:0};p(w)})};return(e,f)=>{const l=X,p=oe,w=Z,x=ee,S=le,A=se,K=j;return s(),$("div",de,[v(K,{columns:N,ref_key:"actionRef",ref:I,request:o(te),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:q,"before-query-params":V,search:!1,pagination:!1},{tableHeader:t(()=>[o(c)?(s(),d(p,{key:1},{default:t(()=>[v(l,{onClick:B},{default:t(()=>[r("取消")]),_:1}),v(l,{type:"primary",onClick:M,loading:o(h)},{default:t(()=>[r("立即生成数据")]),_:1},8,["loading"])]),_:1})):(s(),d(p,{key:0},{default:t(()=>[v(l,{onClick:U},{default:t(()=>[r("编辑")]),_:1}),v(l,{type:"primary",onClick:M,loading:o(h)},{default:t(()=>[r("立即生成数据")]),_:1},8,["loading"])]),_:1}))]),selectRender:t(({column:a,record:n,index:D})=>[o(c)?(s(),d(w,{key:0,value:n[a.dataIndex],"onUpdate:value":u=>n[a.dataIndex]=u,options:a.valueEnum,placeholder:"请选择",allowClear:"",showArrow:"","show-search":"","filter-option":(u,b)=>((b==null?void 0:b.label)??"").toLowerCase().includes(u.toLowerCase()),style:{width:"90%"}},null,8,["value","onUpdate:value","options","filter-option"])):(s(),d(x,{key:1},{title:t(()=>[r(i(o(E)(n[a.dataIndex],a.valueEnum)),1)]),default:t(()=>[g("span",ie,i(o(E)(n[a.dataIndex],a.valueEnum)),1)]),_:2},1024))]),createTimeRender:t(({column:a,record:n,index:D})=>[v(x,null,{title:t(()=>[r(i(o(y)().format("YYYY-MM-DD")),1)]),default:t(()=>[g("span",ue,i(o(y)().format("YYYY-MM-DD")),1)]),_:1})]),monthRender:t(({column:a,record:n,index:D})=>[o(c)?(s(),d(S,{key:0,value:n[a.dataIndex],"onUpdate:value":u=>n[a.dataIndex]=u,"value-format":"YYYY-MM",picker:"month",placeholder:"请选择",allowClear:"",style:{width:"90%"}},null,8,["value","onUpdate:value"])):(s(),d(x,{key:1},{title:t(()=>[r(i(n[a.dataIndex]),1)]),default:t(()=>[g("span",re,i(n[a.dataIndex]),1)]),_:2},1024))]),dayRender:t(({column:a,record:n,index:D})=>[o(c)?(s(),d(S,{key:0,value:n[a.dataIndex],"onUpdate:value":u=>n[a.dataIndex]=u,"value-format":"YYYY-MM-DD",placeholder:"请选择",allowClear:"",style:{width:"90%"},onChange:()=>P(n,a)},null,8,["value","onUpdate:value","onChange"])):(s(),d(x,{key:1},{title:t(()=>[r(i(n[a.dataIndex]),1)]),default:t(()=>[g("span",ce,i(n[a.dataIndex]),1)]),_:2},1024))]),numberRender:t(({column:a,record:n,index:D})=>[o(c)?(s(),d(A,{key:0,value:n[a.dataIndex],"onUpdate:value":u=>n[a.dataIndex]=u,controls:!1,placeholder:"请输入",min:0,precision:0,style:{width:"90%"}},null,8,["value","onUpdate:value"])):(s(),d(x,{key:1},{title:t(()=>[r(i(n[a.dataIndex]),1)]),default:t(()=>[g("span",pe,i(n[a.dataIndex]),1)]),_:2},1024))]),_:1},8,["request"])])}}});const be=ae(_e,[["__scopeId","data-v-7ffd1c0c"]]);export{be as default};
