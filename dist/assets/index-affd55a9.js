import{aj as ne,d as x,a1 as H,r as k,ao as ie,aa as P,o as Re,u as p,l as Me,a3 as me,aD as X,aS as T,c as K,eL as xe,b4 as ue,w as ce,cx as eo,eM as q,aA as $,a as w,b as L,e as t,f as d,eN as oo,eO as no,al as Pe,V as to,eP as lo,co as $e,aW as ao,Y as ro,eQ as so,aP as Ke,aY as io,bb as Be,af as we,aK as uo,cY as co,ac as po,v as Z,bX as fo,aI as pe,Z as fe,y as Q,g as J,aB as mo,ai as he,F as De,eR as Oe,ef as vo,n as _o,aq as go,ar as Ae,a2 as de,eS as bo,K as ho,h as U,i as be,s as wo,x as Io,bc as se,bi as yo,bg as Co,be as Eo,bf as ko,bI as To,bJ as So,bY as No,bh as $o,p as Oo,j as <PERSON>,_ as Fo}from"./index-db94d997.js";import{E as Ue}from"./index-66bdd7b5.js";import{i as Ro}from"./icon-831229e8.js";import{c as Mo}from"./index-ec316fb4.js";import{c as Ge}from"./refs-02993704.js";import{E as Po}from"./index-4481a9dc.js";import{E as Ko,a as Bo}from"./index-7c60ebfa.js";import{E as Do}from"./ExclamationCircleOutlined-de0528d4.js";import{_ as Ao}from"./index-39334618.js";import"./index-326d414f.js";const Uo=x({inheritAttrs:!1});function Go(e,r,u,i,o,v){return H(e.$slots,"default")}var zo=ne(Uo,[["render",Go],["__file","collection.vue"]]);const Yo=x({name:"ElCollectionItem",inheritAttrs:!1});function Vo(e,r,u,i,o,v){return H(e.$slots,"default")}var jo=ne(Yo,[["render",Vo],["__file","collection-item.vue"]]);const ze="data-el-collection-item",Ye=e=>{const r=`El${e}Collection`,u=`${r}Item`,i=Symbol(r),o=Symbol(u),v={...zo,name:r,setup(){const f=k(null),b=new Map;ie(i,{itemMap:b,getItems:()=>{const y=p(f);if(!y)return[];const h=Array.from(y.querySelectorAll(`[${ze}]`));return[...b.values()].sort((s,g)=>h.indexOf(s.ref)-h.indexOf(g.ref))},collectionRef:f})}},_={...jo,name:u,setup(f,{attrs:b}){const C=k(null),y=P(i,void 0);ie(o,{collectionItemRef:C}),Re(()=>{const h=p(C);h&&y.itemMap.set(h,{ref:h,...b})}),Me(()=>{const h=p(C);y.itemMap.delete(h)})}};return{COLLECTION_INJECTION_KEY:i,COLLECTION_ITEM_INJECTION_KEY:o,ElCollection:v,ElCollectionItem:_}},Jo=me({style:{type:X([String,Array,Object])},currentTabId:{type:X(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:X(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:Ho,ElCollectionItem:qo,COLLECTION_INJECTION_KEY:Ie,COLLECTION_ITEM_INJECTION_KEY:Wo}=Ye("RovingFocusGroup"),ye=Symbol("elRovingFocusGroup"),Ve=Symbol("elRovingFocusGroupItem"),Qo={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},Xo=(e,r)=>{if(r!=="rtl")return e;switch(e){case T.right:return T.left;case T.left:return T.right;default:return e}},Zo=(e,r,u)=>{const i=Xo(e.key,u);if(!(r==="vertical"&&[T.left,T.right].includes(i))&&!(r==="horizontal"&&[T.up,T.down].includes(i)))return Qo[i]},xo=(e,r)=>e.map((u,i)=>e[(i+r)%e.length]),Ce=e=>{const{activeElement:r}=document;for(const u of e)if(u===r||(u.focus(),r!==document.activeElement))return},Le="currentTabIdChange",Fe="rovingFocusGroup.entryFocus",en={bubbles:!1,cancelable:!0},on=x({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:Jo,emits:[Le,"entryFocus"],setup(e,{emit:r}){var u;const i=k((u=e.currentTabId||e.defaultCurrentTabId)!=null?u:null),o=k(!1),v=k(!1),_=k(null),{getItems:f}=P(Ie,void 0),b=K(()=>[{outline:"none"},e.style]),C=m=>{r(Le,m)},y=()=>{o.value=!0},h=q(m=>{var I;(I=e.onMousedown)==null||I.call(e,m)},()=>{v.value=!0}),S=q(m=>{var I;(I=e.onFocus)==null||I.call(e,m)},m=>{const I=!p(v),{target:B,currentTarget:A}=m;if(B===A&&I&&!p(o)){const z=new Event(Fe,en);if(A==null||A.dispatchEvent(z),!z.defaultPrevented){const N=f().filter(D=>D.focusable),F=N.find(D=>D.active),R=N.find(D=>D.id===p(i)),ee=[F,R,...N].filter(Boolean).map(D=>D.ref);Ce(ee)}}v.value=!1}),s=q(m=>{var I;(I=e.onBlur)==null||I.call(e,m)},()=>{o.value=!1}),g=(...m)=>{r("entryFocus",...m)};ie(ye,{currentTabbedId:xe(i),loop:ue(e,"loop"),tabIndex:K(()=>p(o)?-1:0),rovingFocusGroupRef:_,rovingFocusGroupRootStyle:b,orientation:ue(e,"orientation"),dir:ue(e,"dir"),onItemFocus:C,onItemShiftTab:y,onBlur:s,onFocus:S,onMousedown:h}),ce(()=>e.currentTabId,m=>{i.value=m??null}),eo(_,Fe,g)}});function nn(e,r,u,i,o,v){return H(e.$slots,"default")}var tn=ne(on,[["render",nn],["__file","roving-focus-group-impl.vue"]]);const ln=x({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:Ho,ElRovingFocusGroupImpl:tn}});function an(e,r,u,i,o,v){const _=$("el-roving-focus-group-impl"),f=$("el-focus-group-collection");return w(),L(f,null,{default:t(()=>[d(_,oo(no(e.$attrs)),{default:t(()=>[H(e.$slots,"default")]),_:3},16)]),_:3})}var rn=ne(ln,[["render",an],["__file","roving-focus-group.vue"]]);const sn=x({components:{ElRovingFocusCollectionItem:qo},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:r}){const{currentTabbedId:u,loop:i,onItemFocus:o,onItemShiftTab:v}=P(ye,void 0),{getItems:_}=P(Ie,void 0),f=Pe(),b=k(null),C=q(s=>{r("mousedown",s)},s=>{e.focusable?o(p(f)):s.preventDefault()}),y=q(s=>{r("focus",s)},()=>{o(p(f))}),h=q(s=>{r("keydown",s)},s=>{const{key:g,shiftKey:m,target:I,currentTarget:B}=s;if(g===T.tab&&m){v();return}if(I!==B)return;const A=Zo(s);if(A){s.preventDefault();let N=_().filter(F=>F.focusable).map(F=>F.ref);switch(A){case"last":{N.reverse();break}case"prev":case"next":{A==="prev"&&N.reverse();const F=N.indexOf(B);N=i.value?xo(N,F+1):N.slice(F+1);break}}to(()=>{Ce(N)})}}),S=K(()=>u.value===p(f));return ie(Ve,{rovingFocusGroupItemRef:b,tabIndex:K(()=>p(S)?0:-1),handleMousedown:C,handleFocus:y,handleKeydown:h}),{id:f,handleKeydown:h,handleFocus:y,handleMousedown:C}}});function un(e,r,u,i,o,v){const _=$("el-roving-focus-collection-item");return w(),L(_,{id:e.id,focusable:e.focusable,active:e.active},{default:t(()=>[H(e.$slots,"default")]),_:3},8,["id","focusable","active"])}var dn=ne(sn,[["render",un],["__file","roving-focus-item.vue"]]);const cn=me({trigger:lo.trigger,effect:{...$e.effect,default:"light"},type:{type:X(String)},placement:{type:X(String),default:"bottom"},popperOptions:{type:X(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:X([Number,String]),default:0},maxHeight:{type:X([Number,String]),default:""},popperClass:{type:String,default:""},disabled:Boolean,role:{type:String,default:"menu"},buttonProps:{type:X(Object)},teleported:$e.teleported}),je=me({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:Ro}}),pn=me({onKeydown:{type:X(Function)}}),fn=[T.down,T.pageDown,T.home],Je=[T.up,T.pageUp,T.end],mn=[...fn,...Je],{ElCollection:vn,ElCollectionItem:_n,COLLECTION_INJECTION_KEY:gn,COLLECTION_ITEM_INJECTION_KEY:bn}=Ye("Dropdown"),ve=Symbol("elDropdown"),{ButtonGroup:hn}=Ue,wn=x({name:"ElDropdown",components:{ElButton:Ue,ElButtonGroup:hn,ElScrollbar:ao,ElDropdownCollection:vn,ElTooltip:ro,ElRovingFocusGroup:rn,ElOnlyChild:so,ElIcon:Ke,ArrowDown:io},props:cn,emits:["visible-change","click","command"],setup(e,{emit:r}){const u=Be(),i=we("dropdown"),{t:o}=uo(),v=k(),_=k(),f=k(null),b=k(null),C=k(null),y=k(null),h=k(!1),S=[T.enter,T.space,T.down],s=K(()=>({maxHeight:co(e.maxHeight)})),g=K(()=>[i.m(F.value)]),m=K(()=>Mo(e.trigger)),I=Pe().value,B=K(()=>e.id||I);ce([v,m],([c,O],[j])=>{var W,M,ae;(W=j==null?void 0:j.$el)!=null&&W.removeEventListener&&j.$el.removeEventListener("pointerenter",G),(M=c==null?void 0:c.$el)!=null&&M.removeEventListener&&c.$el.removeEventListener("pointerenter",G),(ae=c==null?void 0:c.$el)!=null&&ae.addEventListener&&O.includes("hover")&&c.$el.addEventListener("pointerenter",G)},{immediate:!0}),Me(()=>{var c,O;(O=(c=v.value)==null?void 0:c.$el)!=null&&O.removeEventListener&&v.value.$el.removeEventListener("pointerenter",G)});function A(){z()}function z(){var c;(c=f.value)==null||c.onClose()}function N(){var c;(c=f.value)==null||c.onOpen()}const F=po();function R(...c){r("command",...c)}function G(){var c,O;(O=(c=v.value)==null?void 0:c.$el)==null||O.focus()}function ee(){}function D(){const c=p(b);m.value.includes("hover")&&(c==null||c.focus()),y.value=null}function te(c){y.value=c}function le(c){h.value||(c.preventDefault(),c.stopImmediatePropagation())}function n(){r("visible-change",!0)}function l(c){(c==null?void 0:c.type)==="keydown"&&b.value.focus()}function E(){r("visible-change",!1)}return ie(ve,{contentRef:b,role:K(()=>e.role),triggerId:B,isUsingKeyboard:h,onItemEnter:ee,onItemLeave:D}),ie("elDropdown",{instance:u,dropdownSize:F,handleClick:A,commandHandler:R,trigger:ue(e,"trigger"),hideOnClick:ue(e,"hideOnClick")}),{t:o,ns:i,scrollbar:C,wrapStyle:s,dropdownTriggerKls:g,dropdownSize:F,triggerId:B,triggerKeys:S,currentTabId:y,handleCurrentTabIdChange:te,handlerMainButtonClick:c=>{r("click",c)},handleEntryFocus:le,handleClose:z,handleOpen:N,handleBeforeShowTooltip:n,handleShowTooltip:l,handleBeforeHideTooltip:E,onFocusAfterTrapped:c=>{var O,j;c.preventDefault(),(j=(O=b.value)==null?void 0:O.focus)==null||j.call(O,{preventScroll:!0})},popperRef:f,contentRef:b,triggeringElementRef:v,referenceElementRef:_}}});function In(e,r,u,i,o,v){var _;const f=$("el-dropdown-collection"),b=$("el-roving-focus-group"),C=$("el-scrollbar"),y=$("el-only-child"),h=$("el-tooltip"),S=$("el-button"),s=$("arrow-down"),g=$("el-icon"),m=$("el-button-group");return w(),Z("div",{class:fe([e.ns.b(),e.ns.is("disabled",e.disabled)])},[d(h,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":e.trigger==="hover"?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":(_=e.referenceElementRef)==null?void 0:_.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":e.trigger==="hover"?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},fo({content:t(()=>[d(C,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:t(()=>[d(b,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:t(()=>[d(f,null,{default:t(()=>[H(e.$slots,"dropdown")]),_:3})]),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])]),_:3},8,["wrap-style","view-class"])]),_:2},[e.splitButton?void 0:{name:"default",fn:t(()=>[d(y,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:t(()=>[H(e.$slots,"default")]),_:3},8,["id","tabindex"])])}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(w(),L(m,{key:0},{default:t(()=>[d(S,pe({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:t(()=>[H(e.$slots,"default")]),_:3},16,["size","type","disabled","tabindex","onClick"]),d(S,pe({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:t(()=>[d(g,{class:fe(e.ns.e("icon"))},{default:t(()=>[d(s)]),_:1},8,["class"])]),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])]),_:3})):Q("v-if",!0)],2)}var yn=ne(wn,[["render",In],["__file","dropdown.vue"]]);const Cn=x({name:"DropdownItemImpl",components:{ElIcon:Ke},props:je,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:r}){const u=we("dropdown"),{role:i}=P(ve,void 0),{collectionItemRef:o}=P(bn,void 0),{collectionItemRef:v}=P(Wo,void 0),{rovingFocusGroupItemRef:_,tabIndex:f,handleFocus:b,handleKeydown:C,handleMousedown:y}=P(Ve,void 0),h=Ge(o,v,_),S=K(()=>i.value==="menu"?"menuitem":i.value==="navigation"?"link":"button"),s=q(g=>{const{code:m}=g;if(m===T.enter||m===T.space)return g.preventDefault(),g.stopImmediatePropagation(),r("clickimpl",g),!0},C);return{ns:u,itemRef:h,dataset:{[ze]:""},role:S,tabIndex:f,handleFocus:b,handleKeydown:s,handleMousedown:y}}});function En(e,r,u,i,o,v){const _=$("el-icon");return w(),Z(De,null,[e.divided?(w(),Z("li",{key:0,role:"separator",class:fe(e.ns.bem("menu","item","divided"))},null,2)):Q("v-if",!0),J("li",pe({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:f=>e.$emit("clickimpl",f),onFocus:e.handleFocus,onKeydown:he(e.handleKeydown,["self"]),onMousedown:e.handleMousedown,onPointermove:f=>e.$emit("pointermove",f),onPointerleave:f=>e.$emit("pointerleave",f)}),[e.icon?(w(),L(_,{key:0},{default:t(()=>[(w(),L(mo(e.icon)))]),_:1})):Q("v-if",!0),H(e.$slots,"default")],16,["aria-disabled","tabindex","role","onClick","onFocus","onKeydown","onMousedown","onPointermove","onPointerleave"])],64)}var kn=ne(Cn,[["render",En],["__file","dropdown-item-impl.vue"]]);const He=()=>{const e=P("elDropdown",{}),r=K(()=>e==null?void 0:e.dropdownSize);return{elDropdown:e,_elDropdownSize:r}},Tn=x({name:"ElDropdownItem",components:{ElDropdownCollectionItem:_n,ElRovingFocusItem:dn,ElDropdownItemImpl:kn},inheritAttrs:!1,props:je,emits:["pointermove","pointerleave","click"],setup(e,{emit:r,attrs:u}){const{elDropdown:i}=He(),o=Be(),v=k(null),_=K(()=>{var s,g;return(g=(s=p(v))==null?void 0:s.textContent)!=null?g:""}),{onItemEnter:f,onItemLeave:b}=P(ve,void 0),C=q(s=>(r("pointermove",s),s.defaultPrevented),Oe(s=>{if(e.disabled){b(s);return}const g=s.currentTarget;g===document.activeElement||g.contains(document.activeElement)||(f(s),s.defaultPrevented||g==null||g.focus())})),y=q(s=>(r("pointerleave",s),s.defaultPrevented),Oe(b)),h=q(s=>{if(!e.disabled)return r("click",s),s.type!=="keydown"&&s.defaultPrevented},s=>{var g,m,I;if(e.disabled){s.stopImmediatePropagation();return}(g=i==null?void 0:i.hideOnClick)!=null&&g.value&&((m=i.handleClick)==null||m.call(i)),(I=i.commandHandler)==null||I.call(i,e.command,o,s)}),S=K(()=>({...e,...u}));return{handleClick:h,handlePointerMove:C,handlePointerLeave:y,textContent:_,propsAndAttrs:S}}});function Sn(e,r,u,i,o,v){var _;const f=$("el-dropdown-item-impl"),b=$("el-roving-focus-item"),C=$("el-dropdown-collection-item");return w(),L(C,{disabled:e.disabled,"text-value":(_=e.textValue)!=null?_:e.textContent},{default:t(()=>[d(b,{focusable:!e.disabled},{default:t(()=>[d(f,pe(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:t(()=>[H(e.$slots,"default")]),_:3},16,["onPointerleave","onPointermove","onClickimpl"])]),_:3},8,["focusable"])]),_:3},8,["disabled","text-value"])}var qe=ne(Tn,[["render",Sn],["__file","dropdown-item.vue"]]);const Nn=x({name:"ElDropdownMenu",props:pn,setup(e){const r=we("dropdown"),{_elDropdownSize:u}=He(),i=u.value,{focusTrapRef:o,onKeydown:v}=P(vo,void 0),{contentRef:_,role:f,triggerId:b}=P(ve,void 0),{collectionRef:C,getItems:y}=P(gn,void 0),{rovingFocusGroupRef:h,rovingFocusGroupRootStyle:S,tabIndex:s,onBlur:g,onFocus:m,onMousedown:I}=P(ye,void 0),{collectionRef:B}=P(Ie,void 0),A=K(()=>[r.b("menu"),r.bm("menu",i==null?void 0:i.value)]),z=Ge(_,C,o,h,B),N=q(R=>{var G;(G=e.onKeydown)==null||G.call(e,R)},R=>{const{currentTarget:G,code:ee,target:D}=R;if(G.contains(D),T.tab===ee&&R.stopImmediatePropagation(),R.preventDefault(),D!==p(_)||!mn.includes(ee))return;const le=y().filter(n=>!n.disabled).map(n=>n.ref);Je.includes(ee)&&le.reverse(),Ce(le)});return{size:i,rovingFocusGroupRootStyle:S,tabIndex:s,dropdownKls:A,role:f,triggerId:b,dropdownListWrapperRef:z,handleKeydown:R=>{N(R),v(R)},onBlur:g,onFocus:m,onMousedown:I}}});function $n(e,r,u,i,o,v){return w(),Z("ul",{ref:e.dropdownListWrapperRef,class:fe(e.dropdownKls),style:_o(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:e.onBlur,onFocus:e.onFocus,onKeydown:he(e.handleKeydown,["self"]),onMousedown:he(e.onMousedown,["self"])},[H(e.$slots,"default")],46,["role","aria-labelledby","onBlur","onFocus","onKeydown","onMousedown"])}var We=ne(Nn,[["render",$n],["__file","dropdown-menu.vue"]]);const On=go(yn,{DropdownItem:qe,DropdownMenu:We}),Ln=Ae(qe),Fn=Ae(We);function Rn(e){return de({url:"/user/menuInfo/getAllMenuInfoList",method:"GET",data:e})}function Mn(e){return de({url:"/user/menuInfo/addMenuInfo",method:"POST",data:e})}function Pn(e){return de({url:"/user/menuInfo/delMenuInfo",method:"GET",data:e})}function Kn(e){return de({url:"/user/menuInfo/updateMenuInfo",method:"POST",data:e})}function Bn(e){return de({url:"/user/menuInfo/sortMenuInfo",method:"POST",data:e})}const Dn=e=>(Oo("data-v-70dfcc82"),e=e(),Lo(),e),An={class:"menu_container"},Un={class:"label"},Gn={class:"top"},zn=Dn(()=>J("div",{class:"border_line"},null,-1)),Yn={class:"bottom"},Vn=["onMouseleave","onMouseenter","onClick"],jn={key:0},Jn={key:1},Hn={class:"content"},qn={class:"top"},Wn={key:1},Qn={key:0,class:"bottom"},Xn=x({__name:"index",setup(e){const r=bo({scriptUrl:"/js/iconfont.js"}),u=k(""),i=k(),o=ho({id:"",parentId:"",parentName:"",level:"",siblingSort:"",icon:"",menuCode:"",menuName:"",isShow:1,isLeaf:0,isLink:0,menuUrl:"",remark:""}),v=k([{label:"驾驶舱",value:"icon-jiashicang"},{label:"建设管理",value:"icon-jiansheguanli"},{label:"生产管理",value:"icon-shengchanguanli"},{label:"资产管理",value:"icon-zichanguanli"},{label:"财务管理",value:"icon-caiwuguanli"},{label:"菜单管理",value:"icon-caidanpeizhi"},{label:"系统管理",value:"icon-xitongguanli"},{label:"日志管理",value:"icon-rizhiguanli"}]),_=k({style:{width:"150px"}}),f=k([]),b={label:"menuName"},C=k(),y=(n,l)=>{n.showAdd=!1},h=(n,l)=>{n.showAdd=!0},S=(n,l)=>{let E=[];E.push(l.id),f.value=E,console.log("selectKeys.value=",f.value),console.log("data=",l),console.log("node=",n),A(n,l)},s=(n,l)=>{var j,W,M;console.log("node=",n);let E=(j=n==null?void 0:n.parent)==null?void 0:j.childNodes;E.forEach((ae,_e)=>{ae.data.siblingSort=_e+1});let Y=E.indexOf(n);console.log("index=",Y);let V;l===1?V=E[Y-1]:l===2&&(V=E[Y+1]);let c=(W=V==null?void 0:V.data)==null?void 0:W.siblingSort;V.data.siblingSort=(M=n==null?void 0:n.data)==null?void 0:M.siblingSort,n.data.siblingSort=c;const O=E.map(ae=>ae.data);return console.log("newNodeList=",O),O},g=(n,l)=>{const E=s(n,1)||[];console.log("newNodeList=",E),R({list:E},1)},m=(n,l)=>{const E=s(n,2)||[];console.log("newNodeList=",E),R({list:E},2)},I=k([]),B=()=>{u.value="",o.id="",o.parentId="",o.parentName="",o.level="",o.siblingSort="",o.icon="",o.menuCode="",o.menuName="",o.isLeaf=0,o.isLink=0,o.menuUrl="",o.remark=""},A=(n,l)=>{u.value="edit";for(let E of Object.keys(l))o[E]=l[E];o.parentName=n.parent.data.menuName,console.log("formState=",o)},z=n=>{if(console.log("add_node=",n),u.value="add",n){const{data:l}=n;o.parentId=l.id,o.parentName=l.menuName,o.level=l.level+1,o.siblingSort=n.childNodes.length+1}else o.parentId=519,o.parentName="",o.level=1,o.siblingSort=I.value.length+1;o.id="",o.icon="",o.menuCode="",o.menuName="",o.isLeaf=0,o.isLink=0,o.menuUrl="",o.remark="",console.log("formState=",o)},N=()=>{let n={id:o.id};console.log("删除params=",n),Pn(n).then(l=>{console.log("删除成功"),se.info("删除成功"),te(),B()})},F=()=>{if(o.children&&o.children.length>0){se.error("请先删除下级菜单，再进行删除操作");return}yo.confirm({title:"确认提示",icon:d(Do),content:"确认要删除吗？",onOk:()=>N(),onCancel:()=>{console.log("取消删除")}})},R=(n,l)=>{Bn(n).then(E=>{console.log("移动成功"),l===1?se.info("上移成功"):l===2&&se.info("下移成功"),te()})},G=()=>{i.value.validateFields().then(n=>{console.log("values=",n);let l={...n,...o};u.value==="add"?ee(l):u.value==="edit"&&D(l)})},ee=n=>{Mn(n).then(l=>{console.log("新增成功"),se.info("新增成功"),te(),B()})},D=n=>{Kn(n).then(l=>{console.log("修改成功"),se.info("修改成功"),te(),B()})},te=()=>{Rn({}).then(n=>{console.log("res=",n),n&&n.length>0?I.value=n[0].children:I.value=[]})};Re(()=>{te()});const le=K(()=>o.parentName||"无");return ce(I,n=>{(!n||n.length===0)&&(u.value="")}),ce(()=>o.isLink,n=>{o.isLeaf=n}),(n,l)=>{const E=$("plus-outlined"),Y=Co,V=Ln,c=Fn,O=On,j=Po,W=Eo,M=ko,ae=Ko,_e=Bo,re=To,ge=So,Qe=No,Xe=$o,Ze=Ao;return w(),Z("div",An,[J("div",Un,[J("div",Gn,[d(Y,{type:"primary",onClick:l[0]||(l[0]=()=>z())},{icon:t(()=>[d(E)]),default:t(()=>[U(" 新增一级菜单 ")]),_:1})]),zn,J("div",Yn,[d(j,{style:{"max-width":"400px"},data:I.value,props:b,"empty-text":"","node-key":"id","highlight-current":"","default-expand-all":!1,"expand-on-click-node":!1,ref_key:"cRef",ref:C},{default:t(({node:a,data:oe})=>{var Ee;return[J("span",{class:"custom-tree-node",onMouseleave:()=>y(a,oe),onMouseenter:()=>h(a,oe),onClick:()=>S(a,oe)},[J("span",null,be(a.label),1),((Ee=a==null?void 0:a.parent)==null?void 0:Ee.childNodes.length)>1||oe.isLeaf!==1?(w(),L(O,{key:0},{dropdown:t(()=>[d(c,null,{default:t(()=>{var ke,Te,Se,Ne;return[oe.isLeaf!==1&&p(o).isLink!=1?(w(),L(V,{key:0,style:{"--el-color-primary":"#29cca0"},onClick:()=>z(a)},{default:t(()=>[U("新增菜单")]),_:2},1032,["onClick"])):Q("",!0),((ke=a==null?void 0:a.parent)==null?void 0:ke.childNodes.indexOf(a))!==0?(w(),L(V,{key:1,style:{"--el-color-primary":"#29cca0"},onClick:()=>g(a,oe)},{default:t(()=>[U("上移")]),_:2},1032,["onClick"])):Q("",!0),((Te=a==null?void 0:a.parent)==null?void 0:Te.childNodes.indexOf(a))!==((Ne=(Se=a==null?void 0:a.parent)==null?void 0:Se.childNodes)==null?void 0:Ne.length)-1?(w(),L(V,{key:2,style:{"--el-color-primary":"#29cca0"},onClick:()=>m(a,oe)},{default:t(()=>[U("下移")]),_:2},1032,["onClick"])):Q("",!0)]}),_:2},1024)]),default:t(()=>[a.isCurrent||a.showAdd?(w(),Z("span",jn,"...")):(w(),Z("span",Jn))]),_:2},1024)):Q("",!0)],40,Vn)]}),_:1},8,["data"])])]),J("div",Hn,[J("div",qn,[u.value?(w(),L(Xe,{key:0,ref_key:"formRef",ref:i,model:p(o),"label-col":_.value},{default:t(()=>[d(M,{label:"上级菜单",style:{width:"50%"}},{default:t(()=>[d(W,{value:p(le),"onUpdate:value":l[1]||(l[1]=a=>wo(le)?le.value=a:null),disabled:""},null,8,["value"])]),_:1}),d(M,{label:"菜单名称",name:"menuName",required:"",style:{width:"50%"}},{default:t(()=>[d(W,{value:p(o).menuName,"onUpdate:value":l[2]||(l[2]=a=>p(o).menuName=a),placeholder:"请输入",maxlength:10},null,8,["value"])]),_:1}),d(M,{label:"菜单标识",name:"menuCode",required:"",style:{width:"50%"}},{default:t(()=>[d(W,{value:p(o).menuCode,"onUpdate:value":l[3]||(l[3]=a=>p(o).menuCode=a),disabled:u.value==="edit",placeholder:"请输入"},null,8,["value","disabled"])]),_:1}),p(o).level===1?(w(),L(M,{key:0,label:"菜单图标",name:"icon",required:p(o).level===1,style:{width:"50%"}},{default:t(()=>[d(_e,{modelValue:p(o).icon,"onUpdate:modelValue":l[4]||(l[4]=a=>p(o).icon=a),placeholder:"请选择",filterable:"",clearable:"",style:{"--el-color-primary":"#29cca0"},"fit-input-width":!0,"default-expanded-keys":f.value},{label:t(({label:a,value:oe})=>[d(p(r),{type:oe,style:{"margin-right":"10px"}},null,8,["type"]),J("span",null,be(a),1)]),default:t(()=>[(w(!0),Z(De,null,Io(v.value,a=>(w(),L(ae,{key:a.value,label:a.label,value:a.value,style:{"--el-color-primary":"#29cca0"}},{default:t(()=>[d(p(r),{type:a.value,style:{"margin-right":"20px"}},null,8,["type"]),J("span",null,be(a.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","default-expanded-keys"])]),_:1},8,["required"])):Q("",!0),d(M,{label:"是否在菜单栏显示",name:"isShow",required:"",style:{width:"50%"}},{default:t(()=>[d(ge,{value:p(o).isShow,"onUpdate:value":l[5]||(l[5]=a=>p(o).isShow=a),disabled:u.value==="edit"},{default:t(()=>[d(re,{value:1},{default:t(()=>[U("是")]),_:1}),d(re,{value:0},{default:t(()=>[U("否")]),_:1})]),_:1},8,["value","disabled"])]),_:1}),d(M,{label:"是否为最后一级",name:"isLeaf",required:"",style:{width:"50%"}},{default:t(()=>[d(ge,{value:p(o).isLeaf,"onUpdate:value":l[6]||(l[6]=a=>p(o).isLeaf=a),disabled:u.value==="edit"||p(o).isLink===1},{default:t(()=>[d(re,{value:1},{default:t(()=>[U("是")]),_:1}),d(re,{value:0},{default:t(()=>[U("否")]),_:1})]),_:1},8,["value","disabled"])]),_:1}),d(M,{label:"是否为外部链接",name:"isLink",required:"",style:{width:"50%"}},{default:t(()=>[d(ge,{value:p(o).isLink,"onUpdate:value":l[7]||(l[7]=a=>p(o).isLink=a)},{default:t(()=>[d(re,{value:1},{default:t(()=>[U("是")]),_:1}),d(re,{value:0},{default:t(()=>[U("否")]),_:1})]),_:1},8,["value"])]),_:1}),p(o).isLeaf===1?(w(),L(M,{key:1,label:"页面地址",name:"menuUrl",style:{width:"50%"},required:""},{default:t(()=>[d(W,{value:p(o).menuUrl,"onUpdate:value":l[8]||(l[8]=a=>p(o).menuUrl=a),placeholder:"请输入"},null,8,["value"])]),_:1})):Q("",!0),d(M,{label:"备注",name:"remark"},{default:t(()=>[d(Qe,{rows:3,placeholder:"请输入",maxlength:100,value:p(o).remark,"onUpdate:value":l[9]||(l[9]=a=>p(o).remark=a)},null,8,["value"])]),_:1})]),_:1},8,["model","label-col"])):(w(),Z("div",Wn))]),u.value?(w(),Z("div",Qn,[d(Ze,null,{default:t(()=>[u.value==="add"?(w(),L(Y,{key:0,onClick:B},{default:t(()=>[U("取消")]),_:1})):(w(),L(Y,{key:1,onClick:F},{default:t(()=>[U("删除")]),_:1})),d(Y,{type:"primary",onClick:G},{default:t(()=>[U("提交")]),_:1})]),_:1})])):Q("",!0)])])}}});const it=Fo(Xn,[["__scopeId","data-v-70dfcc82"]]);export{it as default};
