import{cH as ne,d as te,bC as ae,bs as l,dw as le,o as ie,c2 as se,r as ce,db as re,b4 as ue,cJ as L,bu as de,c as b,dr as ve,dx as fe,di as pe,du as me,bD as Ce,f as i,dy as be,dz as ye,bn as ge,bt as u,dA as Pe,dB as O,dC as _e,bg as xe,dD as ke,d7 as Te}from"./index-db94d997.js";var Be=["placement","overlayClassName"],Ne=function(){return l(l({},Pe()),{},{prefixCls:String,content:u.any,title:u.any,okType:{type:String,default:"primary"},disabled:{type:Boolean,default:!1},okText:u.any,cancelText:u.any,icon:u.any,okButtonProps:{type:Object,default:void 0},cancelButtonProps:{type:Object,default:void 0},showCancel:{type:Boolean,default:!0},onConfirm:Function,onCancel:Function})},$e=te({compatConfig:{MODE:3},name:"APopconfirm",props:ae(Ne(),l(l({},le()),{},{trigger:"click",transitionName:"zoom-big",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0,okType:"primary",disabled:!1})),slots:["title","content","okText","icon","cancelText","cancelButton","okButton"],emits:["update:visible","visibleChange"],setup:function(o,f){var t=f.slots,y=f.emit,z=f.expose;ie(function(){se(o.defaultVisible===void 0,"Popconfirm","'defaultVisible' is deprecated, please use 'v-model:visible'")});var g=ce();z({getPopupDomNode:function(){var e,n;return(e=g.value)===null||e===void 0||(n=e.getPopupDomNode)===null||n===void 0?void 0:n.call(e)}});var A=re(!1,{value:ue(o,"visible"),defaultValue:o.defaultVisible}),P=L(A,2),_=P[0],E=P[1],d=function(e,n){o.visible===void 0&&E(e),y("update:visible",e),y("visibleChange",e,n)},K=function(e){d(!1,e)},x=function(e){var n;return(n=o.onConfirm)===null||n===void 0?void 0:n.call(o,e)},M=function(e){var n;d(!1,e),(n=o.onCancel)===null||n===void 0||n.call(o,e)},F=function(e){e.keyCode===Te.ESC&&_&&d(!1,e)},I=function(e){var n=o.disabled;n||d(e)},k=de("popconfirm",o),W=k.prefixCls,p=k.getPrefixCls,q=b(function(){return p()}),s=b(function(){return p("popover")}),H=b(function(){return p("btn")}),J=ve("Popconfirm",fe.Popconfirm),U=L(J,1),T=U[0],G=function(){var e,n,c,r,v=o.okButtonProps,B=o.cancelButtonProps,m=o.title,C=m===void 0?(e=t.title)===null||e===void 0?void 0:e.call(t):m,N=o.cancelText,Q=N===void 0?(n=t.cancel)===null||n===void 0?void 0:n.call(t):N,$=o.okText,X=$===void 0?(c=t.okText)===null||c===void 0?void 0:c.call(t):$,h=o.okType,V=o.icon,Y=V===void 0?(r=t.icon)===null||r===void 0?void 0:r.call(t):V,w=o.showCancel,Z=w===void 0?!0:w,D=t.cancelButton,R=t.okButton,j=l({onClick:M,size:"small"},B),ee=l(l({onClick:x},O(h)),{},{size:"small"},v);return i("div",{class:"".concat(s.value,"-inner-content")},[i("div",{class:"".concat(s.value,"-message")},[Y||i(_e,null,null),i("div",{class:"".concat(s.value,"-message-title")},[C])]),i("div",{class:"".concat(s.value,"-buttons")},[Z?D?D(j):i(xe,j,{default:function(){return[Q||T.value.cancelText]}}):null,R?R(ee):i(ke,{buttonProps:l(l({size:"small"},O(h)),v),actionFn:x,close:K,prefixCls:H.value,quitOnNullishReturnValue:!0,emitEvent:!0},{default:function(){return[X||T.value.okText]}})])])};return function(){var a,e=o.placement,n=o.overlayClassName,c=pe(o,Be),r=me(c,["title","content","cancelText","okText","onUpdate:visible","onConfirm","onCancel"]),v=Ce(W.value,n);return i(ge,l(l({},r),{},{prefixCls:s.value,placement:e,onVisibleChange:I,visible:_.value,overlayClassName:v,transitionName:ye(q.value,"zoom-big",o.transitionName),ref:g}),{default:function(){return[be(((a=t.default)===null||a===void 0?void 0:a.call(t))||[],{onKeydown:function(C){F(C)}},!1)]},title:G})}}});const Ve=ne($e);export{Ve as _};
