import{d as H,r as j,o as L,a as V,v as W,g as s,i as o,u as t,S as d,T as p,z,U as Y,f as k,F as Q,p as G,j as J,_ as K}from"./index-db94d997.js";import{T as X,a as Z}from"./TabTwo-4c51de56.js";import{b as $}from"./index-23cd6eea.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-0b09ecf1.js";import"./index.vue_vue_type_style_index_0_lang-447fd504.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./weiZhi-78534cab.js";import"./CaretUpOutlined-7e71a64b.js";import"./index-39334618.js";const e=i=>(G("data-v-95d97055"),i=i(),J(),i),ss={class:"card top"},ts=e(()=>s("div",{class:"title"},"基本信息",-1)),as={class:"content"},os={class:"item"},es=e(()=>s("span",{class:"label"},"电站编号：",-1)),ns={class:"value"},cs={class:"item"},is=e(()=>s("span",{class:"label"},"业主姓名：",-1)),_s={class:"value"},ls={class:"item"},ds=e(()=>s("span",{class:"label"},"产权公司：",-1)),ps={class:"value"},hs={class:"item"},ms=e(()=>s("span",{class:"label"},"业主类型：",-1)),us={class:"value"},rs={class:"item"},vs=e(()=>s("span",{class:"label"},"联系电话：",-1)),bs={class:"value"},fs={class:"item"},Is=e(()=>s("span",{class:"label"},"身份证号：",-1)),Ts={class:"value"},ys={class:"item"},gs=e(()=>s("span",{class:"label"},"业主年龄：",-1)),Ns={class:"value"},Cs={class:"item"},Ds=e(()=>s("span",{class:"label"},"装机容量：",-1)),Ss={class:"value"},Es={class:"item"},Os=e(()=>s("span",{class:"label"},"制单时间：",-1)),Ps={class:"value"},Us={class:"item"},qs=e(()=>s("span",{class:"label"},"上网类型：",-1)),ws={class:"value"},Ms={class:"item"},Rs=e(()=>s("span",{class:"label"},"电站类型：",-1)),xs={class:"value"},As={class:"item"},js=e(()=>s("span",{class:"label"},"项目公司：",-1)),ks={class:"value"},Bs={class:"item"},Fs=e(()=>s("span",{class:"label"},"行政区划：",-1)),Hs={class:"value"},Ls={class:"item"},Vs=e(()=>s("span",{class:"label"},"详细地址：",-1)),Ws={class:"value"},zs={class:"item"},Ys=e(()=>s("span",{class:"label"},"组件数量：",-1)),Qs={class:"value"},Gs={class:"base_content"},Js=H({__name:"Detail",setup(i){var m;const h=(m=history.state)==null?void 0:m.pdata,B={...h,companyCode:h.ownerCompanyCode},n=j(B);console.log("技术资料pdata=",n.value);const a=j(),F=()=>{let u={delStatus:0,pageNum:1,pageSize:10,stationUniqueId:n.value.stationUniqueId};$(u).then(l=>{console.log("技术资料res=",l);const{code:_,data:c}=l;_===200&&c&&c.length>0&&(a.value=c[0])})};return L(()=>{F()}),(u,l)=>{var _,c,r,v,b,f,I,T,y,g,N,C,D,S,E,O,P,U,q,w,M,R,x,A;return V(),W(Q,null,[s("div",ss,[ts,s("div",as,[s("div",os,[es,s("span",ns,o((_=t(a))==null?void 0:_.stationUniqueId),1)]),s("div",cs,[is,s("span",_s,o((r=(c=t(a))==null?void 0:c.householdInfo)==null?void 0:r.nameHoh),1)]),s("div",ls,[ds,s("span",ps,o((v=t(a))==null?void 0:v.ownerCompanyName),1)]),s("div",hs,[ms,s("span",us,o(t(d)((b=t(a))==null?void 0:b.tyPower,t(p)("PROPRIETOR_TYPE"))),1)]),s("div",rs,[vs,s("span",bs,o((I=(f=t(a))==null?void 0:f.householdInfo)==null?void 0:I.nimMpHoh),1)]),s("div",fs,[Is,s("span",Ts,o((y=(T=t(a))==null?void 0:T.householdInfo)==null?void 0:y.numIdHoh),1)]),s("div",ys,[gs,s("span",Ns,o((N=(g=t(a))==null?void 0:g.householdInfo)==null?void 0:N.age),1)]),s("div",Cs,[Ds,s("span",Ss,o((C=t(a))!=null&&C.capins?t(z)((D=t(a))==null?void 0:D.capins)+"kW":""),1)]),s("div",Es,[Os,s("span",Ps,o(t(Y)((S=t(a))==null?void 0:S.operateDate)),1)]),s("div",Us,[qs,s("span",ws,o(t(d)((E=t(a))==null?void 0:E.netmode,t(p)("POWERSTA_NETMODE"))),1)]),s("div",Ms,[Rs,s("span",xs,o(t(d)((O=t(a))==null?void 0:O.businessStationType,t(p)("EAM_POWER_STATION_TYPE_CONDITION"))),1)]),s("div",As,[js,s("span",ks,o((P=t(a))==null?void 0:P.projectCompanyName),1)]),s("div",Bs,[Fs,s("span",Hs,o(((U=t(a))==null?void 0:U.provinceName)||"")+o(((q=t(a))==null?void 0:q.cityName)||"")+" "+o(((w=t(a))==null?void 0:w.areaName)||""),1)]),s("div",Ls,[Vs,s("span",Ws,o((M=t(a))==null?void 0:M.projectLocation),1)]),s("div",zs,[Ys,s("span",Qs,o((R=t(a))==null?void 0:R.componentQuantity),1)])])]),s("div",Gs,[k(X,{stationCode:(x=t(n))==null?void 0:x.stationUniqueId,pdata:t(n)},null,8,["stationCode","pdata"]),k(Z,{stationCode:(A=t(n))==null?void 0:A.stationUniqueId,pdata:t(n)},null,8,["stationCode","pdata"])])],64)}}});const lt=K(Js,[["__scopeId","data-v-95d97055"]]);export{lt as default};
