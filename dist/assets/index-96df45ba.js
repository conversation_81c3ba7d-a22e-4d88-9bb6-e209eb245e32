import{a2 as t}from"./index-db94d997.js";function a(e){return t({url:"/user/UserDataAuth/getCompanyTreeVO",method:"GET",data:e})}function u(e){return t({url:"/web/common/v1/getAreaTreeByCompanyCode",method:"POST",data:e})}function s(e){return t({url:"/user/UserDataAuth/getUserDataAuthVOList",method:"POST",data:e})}function n(e){return t({url:"/user/UserDataAuth/companyIdExists",method:"GET",data:e})}function o(e){return t({url:"/user/UserDataAuth/saveUserDAtaAuth",method:"POST",data:e})}function h(e){return t({url:"/user/UserDataAuth/cancelUserDataAuth",method:"POST",data:e})}export{u as a,h as b,n as c,s as d,a as g,o as s};
