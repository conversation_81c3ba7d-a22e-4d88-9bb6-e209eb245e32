import{d as re,r as _,K as pe,o as _e,D as fe,at as he,w as W,a as h,v as b,g as c,f as l,e as s,h as y,u as n,y as M,b as z,i as v,z as Y,e9 as J,m as q,F as me,k as ue,au as ye,q as O,bf as ge,bI as ve,bU as xe,av as ke,bg as Ie,bh as we,bn as be,ay as Ce,az as De,p as ze,j as Se,_ as Ne}from"./index-db94d997.js";import{k as Pe,l as Fe,m as Te,n as Me}from"./index-4cf21c7d.js";import{e as je}from"./empty1-348ef1fe.js";import{R as Ee}from"./dayjs-a8e42122.js";import{_ as Le}from"./index-42d7fb9b.js";import{_ as Re}from"./index-39334618.js";import"./customParseFormat-ed0c33ac.js";const k=S=>(ze("data-v-af58979f"),S=S(),Se(),S),Ue={class:"statements_container"},Ve=k(()=>c("div",{class:"item_title"},"时间维度",-1)),Be={style:{display:"flex","flex-direction":"row","align-items":"center"}},Ae={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},We={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Ye=k(()=>c("div",{style:{flex:"1",display:"flex","align-items":"center"}},null,-1)),Je=k(()=>c("div",{style:{flex:"1",display:"flex","align-items":"center","padding-left":"5%"}},null,-1)),qe=k(()=>c("div",{class:"item_title"},"统计维度",-1)),Oe={style:{display:"flex","flex-direction":"row","align-items":"center"}},$e={style:{flex:"1",display:"flex","align-items":"center","padding-right":"5%"}},Ke={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Qe=k(()=>c("span",{style:{"margin-left":"5px"}},"全选",-1)),Ge={style:{flex:"1",display:"flex","align-items":"center"}},He={style:{width:"150px",display:"flex","justify-content":"flex-end","flex-direction":"row"}},Xe=k(()=>c("span",{style:{"margin-left":"5px"}},"全选",-1)),Ze={style:{flex:"1",display:"flex","justify-content":"flex-end","margin-bottom":"24px","padding-left":"5%"}},et={key:0,class:"statements_container",style:{"margin-top":"24px","max-height":"740px",position:"relative"}},tt={style:{display:"flex","flex-direction":"row","justify-content":"flex-end","margin-bottom":"14px"}},at={class:"text-hide"},nt={class:"text-hide"},it={class:"text-hide"},ot={class:"text-hide"},st={key:1,class:"content_container"},dt={key:2,class:"content_container",style:{position:"relative"}},lt=["src"],ct=k(()=>c("span",{class:"empty_text"}," 暂无数据，请选择正确查询信息 ",-1)),rt=re({__name:"index",setup(S){const N=_(),g=_(!1),I=_([]),p=_({}),C=_([]),D=_([]),P=_(0),F=_(1),j=_(10),w=_([]),i=pe({weiType:"1",corpId3:"",time1:[],time2:"",time3:"",companyId:[],companyId_checked:!1,companyId_indeterminate:!1,provinceId:[],provinceId_checked:!1,provinceId_indeterminate:!1,checked4:!0,checked5:!1}),$=_([{title:"产权公司",dataIndex:"corpName3",resizable:!0,width:140},{title:"开始时间",dataIndex:"startTime",width:120,resizable:!0},{title:"结束时间",dataIndex:"endTime",search:!1,width:120,resizable:!0},{title:"区间内营业收入(万元)",dataIndex:"incwtInter",search:!1,formatMoney:!0,width:150,resizable:!0},{title:"区间内发电量(万kWh)",dataIndex:"enwtInter",search:!1,formatMoney:!0,width:150,resizable:!0},{title:"售电电价(元/kWh)",dataIndex:"sePriofEn",search:!1,width:150,formatDecimal:4,resizable:!0}]),K=_([{title:"省",dataIndex:"provinceName",resizable:!0,width:100},{title:"开始时间",dataIndex:"startTime",width:120,resizable:!0},{title:"结束时间",dataIndex:"endTime",search:!1,width:120,resizable:!0},{title:"区间内营业收入(万元)",dataIndex:"incwtInter",search:!1,formatMoney:!0,width:150,resizable:!0},{title:"区间内发电量(万kWh)",dataIndex:"enwtInter",search:!1,formatMoney:!0,width:150,resizable:!0},{title:"售电电价(元/kWh)",dataIndex:"sePriofEn",search:!1,width:150,formatDecimal:4,resizable:!0}]),Q=e=>{const t=i.time1;if(!t||t.length===0)return!1;const d=t[0]&&e.diff(t[0],"month")>11;return t[1]&&ue(t[1]).diff(e,"month")>10||d},G=e=>{i.time1=e},L=(e,{attrs:t})=>t.vnodes,H=(e,t)=>{i[e]=t.map(d=>d.value)},R=(e,t,d)=>{var x;((x=e==null?void 0:e.target)==null?void 0:x.checked)?H(t,d):i[t]=[],i[t+"_indeterminate"]=!1},U=(e,t,d,r)=>{t.length===0?(i[r+"_indeterminate"]=!1,i[r+"_checked"]=!1):t.length===d.length?(i[r+"_indeterminate"]=!1,i[r+"_checked"]=!0):(i[r+"_indeterminate"]=!0,i[r+"_checked"]=!1)},X=_({companyId:[{validator:async(e,t)=>{if((!t||t.length===0)&&i.checked4)return Promise.reject("请选择产权公司")},trigger:"change"}],provinceId:[{validator:async(e,t)=>{if((!t||t.length===0)&&i.checked5)return Promise.reject("请选择行政区划")},trigger:"change"}],time1:[{validator:async(e,t)=>{if(!t||t.length===0)return Promise.reject("请选择时间周期")},trigger:"change"}]}),Z=e=>{V(e)},ee=e=>{console.log(e)},te=()=>{N.value.resetFields(),p.value={},C.value=[],D.value=[],i.companyId_checked=!1,i.companyId_indeterminate=!1,i.provinceId_checked=!1,i.provinceId_indeterminate=!1},ae=(e,t,d)=>{F.value=e.current,j.value=e.pageSize,P.value=e.total;const r={...i,pageNum:e.current,pageSize:e.pageSize};V(r)},ne=()=>{ye({pid:0}).then(e=>{if(e&&e.length>0){let t=e.map(d=>({label:d.name,value:d.code}));I.value=t}})},V=e=>{let t=e==null?void 0:e.time1[0],d=e==null?void 0:e.time1[1];if(i.checked4){let r=O.cloneDeep(e.companyId)||[],m={corpId3:JSON.stringify(r),startTime:t,endTime:d,pageNum:F.value,pageSize:j.value,checked4:i.checked4};console.log("params=",m),g.value=!0,Pe(m).then(f=>{g.value=!1,p.value=m,console.log("公司维度res=",f),C.value=f||[],B(f,1)}).catch(f=>{g.value=!1})}else if(i.checked5){let r=O.cloneDeep(e.provinceId)||[],m={type:"1",id:JSON.stringify(r),startTime:t,endTime:d,pageNum:F.value,pageSize:j.value,checked5:i.checked5};console.log("params=",m),g.value=!0,Fe(m).then(f=>{g.value=!1,p.value=m,console.log("地区维度res=",f),D.value=f||[],B(f,2)}).catch(f=>{g.value=!1})}},B=(e,t)=>{t===1?(C.value=(e==null?void 0:e.result)||[],P.value=(e==null?void 0:e.total)||0):t===2&&(D.value=(e==null?void 0:e.result)||[],P.value=(e==null?void 0:e.total)||0)};_e(()=>{ie(),ne()}),fe(()=>{console.log("onActivated")});const ie=()=>{he({}).then(e=>{console.log("产权公司res=",e);let d=(e||[]).map(r=>({label:r.companyName,value:r.companyCode}));w.value=d})};W(()=>i.checked4,e=>{e&&(i.checked5=!1,i.checked6=!1,i.provinceId=[],N.value.clearValidate(["provinceId"]))}),W(()=>i.checked5,e=>{e&&(i.checked4=!1,i.checked6=!1,i.companyId=[],N.value.clearValidate(["companyId"]))});const oe=()=>{console.log("pageParams=",p.value),p.value.checked4?Te(p.value).then(e=>{console.log("公司维度导出res:",e)}):p.value.checked5&&Me(p.value).then(e=>{console.log("公司维度导出res:",e)})};return(e,t)=>{const d=ge,r=Ee,x=ve,m=xe,f=Le,A=ke,E=Ie,se=Re,de=we,T=be,le=Ce,ce=De;return h(),b(me,null,[c("div",Ue,[l(de,{ref_key:"formRef",ref:N,name:"formRef",model:n(i),rules:n(X),onFinish:Z,onFinishFailed:ee},{default:s(()=>[Ve,c("div",Be,[c("div",Ae,[c("div",We,[l(d,{name:"checked1"},{default:s(()=>[y(" 时间周期： ")]),_:1})]),l(d,{name:"time1",style:{flex:"1",display:"flex"}},{default:s(()=>[l(r,{value:n(i).time1,"onUpdate:value":t[0]||(t[0]=a=>n(i).time1=a),style:{width:"100%"},"disabled-date":Q,onCalendarChange:G,picker:"month",valueFormat:"YYYY-MM"},null,8,["value"])]),_:1})]),Ye,Je]),qe,c("div",Oe,[c("div",$e,[c("div",Ke,[l(d,{name:"checked4"},{default:s(()=>[l(x,{checked:n(i).checked4,"onUpdate:checked":t[1]||(t[1]=a=>n(i).checked4=a)},{default:s(()=>[y("产权公司：")]),_:1},8,["checked"])]),_:1})]),l(d,{name:"companyId",style:{flex:"1",display:"flex"}},{default:s(()=>[l(A,{value:n(i).companyId,"onUpdate:value":t[5]||(t[5]=a=>n(i).companyId=a),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:n(w),mode:"multiple",disabled:!n(i).checked4,"filter-option":(a,o)=>((o==null?void 0:o.label)??"").toLowerCase().includes(a.toLowerCase()),onChange:t[6]||(t[6]=(a,o)=>U(a,o,n(w),"companyId"))},{dropdownRender:s(({menuNode:a})=>[n(w)&&n(w).length>0?(h(),b("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[4]||(t[4]=o=>o.preventDefault())},[l(m,{checked:n(i).companyId_checked,"onUpdate:checked":t[2]||(t[2]=o=>n(i).companyId_checked=o),indeterminate:n(i).companyId_indeterminate,onChange:t[3]||(t[3]=o=>R(o,"companyId",n(w)))},null,8,["checked","indeterminate"]),Qe],32)):M("",!0),l(f,{style:{margin:"4px 0"}}),l(L,{vnodes:a},null,8,["vnodes"])]),_:1},8,["value","options","disabled","filter-option"])]),_:1})]),c("div",Ge,[c("div",He,[l(d,{name:"checked5"},{default:s(()=>[l(x,{checked:n(i).checked5,"onUpdate:checked":t[7]||(t[7]=a=>n(i).checked5=a)},{default:s(()=>[y("行政区划：")]),_:1},8,["checked"])]),_:1})]),l(d,{name:"provinceId",style:{flex:"1",display:"flex"}},{default:s(()=>[l(A,{value:n(i).provinceId,"onUpdate:value":t[11]||(t[11]=a=>n(i).provinceId=a),style:{width:"100%"},"max-tagCount":1,"show-search":"","show-arrow":"",placeholder:"请选择",options:n(I),mode:"multiple",disabled:!n(i).checked5,"filter-option":(a,o)=>((o==null?void 0:o.label)??"").toLowerCase().includes(a.toLowerCase()),onChange:t[12]||(t[12]=(a,o)=>U(a,o,n(I),"provinceId"))},{dropdownRender:s(({menuNode:a})=>[n(I)&&n(I).length>0?(h(),b("div",{key:0,style:{padding:"4px 8px",cursor:"pointer"},onMousedown:t[10]||(t[10]=o=>o.preventDefault())},[l(m,{checked:n(i).provinceId_checked,"onUpdate:checked":t[8]||(t[8]=o=>n(i).provinceId_checked=o),indeterminate:n(i).provinceId_indeterminate,onChange:t[9]||(t[9]=o=>R(o,"provinceId",n(I)))},null,8,["checked","indeterminate"]),Xe],32)):M("",!0),l(f,{style:{margin:"4px 0"}}),l(L,{vnodes:a},null,8,["vnodes"])]),_:1},8,["value","options","disabled","filter-option"])]),_:1})]),c("div",Ze,[l(se,null,{default:s(()=>[l(E,{onClick:te},{default:s(()=>[y("重置")]),_:1}),l(E,{type:"primary","html-type":"submit"},{default:s(()=>[y("查询")]),_:1})]),_:1})])])]),_:1},8,["model","rules"])]),n(p).checked4||n(p).checked5?(h(),b("div",et,[c("div",tt,[n(p).checked4&&n(C).length>0||n(p).checked5&&n(D).length>0?(h(),z(E,{key:0,type:"primary",onClick:oe},{default:s(()=>[y("导出")]),_:1})):M("",!0)]),l(le,{style:{"margin-bottom":"24px"},columns:n(p).checked4?n($):n(p).id?n(K):[],"data-source":n(p).checked4?n(C):n(p).checked5?n(D):[],pagination:{current:n(F),total:n(P),showTotal:a=>`共 ${a} 条`,size:"small",showQuickJumper:!0,showSizeChanger:!0},scroll:{y:500},loading:n(g),onChange:ae},{bodyCell:s(({column:a,text:o,record:u,index:ht})=>[a!=null&&a.formatMoney&&!a.render?(h(),z(T,{key:0},{title:s(()=>[y(v(n(Y)(u[a.dataIndex])),1)]),default:s(()=>[c("span",at,v(n(Y)(u[a.dataIndex])),1)]),_:2},1024)):a!=null&&a.formatFixed&&!a.render?(h(),z(T,{key:1},{title:s(()=>[y(v(u[a.dataIndex]&&n(J)(u[a.dataIndex])),1)]),default:s(()=>[c("span",nt,v(u[a.dataIndex]&&n(J)(u[a.dataIndex])),1)]),_:2},1024)):(a!=null&&a.formatDecimal||(a==null?void 0:a.formatDecimal)==0)&&!a.render?(h(),z(T,{key:2},{title:s(()=>[y(v(n(q)(u[a.dataIndex],a==null?void 0:a.formatDecimal)),1)]),default:s(()=>[c("span",it,v(n(q)(u[a.dataIndex],a==null?void 0:a.formatDecimal)),1)]),_:2},1024)):a.render?M("",!0):(h(),z(T,{key:3},{title:s(()=>[y(v(u[a.dataIndex]),1)]),default:s(()=>[c("span",ot,v(u[a.dataIndex]),1)]),_:2},1024))]),_:1},8,["columns","data-source","pagination","loading"])])):n(g)?(h(),b("div",st,[l(ce)])):(h(),b("div",dt,[c("img",{src:n(je),style:{width:"188px",height:"168px"}},null,8,lt),ct]))],64)}}});const It=Ne(rt,[["__scopeId","data-v-af58979f"]]);export{It as default};
