import{_ as P}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as E,I as q,r,o as B,D as R,at as V,a as h,v as F,f as o,e as t,h as s,u as x,b as O,y as $,i as v,g as A,q as b,bg as L,bn as U,_ as G}from"./index-db94d997.js";import{g as H,e as J}from"./index-9f4dd0ea.js";import{_ as Q}from"./index-39334618.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./dayjs-a8e42122.js";import"./customParseFormat-ed0c33ac.js";import"./index-07f7e8bf.js";const j={class:"areaPrice"},K={class:"text-hide"},W=E({__name:"index",setup(X){const u=q(),i=r(),p=r([]),m=r([{label:"是",value:1},{label:"否",value:0}]),g=r([{label:"运维协商中",value:1},{label:"公司确认中",value:2},{label:"已完成",value:3}]),w=r([{label:"已结案",value:1},{label:"未结案",value:0}]),I=[{title:"电站编号",dataIndex:"stationUniqueId",resizable:!0,fixed:"left",order:4,width:120},{title:"业主名称",dataIndex:"householdName",resizable:!0,fixed:"left",order:5,width:120},{title:"产权公司",key:"companyCode",dataIndex:"companyCode",valueType:"select",width:160,valueEnum:p,fixed:"left",order:1,render:!0},{title:"案件编号",dataIndex:"caseNumber",resizable:!0,order:2,width:120},{title:"创建时间",dataIndex:"createTime",valueType:"date",resizable:!0,order:7,width:120},{title:"保单号",dataIndex:"policyNumber",resizable:!0,width:120,order:3},{title:"险种",dataIndex:"insuranceTypeName",search:!1,resizable:!0,width:120},{title:"摘要",dataIndex:"digest",search:!1,resizable:!0,width:120},{title:"理赔金额(元)",dataIndex:"claimAmount",resizable:!0,search:!1,formatMoney:!0,width:120},{title:"理赔进度",dataIndex:"claimProgress",valueType:"select",valueEnum:g,resizable:!0,search:!1,formatMoney:!0,width:120},{title:"保费是否到账",dataIndex:"premiumStatus",valueType:"select",valueEnum:m,resizable:!0,search:!1,formatMoney:!0,width:120},{title:"是否与运维结算",dataIndex:"operationsSettleStatus",valueType:"select",valueEnum:m,resizable:!0,order:8,width:120},{title:"结案标志",dataIndex:"caseStatus",valueType:"select",valueEnum:w,resizable:!0,width:120,order:6},{title:"操作",key:"action",search:!1,render:!0,fixed:"right",align:"center",width:80}];B(()=>{C()}),R(()=>{i.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&D()});const C=()=>{V({}).then(e=>{console.log("产权公司res=",e);let l=(e||[]).map(n=>({label:n.companyName,value:n.companyCode}));p.value=l})},z=()=>{u.push({path:"/financeManage/dailyManage/outPolicy/dataImport",query:{templateType:14,fileType:".csv,.xls,.xlsx",fileSize:30}})},S=()=>{var a;let e=(a=i.value)==null?void 0:a.getInitialFormStateNew();J(e)},_=(e,a)=>{e===0?u.push({path:"/financeManage/dailyManage/outPolicy/detail",state:{pdata:b.cloneDeep(a),type:e}}):e===1&&u.push({path:"/financeManage/dailyManage/outPolicy/edit",state:{pdata:b.cloneDeep(a),type:e}})},f=r([]),k=e=>{f.value=e||[]},D=()=>{var e;(e=i.value)==null||e.reload()},N=(e,a)=>{let l={};return new Promise(n=>{const c={...l,...e,noJoin:!0,delStatus:0};n(c)})};return(e,a)=>{const l=L,n=Q,c=U,T=P;return h(),F("div",j,[o(T,{columns:I,ref_key:"actionRef",ref:i,request:x(H),"label-col":{style:{width:"120px"}},"wrapper-col":{span:16},onGetDataSource:k,"before-query-params":N},{tableHeader:t(()=>[o(n,null,{default:t(()=>[o(l,{onClick:z},{default:t(()=>[s("批量导入")]),_:1}),x(f).length>0?(h(),O(l,{key:0,onClick:S},{default:t(()=>[s("导出")]),_:1})):$("",!0),o(l,{type:"primary",onClick:a[0]||(a[0]=y=>_(0))},{default:t(()=>[s("新建出险单")]),_:1})]),_:1})]),companyCodeRender:t(({column:y,record:d,index:M})=>[o(c,null,{title:t(()=>[s(v(d.companyName),1)]),default:t(()=>[A("span",K,v(d.companyName),1)]),_:2},1024)]),actionRender:t(({column:y,record:d,index:M})=>[o(n,null,{default:t(()=>[o(l,{type:"link",size:"small",onClick:Y=>_(1,d)},{default:t(()=>[s("编辑")]),_:2},1032,["onClick"])]),_:2},1024)]),_:1},8,["request"])])}}});const ie=G(W,[["__scopeId","data-v-37696535"]]);export{ie as default};
