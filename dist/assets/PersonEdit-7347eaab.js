import{_ as U}from"./index.vue_vue_type_style_index_0_lang-447fd504.js";import{d as A,r as i,I as V,o as v,D as O,at as Q,a as l,v as $,g as x,f as u,e as r,h as c,u as T,b as y,y as G,i as p,bc as H,bg as J,bn as W,_ as X}from"./index-db94d997.js";import{f as Z,u as ee,h as te}from"./index-2196a4be.js";import{D as ae}from"./dayjs-a8e42122.js";import{_ as ne}from"./index-39334618.js";import{_ as re}from"./index-4a280682.js";import"./CForm-ffa1b2bc.js";import"./index-42d7fb9b.js";import"./index-07f7e8bf.js";import"./customParseFormat-ed0c33ac.js";const oe={class:"areaPrice"},de={class:"areaPrice_table"},se={class:"text-hide"},le={class:"text-hide"},ce={class:"text-hide"},ie=A({__name:"PersonEdit",setup(ue){i(!1);const _=i(),z=["provinceCodeList","cityCodeList","areaCodeList"],E=i([]),I=i([]),D=i(!1),m=i(!1),S=V(),K=[{title:"产权公司",key:"companyCodeList",dataIndex:"companyCodeList",valueType:"multipleSelect",maxTagCount:3,width:120,valueEnum:I,order:1,render:!0,fixed:"left"},{title:"科目",dataIndex:"subject",search:!1,resizable:!0,width:100,fixed:"left"},{title:"年份",key:"yKey",dataIndex:"yKey",search:!1,resizable:!0,render:!0,width:120,fixed:"left"},{title:"时间周期",dataIndex:"datongrd",valueType:"dateRange",search:!0,hideInTable:!0,dateFormat:"YYYY",width:120,order:2},{title:"1月",key:"month",dataIndex:"january",search:!1,resizable:!0,render:!0,width:120},{title:"2月",key:"month",dataIndex:"february",search:!1,resizable:!0,render:!0,width:120},{title:"3月",key:"month",dataIndex:"march",search:!1,resizable:!0,render:!0,width:120},{title:"4月",key:"month",dataIndex:"april",search:!1,resizable:!0,render:!0,width:120},{title:"5月",key:"month",dataIndex:"may",search:!1,resizable:!0,render:!0,width:120},{title:"6月",key:"month",dataIndex:"june",search:!1,resizable:!0,render:!0,width:120},{title:"7月",key:"month",dataIndex:"july",search:!1,resizable:!0,render:!0,width:120},{title:"8月",key:"month",dataIndex:"august",search:!1,resizable:!0,render:!0,width:120},{title:"9月",key:"month",dataIndex:"september",search:!1,resizable:!0,render:!0,width:120},{title:"10月",key:"month",dataIndex:"october",search:!1,resizable:!0,render:!0,width:120},{title:"11月",key:"month",dataIndex:"november",search:!1,resizable:!0,render:!0,width:120},{title:"12月",key:"month",dataIndex:"december",search:!1,resizable:!0,render:!0,width:120},{title:"合计",dataIndex:"eqSum",search:!1,resizable:!0,width:120},{title:"操作",key:"action",dataIndex:"action",search:!1,width:120,render:!0,fixed:"right"}];v(()=>{L()}),O(()=>{_.value.getInitialFormStateNew().hasOwnProperty("delStatus")&&C()});const L=()=>{Q({}).then(e=>{console.log("产权公司res=",e);let a=(e||[]).map(t=>({label:t.companyName,value:t.companyCode}));I.value=a})},N=()=>{S.push({path:"/financeManage/electricQuantity/baseDisassemble/personEdit/dataImport",query:{templateType:3,fileType:".csv,.xls,.xlsx",fileSize:30}})},P=async(e,o)=>{e.isEdit=!0,e.yKeyTem=e.yKey,e.januaryTem=e.january,e.februaryTem=e.february,e.marchTem=e.march,e.aprilTem=e.april,e.mayTem=e.may,e.juneTem=e.june,e.julyTem=e.july,e.augustTem=e.august,e.septemberTem=e.september,e.octoberTem=e.october,e.novemberTem=e.november,e.decemberTem=e.december},Y=e=>{e.isEdit=!1,D.value=!1,e.yKey=e.yKeyTem,e.january=e.januaryTem,e.february=e.februaryTem,e.march=e.marchTem,e.april=e.aprilTem,e.may=e.mayTem,e.june=e.juneTem,e.july=e.julyTem,e.august=e.augustTem,e.september=e.septemberTem,e.october=e.octoberTem,e.november=e.novemberTem,e.december=e.decemberTem},q=(e,o)=>{let a={...e};m.value=!0,ee(a).then(t=>{m.value=!1,console.log("修改成功"),H.info("修改成功"),C()}).catch(t=>{m.value=!1})},R=()=>{var o;let e=(o=_.value)==null?void 0:o.getInitialFormStateNew();te(e)},w=i([]),M=e=>{w.value=e||[]},C=()=>{var e;(e=_.value)==null||e.reload()},B=e=>{const o=new Map;return e.forEach(a=>{const t=z[a.level-1];if(o.has(t))o.get(t).push(a.value);else{let s=[];s.push(a.value),o.set(t,s)}}),Object.fromEntries(o)},j=(e,o,a)=>!e||!o?[]:(e.forEach(t=>{o.find(h=>t.value===h)&&a.push(t),t.children&&t.children.length>0&&j(t.children,o,a)}),a),F=(e,o)=>new Promise(a=>{const t=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[0]:"",s=e!=null&&e.datongrd&&(e==null?void 0:e.datongrd.length)>0?e==null?void 0:e.datongrd[1]:"";let h={delStatus:0,noJoin:!0,startYKey:t,endYKey:s};const g=j(E.value,e==null?void 0:e.cityTree,[]);let k=B(g);e==null||delete e.datongrd;const d={...h,...e,...k};a(d)});return(e,o)=>{const a=J,t=ne,s=W,h=ae,g=re,k=U;return l(),$("div",oe,[x("div",de,[u(k,{columns:K,ref_key:"actionRef",ref:_,request:T(Z),"label-col":{style:{width:"100px"}},"wrapper-col":{span:16},scroll:{x:500},"before-query-params":F,onGetDataSource:M},{tableHeader:r(()=>[u(t,null,{default:r(()=>[u(a,{onClick:N},{default:r(()=>[c("批量上传")]),_:1}),T(w).length>0?(l(),y(a,{key:0,type:"primary",onClick:R},{default:r(()=>[c("导出")]),_:1})):G("",!0)]),_:1})]),companyCodeListRender:r(({column:d,record:n,index:f})=>[u(s,null,{title:r(()=>[c(p(n.companyName),1)]),default:r(()=>[x("span",se,p(n.companyName),1)]),_:2},1024)]),yKeyRender:r(({column:d,record:n,index:f})=>[n.isEdit?(l(),y(h,{key:0,value:n[d.dataIndex],"onUpdate:value":b=>n[d.dataIndex]=b,style:{width:"95%"},picker:"year",valueFormat:"YYYY"},null,8,["value","onUpdate:value"])):(l(),y(s,{key:1},{title:r(()=>[c(p(n[d.dataIndex]),1)]),default:r(()=>[x("span",le,p(n[d.dataIndex]),1)]),_:2},1024))]),monthRender:r(({column:d,record:n,index:f})=>[n.isEdit?(l(),y(g,{key:0,value:n[d.dataIndex],"onUpdate:value":b=>n[d.dataIndex]=b,stringMode:!0,maxlength:15,style:{width:"95%"},controls:!1},null,8,["value","onUpdate:value"])):(l(),y(s,{key:1},{title:r(()=>[c(p(n[d.dataIndex]),1)]),default:r(()=>[x("span",ce,p(n[d.dataIndex]),1)]),_:2},1024))]),actionRender:r(({column:d,record:n,index:f})=>[u(t,null,{default:r(()=>[n.isEdit?(l(),y(t,{key:1},{default:r(()=>[u(a,{size:"small",type:"link",onClick:()=>Y(n)},{default:r(()=>[c(" 取消 ")]),_:2},1032,["onClick"]),u(a,{size:"small",type:"link",onClick:b=>q(n,f),disabled:T(m)},{default:r(()=>[c(" 保存 ")]),_:2},1032,["onClick","disabled"])]),_:2},1024)):(l(),y(a,{key:0,size:"small",type:"link",onClick:()=>P(n,f)},{default:r(()=>[c(" 编辑 ")]),_:2},1032,["onClick"]))]),_:2},1024)]),_:1},8,["request"])])])}}});const Te=X(ie,[["__scopeId","data-v-9cd70b05"]]);export{Te as default};
