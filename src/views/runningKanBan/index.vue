<template>
  <div class="card">
    <a-row :span="24">
      <a-col :span="6" style="padding-right: 12px">
        <a-select placeholder="全部公司" style="width: 100%" disabled v-model:value="formState.companyCode" :options="companyCodeData" />
      </a-col>
      <a-col :span="6" style="padding-left: 12px">
        <a-select placeholder="全部省" style="width: 100%" allowClear v-model:value="formState.provinceCode" :options="provinceCodeData" />
      </a-col>
    </a-row>
  </div>
  <div class="card" style="margin-top: 12px">
    <div class="title" style="
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      ">
      发电情况总览
      <a-button type="primary" :loading="exportLoading" @click="exportExcel" v-if="dataSource.length > 0">导出</a-button>
    </div>
    <a-table sticky :columns="columns" :data-source="dataSource" :scroll="{ x: '100%', y: 300 }" :pagination="false" :loading="loading">
      <template #emptyText>
        <a-empty :image="simpleImage" :imageStyle="{ height: '100px' }" />
      </template>
    </a-table>
  </div>
  <div style="display: flex; margin-top: 12px; margin-bottom: 24px; height: 300px">
    <div class="card" style="flex: 1; margin-right: 6px; padding: 12px 24px; overflow-y: auto">
      <a-spin :spinning="loading1" :delay="delayTime">
        <div class="header">
          <div class="title">项目公司利用小时数排名</div>
          <div class="radio_box">
            <el-radio-group v-model="radioType1" @change="dateChange1">
              <el-radio-button v-for="(item, i) in radioData1" :key="i" :value="item.value">{{
                item.name
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="list_item" style="color: #999; border-bottom-width: 0">
          <div style="width: 60px">排名</div>
          <div style="flex: 1">公司名称</div>
          <div style="width: 90px">昨日利用小时</div>
        </div>
        <div v-if="dataSource1.length > 0" style="overflow-y: auto; height: 200px">
          <div class="list_item" v-for="(item, index) in dataSource1">
            <div style="width: 60px">{{ index + 1 }}</div>
            <div style="flex: 1">{{ item.companyName }}</div>
            <div style="width: 80px">{{ item.useHours }}</div>
          </div>
        </div>
        <div v-else>
          <a-empty :image="simpleImage" :imageStyle="{ height: '100px' }" />
        </div>
      </a-spin>
    </div>

    <div class="card" style="flex: 1; margin: 0px 6px; padding: 12px 24px">
      <a-spin :spinning="loading2" :delay="delayTime">
        <div class="header">
          <div class="title">项目公司发电计划完成情况排名</div>
          <div class="radio_box">
            <el-radio-group v-model="radioType2" @change="dateChange2">
              <el-radio-button v-for="(item, i) in radioData2" :key="i" :value="item.value">{{
                item.name
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="list_item" style="color: #999; border-bottom-width: 0">
          <div style="width: 60px">排名</div>
          <div style="flex: 1">公司名称</div>
          <div style="width: 120px">发电计划完成率(%)</div>
        </div>
        <div v-if="dataSource2.length > 0" style="overflow-y: auto; height: 200px">
          <div class="list_item" v-for="(item, index) in dataSource2">
            <div style="width: 60px">{{ index + 1 }}</div>
            <div style="flex: 1">{{ item.companyName }}</div>
            <div style="width: 120px">{{ item.completedRate }}</div>
          </div>
        </div>
        <div v-else>
          <a-empty :image="simpleImage" :imageStyle="{ height: '100px' }" />
        </div>
      </a-spin>
    </div>

    <div class="card" style="flex: 1; margin-left: 6px; padding: 12px 24px; overflow-y: auto">
      <a-spin :spinning="loading3" :delay="delayTime">
        <div class="header">
          <div class="title">项目公司发电量同比增长排名</div>
          <div class="radio_box">
            <el-radio-group v-model="radioType3" @change="dateChange3">
              <el-radio-button v-for="(item, i) in radioData2" :key="i" :value="item.value">{{
                item.name
              }}</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="list_item" style="color: #999; border-bottom-width: 0">
          <div style="width: 60px">排名</div>
          <div style="flex: 1">公司名称</div>
          <div style="width: 120px">同比发电量增长(%)</div>
        </div>
        <div style="overflow-y: auto; height: 200px">
          <div v-if="dataSource3.length > 0">
            <div class="list_item" v-for="(item, index) in dataSource3">
              <div style="width: 60px">{{ index + 1 }}</div>
              <div style="flex: 1">{{ item.companyName }}</div>
              <div style="width: 120px">{{ item.upRate }}</div>
            </div>
          </div>
          <div v-else>
            <a-empty :image="simpleImage" :imageStyle="{ height: '100px' }" />
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue'
import { Empty } from 'ant-design-vue'
import { getCompanyList, getCityTreeRequest } from '@/api/common'
import {
  getEqStatusOverview,
  exportEqStatusOverview,
  getEqUseHours,
  getPlanEqCompletedRank,
  getCompanyUp
} from '@/api/runningKanBan'

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

const formState = reactive<any>({
  companyCode: undefined,
  provinceCode: undefined
})
const delayTime = 300
const columns = ref<TableColumnsType>([
  {
    title: '项目公司',
    width: 180,
    dataIndex: 'companyName',
    fixed: 'left',
    ellipsis: true
  },
  {
    title: '装机容量(万kWh)',
    width: 160,
    dataIndex: 'capins',
    key: 'age'
  },
  {
    title: '当日发电量(万kWh)',
    dataIndex: 'todayEq',
    width: 160
  },
  {
    title: '月累计发电量(万kWh)',
    dataIndex: 'monthTotalEq',
    key: '2',
    width: 180
  },
  {
    title: '年累计发电量(万kWh)',
    dataIndex: 'yearTotalEq',
    key: '3',
    width: 180
  },
  {
    title: '当日利用小时数(h)',
    dataIndex: 'dayUseHours',
    key: '4',
    width: 150
  },
  // {
  //   title: '月利用小时数(h)',
  //   dataIndex: 'monthTotalEq',
  //   key: '5',
  //   width: 150
  // },
  // {
  //   title: '年利用小时数(h)',
  //   dataIndex: 'yearUseHours',
  //   key: '6',
  //   width: 150
  // },
  {
    title: '年基准发电量',
    dataIndex: 'monthDatumEq',
    key: '7',
    width: 150
  },
  { title: '年计划发电量', dataIndex: 'yearPlanEq', key: '8', width: 150 },
  { title: '月同比增长', dataIndex: 'monthEqYoyAddRate', key: '9', width: 150 },
  { title: '年同比增长', dataIndex: 'yearEqYoyAddRate', key: '10', width: 150 },
  { title: '月基准完成占比', dataIndex: 'monthDatumCompletedRate', key: '11', width: 150 },
  { title: '月计划完成占比', dataIndex: 'monthPlanCompletedRate', key: '12', width: 150 },
  { title: '年基准完成占比', dataIndex: 'yearDatumCompletedRate', key: '13', width: 150 },
  { title: '年计划完成占比', dataIndex: 'yearPlanCompletedRate', key: '14', width: 150 }
])

const data = []
for (let i = 0; i < 23; i++) {
  data.push({
    key: i,
    name: `项目公司${i + 1}`,
    age: 32,
    address: `${i + 1}`
  })
}
const data1 = [{}, {}, {}, {}, {}, {}, {}, {}]
const loading = ref<boolean>(false)
const dataSource = ref<any>([])
const loading1 = ref<boolean>(false)
const dataSource1 = ref<any>([])
const dateChange1 = (val: any) => {
  console.log('val1=', val)
}
const loading2 = ref<boolean>(false)
const dataSource2 = ref<any>([])
const dateChange2 = (val: any) => {
  console.log('val2=', val)
}
const loading3 = ref<boolean>(false)
const dataSource3 = ref<any>([])
const dateChange3 = (val: any) => {
  console.log('val3=', val)
}
const radioType1 = ref<any>(3)
const radioData1 = ref<any>([
  {
    value: 3,
    name: '日'
  },
  {
    value: 1,
    name: '月'
  },
  {
    value: 2,
    name: '年'
  }
])
const radioType2 = ref<any>(1)
const radioType3 = ref<any>(1)
const radioData2 = ref<any>([
  {
    value: 1,
    name: '月'
  },
  {
    value: 2,
    name: '年'
  }
])
let pageParams = {}
const getTableList = () => {
  let params = {
    companyCode: formState.companyCode,
    provinceCode: formState.provinceCode
  }
  loading.value = true
  getEqStatusOverview(params)
    .then((res: any) => {
      console.log('发电总览res=', res)
      loading.value = false
      pageParams = params
      dataSource.value = res || []
    })
    .catch((err: any) => {
      loading.value = false
    })
}
const exportLoading = ref<any>(false)
const exportExcel = () => {
  exportLoading.value = true
  exportEqStatusOverview(pageParams)
    .then((res: any) => {
      console.log('导出res=', res)
      exportLoading.value = false
    })
    .catch((err: any) => {
      exportLoading.value = false
    })
}
//公司利用小时数排名
const getEqUseHoursList = () => {
  let params = {
    type: radioType1.value,
    companyCode: formState.companyCode,
    provinceCode: formState.provinceCode
  }
  loading1.value = true
  getEqUseHours(params)
    .then((res: any) => {
      loading1.value = false
      dataSource1.value = res || []
    })
    .catch((err: any) => {
      loading1.value = false
    })
}
//公司发电计划完成排行
const getPlanEqCompletedRankList = () => {
  let params = {
    type: radioType2.value,
    companyCode: formState.companyCode,
    provinceCode: formState.provinceCode
  }
  loading2.value = true
  getPlanEqCompletedRank(params)
    .then((res: any) => {
      loading2.value = false
      dataSource2.value = res || []
    })
    .catch((err: any) => {
      loading2.value = false
    })
}
//公司同比增长排名
const getCompanyUpList = () => {
  let params = {
    type: radioType3.value,
    companyCode: formState.companyCode,
    provinceCode: formState.provinceCode
  }
  loading3.value = true
  getCompanyUp(params)
    .then((res: any) => {
      loading3.value = false
      dataSource3.value = res || []
    })
    .catch((err: any) => {
      loading3.value = false
    })
}

onMounted(() => {
  getCompanyData()
  getProvinceCodeData()
})

watch(
  () => formState.companyCode,
  (newVal: any) => {
    getTableList()
    getEqUseHoursList()
    getPlanEqCompletedRankList()
    getCompanyUpList()
  },
  {
    immediate: true
  }
)
watch(radioType1, () => {
  getEqUseHoursList()
})
watch(radioType2, () => {
  getPlanEqCompletedRankList()
})
watch(radioType3, () => {
  getCompanyUpList()
})

const companyCodeData = ref<any>([])
const getCompanyData = () => {
  getCompanyList({}).then((res: any) => {
    let data = res || []
    let newData = data.map((item: any) => {
      return { label: item.companyName, value: item.companyCode }
    })
    companyCodeData.value = newData
    newData.forEach((item: any) => {
      console.log('item1', item)
      if (item.value === '67dbc931-294f-4bc4-aea4-148699e97615') {
        console.log('item', item.value)
        formState.companyCode = '67dbc931-294f-4bc4-aea4-148699e97615'
      }
    })
  })
}
const provinceCodeData = ref<any>([])
const getProvinceCodeData = () => {
  getCityTreeRequest({ pid: 0 }).then((res: any) => {
    if (res && res.length > 0) {
      let result = res.map((item: any) => {
        return {
          label: item.name,
          value: item.code
        }
      })
      provinceCodeData.value = result
    }
  })
}
</script>
<style lang="less" scoped>
.card {
  padding: 24px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);

  .header {
    height: 44px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .radio_box {
      :deep(.el-radio-group) {
        .el-radio-button {
          width: 28px;

          &.is-active {
            .el-radio-button__inner {
              color: var(--el-color-primary);
              background: rgb(41 204 160 / 10%);
            }
          }

          .el-radio-button__inner {
            display: block;
            padding: 6px 0;
          }
        }
      }
    }
  }

  .title {
    color: #333;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    /* 137.5% */
  }

  .list_item {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    /* 137.5% */
    height: 36px;
    border-bottom: solid 1px #f4eded;
  }
}
</style>
