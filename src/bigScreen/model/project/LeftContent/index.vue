<template>
  <BCard title="发电计划完成率" :isHidePadding="true">
    <template #content>
      <div class="content_box">
        <div class="formList">
          <BTab :tabList="tabList" @tabChange="handleTabChange" :tabValue="tabValue"></BTab>
          <span style="color: #fff; margin-left: 20px">万kWh</span>
        </div>
        <div class="BTable_wrap">
          <BTable :columns="customColumns" :dataList="dataList" :isHideRowSty="true" minHeight="28px">
            <template #zoneNameTitle>
              <div style="display: flex; align-items: center">
                {{ cockpitType === 1 ? '省份' : cockpitType === 2 ? '市' : '' }}
                <img :src="switchImg" style="margin-left: 4px; cursor: pointer" @click="switchColumns" />
              </div>
            </template>
            <template #nameTitle>
              <div style="display: flex; align-items: center">
                公司
                <img :src="switchImg" style="margin-left: 4px; cursor: pointer" @click="switchColumns" />
              </div>
            </template>
          </BTable>
        </div>
      </div>
    </template>
  </BCard>
</template>

<script setup lang="ts">
import type { columnsProps } from '../../../components/BTable/index.vue'
import BTab from '../../../components/BTab/index.vue'
import BCard from '../../../components/BCard/index.vue'
import BTable from '../../../components/BTable/index.vue'
import { getInstalledCapacityListNew, getInstalledCapacityCompanyListNew } from '@/bigScreen/api'
import switchImg from '@/bigScreen/assets/images/switch.png'
import { useCompanyStore } from '@/stores/companyTree'

const companyStore = useCompanyStore()

interface Props {
  cockpitType: any
  zoneData: any
}

const props = withDefaults(defineProps<Props>(), {
  cockpitType: '',
  zoneData: {}
})

const { cockpitType, zoneData } = toRefs(props)

const tabValue = ref<number>(1)
const columns1 = ref<columnsProps[]>([
  {
    name: '排行',
    key: 'zoneName',
    width: '14%',
    isSort: true
  },
  {
    name: '省份',
    key: 'zoneName',
    width: '20%',
    isCustom: true,
    isSort: false
  },
  {
    name: '计划发电量',
    width: '25%',
    key: 'planPower',
    formatMoney: 1,
    isSort: false
  },
  {
    name: '已完成发电量',
    width: '25%',
    key: 'completedPower',
    formatMoney: 1,
    isSort: false
  },
  {
    name: '完成比例',
    width: '16%',
    key: 'completedRatio',
    isSort: false,
    formatPercentage: 1
  }
])
const columns2 = ref<columnsProps[]>([
  {
    name: '排行',
    key: 'zoneName',
    width: '14%',
    isSort: true
  },
  {
    name: '市',
    key: 'zoneName',
    width: '20%',
    isCustom: true,
    isSort: false
  },
  {
    name: '计划发电量',
    width: '25%',
    key: 'planPower',
    formatMoney: 1,
    isSort: false
  },
  {
    name: '已完成发电量',
    width: '25%',
    key: 'completedPower',
    formatMoney: 1,
    isSort: false
  },
  {
    name: '完成比例',
    width: '16%',
    key: 'completedRatio',
    isSort: false,
    formatPercentage: 1
  }
])
const columns3 = ref<columnsProps[]>([
  {
    name: '排行',
    key: 'name',
    width: '14%',
    isSort: true
  },
  {
    name: '公司',
    key: 'name',
    width: '20%',
    isCustom: true,
    isSort: false
  },
  {
    name: '计划发电量',
    width: '25%',
    key: 'planPower',
    formatMoney: 1,
    isSort: false
  },
  {
    name: '已完成发电量',
    width: '25%',
    key: 'completedPower',
    formatMoney: 1,
    isSort: false
  },
  {
    name: '完成比例',
    width: '16%',
    key: 'completedRatio',
    isSort: false,
    formatPercentage: 1
  }
])
const dataList = ref<any[]>([])
const tabList = [
  {
    label: '本月',
    value: 1
  },
  {
    label: '本年',
    value: 2
  }
]
const showProvince = ref<boolean>(true)
const switchColumns = () => {
  console.log('省份-公司切换')
  showProvince.value = !showProvince.value
  getData()
}
const customColumns = computed(() => {
  let columns: any = ''
  if (showProvince.value) {
    columns =
      cockpitType.value === 1 ? columns1.value : cockpitType.value === 2 ? columns2.value : ''
  } else {
    columns = columns3.value
  }
  return columns
})

onMounted(() => {
  getData()
})

watch(
  () => companyStore.companyCode,
  (newVal: any) => {
    getData()
  }
)

watch([cockpitType, zoneData], (newVal: any) => {
  getData()
})

// 处理重庆市数据的专门函数
const processChongqingData = (data: any[]) => {
  if (!data || data.length === 0) return data

  // 找到重庆市的数据项
  const chongqingIndex = data.findIndex(item => item.zoneName === '重庆市')

  if (chongqingIndex !== -1) {
    // 获取重庆市数据并修改planPower为0
    const chongqingData = { ...data[chongqingIndex], planPower: 0, completedRatio: null }

    // 从原位置移除重庆市数据
    const newData = data.filter((_, index) => index !== chongqingIndex)

    // 将修改后的重庆市数据添加到末尾
    newData.push(chongqingData)

    return newData
  }

  return data
}

const getData = () => {
  if (showProvince.value) {
    getProvinceData()
  } else {
    getDataCompany()
  }
}
const getProvinceData = () => {
  let params = {
    cockpitType: cockpitType.value,
    timeType: tabValue.value,
    zoneCode: zoneData.value?.zoneCode,
    assetCompanyCodes: companyStore.companyCode
  }
  getInstalledCapacityListNew(params).then((res: any) => {
    console.log('res=1111', res)
    const processedData = processChongqingData(res || [])
    dataList.value = processedData
  })
}
const getDataCompany = () => {
  let params = {
    cockpitType: cockpitType.value,
    timeType: tabValue.value,
    zoneCode: zoneData.value?.zoneCode,
    assetCompanyCodes: companyStore.companyCode
  }
  getInstalledCapacityCompanyListNew(params).then((res: any) => {
    console.log('res=1111', res)
    // const processedData = processChongqingData(res || [])
    dataList.value = res || []
    console.log(123323, dataList.value)
  })
}

const handleTabChange = (value: number) => {
  tabValue.value = value
}
watch(
  tabValue,
  () => {
    getData()
  }
  // {
  //   immediate: true
  // }
)
</script>

<style lang="less" scoped>
.content_box {
  display: flex;
  flex-direction: column;
  height: 100%;

  .formList {
    flex-shrink: 0;
    padding: 16px 16px 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .BTable_wrap {
    flex: 1;
    overflow: hidden;
  }
}
</style>
