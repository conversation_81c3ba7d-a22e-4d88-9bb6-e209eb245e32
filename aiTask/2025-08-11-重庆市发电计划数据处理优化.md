# 上下文
文件名: aiTask/2025-08-11-重庆市发电计划数据处理优化.md
创建于: 2025-08-11 13:47:16
创建者: victory
关联协议: AUGMENT-AI + 多维思考 + 代理执行协议

# 任务描述
在返回数据中有重庆市，需要把重庆市的planPower变为0，放到数据的最后一条

# 项目概述
大屏项目中的发电计划完成率模块，需要对重庆市的数据进行特殊处理，将其计划发电量设为0并移动到列表末尾

---
*以下部分由AI在协议执行过程中维护*
---

# 分析 (由RESEARCH模式填充)
通过代码分析发现：
1. 数据通过 `getInstalledCapacityListNew` 或 `getInstalledCapacityCompanyListNew` API 获取
2. 数据项包含 `zoneName`（省份/市名称）和 `planPower`（计划发电量）字段
3. 从配置文件可以看到重庆市的标识是 "重庆市"
4. 数据在 `getProvinceData()` 和 `getDataCompany()` 两个函数中处理
5. 需要在数据获取后、赋值给 `dataList.value` 之前进行处理

# 提议的解决方案 (由INNOVATE模式填充)
评估了三种方案：
1. 在数据获取后直接处理 - 简单但需要重复代码
2. 使用计算属性处理 - 符合响应式理念但增加计算开销
3. 创建专门的数据处理函数 - 代码复用性好，逻辑清晰

最终选择方案3：创建专门的数据处理函数来处理重庆市的特殊逻辑

# 实现计划 (由PLAN模式生成)
文件修改：`src/bigScreen/model/project/LeftContent/index.vue`

Implementation Checklist:
1. 在 `getData` 函数之前添加 `processChongqingData` 数据处理函数
2. 修改 `getProvinceData` 函数中的数据赋值逻辑，调用数据处理函数
3. 修改 `getDataCompany` 函数中的数据赋值逻辑，调用数据处理函数

# 当前执行步骤 (由EXECUTE模式在开始步骤时更新)
> 当前正在执行: "所有步骤已完成"

# 任务进度 (由EXECUTE模式在每个步骤完成后追加)
*   2025-08-11 13:47:16
    *   步骤: 1. 在 `getData` 函数之前添加 `processChongqingData` 数据处理函数
    *   修改: src/bigScreen/model/project/LeftContent/index.vue (第214-241行)
    *   更改摘要: 添加了专门处理重庆市数据的函数，将重庆市的planPower设为0并移到数组末尾
    *   原因: 执行计划步骤 1
    *   阻塞因素: 无
    *   用户确认状态: 等待确认

*   2025-08-11 13:47:16
    *   步骤: 2. 修改 `getProvinceData` 函数中的数据赋值逻辑，调用数据处理函数
    *   修改: src/bigScreen/model/project/LeftContent/index.vue (第255-259行)
    *   更改摘要: 在省份数据获取后调用processChongqingData函数处理数据
    *   原因: 执行计划步骤 2
    *   阻塞因素: 无
    *   用户确认状态: 等待确认

*   2025-08-11 13:47:16
    *   步骤: 3. 修改 `getDataCompany` 函数中的数据赋值逻辑，调用数据处理函数
    *   修改: src/bigScreen/model/project/LeftContent/index.vue (第268-272行)
    *   更改摘要: 在公司数据获取后调用processChongqingData函数处理数据
    *   原因: 执行计划步骤 3
    *   阻塞因素: 无
    *   用户确认状态: 等待确认

# 最终审查 (由REVIEW模式填充)
**实现完全符合最终计划。**

验证结果：
- ✅ 成功添加了 `processChongqingData` 数据处理函数
- ✅ 函数正确识别重庆市数据并将 planPower 设为 0
- ✅ 重庆市数据被正确移动到数组末尾
- ✅ 两个数据获取函数都正确调用了处理函数
- ✅ 代码质量良好，注释完整，错误处理完善
- ✅ 不会影响其他数据项的正常显示

功能测试要点：
1. 当返回数据包含重庆市时，重庆市的 planPower 应为 0 且位于列表末尾
2. 当返回数据不包含重庆市时，其他数据正常显示
3. 空数据或异常数据不会导致错误
